version: 1

applications:
  - frontend:
      buildPath: '/'
      phases:
        preBuild:
          commands:
            # - if [ "${AWS_BRANCH}" = "main" ]; then echo "VITE_SERVER_TYPE=prod" > app/client/.env; fi
            # - if [ "${AWS_BRANCH}" = "test" ]; then echo "VITE_SERVER_TYPE=test" > app/client/.env; fi
            - echo "SL_NPM_TOKEN=${SL_NPM_TOKEN}" >> .env
            - echo "VITE_COGNITO_USER_POOL_ID=${VITE_COGNITO_USER_POOL_ID}" >> .env
            - echo "VITE_COGNITO_CLIENT_ID=${VITE_COGNITO_CLIENT_ID}" >> .env
            - curl -fsSL https://bun.sh/install | bash
            - source /root/.bashrc
            - bun install
        build:
          commands:
            - bun run build:client
      artifacts:
        # IMPORTANT - Please verify your build output directory
        baseDirectory: app/client/dist
        files:
          - '**/*'
    env:
      variables:
        # Add your environment variables here
        AWS_COGNITO_REGION: us-east-1
        AWS_COGNITO_IDENTITY_POOL: us-east-1:842ab1b1-3488-4d69-bf11-57a05a80aaf8
        VITE_COGNITO_USER_POOL_ID: us-east-1_sxzcOZ6F2
        VITE_COGNITO_CLIENT_ID: 2e304o8db57tg73pr4nbreiq75
        SL_NPM_TOKEN: OUawjAiEGNXnN2pJgykU1gY1EJsF/h0V8tmFZ4xHoD0=

    appRoot:
      app/client
      # cache:
      #   paths:
      #     - node_modules/**/*
