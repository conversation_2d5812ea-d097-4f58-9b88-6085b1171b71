{"name": "marketplace-app", "devDependencies": {"@types/bun": "^1.2.14", "@types/lodash": "^4.17.18", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "^5.8.3"}, "private": true, "scripts": {"dev:app": "bun --filter '@marketplace/{client,server}' dev", "dev:client": "bun --filter @marketplace/client dev", "dev:server": "bun --filter @marketplace/server dev", "build:client": "bun --filter @marketplace/client build", "build:server": "bun --filter @marketplace/server build", "preview:client": "bun --filter @marketplace/client preview", "start:server": "bun --filter @marketplace/server start"}, "workspaces": ["app/*"], "dependencies": {"@sentry/browser": "^9.40.0", "@sentry/react": "^9.40.0", "@spatiallaser/map": "1.0.263"}}