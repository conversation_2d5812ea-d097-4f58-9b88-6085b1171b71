# Marketplace Application

## Project Overview

Marketplace Application is a modern web application built with <PERSON><PERSON>, Vite, and Bun. It provides a platform for property listing and management with integration to various backend services.

## Technology Stack

- **Frontend**:
  - React
  - Vite
  - TanStack Router
  - AWS Amplify
  - TypeScript
  - Tailwind CSS

- **Backend**:ß
  - Bun
  - Express
  - TypeScript

- **Icons**:
  - lucide-react

## Project Structure

```
marketplace-app-v1/
├── app/
│   ├── client/          # Frontend application
│   │   ├── src/        # Source code
│   │   ├── public/     # Static assets
│   │   └── package.json
│   └── server/         # Backend application
│       ├── src/        # Source code
│       ├── .env        # Environment variables
│       └── package.json
└── package.json        # Root package.json (monorepo configuration)
```

## Development Setup

### Prerequisites

- Node.js (v18 or later)
- Bun (v1.0 or later)

### Installation

1. Clone the repository
2. Install dependencies:
```bash
bun install
```

### Environment Setup

1. Copy `.env.example` to `.env` in both the `app/client` and `app/server` directories if they don't already exist.
2. Update the environment variables as needed.  Specifically:
    * In `app/server/.env`, set `BACKEND_PORT` (default: 3001) to configure the backend server port.
    * In `app/client/.env`, set `VITE_BACKEND_URL` (default: http://localhost:3001) to configure the backend URL for the frontend proxy.

### Running the Application

#### Development Mode

Run both frontend and backend:
```bash
bun run dev:app
```
The frontend will be accessible at http://localhost:8000, and the backend API will be running on http://localhost:3001 (or the port specified by `BACKEND_PORT`).

Run frontend only:
```bash
bun run dev:client
```

Run backend only:
```bash
bun run dev:server
```

#### Production Build

Build frontend:
```bash
bun run build:client
```

Build backend:
```bash
bun run build:server
```

## API Documentation

The application integrates with several backend services:

- MLS Services
- Parcel Services
- Acquisition Services
- Underwriting Services
- Tracking Services

For detailed API documentation, please refer to [API_DOCUMENTATION_EXAMPLES.md](doc/API_DOCUMENTATION_EXAMPLES.md).

## Authentication

The application uses AWS Cognito for authentication. To set up authentication:

1. Create a Cognito User Pool in AWS Console
2. Update the environment variables in `.env`:
   - `VITE_COGNITO_USER_POOL_ID`
   - `VITE_COGNITO_CLIENT_ID`

## Code Standards

This project uses ESLint and Prettier for code quality and consistency:

```bash
# Run linting
bun run lint

# Format code
bun run format
```

## Deployment

### Frontend
The frontend can be deployed to any static hosting service (e.g., AWS S3, Vercel, Netlify).

### Backend
The backend can be deployed to any Node.js hosting service (e.g., AWS EC2, Heroku).

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request
