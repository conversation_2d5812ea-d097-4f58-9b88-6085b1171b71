import { Express } from 'express';
import axios from 'axios';

export function setupRoutes(app: Express) {
  // Health check endpoint
  app.get('/health', (req, res) => {
    res.json({ status: 'ok' });
  });

  // OffMarket API endpoints
  app.get('/api/offMarket/:serverType/*', async (req, res) => {
    try {
      const serverType = req.params.serverType;
      const baseUrl = serverType === 'prod' 
        ? 'http://ec2-54-146-231-140.compute-1.amazonaws.com:8055'
        : 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8080';
      
      // Get the remaining path after /api/offMarket/:serverType/
      const remainingPath = req.path.split('/').slice(4).join('/');
      const targetUrl = `${baseUrl}/${remainingPath}`;
      
      console.log('Request details:', {
        originalPath: req.path,
        remainingPath,
        targetUrl,
        method: req.method,
        headers: req.headers,
        query: req.query,
        body: req.body
      });
      
      const response = await axios({
        method: req.method,
        url: targetUrl,
        data: { ...req.body, ...req.query },
        headers: {
          ...req.headers,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`Response from ${targetUrl}:`, {
        status: response.status,
        statusText: response.statusText,
        data: response.data
      });
      
      res.json(response.data);
    } catch (error: any) {
      console.error('Error in offMarket endpoint:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers
        }
      });
      
      if (error.response) {
        res.status(error.response.status).json(error.response.data);
      } else {
        res.status(500).json({ 
          error: 'Internal server error',
          message: error.message 
        });
      }
    }
  });

  // CMA API endpoints
  app.use('/api/cma/:serverType/*', async (req, res) => {
    try {
      const serverType = req.params.serverType;
      const baseUrl = serverType === 'prod'
        ? 'http://ec2-54-146-231-140.compute-1.amazonaws.com:8080'
        : 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8080';

      const targetUrl = `${baseUrl}/mls`;

      console.log('Request details:', {
        originalPath: req.path,
        targetUrl,
        method: req.method,
        headers: req.headers,
        query: req.query,
        body: req.body
      });

      const response = await axios({
        method: req.method,
        url: targetUrl,
        data: { ...req.body, ...req.query },
        headers: {
          ...req.headers,
          'Content-Type': 'application/json'
        }
      });

      console.log(`Response from ${targetUrl}:`, {
        status: response.status,
        statusText: response.statusText,
        data: response.data
      });

      res.json(response.data);
    } catch (error: any) {
      console.error('Error in CMA endpoint:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers
        }
      });

      if (error.response) {
        res.status(error.response.status).json(error.response.data);
      } else {
        res.status(500).json({
          error: 'Internal server error',
          message: error.message
        });
      }
    }
  });

  // Acquisition API endpoints
  app.use('/api/acq/:serverType', async (req, res) => {
    try {
      const serverType = req.params.serverType;
      const baseUrl = serverType === 'prod' 
        ? 'http://ec2-54-146-231-140.compute-1.amazonaws.com:8090'
        : 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8090';
      
      const response = await axios({
        method: req.method,
        url: `${baseUrl}${req.url}`,
        data: req.body,
        headers: req.headers,
        params: req.query
      });
      res.json(response.data);
    } catch (error: any) {
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  // Underwriting API endpoints
  app.get('/api/underwriting/:serverType/*', async (req, res) => { // Added /* to match subpaths
    try {
      const serverType = req.params.serverType;
      const baseUrl = serverType === 'prod'
        ? 'http://ec2-54-146-231-140.compute-1.amazonaws.com:8046'
        : 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8046';

      // Get the remaining path after /api/underwriting/:serverType/
      const remainingPath = req.path.split('/').slice(4).join('/'); // Adjusted slice index
      const targetUrl = `${baseUrl}/${remainingPath}`; // Use the remaining path

      // 1. Trim Query Params
      const trimmedQueryParams = Object.entries(req.query).reduce((acc, [key, value]) => {
        acc[key] = typeof value === 'string' ? value.trim() : value;
        return acc;
      }, {} as Record<string, any>);

      // 2. Prepare Headers to Forward
      const headersToForward: Record<string, string> = {};
      for (const key in req.headers) {
        // Forward most headers, but exclude problematic ones
        if (!['host', 'connection', 'content-length'].includes(key.toLowerCase())) {
          headersToForward[key] = req.headers[key] as string;
        }
      }
      // Ensure essential headers are present or default if missing from original request
      headersToForward['content-type'] = req.headers['content-type'] || 'application/json';
      headersToForward['accept'] = req.headers['accept'] || '*/*';
      // Keep existing Authorization logic (if Authorization header exists)
      if (!req.headers.authorization) {
          delete headersToForward.authorization; // Remove if not present in original
      }


      const response = await axios({
        method: req.method,
        url: targetUrl, // Use the constructed targetUrl
        data: req.body, // Send body separately
        headers: headersToForward, // Use the filtered & prepared headers
        params: trimmedQueryParams, // Use the trimmed query params
        timeout: 15000 // Add timeout for resilience (15 seconds)
      });
      console.log('Underwriting Response Status:', response.status);
      res.status(response.status).json(response.data); // Forward status and data
    } catch (error: any) {
      // --- DETAILED ERROR LOGGING ---
      console.error('Error in underwriting endpoint:', {
        message: error.message,
        status: error.response?.status,
        responseData: error.response?.data,
        requestConfig: {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers,
          params: error.config?.params,
          data: error.config?.data,
        },
        stack: error.stack // Log the stack trace
      });
      // --- END DETAILED LOGGING ---

      if (error.response) {
         res.status(error.response.status).json(error.response.data);
      } else {
         res.status(500).json({ error: 'Internal server error', message: error.message });
      }
    }
  });

  // Tracking API endpoints
  app.use('/api/tracking/:serverType', async (req, res) => {
    try {
      const serverType = req.params.serverType;
      const baseUrl = serverType === 'prod' 
        ? 'http://ec2-44-222-3-252.compute-1.amazonaws.com:8070'
        : 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8070';
      
      const response = await axios({
        method: req.method,
        url: `${baseUrl}${req.url}`,
        data: req.body,
        headers: req.headers,
        params: req.query
      });
      res.json(response.data);
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  });
}
