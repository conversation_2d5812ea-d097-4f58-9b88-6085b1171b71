{"name": "@marketplace/server", "version": "0.0.0", "private": true, "scripts": {"dev": "bun --watch src/index.ts", "start": "bun src/index.ts", "build": "tsc", "lint": "eslint ."}, "dependencies": {"axios": "^1.8.4", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.3"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.14.1", "bun-types": "latest", "eslint": "^9.21.0", "typescript": "^5.0.0"}}