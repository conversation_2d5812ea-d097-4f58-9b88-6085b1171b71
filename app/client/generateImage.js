import axios from 'axios';

const generateImage = async () => {
    try {
        const response = await axios.post('https://api.glitterly.app/generate-image', {
            prompt: 'A beautiful sunset over the mountains',
            size: '1920x1080',
            format: 'png'
        });

        console.log('Response Data:', response.data);
    } catch (error) {
        console.error('Error generating image:', error);
    }
};

generateImage();
