import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { TanStackRouterVite } from '@tanstack/router-plugin/vite';
import tailwindcss from '@tailwindcss/vite';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  base: `/portal/`,
  plugins: [
    // Please make sure that '@tanstack/router-plugin' is passed before '@vitejs/plugin-react'
    TanStackRouterVite({ target: 'react', autoCodeSplitting: true }),
    react(),
    tailwindcss(),
  ],
  define: {
    // for @spatiallaser/map
    global: 'window',
    'process.env': {
      NODE_ENV: JSON.stringify(process.env.NODE_ENV),
      // Add any other environment variables you need to expose
      VITE_COGNITO_USER_POOL_ID: JSON.stringify(process.env.VITE_COGNITO_USER_POOL_ID),
      VITE_COGNITO_CLIENT_ID: JSON.stringify(process.env.VITE_COGNITO_CLIENT_ID),
    },
  },
  resolve: {
    alias: {
      // Add this alias configuration
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: { // Add this server configuration
    // host: '0.0.0.0', // Optional: Ensures the server is accessible externally
    port: 8000, // Make sure this matches the port ngrok is forwarding
    // allowedHosts: [
    //   '3246-47-184-129-204.ngrok-free.app',
    //   // Add any other hosts you might need, e.g., localhost for local testing
    //   'localhost',
    //   '127.0.0.1',
    // ],
    proxy: {
      // MLS prod
      '/api/v1': {
        target: 'http://***********:8010',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/v1/, ''),
      },
      // MLS test
      '/api/test': {
        target: 'http://************:8010',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/test/, ''),
      },
      // smelly-bun-server
      '/api/sbs/exp': {
        target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:9003',
        // target: 'http://localhost:9003', 
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/sbs\/exp/, ''),
      },
      '/api/sbs/prod': {
        target: 'http://ec2-54-146-231-140.compute-1.amazonaws.com:9003',
        // target: 'http://localhost:9003',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/sbs\/prod/, ''),
      },
      
      // '/api/mls/test': {
      '/api/mlsTest': {
        target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8050',
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/api\/mls\/test/, ''),
        rewrite: (path) => path.replace(/^\/api\/mlsTest/, ''),
      },
      '/api/mls/prod': {
        target: 'http://ec2-54-146-231-140.compute-1.amazonaws.com:8050',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/mls\/prod/, ''),
      },
      // CMA backend for experiments
      '/api/cma/exp': {
        target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/cma\/exp/, ''),
      },
      '/api/cma/prod': {
        target: 'http://ec2-54-146-231-140.compute-1.amazonaws.com:8080',
        // target: 'http://ec2-44-222-3-252.compute-1.amazonaws.com:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/cma\/prod/, ''),
      },
      '/api/acq/exp': {
        // target: 'http://localhost:8090',
        target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8090',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/acq\/exp/, ''),
      },
      '/api/acq/prod': {
        // target: 'http://localhost:8090',
        target: 'http://ec2-54-146-231-140.compute-1.amazonaws.com:8090',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/acq\/prod/, ''),
      },
      '/api/acq/salesforceIntegrationTest': {
        target: 'http://ec2-18-218-137-70.us-east-2.compute.amazonaws.com:8090',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/acq\/salesforceIntegrationTest/, ''),
      },
      // reworked off-market APIs prod
      '/api/offMarket/prod': {
        target: 'http://ec2-54-146-231-140.compute-1.amazonaws.com:8055',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/offMarket\/prod/, ''),
      },
      // reworked off-market APIs text
      '/api/offMarket/exp': {
        target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8055',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/offMarket\/exp/, ''),
      },
      '/api/underwriting/prod': {
        target: 'http://ec2-54-146-231-140.compute-1.amazonaws.com:8046',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/underwriting\/prod/, ''),
      },
      '/api/underwriting/exp': {
        target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8046',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/underwriting\/exp/, ''),
      },
      '/api/tracking/prod': {
        target: 'http://ec2-44-222-3-252.compute-1.amazonaws.com:8070',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/tracking\/prod/, ''),
      },
      '/api/tracking/exp': {
        target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8070',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/tracking\/exp/, ''),
      },
      '/api/elixir/prod': {
        target: 'http://ec2-54-146-231-140.compute-1.amazonaws.com:4000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/elixir\/prod/, ''),
      },
      '/api/elixir/exp': {
        target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:4000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/elixir\/exp/, ''),
      },
      '/privacy-policy/': {
        target: 'http://localhost:8000/privacy-policy.html',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/privacy-policy\//, ''),
      },
    }
  },
})
