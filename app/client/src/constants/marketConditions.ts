export type MetricType = 'active' | 'active_median_psf' | 'closed' | 'closed_median_psf' | 'active_median_current_price' | 'closed_median_close_price' | 'median_dom' | 'months_of_inventory';

export type MetricConfiguration = {
  label: string;
  xAxisLabel: string;
  maxDomain: number;
  tickValues: number[];
  valuePrefix: string;
};

export const metricConfig: Record<MetricType, MetricConfiguration> = {
  active: {
    label: "Active",
    xAxisLabel: "Active",
    maxDomain: 600,
    tickValues: [0, 100, 200, 300, 400, 500, 600],
    valuePrefix: '',
  },
  active_median_psf: {
    label: "Median PSF",
    xAxisLabel: "Median PSF",
    maxDomain: 2,
    tickValues: [0, 0.5, 1.0, 1.5, 2.0],
    valuePrefix: '$',
  },
  active_median_current_price: {
    label: "Active Price",
    xAxisLabel: "Active Price",
    maxDomain: 2500,
    tickValues: [0, 500, 1000, 1500, 2000, 2500],
    valuePrefix: '$',
  },
  closed: {
    label: "Closed",
    xAxisLabel: "Closed",
    maxDomain: 600,
    tickValues: [0, 100, 200, 300, 400, 500, 600],
    valuePrefix: '',
  },
  closed_median_psf: {
    label: "Closed Median PSF",
    xAxisLabel: "Closed Median PSF",
    maxDomain: 2,
    tickValues: [0, 0.5, 1.0, 1.5, 2.0],
    valuePrefix: '$',
  },
  closed_median_close_price: {
    label: "Closed Price",
    xAxisLabel: "Closed Price",
    maxDomain: 2500,
    tickValues: [0, 500, 1000, 1500, 2000, 2500],
    valuePrefix: '$',
  },
  median_dom: {
    label: "Days on Market",
    xAxisLabel: "Days on Market",
    maxDomain: 100,
    tickValues: [0, 20, 40, 60, 80, 100],
    valuePrefix: '',
  },
  months_of_inventory: {
    label: "Months of Inventory",
    xAxisLabel: "Months of Inventory",
    maxDomain: 6,
    tickValues: [0, 1, 2, 3, 4, 5, 6],
    valuePrefix: '',
  },
};

export const shouldUseFixedTicks = (metricType: MetricType): boolean => {
  return ['active_median_psf', 'closed_median_psf', 'months_of_inventory'].includes(metricType);
};

export const defaultConfig: MetricConfiguration = {
  label: "Value",
  xAxisLabel: "Value",
  maxDomain: 100,
  tickValues: [0, 25, 50, 75, 100],
  valuePrefix: '',
};

export const calculateFinalConfig = (
  selected: MetricType, 
  dynamicTickValues: number[]
): MetricConfiguration => {
  const baseConfig = metricConfig[selected] || defaultConfig;
  const dynamicMaxDomain = dynamicTickValues.length > 0 
    ? dynamicTickValues[dynamicTickValues.length - 1] 
    : 100;
    
  return {
    ...baseConfig,
    tickValues: shouldUseFixedTicks(selected) 
      ? baseConfig.tickValues 
      : dynamicTickValues,
    maxDomain: Math.max(baseConfig.maxDomain, dynamicMaxDomain * 1.1)
  };
};

export const generateTickValues = (data: { value: number }[], maxTickCount = 6): number[] => {
  if (!data || !data.length) return [0];

  // Find the maximum value in the data
  const maxValue = Math.max(...data.map(item => item.value || 0));

  // Round up to a nice number
  const roundedMax = maxValue <= 5 ?
    Math.ceil(maxValue * 2) / 2 : // For small numbers, round to nearest 0.5
    Math.ceil(maxValue / 10) * 10; // For larger numbers, round to nearest 10

  // Create evenly spaced ticks
  const tickStep = roundedMax / (maxTickCount - 1);

  // Generate tick array
  return Array.from({ length: maxTickCount }, (_, i) =>
    Number((i * tickStep).toFixed(roundedMax < 5 ? 1 : 0))
  );
};