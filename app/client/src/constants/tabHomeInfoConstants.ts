
export interface FieldConfig {
  label: string;
  displayCondition: DisplayType;
}

export enum Display {
  VALUE = "value",
  BOOLEAN_ALWAYS = "boolean_always",
  BOOLEAN_CONDITIONAL = "boolean_conditional",
  CONVERTED_PERCENTAGE = "converted_percentage",
  REMOVE_DECIMAL = "remove_decimal",
  CONVERTED_YEAR_MONTH = "converted_year_month"
}

// Update the DisplayType to use the enum
export type DisplayType = Display;

export const FALSY_STRING_VALUES = ['false', 'no', '0', 'n/a', 'none', ''];

export enum HomeInfoField {
  PlanName = 'plan_name',
  Stories = 'stories',
  Garage = 'garage',
  EstMoveIn = 'move_in_month',
  HoaFeeAnnual = 'hoafeeannual',
  AnnualSpecialAssessmentFees = 'annual_special_assessment_fees',
}

export enum CommunityAmenityField {
  Community = 'community',
  CommunityType = 'community_type',
  TaxRate = 'taxrate',
  ActiveAdult = 'active_adult',
  SchoolDistrict = 'school_district',
}

export const HOME_INFO_ORDER = [
  HomeInfoField.PlanName,
  HomeInfoField.Stories,
  HomeInfoField.Garage,
  HomeInfoField.EstMoveIn,
  HomeInfoField.HoaFeeAnnual,
  HomeInfoField.AnnualSpecialAssessmentFees,
];

export const HOME_INFO_CONFIG: Record<HomeInfoField, FieldConfig> = {
  [HomeInfoField.PlanName]: { 
    label: 'Plan Name',
    displayCondition: Display.VALUE,
  },
  [HomeInfoField.Stories]: { 
    label: 'Stories',
    displayCondition: Display.REMOVE_DECIMAL,
  },
  [HomeInfoField.Garage]: { 
    label: 'Car Garage',
    displayCondition: Display.REMOVE_DECIMAL,
  },
  [HomeInfoField.EstMoveIn]: { 
    label: 'Est Move in',
    displayCondition: Display.CONVERTED_YEAR_MONTH,
  },
  [HomeInfoField.HoaFeeAnnual]: { 
    label: 'Annual HOA Fees',
    displayCondition: Display.VALUE,
  },
  [HomeInfoField.AnnualSpecialAssessmentFees]: { 
    label: 'Annual Special Assessment Fees',
    displayCondition: Display.VALUE,
  },
};

export const COMMUNITY_AMENITY_ORDER = [
  CommunityAmenityField.Community,
  CommunityAmenityField.CommunityType,
  CommunityAmenityField.TaxRate,
  CommunityAmenityField.ActiveAdult,
];

export const COMMUNITY_AMENITY_CONFIG: Record<CommunityAmenityField, FieldConfig> = {
  [CommunityAmenityField.Community]: { 
    label: 'Community',
    displayCondition: Display.VALUE,
  },
  [CommunityAmenityField.CommunityType]: { 
    label: 'Community Type',
    displayCondition: Display.VALUE,
  },
  [CommunityAmenityField.TaxRate]: { 
    label: 'Property Tax Rate',
    displayCondition: Display.CONVERTED_PERCENTAGE,
  },
  [CommunityAmenityField.ActiveAdult]: { 
    label: 'Active Adult',
    displayCondition: Display.BOOLEAN_ALWAYS,
  },
  [CommunityAmenityField.SchoolDistrict]: { 
    label: 'School District',
    displayCondition: Display.VALUE,
  },
};