
export interface FieldConfig {
  label: string;
  displayCondition: DisplayType;
  icon: string;
}

export enum Display {
  VALUE = "value",
  BOOLEAN_ALWAYS = "boolean_always",
  BOOLEAN_CONDITIONAL = "boolean_conditional",
  CONVERTED_PERCENTAGE = "converted_percentage",
  REMOVE_DECIMAL = "remove_decimal",
  CONVERTED_YEAR_MONTH = "converted_year_month"
}

// Update the DisplayType to use the enum
export type DisplayType = Display;

export const FALSY_STRING_VALUES = ['false', 'no', '0', 'n/a', 'none', ''];

export enum HomeInfoField {
  Homesite = 'homesite#',
  PlanName = 'plan_name',
  LotNumber = 'lot#',
  Elevation = 'elevation',
  Stories = 'stories',
  Garage = 'garage',
  EstimatedComplete = 'move_in_month',
  EstimatedCOE = 'estimated_coe',
  HoaFeeAnnual = 'hoafeeannual',
  CommunityDevelopmentFee = 'communitydevelopmentfee',
  Solar = 'solar',
  Basement = 'basement',
  BonusRoom = 'bonus_room'
}

export enum CommunityAmenityField {
  MasterComm = 'master_comm',
  Community = 'community',
  County = 'county',
  CommunityType = 'community_type',
  TaxRate = 'taxrate',
  ActiveAdult = 'active_adult',
  SchoolDistrict = 'school_district',
  GateComm = 'gate_community',
  Pool = 'pool',
  Lake = 'lake',
  GolfCourse = 'golf_course',
  Waterfront = 'waterfront',
  Playground = 'playground',
  Park = 'park',
  ClubHouse = 'club_house',
  Tennis = 'tennis',
  PicnicArea = 'picnic_area',
  SoccerField = 'soccer_field',
  BaseballField = 'baseball_field',
  WalkingTrails = 'walking_trails',
  AerobicsStudio = 'aerobics_studio',
  BasketballCourt = 'basketball_court',
  VolleyballCourt = 'volleyball_court'
}

export const HOME_INFO_ORDER = [
  HomeInfoField.Homesite,
  HomeInfoField.PlanName,
  HomeInfoField.LotNumber,
  HomeInfoField.Elevation,
  HomeInfoField.Stories,
  HomeInfoField.Garage,
  HomeInfoField.EstimatedComplete,
  HomeInfoField.EstimatedCOE,
  HomeInfoField.HoaFeeAnnual,
  HomeInfoField.CommunityDevelopmentFee,
  // HomeInfoField.Solar,
  HomeInfoField.Basement,
  // HomeInfoField.BonusRoom,
];

export const HOME_INFO_CONFIG: Record<HomeInfoField, FieldConfig> = {
  [HomeInfoField.Homesite]: { 
    label: 'Homesite Number',
    displayCondition: Display.VALUE,
    icon: 'Home'
  },
  [HomeInfoField.PlanName]: { 
    label: 'Plan Name',
    displayCondition: Display.VALUE,
    icon: 'ALargeSmall'
  },
  [HomeInfoField.LotNumber]: { 
    label: 'Lot Number',
    displayCondition: Display.VALUE,
    icon: 'Hash' 
  },
  [HomeInfoField.Elevation]: { 
    label: 'Elevation',
    displayCondition: Display.VALUE,
    icon: 'Mountain' 
  },
  [HomeInfoField.Stories]: { 
    label: 'Stories',
    displayCondition: Display.REMOVE_DECIMAL,
    icon: 'ArrowUp' 
  },
  [HomeInfoField.Garage]: { 
    label: 'Car Garage',
    displayCondition: Display.REMOVE_DECIMAL,
    icon: 'CarFront' 
  },
  [HomeInfoField.EstimatedComplete]: { 
    label: 'Estimated Move in Month',
    displayCondition: Display.CONVERTED_YEAR_MONTH,
    icon: 'Calendar' 
  },
  [HomeInfoField.EstimatedCOE]: { 
    label: 'Estimated Close of Escrow',
    displayCondition: Display.VALUE,
    icon: 'Calendar' 
  },
  [HomeInfoField.HoaFeeAnnual]: { 
    label: 'Annual HOA Fees',
    displayCondition: Display.VALUE,
    icon: 'DollarSign' 
  },
  [HomeInfoField.CommunityDevelopmentFee]: { 
    label: 'Community Development Fee',
    displayCondition: Display.VALUE,
    icon: 'DollarSign' 
  },
  [HomeInfoField.Solar]: { 
    label: 'Solar Panels',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'Sun' 
  },
  [HomeInfoField.Basement]: { 
    label: 'Basement',
    displayCondition: Display.BOOLEAN_ALWAYS,
    icon: 'DoorOpen' 
  },
  [HomeInfoField.BonusRoom]: { 
    label: 'Bonus Room',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'Sofa' 
  }
};

export const COMMUNITY_AMENITY_ORDER = [
  CommunityAmenityField.MasterComm,
  CommunityAmenityField.Community,
  CommunityAmenityField.County,
  CommunityAmenityField.CommunityType,
  CommunityAmenityField.TaxRate,
  CommunityAmenityField.ActiveAdult,
  // CommunityAmenityField.SchoolDistrict,
  // CommunityAmenityField.GateComm,
  // CommunityAmenityField.Pool,
  // CommunityAmenityField.Lake,
  // CommunityAmenityField.GolfCourse,
  // CommunityAmenityField.Waterfront,
  // CommunityAmenityField.Playground,
  // CommunityAmenityField.Park,
  // CommunityAmenityField.ClubHouse,
  // CommunityAmenityField.Tennis,
  // CommunityAmenityField.PicnicArea,
  // CommunityAmenityField.SoccerField,
  // CommunityAmenityField.BaseballField,
  // CommunityAmenityField.WalkingTrails,
  // CommunityAmenityField.AerobicsStudio,
  // CommunityAmenityField.BasketballCourt,
  // CommunityAmenityField.VolleyballCourt,
];

export const COMMUNITY_AMENITY_CONFIG: Record<CommunityAmenityField, FieldConfig> = {
  [CommunityAmenityField.MasterComm]: { 
    label: 'Master Planned Community',
    displayCondition: Display.VALUE,
    icon: 'ALargeSmall'
  },
  [CommunityAmenityField.Community]: { 
    label: 'Community',
    displayCondition: Display.VALUE,
    icon: 'ALargeSmall'
  },
  [CommunityAmenityField.County]: { 
    label: 'County',
    displayCondition: Display.VALUE,
    icon: 'LandPlot'
  },
  [CommunityAmenityField.CommunityType]: { 
    label: 'Community Type',
    displayCondition: Display.VALUE,
    icon: 'ALargeSmall'
  },
  [CommunityAmenityField.TaxRate]: { 
    label: 'Property Tax Rate',
    displayCondition: Display.CONVERTED_PERCENTAGE,
    icon: 'DollarSign'
  },
  [CommunityAmenityField.ActiveAdult]: { 
    label: 'Active Adult',
    displayCondition: Display.BOOLEAN_ALWAYS,
    icon: 'Landmark'
  },
  [CommunityAmenityField.SchoolDistrict]: { 
    label: 'School District',
    displayCondition: Display.VALUE,
    icon: 'GraduationCap'
  },
  [CommunityAmenityField.GateComm]: { 
    label: 'Gated Community',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'Fence'
  },
  [CommunityAmenityField.Pool]: { 
    label: 'Pool',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'WavesLadder'
  },
  [CommunityAmenityField.Lake]: { 
    label: 'Lake',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'Waves'
  },
  [CommunityAmenityField.GolfCourse]: { 
    label: 'Golf Course',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'FlagTriangleRight'
  },
  [CommunityAmenityField.Waterfront]: { 
    label: 'Waterfront',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'Haze'
  },
  [CommunityAmenityField.Playground]: { 
    label: 'Playground',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'Baby'
  },
  [CommunityAmenityField.Park]: { 
    label: 'Park',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'Trees'
  },
  [CommunityAmenityField.ClubHouse]: { 
    label: 'Club House',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'Armchair'
  },
  [CommunityAmenityField.Tennis]: { 
    label: 'Tennis',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'SportsTennis'
  },
  [CommunityAmenityField.PicnicArea]: { 
    label: 'Picnic Area',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'Sandwich'
  },
  [CommunityAmenityField.SoccerField]: { 
    label: 'Soccer Field',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'SportsSoccer'
  },
  [CommunityAmenityField.BaseballField]: { 
    label: 'Baseball Field',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'SportsBaseball'
  },
  [CommunityAmenityField.WalkingTrails]: { 
    label: 'Walking Trails',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'Leaf'
  },
  [CommunityAmenityField.AerobicsStudio]: { 
    label: 'Aerobics Studio',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'Dumbbell'
  },
  [CommunityAmenityField.BasketballCourt]: { 
    label: 'Basketball Court',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'Dribbble'
  },
  [CommunityAmenityField.VolleyballCourt]: { 
    label: 'Volleyball Court',
    displayCondition: Display.BOOLEAN_CONDITIONAL,
    icon: 'Volleyball'
  }
};


import SportsTennisOutlinedIcon from '@mui/icons-material/SportsTennisOutlined';
import SportsBaseballOutlinedIcon from '@mui/icons-material/SportsBaseballOutlined';
import SportsSoccerOutlinedIcon from '@mui/icons-material/SportsSoccerOutlined';

// MUI SVG Icons mapping
export const MUI_SVG_ICONS = {
  'SportsTennis': SportsTennisOutlinedIcon,
  'SportsSoccer': SportsSoccerOutlinedIcon,
  'SportsBaseball': SportsBaseballOutlinedIcon
};

//Other icons are from lucide-react