import evernestLogo from "../assets/Evernest_Logo.png";
import homeRiverLogo from "../assets/HomeRiver_Logo.png";
import myndLogo from "../assets/Mynd_Logo.png";

export enum TabResourcesField {
  PropertyManagementFee = "pmFee",
  LeasingFee = "leasingFee",
  RenewalFee = "renewalFee",
  WaivedPMFees = "waivedPMFees",
}

export interface ResourceFieldConfig {
  label: string;
  tooltip?: string;
}

export const RESOURCES_ORDER = [
  TabResourcesField.PropertyManagementFee,
  TabResourcesField.LeasingFee,
  TabResourcesField.RenewalFee,
  TabResourcesField.WaivedPMFees,
];

export const RESOURCES_CONFIG: Record<TabResourcesField, ResourceFieldConfig> =
  {
    [TabResourcesField.PropertyManagementFee]: {
      label: "Property Management Fee",
    },
    [TabResourcesField.LeasingFee]: {
      label: "Leasing Fee",
    },
    [TabResourcesField.RenewalFee]: {
      label: "Renewal Leasing Fee",
    },
    [TabResourcesField.WaivedPMFees]: {
      label: "Months of Waived PM Fees for Lennar Buyers",
    },
  };

interface PropertyManagementCompany {
  name: string;
  website: string;
  contact?: string;
  email: string;
  phone?: string;
  description: string;
  "Waived PM fees": string;
  prefix: "hr_" | "rw_" | "mynd_" | "evernest_";
  handleValue: "dollar" | "percentage";
  logo: string;
}

export const Property_Management_Companies: PropertyManagementCompany[] = [
  {
    name: "Evernest",
    website: "https://www.evernest.co/",
    contact: "Hunter Terryn",
    email: "<EMAIL>",
    phone: "2055462231",
    description:
      "Evernest is a full-service real estate and property management firm operating in markets nationwide. We help landlords and investors with property management, renovations, brokerage, and more - offering seamless, hands-off solutions for marketing, leasing, and maintenance. With local teams and in-house maintenance staff, we make it easy to maximize the value of your rental property, whether you own one home or a growing portfolio.",
    "Waived PM fees": "4 months",
    prefix: "evernest_",
    handleValue: "percentage",
    logo: evernestLogo,
  },
  // {
  //   name: "Mynd",
  //   website: "https://www.mynd.co/",
  //   contact: "Kassidi Gibbons",
  //   email: "<EMAIL>",
  //   description:
  //     "Mynd, a Roofstock company, is redefining property management for today/’s rental owners. We combine an all-in-one digital platform with local market experts to take the stress out of managing rental homes. From marketing and leasing to rent collection, maintenance coordination, and resident support, Mynd handles the day-to-day so owners can focus on the big picture. Our technology gives you real-time visibility into property performance with dashboards that track income, expenses, and work orders — all from your phone or computer. Owners get transparent communication, faster service response times, and reliable financial reporting that makes scaling a rental portfolio easier.",
  //   "Waived PM fees": "3 months",
  //   prefix: "mynd_",
  //   handleValue: "dollar",
  //   logo: myndLogo,
  // },
  {
    name: "Home River",
    website: "https://www.homeriver.com/",
    email: "<EMAIL>",
    description:
      "HomeRiver Group® strives to be the premier national residential property management company in the United States, offering acquisition, renovation, leasing, management, maintenance and brokerage. We are the largest third-party property management company in the United States, managing over 20,000 homes in over 60 markets. You’ll get one-stop-shopping for all your management, investment, and real estate services, and have access to standardized systems and best practices. We are intently focused on serving both owners and tenants, and are dedicated to a culture of integrity, superlative performance and respect.",
    "Waived PM fees": "2 months",
    prefix: "hr_",
    handleValue: "percentage",
    logo: homeRiverLogo,
  },
  // {
  //   name: "Renters Warehouse",
  //   website: "https://www.renterswarehouse.com/",
  //   contact: " ",
  //   email: "",
  //   phone: "",
  //   description: "",
  //   "Waived PM fees": "3 months",
  //   prefix: "rw_",
  //   handleValue: "dollar",
  // },
];

export const HOME_RIVER_CONTACTS = {
  REBECCA: { name: "Rebecca Vazquez", phone: "************" },
  JP: { name: "JP Reese", phone: "************" },
};

export const HOME_RIVER_MARKET_TO_CONTACT: Record<
  string,
  keyof typeof HOME_RIVER_CONTACTS
> = {
  Birmingham: "REBECCA",
  "Gulf Coast": "JP",
  Huntsville: "REBECCA",
  Phoenix: "JP",
  "Northwest Arkansas": "JP",
  "Inland Empire": "JP",
  "Orange County": "JP",
  Sacramento: "JP",
  "Sussex County": "REBECCA",
  "Jacksonville / St. Augustine": "REBECCA",
  Miami: "REBECCA",
  "Naples / Ft. Myers": "REBECCA",
  Ocala: "REBECCA",
  Orlando: "REBECCA",
  "Palm Beach": "REBECCA",
  "Sarasota / Manatee": "REBECCA",
  "Space Coast / Melbourne": "REBECCA",
  "Tampa / Manatee": "REBECCA",
  "Treasure Coast": "REBECCA",
  Atlanta: "REBECCA",
  Boise: "JP",
  "Inland Northwest": "JP",
  Chicago: "REBECCA",
  Indianapolis: "REBECCA",
  "Eastern Shore": "REBECCA",
  "MD / D.C. Metro": "REBECCA",
  "Kansas City": "JP",
  Charlotte: "REBECCA",
  "Raleigh / Durham": "REBECCA",
  "Winston-Salem": "REBECCA",
  "Oklahoma City": "JP",
  Stillwater: "JP",
  Tulsa: "JP",
  "Greater Philadelphia Area": "JP",
  Columbia: "REBECCA",
  Chattanooga: "JP",
  Nashville: "JP",
  "Austin / Central Texas": "JP",
  "Dallas / Ft. Worth": "JP",
  Houston: "JP",
  "San Antonio": "JP",
  "Salt Lake City": "JP",
  "VA / D.C. Metro": "REBECCA",
};

export function getHomeRiverContactByMarket(market: string) {
  const contactKey = HOME_RIVER_MARKET_TO_CONTACT[market];
  return contactKey
    ? HOME_RIVER_CONTACTS[contactKey]
    : HOME_RIVER_CONTACTS.REBECCA;
}
