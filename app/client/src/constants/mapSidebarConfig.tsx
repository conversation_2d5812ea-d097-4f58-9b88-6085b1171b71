import {
  GraduationCap,
  Waves,
  Car,
  MapPin,
  Apple,
  School,
  BookOpen,
  FileChartLine,
  Building2,
  Fence,
  DollarSign,
  Blocks,
  BrickWall,
  CloudRainWind,
  Landmark,
  BriefcaseBusiness,
  HousePlus,
  Zap,
  TrainTrack,
} from "lucide-react";
import BoundaryIcon from "../components/Icons/BoundaryIcon";
import TrainIcon from "../components/Icons/TrainIcon";
import BusStopIcon from "../components/Icons/BusStopIcon";

interface MenuItem {
  icon: React.ReactNode;
  label: string;
  submenu: SubMenuItem[];
}

interface SubMenuItem {
  key: string;
  label: string;
  icon: React.ReactNode;
}

export const mapMenuConfig: MenuItem[] = [
  {
    icon: <GraduationCap className="w-6 h-6 text-dark-gray" />,
    label: "School",
    submenu: [
      { key: "school districts", label: "Districts", icon: <Apple className="w-4 h-4 text-dark-gray" /> },
      { key: "school zones", label: "Zones", icon: <School className="w-4 h-4 text-dark-gray" /> },
      { key: "charter school", label: "Charter", icon: <BookOpen className="w-4 h-4 text-dark-gray" /> },
    ],
  },
  {
    icon: <BoundaryIcon className="w-6 h-6 ml-1 text-dark-gray" />,
    label: "Boundary",
    submenu: [
      { key: "cbsa", label: "Metro", icon: <FileChartLine className="w-4 h-4 text-dark-gray" /> },
      { key: "city", label: "City", icon: <Building2 className="w-4 h-4 text-dark-gray" /> },
      { key: "ZIP code", label: "Zip Code", icon: <Fence className="w-4 h-4 text-dark-gray" /> },
      { key: "tax", label: "Tax", icon: <DollarSign className="w-4 h-4 text-dark-gray" /> },
      { key: "subdivision", label: "Subdivision", icon: <Blocks className="w-4 h-4 text-dark-gray" /> },
      { key: "neighborhood", label: "Neighborhood", icon: <BrickWall className="w-4 h-4 text-dark-gray" /> },
    ],
  },
  {
    icon: <Waves className="w-6 h-6 text-dark-gray" />,
    label: "Flood",
    submenu: [
      { key: "flood zone", label: "Flood Zone", icon: <CloudRainWind className="w-4 h-4 text-dark-gray" /> },
    ],
  },
  {
    icon: <MapPin className="w-6 h-6 text-dark-gray" />,
    label: "POI",
    submenu: [
      {
        key: "institutional owners",
        label: "Institutional Owners",
        icon: <Landmark className="w-4 h-4 text-dark-gray" />,
      },
      {
        key: "major employer",
        label: "Major Employers",
        icon: <BriefcaseBusiness className="w-4 h-4 text-dark-gray" />,
      },
      {
        key: "mobile home park",
        label: "Mobile Home Park",
        icon: <HousePlus className="w-4 h-4 text-dark-gray" />,
      },
    ],
  },
  {
    icon: <Car className="w-7 h-7 text-dark-gray" />,
    label: "Infra",
    submenu: [
      { key: "power lines", label: "Power Lines", icon: <Zap className="w-4 h-4 text-dark-gray" /> },
      { key: "rail network", label: "Rail Network", icon: <TrainTrack className="w-4 h-4 text-dark-gray" /> },
      {
        key: "arcgis transit line",
        label: "Transit Line",
        icon: <TrainIcon className="w-4 h-4 text-dark-gray" />,
      },
      { key: "arcgis bus stop", label: "Bus Stops", icon: <BusStopIcon className="w-4 h-4 text-dark-gray" /> },
    ],
  },
];