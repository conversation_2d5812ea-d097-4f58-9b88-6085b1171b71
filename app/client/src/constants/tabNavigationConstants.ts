interface TabNavigation {
  key: string;
  label: string;
  path: string;
}

export const TAB_NAVIGATION: TabNavigation[] = [
  { key: "proForma", label: "Pro Forma", path: "/properties/$id/proForma" },
  { key: "comps", label: "Comps", path: "/properties/$id/comps" },
  { key: "resources", label: "Resources", path: "/properties/$id/resources" },
  { key: "images", label: "Images", path: "/properties/$id/images" },
  { key: "home-info", label: "Home Info", path: "/properties/$id/home-info" },
  {
    key: "demographics",
    label: "Demographics",
    path: "/properties/$id/demographics",
  },
  {
    key: "area-insights",
    label: "Area Insights",
    path: "/properties/$id/area-insights",
  },
];
