import { LucideIcon } from "lucide-react";
import { SquareArrowOutUpRight } from "lucide-react";

export interface FooterLink {
  id: string;
  title: string;
  href?: string;
  icon?: LucideIcon;
  iconSize?: number;
  onClick?: () => void;
  isExternal?: boolean;
}

const DEFAULT_ICON_SIZE = 10;

// Footer links data
export const FOOTER_LINKS: FooterLink[] = [
  {
    id: "privacy",
    title: "Privacy Policy",
    href: "https://www.lennar.com/privacypolicy",
    icon: SquareArrowOutUpRight,
    iconSize: DEFAULT_ICON_SIZE,
    isExternal: true,
  },
  {
    id: "cookiePreference",
    title: "Cookie Preference",
    onClick: () => {
      // Trigger Osano cookie preference modal
      if (window.Osano && typeof window.Osano.cm.showDrawer === 'function') {
        window.Osano.cm.showDrawer('osano-cm-dom-info-dialog-open');
      }
    },
    icon: SquareArrowOutUpRight,
    iconSize: DEFAULT_ICON_SIZE,
    isExternal: false,
  },
  {
    id: "userPreferences",
    title: "Communications Preferences",
    href: "https://www.lennar.com/contact/communicationpreferences",
    icon: SquareArrowOutUpRight,
    iconSize: DEFAULT_ICON_SIZE,
    isExternal: true,
  },
];