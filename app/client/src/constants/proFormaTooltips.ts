import { ProFormaValuesLennar } from '@/lib/utils/types';

export type ProFormaTooltipKeys = keyof ProFormaValuesLennar;

export const proFormaTooltips: Partial<Record<ProFormaTooltipKeys, string>> = {
  "Total Initial Investment": 'The upfront cash required to purchase the home, including down payment and closing costs. To adjust closing cost assumptions, view the expanded pro forma below.',
  "Projected Monthly Rent": 'Estimated monthly rent based on a rental pricing model using recently leased, nearby homes of similar age and size. See the "Comps" tab for the rental comps used in this estimate.',
  "HPA 5Yr": 'The average annual increase in home value at the ZIP code level over the past 5 years. Source: U.S. Census Bureau, 2025',
  "Mortgage Rate": 'The interest rate reflects a 7/6 ARM offered by Lennar Mortgage, an affiliate of Lennar. The rate is fixed for the first 7 years and adjusts every 6 months thereafter. For illustrative purposes only; availability and borrower eligibility are not guaranteed.',
  "Levered Cash Flow Yearly": 'The projected annual profit after accounting for mortgage payments (if any), taxes, insurance, and all other operating expenses.',
  "Cash-on-Cash Return": 'The ratio of annual cash flow to initial cash investment. Reflects the return on the cash invested.',
  "Projected Yield on Bid Price": 'The ratio of net operating income (NOI) to purchase price. As a pre-financing yield metric, cap rate removes the impact of debt. See the pro forma below for NOI details.',
  "Total Return": 'The projected profit over the selected time horizon. Includes cash flow earned during ownership and equity gained from the home\'s appreciated value at sale after paying off any remaining loan balance.',
  "Annualized Return": 'Converts the total return—made up of cash flow earned during ownership and equity gained from home appreciation after paying off the loan—into an average annual return over the selected time horizon.',
};

export const getProFormaTooltip = (key: ProFormaTooltipKeys): string => {
  return proFormaTooltips[key] || '';
};