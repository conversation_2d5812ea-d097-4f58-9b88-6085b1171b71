import { ProFormaValuesLennar } from '@/lib/utils/types';

export type ProFormaTooltipKeys = keyof ProFormaValuesLennar;

export const proFormaTooltips: Partial<Record<ProFormaTooltipKeys, string>> = {
  "Total Initial Investment": 'The upfront cash required to purchase the home, including down payment and closing costs. To adjust closing cost assumptions, view the expanded pro forma below.',
  "Projected Monthly Rent": 'Estimated monthly rent based on a rental pricing model using recently leased, nearby homes of similar age and size. See the "Comps" tab for the rental comps used in this estimate.',
  "HPA 5Yr": 'The average annual increase in home value at the ZIP code level over the past 5 years. Source: U.S. Census Bureau, 2025',
  "Mortgage Rate": 'The interest rate reflects a 7/6 ARM offered by Lennar Mortgage, an affiliate of Lennar. The rate is fixed for the first 7 years and adjusts every 6 months thereafter. For illustrative purposes only; availability and borrower eligibility are not guaranteed.',
  "Levered Cash Flow Yearly": 'The projected annual profit after accounting for mortgage payments (if any), taxes, insurance, and all other operating expenses.',
  "Cash-on-Cash Return": 'The ratio of annual cash flow to initial cash investment. Reflects the return on the cash invested.',
  "Projected Yield on Bid Price": 'The ratio of net operating income (NOI) to purchase price. As a pre-financing yield metric, cap rate removes the impact of debt. See the pro forma below for NOI details.',
  "Total Return": 'The projected profit over the selected time horizon. Includes cash flow earned during ownership and equity gained from the home\'s appreciated value at sale after paying off any remaining loan balance. Assumes 3% annual increases in rent and expenses.',
  "Annualized Return": 'Converts the total return—made up of cash flow earned during ownership and equity gained from home appreciation after paying off the loan—into an average annual return over the selected time horizon.',
  
  "Closing Costs": 'Closing costs are estimated at 1.50% of the purchase price. Actual closing costs may vary.',
  "Property Management Fees":' Calculated as (Rental Income + Other Income - Vacancy Loss) × Management Fee Percentage. The amount shown is an estimate. Fees vary by management company.',
  "Insurance": 'Insurance premiums are estimated at $0.75 per square foot per year, except for FL and CA, which are estimated at $1 per square foot per year. Actual premiums may differ.',
    "Repair & Maintenance": 'New homes with a standard warranty—such as those Lennar provides by market—can have lower repair and maintenance costs compared to existing homes. This estimate reflects those potential savings, though actual expenses may vary.',
  "Annual Community Development Fee": 'These fees only apply in certain communities and reflect annual charges from special taxing districts that help fund infrastructure or community improvements. Special Assessment Fees are separate from standard property taxes.'
};

export const getProFormaTooltip = (key: ProFormaTooltipKeys): string => {
  return proFormaTooltips[key] || '';
};