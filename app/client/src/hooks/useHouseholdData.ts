import { useState, useEffect } from 'react';
import { HouseholdCompositionProps } from '../types/PropertyDetailPage.types';
import { getPsychographicInDistance, getPsychographicInDriveTime } from '@/lib/query/get-psychographic-data';

const defaultHouseholdData: HouseholdCompositionProps[] = [{
  column_name: "",
  percentage: 0,
  percentage2024: 0,
  percentage2022: 0,
}];

export const useHouseholdData = (
  lat: number | null,
  lng: number | null,
  distance: number,
  driveTime: number,
  isDistanceMode: boolean
) => {
  const [data, setData] = useState<HouseholdCompositionProps[]>(defaultHouseholdData);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    // Skip API call if coordinates are missing
    if (lat === null || lng === null) {
      setError(new Error("Missing coordinates"));
      setIsLoading(false);
      return;
    }
    const fetchData = async () => {
      
      setIsLoading(true);
      setError(null);
      
      try {
        let result;
        
        if (isDistanceMode) {
          const distanceData = await getPsychographicInDistance({
            lat,
            lng,
            distance
          });
          result = distanceData ?? defaultHouseholdData;
        } else {
          const driveTimeData = await getPsychographicInDriveTime({
            body: {
              lat,
              lng,
              drivetime: driveTime
            }
          });
          result = driveTimeData ?? defaultHouseholdData;
        }
        
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
        console.error('Error fetching household composition data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [lat, lng, distance, driveTime, isDistanceMode]);

  return { 
    data, 
    isLoading, 
    error 
  };
};