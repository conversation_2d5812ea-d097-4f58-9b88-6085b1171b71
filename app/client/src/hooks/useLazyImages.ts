import { useState, useEffect, useRef, useCallback } from 'react';
import { getImagesLennar, singleImageLennarResponseType } from '@/lib/query/get-images-lennar';
import { LennarSinglePropertyDataType } from '@/lib/utils/types';

interface ImageCache {
  [key: string]: {
    images: singleImageLennarResponseType[];
    loading: boolean;
    error: boolean;
  };
}

interface UseLazyImagesProps {
  property: LennarSinglePropertyDataType;
  isVisible: boolean;
  viewStyle: 'grid' | 'list';
  propertyIndex?: number;
  totalProperties?: number;
}

// Global cache for sharing between components
const globalImageCache: ImageCache = {};

// Track ongoing requests to prevent duplicates
const ongoingRequests = new Set<string>();

export const useLazyImages = ({ 
  property, 
  isVisible, 
  viewStyle, 
  propertyIndex = 0, 
  totalProperties = 0 
}: UseLazyImagesProps) => {
  const [images, setImages] = useState<singleImageLennarResponseType[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Create cache key for the property
  const cacheKey = `${property?.payload?.subjectProperty?.meta?.community}-${property?.payload?.subjectProperty?.meta?.plan}`;

  // Check if we should preload (next 2-3 properties)
  // const shouldPreload = propertyIndex < totalProperties - 1 && propertyIndex < totalProperties - 3;
  const shouldPreload = false;

  // Fetch images function
  const fetchImages = useCallback(async () => {
    // Don't fetch for list view
    if (viewStyle === 'list') {
      return;
    }

    // Check if already cached
    if (globalImageCache[cacheKey]) {
      if (globalImageCache[cacheKey].images.length > 0) {
        setImages(globalImageCache[cacheKey].images);
        setLoading(false);
        setError(false);
        return;
      }
      if (globalImageCache[cacheKey].error) {
        setError(true);
        setLoading(false);
        return;
      }
    }

    // Prevent duplicate requests
    if (ongoingRequests.has(cacheKey)) {
      return;
    }

    // Add to ongoing requests
    ongoingRequests.add(cacheKey);

    // Cancel previous request if exists
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(false);

    try {
      const response = await getImagesLennar({
        project: property?.payload?.subjectProperty?.meta?.community,
        code: property?.payload?.subjectProperty?.meta?.plan,
      });

      if (response && Array.isArray(response?.images) && response?.images?.length > 0) {
        const imageObjects = response.images;
        
        // Update global cache
        globalImageCache[cacheKey] = {
          images: imageObjects,
          loading: false,
          error: false,
        };

        setImages(imageObjects);
        setLoading(false);
        setError(false);
      } else {
        throw new Error('No images found in response');
      }
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('Image request was cancelled for:', cacheKey);
        return; // Request was cancelled
      }

      console.error('Error fetching images for:', cacheKey, err);

      // Update cache with error
      globalImageCache[cacheKey] = {
        images: [],
        loading: false,
        error: true,
      };

      setError(true);
      setLoading(false);
    } finally {
      // Remove from ongoing requests
      ongoingRequests.delete(cacheKey);
    }
  }, [property, cacheKey, viewStyle]);

  // Fetch images when property becomes visible or should preload
  useEffect(() => {
    if ((isVisible || shouldPreload) && viewStyle === 'grid') {
      fetchImages();
    }
  }, [isVisible, shouldPreload, viewStyle, fetchImages]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      // Remove from ongoing requests on cleanup
      ongoingRequests.delete(cacheKey);
    };
  }, [cacheKey]);

  return {
    images,
    loading,
    error,
    refetch: () => {
      setError(false);
      fetchImages();
    },
  };
}; 