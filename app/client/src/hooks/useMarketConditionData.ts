import React from 'react';
import { useQuery } from '@tanstack/react-query';
import z from 'zod';
import {
  getMarketConditionAOIData,
  getMarketConditionZIPCodeData,
  getMarketConditionDistrictData,
  getMarketConditionCountyData,
  getMarketConditionMetroData,
} from '../lib/query/get-market-condition-data';

interface MarketConditionMonthYearProps {
  numberOfMonths: number;
  type: 'current' | 'prior';
  date: string;
}

export const MIN_PRICE = 0;
export const MAX_PRICE = 2147483647;
export const serverType = "prod";
export const defaultMonths: number = 3;
export const FormattedCurrentDate = new Date().toISOString().split('T')[0];

export type Bedrooms = '' | '1' | '2' | '3' | '4' | '5';
export type AreaDataOptions = {
      latitude: number;
      longitude: number;
      radius: number;
    }

export type MarketConditionProps = {
  serverType?: 'exp' | 'prod';
  isLeaseMode: boolean;
  areaData: AreaDataOptions;
  showPrior?: boolean;
  responsive?: boolean;
  showContainerHeader?: boolean;
  children?: React.ReactNode;
};

const useMarketConditionMonthYear = (props: MarketConditionMonthYearProps): { date: string; month: number; year: number } => {
  if (!props || !['current', 'prior'].includes(props.type)) {
    throw new Error("Invalid props: type must be 'current' or 'prior'");
  }

  let date: Date;
  if (props.date) {
    date = new Date(props.date);
  } else {
    date = new Date();
  }

  if (props.type === 'prior') {
    date.setMonth(date.getMonth() - props.numberOfMonths);
  }

  // Format date to yyyy-mm-dd
  const year = date.getFullYear();
  const month = date.getMonth() + 1; // 1-indexed for backend
  const day = String(date.getDate()).padStart(2, '0');

  return {
    date: `${year}-${String(month).padStart(2, '0')}-${day}`,
    month,
    year,
  };
};

const MarketConditionAPIResponseSchema = z.object({
  active: z.number().nullable(),
  closed_average_cdom: z.number().nullable(),
  closed_median_cdom: z.number().nullable(),
  closed_average_close_price_and_size_ratio: z.number().nullable(),
  closed_median_close_price_and_size_ratio: z.number().nullable(),
  closed: z.number().nullable(),
  closed_dom_median: z.number().nullable(),
  active_average_current_price_and_size_ratio: z.number().nullable(),
  active_median_current_price_and_size_ratio: z.number().nullable(),
  ratio: z.number().nullable(),
  closed_median_close_price: z.number().nullable(),
  active_median_current_price: z.number().nullable(),
});

type MarketConditionAPIResponse = z.infer<
  typeof MarketConditionAPIResponseSchema
>;

const convertToMarketCondition = (data: MarketConditionAPIResponse) => {
  return {
    active: data.active,
    active_median_psf: data.active_median_current_price_and_size_ratio,
    active_median_current_price: data.active_median_current_price, // Add median current price
    closed: data.closed,
    closed_median_psf: data.closed_median_close_price_and_size_ratio,
    closed_median_close_price: data.closed_median_close_price, // Add median close price
    median_dom: data.closed_dom_median,
    months_of_inventory: data.ratio,
  };
};

interface MarketConditionAPIProps {
  serverType: 'exp' | 'prod';
  propertyType: 'Residential' | 'Residential Lease';
  numberOfMonths: number;
  areaData: AreaDataOptions;
  type: 'current' | 'prior';
  bedrooms: Bedrooms;
  enabled?: boolean;
  newConstruction: 'true' | 'false' | '';
  prices: [number | null, number | null];
  date: string;
}

// prettier-ignore
export const useMarketConditionAOI = ({enabled = true, ...props}: MarketConditionAPIProps) => {
  const { date, month, year } = useMarketConditionMonthYear({
    numberOfMonths: props.numberOfMonths,
    type: props.type,
    date: props.date,
  });
  
  const aoiType = 'point-radius';
  const { data, isLoading, error } = useQuery({
    queryKey: ['market-condition-aoi', aoiType, props.propertyType, JSON.stringify(props.areaData), props.numberOfMonths, props.type, month, year, props.bedrooms, props.newConstruction, [props.prices[0] || MIN_PRICE, props.prices[1] || MAX_PRICE]],
    queryFn: async ({ signal }) => {
      try {
        if (!props.areaData) return undefined;
        let resp = null;
        const { latitude, longitude, radius } = props.areaData;
        resp = await getMarketConditionAOIData({
          serverType: props.serverType,
          propertyType: props.propertyType,
          lat: latitude,
          lng: longitude,
          distance: radius,
          numberOfMonths: props.numberOfMonths,
          month,
          year,
          date: date,
          bedrooms: props.bedrooms,
          newConstruction: props.newConstruction,
          minPrice: props.prices[0] ? props.prices[0] : MIN_PRICE,
          maxPrice: props.prices[1] ? props.prices[1] : MAX_PRICE,
          signal
        });
        

        const data = await resp?.json();
        const parsed = MarketConditionAPIResponseSchema.parse(data);
        return convertToMarketCondition(parsed);
      } catch (error) {
        console.log('useMarketConditionAOI error:', error);
        return undefined
      }
    },
    retry(failureCount, error) {
      if ((error as { status?: number }).status === 404) return false;
      return failureCount < 3;
    },
    enabled: enabled && props.areaData &&  (Array.isArray(props.areaData) || ('longitude' in props.areaData && 'latitude' in props.areaData && 'radius' in props.areaData))  ? true : false,
  });

  return { data, isLoading, error };
};

// prettier-ignore
export const useMarketConditionZIPCode = ({enabled = true, ...props}: Omit<MarketConditionAPIProps, 'areaData'> & { areaData: {latitude?: number; longitude?:number} }) => {
  const { month, year, date } = useMarketConditionMonthYear({
    numberOfMonths: props.numberOfMonths,
    type: props.type,
    date: props.date,
  });
  const { latitude, longitude } = props.areaData;
  const { data, isLoading, error } = useQuery({
    queryKey: ['market-condition-zipcode', props.propertyType, latitude, longitude, props.numberOfMonths, props.type, month, year, props.bedrooms, props.newConstruction, [props.prices[0] || MIN_PRICE, props.prices[1] || MAX_PRICE]],
    queryFn: async ({signal}) => {
      try {
        const resp = await getMarketConditionZIPCodeData({
          serverType: props.serverType,
          propertyType: props.propertyType,
          lat: latitude as number,
          lng: longitude as number,
          numberOfMonths: props.numberOfMonths,
          month,
          year,
          date: date,
          bedrooms: props.bedrooms,
          newConstruction: props.newConstruction,
          minPrice: props.prices[0] ? props.prices[0] : MIN_PRICE,
          maxPrice: props.prices[1] ? props.prices[1] : MAX_PRICE,
          signal
        });
        const data = await resp.json();
        const parsed = MarketConditionAPIResponseSchema.parse(data);
        return convertToMarketCondition(parsed);
      } catch (error) {
        console.log('useMarketConditionZIPCode error:', error);
        return undefined
      }
    },
    retry(failureCount, error) {
      if ((error as { status?: number }).status === 404) return false;
      return failureCount < 3;
    },
    enabled: enabled && latitude != undefined && longitude != undefined && props.numberOfMonths ? true : false,
  });

  return { data, isLoading, error };
};

// prettier-ignore
export const useMarketConditionSchoolDistrict = ({enabled = true, ...props}: Omit<MarketConditionAPIProps, 'areaData'> & { areaData: {latitude?: number; longitude?:number} }) => {
  const { month, year, date } = useMarketConditionMonthYear({
    numberOfMonths: props.numberOfMonths,
    type: props.type,
    date: props.date,
  });
  const { latitude, longitude } = props.areaData;
  const { data, isLoading, error } = useQuery({
    queryKey: ['market-condition-school-district', props.propertyType, latitude, longitude, props.numberOfMonths, props.type, month, year, props.bedrooms, props.newConstruction, [props.prices[0] || MIN_PRICE, props.prices[1] || MAX_PRICE]],
    queryFn: async ({signal}) => {
      try {
        const resp = await getMarketConditionDistrictData({
          serverType: props.serverType,
          propertyType: props.propertyType,
          lat: latitude as number,
          lng: longitude as number,
          numberOfMonths: props.numberOfMonths,
          month,
          year,
          date: date,
          bedrooms: props.bedrooms,
          newConstruction: props.newConstruction,
          minPrice: props.prices[0] ? props.prices[0] : MIN_PRICE,
          maxPrice: props.prices[1] ? props.prices[1] : MAX_PRICE,
          signal
        });
        const data = await resp.json();
        const parsed = MarketConditionAPIResponseSchema.parse(data);
        return convertToMarketCondition(parsed);
      } catch (error) {
        console.log('useMarketConditionSchoolDistrict error:', error);
        return undefined
      }
    },
    retry(failureCount, error) {
      if ((error as { status?: number }).status === 404) return false;
      return failureCount < 3;
    },
    enabled: enabled && latitude != undefined && longitude != undefined && props.numberOfMonths ? true : false,
  });

  return { data, isLoading, error };
};

// prettier-ignore
export const useMarketConditionCounty = ({enabled = true, ...props}: Omit<MarketConditionAPIProps, 'areaData'> & { areaData: {latitude?: number; longitude?:number} }) => {
  const { month, year,date } = useMarketConditionMonthYear({
    numberOfMonths: props.numberOfMonths,
    type: props.type,
    date: props.date,
  });
  const { latitude, longitude } = props.areaData;
  const { data, isLoading, error } = useQuery({
    queryKey: ['market-condition-county', props.propertyType, latitude, longitude, props.numberOfMonths, props.type, month, year, props.bedrooms, props.newConstruction, [props.prices[0] || MIN_PRICE, props.prices[1] || MAX_PRICE]],
    queryFn: async ({signal}) => {
      try {
        const resp = await getMarketConditionCountyData({
          serverType: props.serverType,
          propertyType: props.propertyType,
          lat: latitude as number,
          lng: longitude as number,
          numberOfMonths: props.numberOfMonths,
          month,
          date: date,
          year,
          bedrooms: props.bedrooms,
          newConstruction: props.newConstruction,
          minPrice: props.prices[0] ? props.prices[0] : MIN_PRICE,
          maxPrice: props.prices[1] ? props.prices[1] : MAX_PRICE,
          signal
        });
        const data = await resp.json();
        const parsed = MarketConditionAPIResponseSchema.parse(data);
        return convertToMarketCondition(parsed);
      } catch (error) {
        console.log('useMarketConditionCounty error:', error);
        return undefined
      }
    },
    retry(failureCount, error) {
      if ((error as { status?: number }).status === 404) return false;
      return failureCount < 3;
    },
    enabled: enabled && latitude != undefined && longitude != undefined && props.numberOfMonths ? true : false,
  });

  return { data, isLoading, error };
};

// prettier-ignore
export const useMarketConditionMetro = ({enabled = true, ...props}:  Omit<MarketConditionAPIProps, 'areaData'> & { areaData: {latitude?: number; longitude?:number} }) => {
  const { month, year, date } = useMarketConditionMonthYear({
    numberOfMonths: props.numberOfMonths,
    type: props.type,
    date: props.date,
  });
  const { latitude, longitude } = props.areaData;
  const { data, isLoading, error } = useQuery({
    queryKey: ['market-condition-metro', props.propertyType, latitude, longitude, props.numberOfMonths, props.type, month, year, props.bedrooms, props.newConstruction, [props.prices[0] || MIN_PRICE, props.prices[1] || MAX_PRICE]],
    queryFn: async ({signal}) => {
      try {
        const resp = await getMarketConditionMetroData({
          serverType: props.serverType,
          propertyType: props.propertyType,
          lat: latitude as number,
          lng: longitude as number,
          numberOfMonths: props.numberOfMonths,
          month,
          date: date,
          year,
          bedrooms: props.bedrooms,
          newConstruction: props.newConstruction,
          minPrice: props.prices[0] ? props.prices[0] : MIN_PRICE,
          maxPrice: props.prices[1] ? props.prices[1] : MAX_PRICE,
          signal
        });
        const data = await resp.json();
        const parsed = MarketConditionAPIResponseSchema.parse(data);
        return convertToMarketCondition(parsed);
      } catch (error) {
        console.log('useMarketConditionMetro error:', error);
        return undefined
      }
    },
    retry(failureCount, error) {
      if ((error as { status?: number }).status === 404) return false;
      return failureCount < 3;
    },
    enabled: enabled && latitude != undefined && longitude != undefined && props.numberOfMonths ? true : false,
  });

  return { data, isLoading, error };
};

export interface MarketConditionData {
  type: string;
  aoi: number | null | undefined;
  zipcode: number | null | undefined;
  schoolDistrict: number | null | undefined;
  county: number | null | undefined;
  metro: number | null | undefined;
}

type CurrentMarketConditionProps = {
  serverType: 'exp' | 'prod';
  isLeaseMode: boolean;
  areaData: AreaDataOptions;
  numberOfMonths: number;
  timePeriod: 'current' | 'prior';
  bedrooms: Bedrooms;
  enabled?: boolean;
  newConstruction: 'true' | 'false' | '';
  prices: [number | null, number | null];
  date: string;
};
export const useMarketConditionData = ({
  enabled = true,
  ...props
}: CurrentMarketConditionProps) => {
  const area = props.areaData;

  const { data: aoiCurrentData, isLoading: aoiCurrentLoading, error: aoiError } =
    useMarketConditionAOI({
      serverType: props.serverType,
      propertyType: !props.isLeaseMode ? 'Residential' : 'Residential Lease',
      areaData: props.areaData,
      numberOfMonths: props.numberOfMonths,
      type: props.timePeriod,
      bedrooms: props.bedrooms,
      date: props.date,
      enabled: enabled,
      newConstruction: props.newConstruction,
      prices: props.prices,
    });
  const { data: zipCodeCurrentData, isLoading: zipCodeCurrentLoading, error: zipCodeError  } =
    useMarketConditionZIPCode({
      serverType: props.serverType,
      propertyType: !props.isLeaseMode ? 'Residential' : 'Residential Lease',
      areaData: { latitude: area?.latitude, longitude: area?.longitude },
      numberOfMonths: props.numberOfMonths,
      type: props.timePeriod,
      bedrooms: props.bedrooms,
      enabled: enabled,
      newConstruction: props.newConstruction,
      date: props.date,
      prices: props.prices,
    });
  const {
    data: schoolDistrictCurrentData,
    isLoading: schoolDistrictCurrentLoading,
    error: schoolDistrictError,
  } = useMarketConditionSchoolDistrict({
    serverType: props.serverType,
    propertyType: !props.isLeaseMode ? 'Residential' : 'Residential Lease',
    areaData: { latitude: area?.latitude, longitude: area?.longitude },
    numberOfMonths: props.numberOfMonths,
    type: props.timePeriod,
    bedrooms: props.bedrooms,
    enabled: enabled,
    newConstruction: props.newConstruction,
    prices: props.prices,
    date: props.date,
  });
  const { data: countyCurrentData, isLoading: countyCurrentLoading, error: countyError } =
    useMarketConditionCounty({
      serverType: props.serverType,
      propertyType: !props.isLeaseMode ? 'Residential' : 'Residential Lease',
      areaData: { latitude: area?.latitude, longitude: area?.longitude },
      numberOfMonths: props.numberOfMonths,
      type: props.timePeriod,
      bedrooms: props.bedrooms,
      enabled: enabled,
      newConstruction: props.newConstruction,
      prices: props.prices,
      date: props.date,
    });
  const { data: metroCurrentData, isLoading: metroCurrentLoading, error: metroError } =
    useMarketConditionMetro({
      serverType: props.serverType,
      propertyType: !props.isLeaseMode ? 'Residential' : 'Residential Lease',
      areaData: { latitude: area?.latitude, longitude: area?.longitude },
      numberOfMonths: props.numberOfMonths,
      type: props.timePeriod,
      bedrooms: props.bedrooms,
      enabled: enabled,
      newConstruction: props.newConstruction,
      prices: props.prices,
      date: props.date,
    });

  // prettier-ignore
  const data = React.useMemo(() => {
    if (!enabled) return undefined;
    const markers = [
      'active', 
      'active_median_psf', 
      'active_median_current_price',
      'closed', 
      'closed_median_psf', 
      'closed_median_close_price',
      'median_dom', 
      'months_of_inventory'
    ];
    const areaDataSet = [
      { key: 'aoi', data: aoiCurrentData }, 
      { key: 'zipcode', data: zipCodeCurrentData }, 
      { key: 'schoolDistrict', data:schoolDistrictCurrentData },
      { key: 'county', data: countyCurrentData }, 
      { key: 'metro', data: metroCurrentData }
    ];
    
    const result = [];

    const isAreaDataLoading = (areaKey: string) => {
      switch (areaKey) {
        case 'aoi':
          return aoiCurrentLoading;
        case 'zipcode':
          return zipCodeCurrentLoading;
        case 'schoolDistrict':
          return schoolDistrictCurrentLoading;
        case 'county':
          return countyCurrentLoading;
        case 'metro':
          return metroCurrentLoading;
        default:
          return false;
      }
    }
    
    for (const marker of markers) {
      const d = {} as MarketConditionData;
        d['type'] = marker;
      for (const areaData of areaDataSet) {
        // @ts-ignore
        d[areaData.key] = isAreaDataLoading(areaData.key) ? 'loading' : areaData.data ? areaData.data[marker as keyof typeof areaData.data] : undefined;
      }
      result.push(d);
    }
    return result;
  }, [
    enabled,
    aoiCurrentData,
    aoiCurrentLoading,
    zipCodeCurrentData,
    zipCodeCurrentLoading,
    schoolDistrictCurrentData,
    schoolDistrictCurrentLoading,
    countyCurrentData,
    countyCurrentLoading,
    metroCurrentData,
    metroCurrentLoading,
  ]);

  const error = React.useMemo(() => {
    if (!enabled) return null;
    return aoiError || zipCodeError || schoolDistrictError || countyError || metroError;
  }, [enabled, aoiError, zipCodeError, schoolDistrictError, countyError, metroError]);
  
  const isLoading = React.useMemo(() => {
    if (!enabled) return false;
    return aoiCurrentLoading || zipCodeCurrentLoading || schoolDistrictCurrentLoading || 
           countyCurrentLoading || metroCurrentLoading;
  }, [
    enabled, 
    aoiCurrentLoading, 
    zipCodeCurrentLoading, 
    schoolDistrictCurrentLoading, 
    countyCurrentLoading, 
    metroCurrentLoading
  ]);

  return { data, error, isLoading };
};
