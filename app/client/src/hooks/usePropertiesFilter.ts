import { useContext } from 'react';
import { PropertiesFilterContext, PropertiesFilterContextType } from '../contexts/PropertiesFilterContext';

/**
 * Hook to access properties filter state
 * @returns Filter state and functions to manipulate it
 */
export const usePropertiesFilterState = (): PropertiesFilterContextType => {
  const context = useContext(PropertiesFilterContext);
  
  if (!context) {
    throw new Error('usePropertiesFilterState must be used within a PropertiesFilterProvider');
  }
  
  return context;
};