import { useContext } from 'react';
import { PanelStateContext, PanelStateContextType } from '../contexts/PanelStateContext';

/**
 * Hook to access panel state
 * @returns Panel state and functions to manipulate it
 */
export const usePanelState = (): PanelStateContextType => {
  const context = useContext(PanelStateContext);
  
  if (!context) {
    throw new Error('usePanelState must be used within a PanelStateProvider');
  }
  
  return context;
};