import { useMemo } from 'react';
import { LennarSinglePropertyDataType } from '@/lib/utils/types';

export interface PropertyImageItem {
  imageName: string;
  url: string;
  category: 'elevation' | 'interior' | 'floorplan' | 'additional_floorplan';
}

// Extract and combine images from property meta data only
export const usePropertyImages = (property: LennarSinglePropertyDataType | undefined) => {
  const orderedImages = useMemo(() => {
    if (!property?.payload?.subjectProperty?.meta) {
      // console.log('usePropertyImages: No property meta data available');
      return [];
    }

    const meta = property.payload.subjectProperty.meta;
    const images: PropertyImageItem[] = [];
 
    // 1. Extract elevation images from property meta
    const elevationImages = meta.spec_image_elevation?.length > 0 && Array.isArray(meta.spec_image_elevation) 
  ? meta.spec_image_elevation 
  : meta.plan_image_elevation?.length > 0 && Array.isArray(meta.plan_image_elevation)
  ? meta.plan_image_elevation 
  : meta.plan_image_interior && Array.isArray(meta.plan_image_interior) && meta.plan_image_interior.length > 0
  ? [meta.plan_image_interior[0]] 
  : [];

if (elevationImages.length > 0) {
  elevationImages.forEach((url: string, index: number) => {
    images.push({
      imageName: `elevation_${index + 1}`,
      url: url,
      category: 'elevation'
    });
  });
  // console.log(`Found ${elevationImages.length} elevation images from property meta (spec or plan)`);
}

    // 2. Extract interior images from property meta (plan_image_interior)
    if (meta.plan_image_interior && Array.isArray(meta.plan_image_interior)) {
      meta.plan_image_interior.forEach((url: string, index: number) => {
        images.push({
          imageName: `interior_${index + 1}`,
          url: url,
          category: 'interior'
        });
      });
      // console.log(`Found ${meta.plan_image_interior.length} interior images from property meta`);
    }

    // 3. Extract floorplan images from property meta
    if (meta.spec_image_floorplan && Array.isArray(meta.spec_image_floorplan)) {
      meta.spec_image_floorplan.forEach((url: string, index: number) => {
        const category = index === 0 ? 'floorplan' : 'additional_floorplan';
        images.push({
          imageName: `floorplan_${index + 1}`,
          url: url,
          category: category
        });
      });
      // console.log(`Found ${meta.spec_image_floorplan.length} floorplan images from property meta`);
    }

    // Images are already in the correct order due to how we added them
    // console.log('usePropertyImages: Final combined images:', {
    //   total: images.length,
    //   elevation: images.filter(img => img.category === 'elevation').length,
    //   interior: images.filter(img => img.category === 'interior').length,
    //   floorplan: images.filter(img => img.category === 'floorplan').length,
    //   additional_floorplan: images.filter(img => img.category === 'additional_floorplan').length,
    //   imageDetails: images.map(img => `${img.imageName} (${img.category})`)
    // });

    return images;
  }, [property]);

  // Extract virtual tour URL
  const virtualTourUrl = useMemo(() => {
    if (!property?.payload?.subjectProperty?.meta) {
      return null;
    }
    
    const meta = property.payload.subjectProperty.meta;
    return meta.plan_virtual_tour || null;
  }, [property]);

  return {
    images: orderedImages,
    virtualTourUrl,
    loading: false, // No loading since we're extracting from existing data
    error: false    // No error handling needed for existing data
  };
}; 