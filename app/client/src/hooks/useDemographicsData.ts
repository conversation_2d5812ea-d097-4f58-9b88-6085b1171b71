import { useState, useEffect } from 'react';
import { DemographicsDataProps } from '../types/PropertyDetailPage.types';
import { getAreaDemographicInDistance, getAreaDemographicInDriveTime } from '@/lib/query/get-area-demographic-data';

const defaultAreaDemographicData: DemographicsDataProps = {
  five_year_hh_income_growth: 0,
  five_year_pop_growth: 0,
  median_hh_income: 0,
  total_households: 0,
  total_population: 0
};

export const useDemographicsData = (
  lat: number | null,
  lng: number | null,
  distance: number,
  driveTime: number,
  isDistanceMode: boolean
) => {
  const [data, setData] = useState<DemographicsDataProps>(defaultAreaDemographicData);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
     // Skip API call if coordinates are missing
    if (lat === null || lng === null) {
      setError(new Error("Missing coordinates"));
      setIsLoading(false);
      return;
    }

    const fetchData = async () => {
      
      setIsLoading(true);
      setError(null);
      
      try {
        let result: DemographicsDataProps;
        
        if (isDistanceMode) {
          const distanceData = await getAreaDemographicInDistance({
            lat,
            lng,
            distance
          });
          result = distanceData ?? defaultAreaDemographicData;
        } else {
          const driveTimeData = await getAreaDemographicInDriveTime({
            body: {
              lat,
              lng,
              drivetime: driveTime
            }
          });
          result = driveTimeData ?? defaultAreaDemographicData;
        }
        
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
        console.error('Error fetching demographics data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [lat, lng, distance, driveTime, isDistanceMode]);

  return { data, isLoading, error };
}