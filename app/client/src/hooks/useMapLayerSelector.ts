import { useState, useCallback, useEffect } from "react";
import { mapMenuConfig } from "../constants/mapSidebarConfig";
import { useMarketplaceMapContext } from "@/contexts/MarketplaceMapContext";

export const useMapLayerSelector = () => {
  const { map } = useMarketplaceMapContext();
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [selectedItems, setSelectedItems] = useState<{
    [key: string]: string[];
  }>({});

  const handleClick = useCallback((label: string) => {
    setActiveSubmenu((prevState) => (prevState === label ? null : label));
  }, []);

  // Function to get all selected item keys
  const getSelectedKeys = (): string[] => {
    const selectedKeys: string[] = [];

    // Iterate through all categories and their selected items
    Object.entries(selectedItems).forEach(([category, selectedLabels]) => {
      selectedLabels.forEach((label) => {
        // Find the corresponding key for this label in the submenu data
        const menuItem = mapMenuConfig.find((item) => item.label === category);
        if (menuItem) {
          const subItem = menuItem.submenu.find((sub) => sub.label === label);
          if (subItem) {
            selectedKeys.push(subItem.key);
          }
        }
      });
    });

    return selectedKeys;
  };

  // Apply selected keys to map when selections change
  useEffect(() => {
    const selectedKeys = getSelectedKeys();

    if (map) {
      map.fire("mapLayers.currentMapLayerOptions", {
        payload: {
          currentMapLayerOptions: selectedKeys,
        },
      });
    }
  }, [selectedItems, map]);

  const handleSubItemClick = (category: string, item: string, key: string) => {
    setSelectedItems((prevSelected) => {
      const categoryItems = prevSelected[category] || [];

      // If item already selected, remove it; otherwise add it
      if (categoryItems.includes(item)) {
        return {
          ...prevSelected,
          [category]: categoryItems.filter((i) => i !== item),
        };
      } else {
        return {
          ...prevSelected,
          [category]: [...categoryItems, item],
        };
      }
    });
    setActiveSubmenu(null);
  };

  // Helper to check if an item is selected
  const isItemSelected = (category: string, item: string): boolean => {
    return selectedItems[category]?.includes(item) || false;
  };

  return {
    activeSubmenu,
    selectedItems,
    handleClick,
    handleSubItemClick,
    isItemSelected,
  };
};
