import { useContext } from 'react';
import { BreakpointContextType, BreakpointContext } from '../contexts/BreakpointContext';

export const useBreakpoint = (): BreakpointContextType => {
  const context = useContext(BreakpointContext);

  if (!context) {
    throw new Error('useBreakpoint must be used within a BreakpointProvider');
  }

  return context;
};

export type { Breakpoint } from '../contexts/BreakpointContext';