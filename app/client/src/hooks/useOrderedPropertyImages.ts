import { useMemo } from 'react';
import { singleImageLennarResponseType } from '@/lib/query/get-images-lennar';

export interface OrderedImageCategory {
  category: 'elevation' | 'interior' | 'floorplan' | 'additional_floorplan';
  images: singleImageLennarResponseType[];
}

// Image categorization based on filename patterns - updated for actual Lennar patterns
const categorizeImage = (imageName: string): OrderedImageCategory['category'] => {
  const name = imageName.toLowerCase();
  
  // Elevation/Exterior images - including render variants
  if (name.includes('rend') || name.includes('elevation') || name.includes('elev') || 
      name.includes('exterior') || name.includes('front') || name.includes('render')) {
    return 'elevation';
  }
  
  // Floorplan images - including SVG files which are typically floorplans
  if (name.includes('_fp_') || name.includes('fp_') || name.includes('floorplan') || 
      name.includes('floor_plan') || name.includes('plan') || name.includes('.svg')) {
    // Check for additional floorplans (floorplan2, floorplan3, etc.)
    if (name.match(/(?:floorplan|fp|plan)[_\s]*[2-9]|(?:plan|fp)[_\s]*[b-z]/i)) {
      return 'additional_floorplan';
    }
    return 'floorplan';
  }
  
  // Interior images - based on actual Lennar patterns observed in selectLennarHeroImage
  if (name.includes('living') || name.includes('kitchen') || name.includes('dining') || 
      name.includes('ownersuite') || name.includes('bedroom') || name.includes('bathroom') || 
      name.includes('master') || name.includes('family') || name.includes('office') ||
      name.includes('foyer') || name.includes('guest') || name.includes('interior')) {
    return 'interior';
  }
  
  // Since the API endpoint is /interior-renders, most images will likely be interior
  // Default to interior for unknown patterns
  return 'interior';
};

export const useOrderedPropertyImages = (images: singleImageLennarResponseType[]) => {
  const orderedImages = useMemo(() => {
    if (!images || images.length === 0) {
      console.log('useOrderedPropertyImages: No images provided');
      return [];
    }

    console.log('useOrderedPropertyImages: Processing', images.length, 'images');

    // Categorize all images
    const categorized: Record<OrderedImageCategory['category'], singleImageLennarResponseType[]> = {
      elevation: [],
      interior: [],
      floorplan: [],
      additional_floorplan: []
    };

    images.forEach(image => {
      const category = categorizeImage(image.imageName);
      categorized[category].push(image);
      console.log(`Image "${image.imageName}" categorized as: ${category}`);
    });

    // Log categorization results
    console.log('Categorization results:', {
      elevation: categorized.elevation.length,
      interior: categorized.interior.length,
      floorplan: categorized.floorplan.length,
      additional_floorplan: categorized.additional_floorplan.length
    });

    // Sort within each category by filename for consistency
    Object.keys(categorized).forEach(key => {
      categorized[key as keyof typeof categorized].sort((a, b) => 
        a.imageName.localeCompare(b.imageName)
      );
    });

    // Return images in the specified order: elevation → interior → floorplan → additional floorplans
    const orderedResult = [
      ...categorized.elevation,
      ...categorized.interior,
      ...categorized.floorplan,
      ...categorized.additional_floorplan
    ];

    console.log('Final ordered images count:', orderedResult.length);
    console.log('Order verification:', orderedResult.map(img => `${img.imageName} (${categorizeImage(img.imageName)})`));

    return orderedResult;
  }, [images]);

  return orderedImages;
};

// Utility function for testing image categorization
export const testImageCategorization = (imageName: string): OrderedImageCategory['category'] => {
  return categorizeImage(imageName);
};

// Example function to validate the specific images mentioned in the requirements
export const validateExampleImages = () => {
  const examples = [
    { name: 'taylor_core_f090_rend_fenway_c.jpg', expectedCategory: 'elevation' },
    { name: 'taylor_core_f090_fp_fenway_ow.svg', expectedCategory: 'floorplan' },
    // Add examples based on actual Lennar patterns
    { name: 'Living_1.jpg', expectedCategory: 'interior' },
    { name: 'Kitchen_1.jpg', expectedCategory: 'interior' },
    { name: 'Dining_1.jpg', expectedCategory: 'interior' },
    { name: 'OwnersSuite_1.jpg', expectedCategory: 'interior' },
  ];

  const results = examples.map(example => ({
    imageName: example.name,
    expectedCategory: example.expectedCategory,
    actualCategory: categorizeImage(example.name),
    isCorrect: categorizeImage(example.name) === example.expectedCategory
  }));

  console.log('Image categorization validation results:', results);
  return results;
};

// Utility to get category statistics for debugging
export const getCategoryStatistics = (images: singleImageLennarResponseType[]) => {
  const stats: Record<string, number> = {};
  
  images.forEach(image => {
    const category = categorizeImage(image.imageName);
    stats[category] = (stats[category] || 0) + 1;
  });
  
  return stats;
}; 