import { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import { OfferSubmissionStep, SimplifiedOffer, UserRole } from '../types/offerTypes';
import { sendOfferData } from '@/lib/query/send-offer-data';
import { submitOffer } from '@/lib/query/submit-offer';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { getUserFirstName, getUserLastName, getUserPhoneNumber, getUserRole } from '@/lib/utils/auth';

// Utility function to normalize user role values from AWS Amplify to API format
const normalizeUserRole = (amplifyRole: string): UserRole => {
  console.log('normalizeUserRole - Input:', amplifyRole, 'Type:', typeof amplifyRole);
  
  // Handle empty or undefined roles
  if (!amplifyRole || amplifyRole.trim() === '') {
    console.warn('User role is empty or undefined, defaulting to "investor"');
    return 'investor';
  }

  const trimmedRole = amplifyRole.trim();
  console.log('normalizeUserRole - Trimmed role:', trimmedRole);

  switch (trimmedRole) {
    case 'Agent':
      console.log('normalizeUserRole - Returning: agent');
      return 'agent';
    case 'Investor':
      console.log('normalizeUserRole - Returning: investor');
      return 'investor';
    case 'Investor and Agent':
      console.log('normalizeUserRole - Returning: agent_investor');
      return 'agent_investor';
    default:
      console.warn(`Unknown user role: "${amplifyRole}", defaulting to 'investor'`);
      return 'investor';
  }
};

export interface OfferFormState {
  email: string;
  phone: string;
  firstName: string;
  lastName: string;
  purchasePrice: number;
  comments?: string;
  financing: "yes" | "no" | "";
  PM: "yes" | "no" | "";
  compensation: string;
}

interface UseOfferFormProps {
  initialEmail: string;
}

export const useOfferForm = ({ initialEmail }: UseOfferFormProps) => {
  const [currentModalStep, setCurrentModalStep] = useState<OfferSubmissionStep>(OfferSubmissionStep.START);
  const [isTermsOfUseOpen, setIsTermsOfUseOpen] = useState(false);
  const [userRole, setUserRole] = useState<UserRole>('investor');
  const [formState, setFormState] = useState<OfferFormState>({
    email: initialEmail,
    phone: "",
    firstName: "",
    lastName: "",
    purchasePrice: 0,
    comments: "",
    financing: "",
    PM: "",
    compensation: ""
  });

  const { selectedBuyersViewRecord, proFormaAllValues, proFormaInputsLennar } = useMarketplaceMapContext();

  // Pre-populate form fields with user data and ProForma values
  useEffect(() => {
    const prePopulateFormFields = async () => {
      try {
        // Get user attributes from AWS Amplify
        const [firstName, lastName, phoneNumber, role] = await Promise.all([
          getUserFirstName(),
          getUserLastName(), 
          getUserPhoneNumber(),
          getUserRole()
        ]);

        setUserRole(normalizeUserRole(role));

        // Get bid price from ProForma if available
        let bidPrice = 0;
        if (proFormaAllValues && proFormaAllValues["Bid Price"]) {
          bidPrice = Number(proFormaAllValues["Bid Price"]);
        }

        setFormState(prev => ({
          ...prev,
          firstName: firstName || prev.firstName,
          lastName: lastName || prev.lastName,
          phone: phoneNumber || prev.phone,
          purchasePrice: bidPrice || prev.purchasePrice
        }));
      } catch (error) {
        console.error('Error pre-populating form fields:', error);
      }
    };

    prePopulateFormFields();
  }, [proFormaAllValues]);

  // Form field update utility
  const updateFormField = <K extends keyof OfferFormState>(field: K, value: OfferFormState[K]) => {
    setFormState(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Step navigation
  const handleStartOffer = () => setCurrentModalStep(OfferSubmissionStep.SUBMIT);
  const handleSubmitOffer = async () => {
    // Data Validation
    if (!selectedBuyersViewRecord?.property_id) {
        toast.error("Property ID is missing.");
        return;
    }

    const fullAddress = selectedBuyersViewRecord?.full_address;
    const city = selectedBuyersViewRecord?.city;
    const state = selectedBuyersViewRecord?.state;
    const postal_code = selectedBuyersViewRecord?.postal_code;

    if (!fullAddress || !city || !state || !postal_code) {
        toast.error("Property address details are incomplete.");
        return;
    }

    const address = `${fullAddress}, ${city}, ${state} ${postal_code}`;

    // Fixed validation to properly check for required fields including phone
    if (!formState.email || !formState.phone || !formState.firstName || !formState.lastName || !formState.purchasePrice) {
        toast.error("Please fill in all required fields (Email, Phone Number, First Name, Last Name, Offer Price).");
        return;
    }

    if (formState.financing === "") {
    toast.error("Please select whether you require financing.");
    return;
}

    if (formState.PM === "") {
        toast.error("Please select whether you would like to be contacted about property managers.");
        return;
    }

    const purchasePriceNumber = formState.purchasePrice;
    if (isNaN(purchasePriceNumber)) {
        toast.error("Offer Price must be a valid number.");
        return;
    }

    // Improved email validation regex and trim whitespace
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(formState.email.trim())) {
        toast.error("Please enter a valid email address.");
        return;
    }

    // Validate user role (temporarily relaxed for debugging)
    const validRoles: UserRole[] = ['agent', 'investor', 'agent_investor'];
    console.log('Current userRole:', userRole, 'Type:', typeof userRole);
    if (!validRoles.includes(userRole)) {
        console.error('Invalid user role detected:', userRole, 'Valid roles:', validRoles);
        console.warn('Proceeding with offer submission despite invalid role for debugging purposes');
        // Temporarily commenting out the return to allow debugging
        // toast.error(`Invalid user role: "${userRole}". Please refresh the page and try again.`);
        // return;
    }
    
    if (userRole === 'agent' && !formState.compensation.trim()) {
        toast.error("Please enter your compensation.");
        return;
    }

    const simplifiedOffer: SimplifiedOffer = {
      selectedBuyersViewRecord: selectedBuyersViewRecord,
        propertyId: selectedBuyersViewRecord.property_id,
        address: address,
        email: formState.email,
        phone: formState.phone,
        firstName: formState.firstName,
        lastName: formState.lastName,
        offerPrice: purchasePriceNumber,
        comments: formState.comments || '',
        userRole: userRole,
        // Extract bid_price and spec_number from property data
        bid_price: selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.spec_price 
          ? parseFloat(selectedBuyersViewRecord.payload.subjectProperty.meta.spec_price)
          : purchasePriceNumber,
        spec_number: selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.spec_number || '',
        ...(userRole === 'agent' 
          ? { compensation: formState.compensation }
          : { financingRequired: formState.financing === "yes", PMRequired: formState.PM === "yes" }
        ),
    };

    try {
        await submitOffer(simplifiedOffer);
        await sendOfferData({
          source: 'offmarket',
          propertyId: selectedBuyersViewRecord?.property_id?.toString() || '',
          body: {
            decision: 'approve',
            notes: '',
            payload: {
              offerForm: {
                firstName: formState.firstName,
                lastName: formState.lastName,
                offerPrice: formState.purchasePrice,
                comments: formState.comments || '' ,
                financing: formState.financing,
                PM: formState.PM,
                email: formState.email,
                phone: formState.phone,
                userRole: userRole,
              },
              proforma: {
                buyAndHold: proFormaAllValues || {},
                userInputs: proFormaInputsLennar?.proFormaValues || {},
              },
              subjectProperty: selectedBuyersViewRecord?.payload?.subjectProperty,
            }
          }
        })
        setCurrentModalStep(OfferSubmissionStep.SUCCESS);
        toast.success("Offer submitted successfully!");
    } catch (error) {
        console.error("Error submitting offer:", error);
        toast.error("Failed to submit offer. Please try again.");
    }
  };

  // Terms modal state
  const openTermsModal = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsTermsOfUseOpen(true);
  };
  const closeTermsModal = () => setIsTermsOfUseOpen(false);

  return {
    currentModalStep,
    formState,
    isTermsOfUseOpen,
    userRole,
    updateFormField,
    handleStartOffer,
    handleSubmitOffer,
    openTermsModal,
    closeTermsModal
  };
};