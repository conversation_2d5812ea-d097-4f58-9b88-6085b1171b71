import { useState, useEffect } from 'react';
import { MajorEmployerDataProps } from '../types/PropertyDetailPage.types';
import { getMajorEmployersInDistance, getMajorEmployersInDriveTime } from '@/lib/query/get-major-employer-data';

const defaultMajorEmployerData: MajorEmployerDataProps[] = [
  {
    companyName: "",
    locationEmployeeSizeActual: 0,
    subcategory: "",
    distance: 0,
  }
];

export const useMajorEmployersData = (
  lat: number | null,
  lng: number | null,
  distance: number,
  driveTime: number,
  isDistanceMode: boolean
) => {
  const [data, setData] = useState<MajorEmployerDataProps[]>(defaultMajorEmployerData);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    // Skip API call if coordinates are missing
    if (lat === null || lng === null) {
      setError(new Error("Missing coordinates"));
      setIsLoading(false);
      return;
    }

    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        let result;
        
        if (isDistanceMode) {
          const distanceData = await getMajorEmployersInDistance({
            lat,
            lng,
            distance
          });
          result = distanceData ?? defaultMajorEmployerData;
        } else {
          const driveTimeData = await getMajorEmployersInDriveTime({
            body: {
              lat,
              lng,
              drivetime: driveTime
            }
          });
          result = driveTimeData ?? defaultMajorEmployerData;
        }
        
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
        console.error('Error fetching major employers data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [lat, lng, distance, driveTime, isDistanceMode]);

  return { 
    data, 
    isLoading, 
    error 
  };
};