// import { DataItem } from "@/components/PropertyDetails/TabInsights/StackBarChart";
// import { getParcelOwnerSummary } from "@/lib/query/get-parcel-owner-summary";
// import { getOwnerRenterCounts } from "@/lib/utils/getOwnerRenterCounts";
// import { useEffect, useState } from "react";

// type ParcelOwnerSummaryDataParams = {
//   lng: number;
//   lat: number;
//   distance: number;
// }

// type OwnerCountItem = {
//   owner_occupied_sl: 'Yes' | 'No';
//   count: number;
// }

// type InstitutionCountItem = {
//   institution: string;
//   count: number;
// }

// export type ParcelOwnerData = {
//   institution_count: InstitutionCountItem[];
//   owner_count: OwnerCountItem[];
//   total_parcels: number;
// };

// const useParcelOwnerData = ({lat, lng, distance}: ParcelOwnerSummaryDataParams) =>{
//   const [occupied, setOccupied] = useState<DataItem[]>([{ name: "Owner/Renter", owner: 0, renter: 0 }]);
//   const [total, setTotal] = useState<number>(0);
//   const [isLoading, setIsLoading] = useState(true);
//   const [error, setError] = useState<Error | null>(null);

//   useEffect(() => {
    
//     if (lat === null || lng === null) {
//       setError(new Error("Missing coordinates"));
//       setIsLoading(false);
//       return;
//     }

//     const fetchData = async () => {
//       try {
//         setIsLoading(true);
//         const result:ParcelOwnerData | null = await getParcelOwnerSummary({
//           lng,
//           lat,
//           distance: distance * 1609.34 // Convert miles to meters
//         });
//           const { owner, renter } = getOwnerRenterCounts(result?.owner_count);
//           setOccupied([{ name: "Owner/Renter", owner, renter }]);
//           setTotal(result ? result.total_parcels : 0);
//           setIsLoading(false);
//       } catch (err) {
//           setError(err instanceof Error ? err : new Error('Unknown error occurred'));
//           console.error('Error fetching owner data:', err);
//           setIsLoading(false);
        
//       }
//     };

//     fetchData();
//   }, [lat, lng, distance]);

//   return { 
//     occupied,
//     total,
//     isLoading,
//     error 
//   };
// }

// export default useParcelOwnerData;