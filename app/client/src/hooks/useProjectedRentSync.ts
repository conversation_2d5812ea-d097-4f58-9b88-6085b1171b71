import { useState, useEffect, useCallback } from 'react';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { saveSingleProForma, getSavedSingleProForma } from "@/lib/query/save-pro-forma-off-market";

// Custom hook for managing projected rent state across components
export const useProjectedRentSync = () => {
  const { proFormaAllValues, selectedBuyersViewRecord } = useMarketplaceMapContext();
  const [userInputProjectedRent, setUserInputProjectedRent] = useState<number | null>(null);
  const [lastPropertyId, setLastPropertyId] = useState<string | null>(null);

  // Load saved user input when property changes
  useEffect(() => {
    const propertyId = selectedBuyersViewRecord?.property_id?.toString();
    
    if (propertyId && propertyId !== lastPropertyId) {
      setLastPropertyId(propertyId);
      getSavedSingleProForma(propertyId).then((savedInputValues) => {
        setUserInputProjectedRent(savedInputValues.userInputProjectedRent || null);
      });
    }
  }, [selectedBuyersViewRecord?.property_id, lastPropertyId]);

  // Save user input to database
  // const saveUserInputValues = useCallback((newProjectedRent: number | null) => {
  //   const propertyId = selectedBuyersViewRecord?.property_id;
  //   if (propertyId) {
  //     saveSingleProForma({
  //       propertyId: propertyId,
  //       source: 'offmarket',
  //       proFormaValues: {
  //         userInputProjectedRent: newProjectedRent,
  //       },
  //     });
  //   }
  // }, [selectedBuyersViewRecord?.property_id]);

  // Update projected rent value
  const updateProjectedRent = useCallback((value: number) => {
    setUserInputProjectedRent(value);
    // saveUserInputValues(value);
    
    // Trigger a custom event to notify other components
    window.dispatchEvent(new CustomEvent('projectedRentUpdated', { 
      detail: { value, propertyId: selectedBuyersViewRecord?.property_id } 
    }));
  }, [selectedBuyersViewRecord?.property_id]);

  // Get current value (user input takes precedence over calculated value)
  const getCurrentValue = useCallback(() => {
    if (userInputProjectedRent !== null) {
      return userInputProjectedRent;
    }
    return proFormaAllValues?.["Projected Monthly Rent"] || 0;
  }, [userInputProjectedRent, proFormaAllValues]);

  // Listen for updates from other components
  useEffect(() => {
    const handleProjectedRentUpdate = (event: CustomEvent) => {
      const { value, propertyId } = event.detail;
      // Only update if it's for the same property and not from this instance
      if (propertyId === selectedBuyersViewRecord?.property_id) {
        setUserInputProjectedRent(value);
      }
    };

    window.addEventListener('projectedRentUpdated', handleProjectedRentUpdate as EventListener);
    return () => {
      window.removeEventListener('projectedRentUpdated', handleProjectedRentUpdate as EventListener);
    };
  }, [selectedBuyersViewRecord?.property_id]);

  return {
    userInputProjectedRent,
    setUserInputProjectedRent,
    updateProjectedRent,
    getCurrentValue,
    // saveUserInputValues,
  };
}; 