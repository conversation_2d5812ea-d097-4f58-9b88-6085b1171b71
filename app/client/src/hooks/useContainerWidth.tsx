import { useState, useRef, useEffect, useCallback, useLayoutEffect } from 'react';
import { useBreakpoint } from './useBreakpoint';

/**
 * Hook to track the width of a container element, with special handling for panel changes
 * 
 * @param isLeftPanelOpen - Boolean indicating if the left panel is open
 * @returns Object containing containerRef, containerWidth, and updateContainerWidth
 */
export const useContainerWidth = (isLeftPanelOpen: boolean) => {
  const windowWidth = window.innerWidth;
  const containerRef = useRef<HTMLDivElement>(null);
  const { isMobile } = useBreakpoint();
  const firstRenderWidth = isMobile ? windowWidth - 32 : windowWidth / 2 - 32;
  const [containerWidth, setContainerWidth] = useState(firstRenderWidth);

  // Update container width function
  const updateContainerWidth = useCallback(() => {
    if (containerRef.current) {
      const width = containerRef.current.offsetWidth;
      if (width > 0) {
        setContainerWidth(width);
      }
    }
  }, []);

  useLayoutEffect(() => {
    updateContainerWidth();
  }, []);

  useEffect(() => {
    if (containerRef.current) {
      const timer = setTimeout(() => {
        const finalWidth = containerRef.current?.offsetWidth || 0;

        // Only update if width actually changed significantly
        if (Math.abs(finalWidth - containerWidth) > 5) {
          setContainerWidth(finalWidth);
        }
      }, 400);

      return () => clearTimeout(timer);
    }
  }, [isLeftPanelOpen, containerWidth]);

  // Set up resize observer
  useLayoutEffect(() => {
    updateContainerWidth();

    const resizeObserver = new ResizeObserver(updateContainerWidth);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => resizeObserver.disconnect();
  }, [updateContainerWidth]);

  // Initial width measurement
  useEffect(() => {
    if (containerRef.current && containerWidth === 0) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [containerWidth]);

  return { containerRef, containerWidth, updateContainerWidth };
}