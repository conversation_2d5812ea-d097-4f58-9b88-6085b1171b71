import { useState, useEffect } from 'react';
import { PropertyDataPoint, PropertyDataPointWithFlag } from '@/lib/query/get-parcel-unit-mix-data';
import { ParcelDataType } from '@/lib/query/get-parcel-data';


export const useCompositionData = (
  rawBedroomData: PropertyDataPoint[] | undefined,
  rawBathroomData: PropertyDataPoint[] | undefined,
  rawSqftData: PropertyDataPoint[] | undefined,
  rawYearData: PropertyDataPoint[] | undefined,
  parcelData: ParcelDataType[] | undefined
) => {
  const [bedroomData, setBedroomData] = useState<PropertyDataPointWithFlag[]>([]);
  const [bathroomData, setBathroomData] = useState<PropertyDataPointWithFlag[]>([]);
  const [squareFeetData, setSquareFeetData] = useState<PropertyDataPointWithFlag[]>([]);
  const [yearBuiltData, setYearBuiltData] = useState<PropertyDataPointWithFlag[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsLoading(true);
    if (!parcelData?.[0]) {
      // Reset all data states if no subject property
      setBedroomData([]);
      setBathroomData([]);
      setSquareFeetData([]);
      setYearBuiltData([]);
      setIsLoading(false);
      return;
    }

    const subjectProperty = parcelData[0];
    const subjectBeds = Math.floor(subjectProperty.beds_count);
    const subjectBaths = Math.floor(subjectProperty.baths);
    const subjectSqFt = subjectProperty.total_area_sq_ft;
    const subjectYearBuilt = subjectProperty.year_built;

  
    processBedroomData(rawBedroomData, subjectBeds, setBedroomData);
    processBathroomData(rawBathroomData, subjectBaths, setBathroomData);
    processSqftData(rawSqftData, subjectSqFt, setSquareFeetData);
    processYearData(rawYearData, subjectYearBuilt, setYearBuiltData);

    setIsLoading(false);
  }, [rawBathroomData, rawBedroomData, rawSqftData, rawYearData, parcelData]);

  return {
    bedroomData,
    bathroomData,
    squareFeetData,
    yearBuiltData,
    isLoading,
  };
}


const processBedroomData = (
  rawData: PropertyDataPoint[] | undefined, 
  subjectBeds: number,
  setData: React.Dispatch<React.SetStateAction<PropertyDataPointWithFlag[]>>
) => {
  if (rawData) {
    const processedData = rawData.map(item => {
      const updatedItem: PropertyDataPointWithFlag = { 
        ...item,
        isSubjectProperty: false 
      };
      if (item.x === '6+') {
        updatedItem.isSubjectProperty = subjectBeds >= 6;
      } else {
        updatedItem.isSubjectProperty = subjectBeds === Number(item.x);
      }
      return updatedItem;
    });
    setData(processedData);
  } else {
    setData([]);
  }
}

const processBathroomData = (
  rawData: PropertyDataPoint[] | undefined, 
  subjectBaths: number,
  setData: React.Dispatch<React.SetStateAction<PropertyDataPointWithFlag[]>>
) => {
  if (rawData) {
    const processedData = rawData.map(item => {
      const updatedItem: PropertyDataPointWithFlag = { 
        ...item,
        isSubjectProperty: false 
      };
      if (item.x === '6+') {
        updatedItem.isSubjectProperty = subjectBaths >= 6;
      } else {
        updatedItem.isSubjectProperty = subjectBaths === Number(item.x);
      }
      return updatedItem;
    });
    setData(processedData);
  } else {
    setData([]);
  }
}

const processSqftData = (
  rawData: PropertyDataPoint[] | undefined, 
  subjectSqFt: number,
  setData: React.Dispatch<React.SetStateAction<PropertyDataPointWithFlag[]>>
) => {
  if (rawData) {
    const processedData = rawData.map(item => {
      const updatedItem: PropertyDataPointWithFlag = { 
        ...item,
        isSubjectProperty: false 
      };
      if (!item.x.includes('<') && !item.x.includes('>')) {
        const [min, max] = item.x.split('-').map(Number);
        updatedItem.isSubjectProperty = (subjectSqFt >= min && subjectSqFt <= max);
      } else if (item.x.includes('<')) {
        const value = Number(item.x.split('<')[1]);
        updatedItem.isSubjectProperty = subjectSqFt < value;
      } else if (item.x.includes('>')) {
        const value = Number(item.x.split('>')[1]);
        updatedItem.isSubjectProperty = subjectSqFt > value;
      }
      return updatedItem;
    });
    setData(processedData);
  } else {
    setData([]);
  }
}

const processYearData = (
  rawData: PropertyDataPoint[] | undefined, 
  subjectYearBuilt: number,
  setData: React.Dispatch<React.SetStateAction<PropertyDataPointWithFlag[]>>
) => {
  if (rawData) {
    const processedData = rawData.map(item => {
      const updatedItem: PropertyDataPointWithFlag = { 
        ...item,
        isSubjectProperty: false 
      };
      if (!item.x.includes('<') && !item.x.includes('>')) {
        const [min, max] = item.x.split('-').map(Number);
        updatedItem.isSubjectProperty = (subjectYearBuilt >= min && subjectYearBuilt <= max);
      } else if (item.x.includes('<')) {
        const value = Number(item.x.split('<')[1]);
        updatedItem.isSubjectProperty = subjectYearBuilt < value;
      } else if (item.x.includes('>')) {
        const value = Number(item.x.split('>')[1]);
        updatedItem.isSubjectProperty = subjectYearBuilt > value;
      }
      return updatedItem;
    });
    setData(processedData);
  } else {
    setData([]);
  }
}