import { useState, useEffect } from 'react';
import { PropertyDataPoint, PropertyDataPointWithFlag } from '@/lib/query/get-parcel-unit-mix-data';
import { ParcelDataType } from '@/lib/query/get-parcel-data';


export const useCompositionData = (
  rawBedroomData?: PropertyDataPoint[],
  rawBathroomData?: PropertyDataPoint[],
  rawSqftData?: PropertyDataPoint[],
  rawYearData?: PropertyDataPoint[],
  parcelData?: ParcelDataType[],
  propertyBeds?: number,
  propertyBaths?: number,
  propertySqft?: number
) => {
  const [bedroomData, setBedroomData] = useState<PropertyDataPointWithFlag[]>([]);
  const [bathroomData, setBathroomData] = useState<PropertyDataPointWithFlag[]>([]);
  const [squareFeetData, setSquareFeetData] = useState<PropertyDataPointWithFlag[]>([]);
  const [yearBuiltData, setYearBuiltData] = useState<PropertyDataPointWithFlag[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsLoading(true);
    if (!parcelData?.[0]) {
      // Reset all data states if no subject property
      setBedroomData([]);
      setBathroomData([]);
      setSquareFeetData([]);
      setYearBuiltData([]);
      setIsLoading(false);
      return;
    }

    const subjectProperty = parcelData[0];
    const subjectBeds = propertyBeds;
    const subjectBaths = propertyBaths;
    const subjectSqFt = propertySqft;
    const subjectYearBuilt = subjectProperty.year_built;

  
    processBedroomData(rawBedroomData, subjectBeds, setBedroomData);
    processBathroomData(rawBathroomData, subjectBaths, setBathroomData);
    processSqftData(rawSqftData, subjectSqFt, setSquareFeetData);
    processYearData(rawYearData, subjectYearBuilt, setYearBuiltData);

    setIsLoading(false);
  }, [rawBathroomData, rawBedroomData, rawSqftData, rawYearData, parcelData]);

  return {
    bedroomData,
    bathroomData,
    squareFeetData,
    yearBuiltData,
    isLoading,
  };
}


const processBedroomData = (
  rawData: PropertyDataPoint[] | undefined, 
  subjectBeds: number | undefined, 
  setData: React.Dispatch<React.SetStateAction<PropertyDataPointWithFlag[]>>
) => {
  if (rawData) {
    const processedData = rawData.map(item => {
      const updatedItem: PropertyDataPointWithFlag = { 
        ...item,
        isSubjectProperty: false 
      };

      if (subjectBeds === undefined) {
        return updatedItem;
      }

      if (item.x === '6+') {
        updatedItem.isSubjectProperty = subjectBeds >= 6;
      } else {
        const itemValue = Number(item.x);
        const roundedSubjectBeds = Math.ceil(subjectBeds);
        updatedItem.isSubjectProperty = roundedSubjectBeds === itemValue;
      }
      return updatedItem;
    });
    setData(processedData);
  } else {
    setData([]);
  }
}

const processBathroomData = (
  rawData: PropertyDataPoint[] | undefined, 
  subjectBaths: number | undefined,
  setData: React.Dispatch<React.SetStateAction<PropertyDataPointWithFlag[]>>
) => {
  if (rawData) {
    const processedData = rawData.map(item => {
      const updatedItem: PropertyDataPointWithFlag = { 
        ...item,
        isSubjectProperty: false 
      };

      if (subjectBaths === undefined) {
        return updatedItem;
      }

      if (item.x === '6+') {
        updatedItem.isSubjectProperty = subjectBaths >= 6;
      } else {
        const itemValue = Number(item.x);
        const roundedSubjectBaths = Math.ceil(subjectBaths);
        updatedItem.isSubjectProperty = roundedSubjectBaths === itemValue;
      }
      return updatedItem;
    });
    setData(processedData);
  } else {
    setData([]);
  }
}

const processSqftData = (
  rawData: PropertyDataPoint[] | undefined, 
  subjectSqFt: number | undefined,
  setData: React.Dispatch<React.SetStateAction<PropertyDataPointWithFlag[]>>
) => {
  if (rawData) {
    const processedData = rawData.map(item => {
      const updatedItem: PropertyDataPointWithFlag = { 
        ...item,
        isSubjectProperty: false 
      };

      if (subjectSqFt === undefined) {
        return updatedItem;
      }
      if (!item.x.includes('<') && !item.x.includes('>')) {
        const [min, max] = item.x.split('-').map(Number);
        updatedItem.isSubjectProperty = (subjectSqFt >= min && subjectSqFt <= max);
      } else if (item.x.includes('<')) {
        const value = Number(item.x.split('<')[1]);
        updatedItem.isSubjectProperty = subjectSqFt < value;
      } else if (item.x.includes('>')) {
        const value = Number(item.x.split('>')[1]);
        updatedItem.isSubjectProperty = subjectSqFt > value;
      }
      return updatedItem;
    });
    setData(processedData);
  } else {
    setData([]);
  }
}

const processYearData = (
  rawData: PropertyDataPoint[] | undefined, 
  subjectYearBuilt: number,
  setData: React.Dispatch<React.SetStateAction<PropertyDataPointWithFlag[]>>
) => {
  if (rawData) {
    const processedData = rawData.map(item => {
      const updatedItem: PropertyDataPointWithFlag = { 
        ...item,
        isSubjectProperty: item.x === ">2011" // Only mark ">2011" as subject property
      };
      return updatedItem;
    });
    setData(processedData);
  } else {
    setData([]);
  }
}