import React, { createContext, useContext, useState } from 'react';

interface FormSubmissionContextType {
  triggerFormSubmission: () => void;
  isSubmitting: boolean;
  setIsSubmitting: (submitting: boolean) => void;
  registerSubmitCallback: (callback: () => void) => void;
}

const FormSubmissionContext = createContext<FormSubmissionContextType | undefined>(undefined);

export const useFormSubmission = () => {
  const context = useContext(FormSubmissionContext);
  if (!context) {
    throw new Error('useFormSubmission must be used within a FormSubmissionProvider');
  }
  return context;
};

interface FormSubmissionProviderProps {
  children: React.ReactNode;
}

export const FormSubmissionProvider: React.FC<FormSubmissionProviderProps> = ({ children }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitCallback, setSubmitCallback] = useState<(() => void) | null>(null);

  const triggerFormSubmission = () => {
    if (submitCallback) {
      submitCallback();
    }
  };

  const registerSubmitCallback = (callback: () => void) => {
    setSubmitCallback(() => callback);
  };

  return (
    <FormSubmissionContext.Provider 
      value={{ 
        triggerFormSubmission, 
        isSubmitting, 
        setIsSubmitting,
        registerSubmitCallback 
      }}
    >
      {children}
    </FormSubmissionContext.Provider>
  );
}; 