import { createContext, useState } from 'react';

export interface DemographicsContextType {
  demographicsDriveTime: number;
  setDemographicsDriveTime: React.Dispatch<React.SetStateAction<number>>;
  demographicsDistance: number;
  setDemographicsDistance: React.Dispatch<React.SetStateAction<number>>;
  isDemographicsDistanceMode: boolean;
  setDemographicsIsDistanceMode: React.Dispatch<React.SetStateAction<boolean>>;

  majorEmployersDriveTime: number;
  setMajorEmployersDriveTime: React.Dispatch<React.SetStateAction<number>>;
  majorEmployersDistance: number;
  setMajorEmployersDistance: React.Dispatch<React.SetStateAction<number>>;
  isMajorEmployersDistanceMode: boolean;
  setMajorEmployersIsDistanceMode: React.Dispatch<React.SetStateAction<boolean>>;

  householdDriveTime: number;
  setHouseholdDriveTime: React.Dispatch<React.SetStateAction<number>>;
  householdDistance: number;
  setHouseholdDistance: React.Dispatch<React.SetStateAction<number>>;
  isHouseholdDistanceMode: boolean;
  setHouseholdIsDistanceMode: React.Dispatch<React.SetStateAction<boolean>>;
}

const defaultDriveTime = 30;
const defaultDistance = 3;

export const DemographicsContext = createContext<DemographicsContextType>({
  // Demographics context values
  demographicsDriveTime: defaultDriveTime,
  setDemographicsDriveTime: () => { },
  demographicsDistance: defaultDistance,
  setDemographicsDistance: () => { },
  isDemographicsDistanceMode: false,
  setDemographicsIsDistanceMode: () => { },
  // Major employers context values
  majorEmployersDriveTime: defaultDriveTime,
  setMajorEmployersDriveTime: () => { },
  majorEmployersDistance: defaultDistance,
  setMajorEmployersDistance: () => { },
  isMajorEmployersDistanceMode: false,
  setMajorEmployersIsDistanceMode: () => { },
  // Household context values
  householdDriveTime: defaultDriveTime,
  setHouseholdDriveTime: () => { },
  householdDistance: defaultDistance,
  setHouseholdDistance: () => { },
  isHouseholdDistanceMode: false,
  setHouseholdIsDistanceMode: () => { },
});

export const DemographicsProvider = ({ children }: { children: React.ReactNode }) => {
  // State for demographic
  const [demographicsDriveTime, setDemographicsDriveTime] = useState<number>(defaultDriveTime);
  const [demographicsDistance, setDemographicsDistance] = useState<number>(defaultDistance);
  const [isDemographicsDistanceMode, setDemographicsIsDistanceMode] = useState<boolean>(false);

  // State for major employers
  const [majorEmployersDriveTime, setMajorEmployersDriveTime] = useState<number>(defaultDriveTime);
  const [majorEmployersDistance, setMajorEmployersDistance] = useState<number>(defaultDistance);
  const [isMajorEmployersDistanceMode, setMajorEmployersIsDistanceMode] = useState<boolean>(false);

  // State for household
  const [householdDriveTime, setHouseholdDriveTime] = useState<number>(defaultDriveTime);
  const [householdDistance, setHouseholdDistance] = useState<number>(defaultDistance);
  const [isHouseholdDistanceMode, setHouseholdIsDistanceMode] = useState<boolean>(false);


  return (
    <DemographicsContext.Provider
      value={{
        demographicsDriveTime,
        setDemographicsDriveTime,
        demographicsDistance,
        setDemographicsDistance,
        isDemographicsDistanceMode,
        setDemographicsIsDistanceMode,

        majorEmployersDriveTime,
        setMajorEmployersDriveTime,
        majorEmployersDistance,
        setMajorEmployersDistance,
        isMajorEmployersDistanceMode,
        setMajorEmployersIsDistanceMode,

        householdDriveTime,
        setHouseholdDriveTime,
        householdDistance,
        setHouseholdDistance,
        isHouseholdDistanceMode,
        setHouseholdIsDistanceMode,
      }}
    >
      {children}
    </DemographicsContext.Provider>
  );
};