import { createContext, useState, ReactNode, Dispatch, SetStateAction, useMemo } from 'react';
import {
  priceOptions,
  bedsOptions,
  bathsOptions,
  PriceRangeValues,
  BedroomMinValues,
  BathroomMinValues,
  FilterConfig,
  MarketFilterConfig,
  PriceFilterConfig,
  BedFilterConfig,
  BathFilterConfig
} from '../types/PropertiesFilterTypes';
import getMarketOptions from '@/lib/utils/getMarketOptions';
import { LennarSinglePropertyDataType } from '@/lib/utils/types';
import { useRouter, useSearch } from '@tanstack/react-router';

export type PropertiesFilterContextType = {
  selectedMarket: string[];
  setSelectedMarket: Dispatch<SetStateAction<string[]>>;
  selectedPrice: PriceRangeValues | "";
  setSelectedPrice: Dispatch<SetStateAction<PriceRangeValues | "">>;
  selectedBeds: BedroomMinValues | "";
  setSelectedBeds: Dispatch<SetStateAction<BedroomMinValues | "">>;
  selectedBaths: BathroomMinValues | "";
  setSelectedBaths: Dispatch<SetStateAction<BathroomMinValues | "">>;
  minCapRate: string;
  setMinCapRate: Dispatch<SetStateAction<string>>;
  filters: FilterConfig[];
  handleMinCapRateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

export const defaultMinCapRate: string = "0";

const defaultContextValue: PropertiesFilterContextType = {
  selectedMarket: [],
  setSelectedMarket: () => { },
  selectedPrice: "",
  setSelectedPrice: () => { },
  selectedBeds: "",
  setSelectedBeds: () => { },
  selectedBaths: "",
  setSelectedBaths: () => { },
  minCapRate: defaultMinCapRate,
  setMinCapRate: () => { },
  filters: [],
  handleMinCapRateChange: () => { },
};

export const PropertiesFilterContext = createContext<PropertiesFilterContextType>(defaultContextValue);

export const PropertiesFilterProvider = ({ children, properties }: { children: ReactNode, properties: LennarSinglePropertyDataType[]; }) => {
  const router = useRouter();
  const search = useSearch({ from: '/_authenticated/properties' });

  // Initialize state from search params
  const [selectedMarket, setSelectedMarketState] = useState<string[]>(search.selectedMarkets ? (Array.isArray(search.selectedMarkets) ? search.selectedMarkets : [search.selectedMarkets]) : []);
  const [selectedPrice, setSelectedPriceState] = useState<PriceRangeValues | "">(search.selectedPrice || "");
  const [selectedBeds, setSelectedBedsState] = useState<BedroomMinValues | "">(search.selectedBeds || "");
  const [selectedBaths, setSelectedBathsState] = useState<BathroomMinValues | "">(search.selectedBaths || "");
  const [minCapRate, setMinCapRateState] = useState<string>(search.minCapRate || "");

  // Update URL search params when filters change
  const setSelectedMarket = (value: string[] | ((prev: string[]) => string[])) => {
    setSelectedMarketState(value);
    const newValue = typeof value === 'function' ? value(selectedMarket) : value;
    router.navigate({
      to: '/properties',
      search: { ...search, selectedMarkets: newValue.length > 0 ? newValue : undefined },
      replace: true,
    });
  };
  const setSelectedPrice = (value: PriceRangeValues | "") => {
    setSelectedPriceState(value);
    router.navigate({
      to: '/properties',
      search: { ...search, selectedPrice: value || undefined },
      replace: true,
    });
  };
  const setSelectedBeds = (value: BedroomMinValues | "") => {
    setSelectedBedsState(value);
    router.navigate({
      to: '/properties',
      search: { ...search, selectedBeds: value || undefined },
      replace: true,
    });
  };
  const setSelectedBaths = (value: BathroomMinValues | "") => {
    setSelectedBathsState(value);
    router.navigate({
      to: '/properties',
      search: { ...search, selectedBaths: value || undefined },
      replace: true,
    });
  };
  const setMinCapRate = (value: string) => {
    setMinCapRateState(value);
    router.navigate({
      to: '/properties',
      search: { ...search, minCapRate: value || undefined },
      replace: true,
    });
  };

  // Dynamically generate market options based on properties
  const marketOptions = useMemo(() => getMarketOptions(properties), [properties]);

  const marketFilter: MarketFilterConfig = {
    label: "Market",
    options: marketOptions,
    value: selectedMarket,
    onChange: setSelectedMarket,
    multiSelect: true
  };

  const priceFilter: PriceFilterConfig = {
    label: "Price",
    options: priceOptions,
    value: selectedPrice,
    onChange: setSelectedPrice,
  };

  const bedsFilter: BedFilterConfig = {
    label: "Bed",
    options: bedsOptions,
    value: selectedBeds,
    onChange: setSelectedBeds,
  };

  const bathsFilter: BathFilterConfig = {
    label: "Bath",
    options: bathsOptions,
    value: selectedBaths,
    onChange: setSelectedBaths,
  };

  const filters: FilterConfig[] = [marketFilter, priceFilter, bedsFilter, bathsFilter];

  const handleMinCapRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMinCapRate(value);
  };

  return (
    <PropertiesFilterContext.Provider
      value={{
        selectedMarket,
        setSelectedMarket,
        selectedPrice,
        setSelectedPrice,
        selectedBeds,
        setSelectedBeds,
        selectedBaths,
        setSelectedBaths,
        minCapRate,
        setMinCapRate,
        filters,
        handleMinCapRateChange
      }}
    >
      {children}
    </PropertiesFilterContext.Provider>
  );
};