import { createContext, useState, ReactNode, Dispatch, SetStateAction, useMemo } from 'react';
import {
  priceOptions,
  bedsOptions,
  bathsOptions,
  PriceRangeValues,
  BedroomMinValues,
  BathroomMinValues,
  FilterConfig,
  MarketFilterConfig,
  PriceFilterConfig,
  BedFilterConfig,
  BathFilterConfig
} from '../types/PropertiesFilterTypes';
import getMarketOptions from '@/lib/utils/getMarketOptions';
import { LennarSinglePropertyDataType } from '@/lib/utils/types';

export type PropertiesFilterContextType = {
  selectedMarket: string | "";
  setSelectedMarket: Dispatch<SetStateAction<string | "">>;
  selectedPrice: PriceRangeValues | "";
  setSelectedPrice: Dispatch<SetStateAction<PriceRangeValues | "">>;
  selectedBeds: BedroomMinValues | "";
  setSelectedBeds: Dispatch<SetStateAction<BedroomMinValues | "">>;
  selectedBaths: BathroomMinValues | "";
  setSelectedBaths: Dispatch<SetStateAction<BathroomMinValues | "">>;
  minCapRate: string;
  setMinCapRate: Dispatch<SetStateAction<string>>;
  filters: FilterConfig[];
  handleMinCapRateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

export const defaultMinCapRate: string = "0";

const defaultContextValue: PropertiesFilterContextType = {
  selectedMarket: "",
  setSelectedMarket: () => { },
  selectedPrice: "",
  setSelectedPrice: () => { },
  selectedBeds: "",
  setSelectedBeds: () => { },
  selectedBaths: "",
  setSelectedBaths: () => { },
  minCapRate: defaultMinCapRate,
  setMinCapRate: () => { },
  filters: [],
  handleMinCapRateChange: () => { },
};

export const PropertiesFilterContext = createContext<PropertiesFilterContextType>(defaultContextValue);

export const PropertiesFilterProvider = ({ children, properties }: { children: ReactNode, properties: LennarSinglePropertyDataType[]; }) => {
  const [selectedMarket, setSelectedMarket] = useState<string | "">("");
  const [selectedPrice, setSelectedPrice] = useState<PriceRangeValues | "">("");
  const [selectedBeds, setSelectedBeds] = useState<BedroomMinValues | "">("");
  const [selectedBaths, setSelectedBaths] = useState<BathroomMinValues | "">("");
  const [minCapRate, setMinCapRate] = useState<string>(defaultMinCapRate);

  // Dynamically generate market options based on properties
  const marketOptions = useMemo(() => getMarketOptions(properties), [properties]);

  const marketFilter: MarketFilterConfig = {
    label: "Market",
    options: marketOptions,
    value: selectedMarket,
    onChange: setSelectedMarket,
  };

  const priceFilter: PriceFilterConfig = {
    label: "Price",
    options: priceOptions,
    value: selectedPrice,
    onChange: setSelectedPrice,
  };

  const bedsFilter: BedFilterConfig = {
    label: "Beds",
    options: bedsOptions,
    value: selectedBeds,
    onChange: setSelectedBeds,
    color: "lightBlue",
  };

  const bathsFilter: BathFilterConfig = {
    label: "Baths",
    options: bathsOptions,
    value: selectedBaths,
    onChange: setSelectedBaths,
    color: "lightBlue",
  };

  const filters: FilterConfig[] = [marketFilter, priceFilter, bedsFilter, bathsFilter];

  const handleMinCapRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMinCapRate(value);
  };

  return (
    <PropertiesFilterContext.Provider
      value={{
        selectedMarket,
        setSelectedMarket,
        selectedPrice,
        setSelectedPrice,
        selectedBeds,
        setSelectedBeds,
        selectedBaths,
        setSelectedBaths,
        minCapRate,
        setMinCapRate,
        filters,
        handleMinCapRateChange
      }}
    >
      {children}
    </PropertiesFilterContext.Provider>
  );
};