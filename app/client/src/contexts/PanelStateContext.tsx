import { createContext, useState, ReactNode } from 'react';

// Define the type for the context value
export type PanelStateContextType = {
  isLeftPanelOpen: boolean;
  toggleLeftPanel: () => void;
  setIsLeftPanelOpen: (state: boolean) => void;
};

export const PanelStateContext = createContext<PanelStateContextType>({
  isLeftPanelOpen: true,
  toggleLeftPanel: () => { },
  setIsLeftPanelOpen: () => { },
});

export const PanelStateProvider = ({ children, initialState = true }: {
  children: ReactNode;
  initialState?: boolean;
}) => {
  const [isLeftPanelOpen, setIsLeftPanelOpen] = useState(initialState);

  const toggleLeftPanel = () => {
    setIsLeftPanelOpen(prev => !prev);
  };

  return (
    <PanelStateContext.Provider value={{
      isLeftPanelOpen,
      toggleLeftPanel,
      setIsLeftPanelOpen
    }}>
      {children}
    </PanelStateContext.Provider>
  );
};