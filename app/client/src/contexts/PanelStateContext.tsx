import { createContext, useState, ReactNode, useEffect } from 'react';
import { useBreakpoint } from '@/hooks/useBreakpoint';


// Define the type for the context value
export type PanelStateContextType = {
  isLeftPanelOpen: boolean;
  toggleLeftPanel: () => void;
  setIsLeftPanelOpen: (state: boolean) => void;
};

// TODO: isLeftPanelOpen default should be based on screen size
// For now, we will default to false
export const PanelStateContext = createContext<PanelStateContextType>({
  isLeftPanelOpen: false,
  toggleLeftPanel: () => { },
  setIsLeftPanelOpen: () => { },
});

export const PanelStateProvider = ({ children, initialState = true }: {
  children: ReactNode;
  initialState?: boolean;
}) => {
  const { isDesktop, isMobile, isTablet, windowWidth } = useBreakpoint();

  // Initialize with undefined to detect if we've set a value
  const [isLeftPanelOpen, setIsLeftPanelOpen] = useState<boolean>(() => {
    // If initialState is provided, use it
    if (initialState !== undefined) {
      return initialState;
    }

    // For SSR or initial render before breakpoint is determined
    return false;
  });

  // Run this effect once on mount and when screen size changes
  useEffect(() => {
    // Skip if initialState is explicitly provided
    if (initialState !== undefined) {
      return;
    }

    // Responsive behavior: open on desktop, close on mobile/tablet
    if (isDesktop) {
      setIsLeftPanelOpen(true);
    } else if (isMobile || isTablet) {
      setIsLeftPanelOpen(false);
    }
  }, [isDesktop, isMobile, isTablet, initialState, windowWidth]);

  const toggleLeftPanel = () => {
    console.log('toggleLeftPanel', isLeftPanelOpen);
    setIsLeftPanelOpen(prev => !prev);
  };

  return (
    <PanelStateContext.Provider value={{
      isLeftPanelOpen,
      toggleLeftPanel,
      setIsLeftPanelOpen
    }}>
      {children}
    </PanelStateContext.Provider>
  );
};