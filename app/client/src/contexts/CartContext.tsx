import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import toast from 'react-hot-toast';
import { 
  Cart, 
  CartItem, 
  CartContextType, 
  BulkOfferSubmission, 
  BulkSubmissionUserInfo 
} from '../types/cartTypes';
import {
  getUserCart,
  createCart,
  addItemToCart,
  removeItemFromCart,
  updateCartItem,
  clearCart as clearCartAPI,
  convertPropertyToCartItem,
  saveCartToStorage,
  loadCartFromStorage,
  clearCartFromStorage
} from '../lib/query/cart-api';
import { submitBulkOffers } from '../lib/query/bulk-submit-offers';
import { LennarSinglePropertyDataType, ProFormaValuesLennar } from '@/lib/utils/types';

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [cart, setCart] = useState<Cart | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isCartModalOpen, setIsCartModalOpen] = useState(false);
  const [isOfferFlowOpen, setIsOfferFlowOpen] = useState(false);

  // Initialize cart on mount
  useEffect(() => {
    initializeCart();
  }, []);

  // Save cart to localStorage whenever cart changes
  useEffect(() => {
    if (cart) {
      saveCartToStorage(cart);
    }
  }, [cart]);

  const initializeCart = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Try to load from localStorage first for immediate UI update
      const localCart = loadCartFromStorage();
      if (localCart) {
        setCart(localCart);
      }

      // Then sync with server
      let serverCart = await getUserCart();
      
      if (!serverCart) {
        // Create new cart if none exists
        serverCart = await createCart();
      }

      setCart(serverCart);
    } catch (error) {
      console.error('Failed to initialize cart:', error);
      setError('Failed to load your cart. Please refresh the page.');
      
      // Fall back to localStorage cart if available
      const localCart = loadCartFromStorage();
      if (localCart) {
        console.log("localCart123: ", localCart)
        setCart(localCart);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const addToCart = async (propertyData: any, purchasePrice?: number, originalRent?: number, buyerFinalRent?: number, payload?: { subjectProperty?: LennarSinglePropertyDataType, proforma?: ProFormaValuesLennar }) => {
    try {
      setIsLoading(true);
      setError(null);

      // Convert property data to cart format
      const cartProperty = convertPropertyToCartItem(propertyData, purchasePrice,originalRent,buyerFinalRent, payload);
      console.log("cartProperty123: ", cartProperty);
      // Check if property is already in cart
      if (cart && cart.items.some(item => item.propertyId === cartProperty.propertyId)) {
        toast.error('This property is already in your cart');
        return;
      }
      const newItem = await addItemToCart(cartProperty);
      
      // Set the purchase price on the new item if provided
      if (purchasePrice && purchasePrice > 0) {
        newItem.offerPrice = purchasePrice;
      }
      
      // Update local state immediately for instant UI feedback
      if (cart) {
        const updatedCart = {
          ...cart,
          items: [...cart.items, newItem],
          totalItems: cart.totalItems + 1,
        };
        setCart(updatedCart);
      } else {
        const newCart = {
          id: newItem.cartId || 1,
          userId: 'current-user',
          status: 'active' as const,
          totalItems: 1,
          items: [newItem],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        setCart(newCart);
      }
      
      // Sync with server in background (don't await to avoid overriding local state)
      setTimeout(async () => {
        try {
          const serverCart = await getUserCart();
          if (serverCart && serverCart.items.length > 0) {
            // Only update if server cart has more recent data
            const serverHasOurItem = serverCart.items.some(item => item.propertyId === newItem.propertyId);
            if (serverHasOurItem) {
              setCart(serverCart);
            }
          }
        } catch (syncError) {
          console.warn('Background cart sync failed:', syncError);
        }
      }, 1000); // Delay to avoid immediate override

      // Update the purchase price on the server if needed (async, don't await)
      if (purchasePrice && purchasePrice > 0 && newItem.propertyId) {
        updateCartItemLocal(newItem.propertyId, { offerPrice: purchasePrice }).catch(error => {
          console.warn('Failed to update offer price on server:', error);
          // Don't show error to user as the local state is already updated
        });
      }

      toast.success('Property added to cart');
    } catch (error) {
      console.error('Failed to add to cart:', error);
      setError('Failed to add property to cart');
      toast.error('Failed to add property to cart');
    } finally {
      setIsLoading(false);
    }
  };

  const removeFromCart = async (propertyId: number) => {
    try {
      setIsLoading(true);
      setError(null);

      await removeItemFromCart(propertyId);
      
      if (cart) {
        const updatedCart = {
          ...cart,
          items: cart.items.filter(item => item.propertyId !== propertyId),
          totalItems: cart.totalItems - 1,
        };
        setCart(updatedCart);
      }

      toast.success('Property removed from cart');
    } catch (error) {
      console.error('Failed to remove from cart:', error);
      setError('Failed to remove property from cart');
      toast.error('Failed to remove property from cart');
    } finally {
      setIsLoading(false);
    }
  };

  const updateCartItemLocal = async (propertyId: number, updates: Partial<CartItem>) => {
    try {
      setError(null);

      const updatedItem = await updateCartItem(propertyId, updates);
      
      if (cart) {
        const updatedCart = {
          ...cart,
          items: cart.items.map(item => 
            item.propertyId === propertyId ? { ...item, ...updatedItem } : item
          ),
        };
        setCart(updatedCart);
      }
    } catch (error) {
      console.error('Failed to update cart item:', error);
      setError('Failed to update cart item');
      toast.error('Failed to update cart item');
    }
  };

  const clearCartLocal = async () => {
    try {
      setIsLoading(true);
      setError(null);

      await clearCartAPI();
      
      setCart(null);
      clearCartFromStorage();
      
      toast.success('Cart cleared');
    } catch (error) {
      console.error('Failed to clear cart:', error);
      setError('Failed to clear cart');
      toast.error('Failed to clear cart');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleItemConfirmation = async (propertyId: number) => {
    if (!cart) return;

    const item = cart.items.find(item => item.propertyId === propertyId);
    if (!item) return;

    await updateCartItemLocal(propertyId, { isConfirmed: !item.isConfirmed });
  };

  const updateOfferPrice = async (propertyId: number, offerPrice: number) => {
    await updateCartItemLocal(propertyId, { offerPrice });
  };

  const submitSelectedOffers = async (userInfo: BulkSubmissionUserInfo): Promise<BulkOfferSubmission> => {
    try {
      setIsLoading(true);
      setError(null);

      if (!cart) {
        throw new Error('No cart found');
      }

      const confirmedItems = cart.items.filter(item => item.isConfirmed);
      if (confirmedItems.length === 0) {
        throw new Error('No properties selected for submission');
      }

      // Validate that all confirmed items have offer prices
      const itemsWithoutPrices = confirmedItems.filter(item => !item.offerPrice || item.offerPrice <= 0);
      if (itemsWithoutPrices.length > 0) {
        throw new Error('All selected properties must have valid offer prices');
      }
      console.log("submitBulkOffers", confirmedItems)
      const submission = await submitBulkOffers(confirmedItems, userInfo);
      
      // Clear the cart after successful submission
      try {
        await clearCartAPI();
        // If successful, clear all frontend state
        setCart(null);
        clearCartFromStorage();
      } catch (clearError) {
        console.warn('Failed to clear entire cart, trying to remove submitted items individually:', clearError);
        
        // Fallback: remove only the submitted items one by one
        try {
          for (const item of confirmedItems) {
            await removeItemFromCart(item.propertyId);
          }
          
          // Update frontend state by removing submitted items
          if (cart) {
            const remainingItems = cart.items.filter(item => !item.isConfirmed);
            if (remainingItems.length === 0) {
              setCart(null);
              clearCartFromStorage();
            } else {
              setCart({
                ...cart,
                items: remainingItems,
                totalItems: remainingItems.length
              });
            }
          }
        } catch (removeError) {
          console.warn('Failed to remove individual items, clearing frontend only:', removeError);
          // Last resort: just clear frontend
          setCart(null);
          clearCartFromStorage();
        }
      }
      
      toast.success(`Submitted offers for ${confirmedItems.length} properties`);
      
      return submission;
    } catch (error) {
      console.error('Failed to submit offers:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to submit offers';
      setError(errorMessage);
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const selectAllItems = async () => {
    if (!cart) return;
    
    try {
      setIsLoading(true);
      
      // Update local state immediately
      const updatedCart = {
        ...cart,
        items: cart.items.map(item => ({ ...item, isConfirmed: true }))
      };
      setCart(updatedCart);

      // Update each item on server
      const updatePromises = cart.items.map(item => 
        updateCartItemLocal(item.propertyId, { isConfirmed: true }).catch(error => {
          console.warn(`Failed to update item ${item.propertyId} on server:`, error);
        })
      );
      
      await Promise.allSettled(updatePromises);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to select all items');
    } finally {
      setIsLoading(false);
    }
  };

  const deselectAllItems = async () => {
    if (!cart) return;
    
    try {
      setIsLoading(true);
      
      // Update local state immediately
      const updatedCart = {
        ...cart,
        items: cart.items.map(item => ({ ...item, isConfirmed: false }))
      };
      setCart(updatedCart);

      // Update each item on server
      const updatePromises = cart.items.map(item => 
        updateCartItemLocal(item.propertyId, { isConfirmed: false }).catch(error => {
          console.warn(`Failed to update item ${item.propertyId} on server:`, error);
        })
      );
      
      await Promise.allSettled(updatePromises);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to deselect all items');
    } finally {
      setIsLoading(false);
    }
  };

  // Utility functions
  const getCartItemCount = useCallback((): number => {
    return cart?.totalItems || 0;
  }, [cart]);

  const getConfirmedItemCount = useCallback((): number => {
    return cart?.items.filter(item => item.isConfirmed).length || 0;
  }, [cart]);

  const isPropertyInCart = useCallback((propertyId: number): boolean => {
    return cart?.items.some(item => item.propertyId === propertyId) || false;
  }, [cart]);

  const getCartItem = useCallback((propertyId: number): CartItem | undefined => {
    return cart?.items.find(item => item.propertyId === propertyId);
  }, [cart]);

  const contextValue: CartContextType = {
    // State
    cart,
    isLoading,
    error,
    
    // Operations
    addToCart,
    removeFromCart,
    updateCartItem: updateCartItemLocal,
    clearCart: clearCartLocal,
    
    // Item operations
    toggleItemConfirmation,
    updateOfferPrice,
    
    // Bulk submission
    submitSelectedOffers,
    
    // Utility functions
    getCartItemCount,
    getConfirmedItemCount,
    isPropertyInCart,
    getCartItem,
    selectAllItems,
    deselectAllItems,
    
    // Modal state
    isCartModalOpen,
    setIsCartModalOpen,
    isOfferFlowOpen,
    setIsOfferFlowOpen,
  };

  return (
    <CartContext.Provider value={contextValue}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};