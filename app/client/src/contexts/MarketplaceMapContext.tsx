import React, { createContext, useContext, useState, ReactNode } from 'react';
import { LennarSinglePropertyDataType, ParcelDataType, ProFormaValuesLennar, searchingModeType } from '@/lib/utils/types';
import { Map as MapboxMap } from 'mapbox-gl';
import { useLoaderData } from '@tanstack/react-router';
import { getCurrentClient } from '@/lib/utils/isAnalystBuyerFlow';

interface MarketplaceMapContextType {
  currentClient: string;
  selectedUserGroup: string | null;
  map: MapboxMap | null;
  setMap: (map: MapboxMap | null) => void;
  allProperties: LennarSinglePropertyDataType[] | null;
  setAllProperties: (properties: LennarSinglePropertyDataType[] | null) => void;
  allPropertiesSubmitted: LennarSinglePropertyDataType[] | null;
  setAllPropertiesSubmitted: (properties: LennarSinglePropertyDataType[] | null) => void;
  selectedBuyersViewRecord: LennarSinglePropertyDataType | null;
  setSelectedBuyersViewRecord: (property: LennarSinglePropertyDataType | null) => void;
  currentMapThemeOption: string | null;
  setCurrentMapThemeOption: (option: string | null) => void;
  propertyModalTabKey: string | null;
  setPropertyModalTabKey: (key: string | null) => void;
  // New state variables from Redux
  propertyModalOpened: boolean;
  setPropertyModalOpened: (opened: boolean) => void;
  mapExpandedView: boolean;
  setMapExpandedView: (expanded: boolean) => void;
  currentHighlightCoordinates: number[]; // comp user is hovering over
  setCurrentHighlightCoordinates: (coordinates: number[]) => void;
  typeHighlightMarker: string;
  setTypeHighlightMarker: (type: string) => void;
  eventCoordinates: [number, number];
  setEventCoordinates: (coordinates: [number, number]) => void;
  currentRadiusMile: number;
  setCurrentRadiusMile: (radius: number) => void;
  priceHighlightMarker: number;
  setPriceHighlightMarker: (price: number) => void;
  mapPropertiesDoneFetching: boolean;
  setMapPropertiesDoneFetching: (done: boolean) => void;
  mapLocateProperty: { type: string; id: string } | null;
  setMapLocateProperty: (property: { type: string; id: string } | null) => void;
  // Viewport-based filtering state
  viewportFilteredProperties: LennarSinglePropertyDataType[] | null;
  setViewportFilteredProperties: (properties: LennarSinglePropertyDataType[] | null) => void;
  viewportFilteredPropertiesSubmitted: LennarSinglePropertyDataType[] | null;
  setViewportFilteredPropertiesSubmitted: (properties: LennarSinglePropertyDataType[] | null) => void;
  isViewportFilteringEnabled: boolean;
  setIsViewportFilteringEnabled: (enabled: boolean) => void;
  mapBounds: [[number, number], [number, number]] | null;
  setMapBounds: (bounds: [[number, number], [number, number]] | null) => void;
  // Additional state variables for MLSLayer
  currentMLSPropertiesFiltered: Record<string, unknown>[];
  setCurrentMLSPropertiesFiltered: (properties: Record<string, unknown>[]) => void;
  currentMLSGeoJSON: Record<string, unknown> | null;
  setCurrentMLSGeoJSON: (geojson: Record<string, unknown> | null) => void;
  showPriceMarkers: boolean;
  setShowPriceMarkers: (show: boolean) => void;
  selectedRowKeysMLSLease: string[];
  setSelectedRowKeysMLSLease: (keys: string[]) => void;
  selectedRowKeysMLSSale: string[];
  setSelectedRowKeysMLSSale: (keys: string[]) => void;
  // Additional state variables for NationalOperatorLayer
  selectedRowKeysNationalOperators: string[];
  setSelectedRowKeysNationalOperators: (keys: string[]) => void;
  currentNationalOperatorsPropertiesFiltered: Record<string, unknown>[];
  setCurrentNationalOperatorsPropertiesFiltered: (properties: Record<string, unknown>[]) => void;
  currentNationalOperatorsGeoJSON: Record<string, unknown> | null;
  setCurrentNationalOperatorsGeoJSON: (geojson: Record<string, unknown> | null) => void;
  // Additional state variables for HotPadsLayer
  selectedRowKeysHotPads: string[];
  setSelectedRowKeysHotPads: (keys: string[]) => void;
  currentHotPadsPropertiesFiltered: Record<string, unknown>[];
  setCurrentHotPadsPropertiesFiltered: (properties: Record<string, unknown>[]) => void;
  currentHotPadsGeoJSON: Record<string, unknown> | null;
  setCurrentHotPadsGeoJSON: (geojson: Record<string, unknown> | null) => void;
  // Additional state variables for NewBuildsLayer
  selectedRowKeysNewBuilds: string[];
  setSelectedRowKeysNewBuilds: (keys: string[]) => void;
  currentNewBuildsProperties: Record<string, unknown>[];
  setCurrentNewBuildsProperties: (properties: Record<string, unknown>[]) => void;
  // Additional state variables for BuyerListingsLayer
  heroImagesLennar: Record<string, unknown>[];
  setHeroImagesLennar: (images: Record<string, unknown>[]) => void;
  selectedPortfolioMarker: string | null;
  setSelectedPortfolioMarker: (marker: string | null) => void;
  // Additional state variables for SelectRadius
  currentStatusMLS: string;
  setCurrentStatusMLS: (status: string) => void;
  currentStartMLS: string;
  setCurrentStartMLS: (start: string) => void;
  currentEndMLS: string;
  setCurrentEndMLS: (end: string) => void;
  expDateFilterOn: boolean;
  setExpDateFilterOn: (on: boolean) => void;
  drawnCustomPolygons: Record<string, unknown>[];
  setDrawnCustomPolygons: (polygons: Record<string, unknown>[]) => void;
  shouldDisableCMA: boolean;
  setShouldDisableCMA: (disable: boolean) => void;
  marketRentPreference: string;
  setMarketRentPreference: (preference: string) => void;
  currentRecordMLS: Record<string, unknown> | null;
  setCurrentRecordMLS: (record: Record<string, unknown> | null) => void;
  circleBbox: number[];
  setCircleBbox: (bbox: number[]) => void;
  // Additional state variables for SelectRadius clear functionality
  subjectPropertyParcelData: ParcelDataType;
  setSubjectPropertyParcelData: (data: ParcelDataType) => void;
  currentParcelOwnerSummary: Record<string, unknown>;
  setCurrentParcelOwnerSummary: (summary: Record<string, unknown>) => void;
  currentBTOwnedProperties: Record<string, unknown>[];
  setCurrentBTOwnedProperties: (properties: Record<string, unknown>[]) => void;
  currentBTOwnedGeoJSON: Record<string, unknown>;
  setCurrentBTOwnedGeoJSON: (geojson: Record<string, unknown>) => void;
  currentNationalOperatorsProperties: Record<string, unknown>[];
  setCurrentNationalOperatorsProperties: (properties: Record<string, unknown>[]) => void;
  currentHotPadsProperties: Record<string, unknown>[];
  setCurrentHotPadsProperties: (properties: Record<string, unknown>[]) => void;
  currentMLSProperties: Record<string, unknown>[];
  setCurrentMLSProperties: (properties: Record<string, unknown>[]) => void;
  currentMultiFamilyProperties: Record<string, unknown>[];
  setCurrentMultiFamilyProperties: (properties: Record<string, unknown>[]) => void;
  currentMultiFamilyGeoJSON: Record<string, unknown>;
  setCurrentMultiFamilyGeoJSON: (geojson: Record<string, unknown>) => void;
  // Additional state variables for ChainStoresProvider
  currentMapLayerOptions: string[];
  setCurrentMapLayerOptions: (options: string[]) => void;
  sadChainStoreDistance: number;
  setSadChainStoreDistance: (distance: number) => void;
  // Additional state variables for RealtorDotComLayer
  currentRealtorDotComGeoJSON: Record<string, unknown> | null;
  setCurrentRealtorDotComGeoJSON: (geojson: Record<string, unknown> | null) => void;
  searchingMode: searchingModeType;
  setSearchingMode: (mode: searchingModeType) => void;
  realtorSingleFamilyHover: Record<string, unknown> | null;
  setRealtorSingleFamilyHover: (property: Record<string, unknown> | null) => void;
  selectedRowKeysRealtorDotCom: string[];
  setSelectedRowKeysRealtorDotCom: (keys: string[]) => void;
  // Additional state variables for Pro Forma
  proFormaAllValues: ProFormaValuesLennar | null;
  setProFormaAllValues: (values: ProFormaValuesLennar | null) => void;
}

const MarketplaceMapContext = createContext<MarketplaceMapContextType | undefined>(undefined);

export const MarketplaceMapProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { userGroup, userEmail } = useLoaderData({ from: '/_authenticated' });

  const [map, setMap] = useState<MapboxMap | null>(null);
  const [allProperties, setAllProperties] = useState<LennarSinglePropertyDataType[] | null>(null);
  const [allPropertiesSubmitted, setAllPropertiesSubmitted] = useState<LennarSinglePropertyDataType[] | null>(null);
  const [selectedBuyersViewRecord, setSelectedBuyersViewRecord] = useState<LennarSinglePropertyDataType | null>(null);
  const [currentMapThemeOption, setCurrentMapThemeOption] = useState<string | null>('Street');
  const [propertyModalTabKey, setPropertyModalTabKey] = useState<string | null>(null);

  // New state variables from Redux
  const [propertyModalOpened, setPropertyModalOpened] = useState<boolean>(false);
  const [mapExpandedView, setMapExpandedView] = useState<boolean>(true);
  const [currentHighlightCoordinates, setCurrentHighlightCoordinates] = useState<number[]>([]);
  const [typeHighlightMarker, setTypeHighlightMarker] = useState<string>('');
  const [eventCoordinates, setEventCoordinates] = useState<[number, number] | []>([]);
  const [currentRadiusMile, setCurrentRadiusMile] = useState<number>(2);
  const [priceHighlightMarker, setPriceHighlightMarker] = useState<number>(0);
  const [mapPropertiesDoneFetching, setMapPropertiesDoneFetching] = useState<boolean>(false);
  const [mapLocateProperty, setMapLocateProperty] = useState<{ type: string; id: string } | null>(null);

  // Viewport-based filtering state
  const [viewportFilteredProperties, setViewportFilteredProperties] = useState<LennarSinglePropertyDataType[] | null>(null);
  const [viewportFilteredPropertiesSubmitted, setViewportFilteredPropertiesSubmitted] = useState<LennarSinglePropertyDataType[] | null>(null);
  const [isViewportFilteringEnabled, setIsViewportFilteringEnabled] = useState<boolean>(true);
  const [mapBounds, setMapBounds] = useState<[[number, number], [number, number]] | null>(null);

  // Additional state variables for MLSLayer
  const [currentMLSPropertiesFiltered, setCurrentMLSPropertiesFiltered] = useState<Record<string, unknown>[]>([]);
  const [currentMLSGeoJSON, setCurrentMLSGeoJSON] = useState<Record<string, unknown> | null>(null);
  const [showPriceMarkers, setShowPriceMarkers] = useState<boolean>(false);
  const [selectedRowKeysMLSLease, setSelectedRowKeysMLSLease] = useState<string[]>([]);
  const [selectedRowKeysMLSSale, setSelectedRowKeysMLSSale] = useState<string[]>([]);

  // Additional state variables for NationalOperatorLayer
  const [selectedRowKeysNationalOperators, setSelectedRowKeysNationalOperators] = useState<string[]>([]);
  const [currentNationalOperatorsPropertiesFiltered, setCurrentNationalOperatorsPropertiesFiltered] = useState<Record<string, unknown>[]>([]);
  const [currentNationalOperatorsGeoJSON, setCurrentNationalOperatorsGeoJSON] = useState<Record<string, unknown> | null>(null);
  // Additional state variables for HotPadsLayer
  const [selectedRowKeysHotPads, setSelectedRowKeysHotPads] = useState<string[]>([]);
  const [currentHotPadsPropertiesFiltered, setCurrentHotPadsPropertiesFiltered] = useState<Record<string, unknown>[]>([]);
  const [currentHotPadsGeoJSON, setCurrentHotPadsGeoJSON] = useState<Record<string, unknown> | null>(null);
  // Additional state variables for NewBuildsLayer
  const [selectedRowKeysNewBuilds, setSelectedRowKeysNewBuilds] = useState<string[]>([]);
  const [currentNewBuildsProperties, setCurrentNewBuildsProperties] = useState<Record<string, unknown>[]>([]);

  // Additional state variables for BuyerListingsLayer
  const [heroImagesLennar, setHeroImagesLennar] = useState<Record<string, unknown>[]>([]);
  const [selectedPortfolioMarker, setSelectedPortfolioMarker] = useState<string | null>(null);

  // Additional state variables for SelectRadius
  const [currentStatusMLS, setCurrentStatusMLS] = useState<string>('');
  const [currentStartMLS, setCurrentStartMLS] = useState<string>('');
  const [currentEndMLS, setCurrentEndMLS] = useState<string>('');
  const [expDateFilterOn, setExpDateFilterOn] = useState<boolean>(false);
  const [drawnCustomPolygons, setDrawnCustomPolygons] = useState<Record<string, unknown>[]>([]);
  const [shouldDisableCMA, setShouldDisableCMA] = useState<boolean>(false);
  const [marketRentPreference, setMarketRentPreference] = useState<string>('');
  const [currentRecordMLS, setCurrentRecordMLS] = useState<Record<string, unknown> | null>(null);
  const [circleBbox, setCircleBbox] = useState<number[]>([]);

  // Additional state variables for SelectRadius clear functionality
  const [subjectPropertyParcelData, setSubjectPropertyParcelData] = useState<ParcelDataType>({} as ParcelDataType);
  const [currentParcelOwnerSummary, setCurrentParcelOwnerSummary] = useState<Record<string, unknown>>({});
  const [currentBTOwnedProperties, setCurrentBTOwnedProperties] = useState<Record<string, unknown>[]>([]);
  const [currentBTOwnedGeoJSON, setCurrentBTOwnedGeoJSON] = useState<Record<string, unknown>>({});
  const [currentNationalOperatorsProperties, setCurrentNationalOperatorsProperties] = useState<Record<string, unknown>[]>([]);
  const [currentHotPadsProperties, setCurrentHotPadsProperties] = useState<Record<string, unknown>[]>([]);
  const [currentMLSProperties, setCurrentMLSProperties] = useState<Record<string, unknown>[]>([]);
  const [currentMultiFamilyProperties, setCurrentMultiFamilyProperties] = useState<Record<string, unknown>[]>([]);
  const [currentMultiFamilyGeoJSON, setCurrentMultiFamilyGeoJSON] = useState<Record<string, unknown>>({});

  // Additional state variables for ChainStoresProvider
  const [currentMapLayerOptions, setCurrentMapLayerOptions] = useState<string[]>([]);
  const [sadChainStoreDistance, setSadChainStoreDistance] = useState<number>(1);

  // Additional state variables for RealtorDotComLayer
  const [currentRealtorDotComGeoJSON, setCurrentRealtorDotComGeoJSON] = useState<Record<string, unknown> | null>(null);
  const [searchingMode, setSearchingMode] = useState<searchingModeType>('Lease');
  const [realtorSingleFamilyHover, setRealtorSingleFamilyHover] = useState<Record<string, unknown> | null>(null);
  const [selectedRowKeysRealtorDotCom, setSelectedRowKeysRealtorDotCom] = useState<string[]>([]);
  // Additional state variables for Pro Forma
  const [proFormaAllValues, setProFormaAllValues] = useState<ProFormaValuesLennar | null>(null);

  return (
    <MarketplaceMapContext.Provider
      value={{
        currentClient: getCurrentClient(Array.isArray(userGroup) ? String(userGroup[0] || '') : '', userEmail, window.location.href),
        selectedUserGroup: Array.isArray(userGroup) ? String(userGroup[0] || '') : null,
        map,
        setMap,
        allProperties,
        setAllProperties,
        allPropertiesSubmitted,
        setAllPropertiesSubmitted,
        selectedBuyersViewRecord,
        setSelectedBuyersViewRecord,
        currentMapThemeOption,
        setCurrentMapThemeOption,
        propertyModalTabKey,
        setPropertyModalTabKey,
        // New state variables
        propertyModalOpened,
        setPropertyModalOpened,
        mapExpandedView,
        setMapExpandedView,
        currentHighlightCoordinates,
        setCurrentHighlightCoordinates,
        typeHighlightMarker,
        setTypeHighlightMarker,
        eventCoordinates: eventCoordinates as [number, number],
        setEventCoordinates,
        currentRadiusMile,
        setCurrentRadiusMile,
        priceHighlightMarker,
        setPriceHighlightMarker,
        mapPropertiesDoneFetching,
        setMapPropertiesDoneFetching,
        mapLocateProperty,
        setMapLocateProperty,
        // Viewport-based filtering state
        viewportFilteredProperties,
        setViewportFilteredProperties,
        viewportFilteredPropertiesSubmitted,
        setViewportFilteredPropertiesSubmitted,
        isViewportFilteringEnabled,
        setIsViewportFilteringEnabled,
        mapBounds,
        setMapBounds,
        // Additional state variables for MLSLayer
        currentMLSPropertiesFiltered,
        setCurrentMLSPropertiesFiltered,
        currentMLSGeoJSON,
        setCurrentMLSGeoJSON,
        showPriceMarkers,
        setShowPriceMarkers,
        selectedRowKeysMLSLease,
        setSelectedRowKeysMLSLease,
        selectedRowKeysMLSSale,
        setSelectedRowKeysMLSSale,
        // Additional state variables for NationalOperatorLayer
        selectedRowKeysNationalOperators,
        setSelectedRowKeysNationalOperators,
        currentNationalOperatorsPropertiesFiltered,
        setCurrentNationalOperatorsPropertiesFiltered,
        currentNationalOperatorsGeoJSON,
        setCurrentNationalOperatorsGeoJSON,
        // Additional state variables for HotPadsLayer
        selectedRowKeysHotPads,
        setSelectedRowKeysHotPads,
        currentHotPadsPropertiesFiltered,
        setCurrentHotPadsPropertiesFiltered,
        currentHotPadsGeoJSON,
        setCurrentHotPadsGeoJSON,
        // Additional state variables for NewBuildsLayer
        selectedRowKeysNewBuilds,
        setSelectedRowKeysNewBuilds,
        currentNewBuildsProperties,
        setCurrentNewBuildsProperties,
        // Additional state variables for BuyerListingsLayer
        heroImagesLennar,
        setHeroImagesLennar,
        selectedPortfolioMarker,
        setSelectedPortfolioMarker,
        // Additional state variables for SelectRadius
        currentStatusMLS,
        setCurrentStatusMLS,
        currentStartMLS,
        setCurrentStartMLS,
        currentEndMLS,
        setCurrentEndMLS,
        expDateFilterOn,
        setExpDateFilterOn,
        drawnCustomPolygons,
        setDrawnCustomPolygons,
        shouldDisableCMA,
        setShouldDisableCMA,
        marketRentPreference,
        setMarketRentPreference,
        currentRecordMLS,
        setCurrentRecordMLS,
        circleBbox,
        setCircleBbox,
        // Additional state variables for SelectRadius clear functionality
        subjectPropertyParcelData,
        setSubjectPropertyParcelData,
        currentParcelOwnerSummary,
        setCurrentParcelOwnerSummary,
        currentBTOwnedProperties,
        setCurrentBTOwnedProperties,
        currentBTOwnedGeoJSON,
        setCurrentBTOwnedGeoJSON,
        currentNationalOperatorsProperties,
        setCurrentNationalOperatorsProperties,
        currentHotPadsProperties,
        setCurrentHotPadsProperties,
        currentMLSProperties,
        setCurrentMLSProperties,
        currentMultiFamilyProperties,
        setCurrentMultiFamilyProperties,
        currentMultiFamilyGeoJSON,
        setCurrentMultiFamilyGeoJSON,
        // Additional state variables for ChainStoresProvider
        currentMapLayerOptions,
        setCurrentMapLayerOptions,
        sadChainStoreDistance,
        setSadChainStoreDistance,
        // Additional state variables for RealtorDotComLayer
        currentRealtorDotComGeoJSON,
        setCurrentRealtorDotComGeoJSON,
        searchingMode,
        setSearchingMode,
        realtorSingleFamilyHover,
        setRealtorSingleFamilyHover,
        selectedRowKeysRealtorDotCom,
        setSelectedRowKeysRealtorDotCom,
        // Additional state variables for Pro Forma
        proFormaAllValues,
        setProFormaAllValues
      }}
    >
      {children}
    </MarketplaceMapContext.Provider>
  );
};

export const useMarketplaceMapContext = () => {
  const context = useContext(MarketplaceMapContext);
  if (context === undefined) {
    throw new Error('useMarketplaceMapContext must be used within a MarketplaceMapProvider');
  }
  return context;
}; 