import React, { createContext, useState, useEffect, ReactNode } from 'react';

export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

const breakpoints = {
  xs: 0,
  sm: 375,
  md: 768,
  lg: 1440,
  xl: 1920,
  '2xl': 2560,
};

export interface BreakpointContextType {
  breakpoint: Breakpoint;
  windowWidth: number;
  isXs: boolean;
  isSm: boolean;
  isMd: boolean;
  isLg: boolean;
  isXl: boolean;
  is2xl: boolean;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

export const BreakpointContext = createContext<BreakpointContextType | undefined>(undefined);

interface BreakpointProviderProps {
  children: ReactNode;
}

export const BreakpointProvider: React.FC<BreakpointProviderProps> = ({ children }) => {
  const [breakpoint, setBreakpoint] = useState<Breakpoint>('lg');
  const [windowWidth, setWindowWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : 0
  );

  const handleResize = () => {
    setWindowWidth(window.innerWidth);
  };

  useEffect(() => {
    handleResize();

    let resizeTimeout: NodeJS.Timeout;
    const debouncedHandleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(handleResize, 100);
    };

    window.addEventListener('resize', debouncedHandleResize);

    return () => {
      window.removeEventListener('resize', debouncedHandleResize);
      clearTimeout(resizeTimeout);
    };
  }, []);

  useEffect(() => {
    if (windowWidth < breakpoints.sm) {
      setBreakpoint('xs');
    } else if (windowWidth < breakpoints.md) {
      setBreakpoint('sm');
    } else if (windowWidth < breakpoints.lg) {
      setBreakpoint('md');
    } else if (windowWidth < breakpoints.xl) {
      setBreakpoint('lg');
    } else if (windowWidth < breakpoints['2xl']) {
      setBreakpoint('xl');
    } else {
      setBreakpoint('2xl');
    }
  }, [windowWidth]);

  const isXs = breakpoint === 'xs';
  const isSm = breakpoint === 'sm';
  const isMd = breakpoint === 'md';
  const isLg = breakpoint === 'lg';
  const isXl = breakpoint === 'xl';
  const is2xl = breakpoint === '2xl';

  const isMobile = isXs || isSm;
  const isTablet = isMd;
  const isDesktop = isLg || isXl || is2xl;

  const value = {
    breakpoint,
    windowWidth,
    isXs,
    isSm,
    isMd,
    isLg,
    isXl,
    is2xl,
    isMobile,
    isTablet,
    isDesktop,
  };

  return (
    <BreakpointContext.Provider value={value}>
      {children}
    </BreakpointContext.Provider>
  );
};