import React, { createContext, useRef, ReactNode } from 'react';

export interface ScrollContextType {
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  scrollToTop: () => void;
}

export const ScrollContext = createContext<ScrollContextType | undefined>(undefined);

interface ScrollProviderProps {
  children: ReactNode;
}

export const ScrollProvider: React.FC<ScrollProviderProps> = ({ children }) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const scrollToTop = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = 0;
    }
  };

  const value: ScrollContextType = {
    scrollContainerRef,
    scrollToTop
  };

  return (
    <ScrollContext.Provider value={value}>
      {children}
    </ScrollContext.Provider>
  );
};