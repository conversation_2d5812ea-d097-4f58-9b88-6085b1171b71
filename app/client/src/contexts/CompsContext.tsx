import React, { createContext, useContext, useState, ReactNode } from 'react';
import {
  parcelData,
  mlsData,
  portfolioPropertyData,
  mlsCompData,
  sfrAndHotPadsCompData,
  realtorDotComCompData,
  filters,
  filterCompDataParamsForLease,
  filterCompDataParamsForSale,
  filterCompDataParams,
  schoolDistrictPolygon,
  countyPolygon,
  ZIPCodePolygon,
} from '@/components/PropertyDetails/TabComps/utils/types';
import dayjs from 'dayjs';
import { dateFormat } from '@/constants';
import filtersDefault from '@/components/PropertyDetails/TabComps/utils/filters-default.json';

interface CompsContextType {
  currentStatusMLS: string;
  setCurrentStatusMLS: (status: string) => void;
  currentStartMLS: string;
  setCurrentStartMLS: (start: string) => void;
  currentEndMLS: string;
  setCurrentEndMLS: (end: string) => void;
  marketRentPreference: 'mls' | 'sfr' | 'hotpads';
  setMarketRentPreference: (preference: 'mls' | 'sfr' | 'hotpads') => void;
  currentSchoolDistrictProperties: schoolDistrictPolygon[];
  setCurrentSchoolDistrictProperties: (properties: schoolDistrictPolygon[]) => void;
  currentCountyData: countyPolygon[];
  setCurrentCountyData: (data: countyPolygon[]) => void;
  currentZipCodeData: ZIPCodePolygon[];
  setCurrentZipCodeData: (data: ZIPCodePolygon[]) => void;
  isSameSchoolDistrict: boolean;
  setIsSameSchoolDistrict: (on: boolean) => void;
  isSameCounty: boolean;
  setIsSameCounty: (on: boolean) => void;
  isSameZIPCode: boolean;
  setIsSameZIPCode: (on: boolean) => void;
  minBeds: number;
  setMinBeds: (beds: number) => void;
  maxBeds: number;
  setMaxBeds: (beds: number) => void;
  minBaths: number;
  setMinBaths: (baths: number) => void;
  maxBaths: number;
  setMaxBaths: (baths: number) => void;
  minSqft: number;
  setMinSqft: (sqft: number) => void;
  maxSqft: number;
  setMaxSqft: (sqft: number) => void;
  minLotSize: number;
  setMinLotSize: (size: number) => void;
  maxLotSize: number;
  setMaxLotSize: (size: number) => void;
  minYearBuilt: number;
  setMinYearBuilt: (year: number) => void;
  maxYearBuilt: number;
  setMaxYearBuilt: (year: number) => void;
  minCumulativeDaysOnMarket: number;
  setMinCumulativeDaysOnMarket: (days: number) => void;
  maxCumulativeDaysOnMarket: number;
  setMaxCumulativeDaysOnMarket: (days: number) => void;
  minCoveredParking: number;
  setMinCoveredParking: (parking: number) => void;
  maxCoveredParking: number;
  setMaxCoveredParking: (parking: number) => void;
  minRent: number;
  setMinRent: (price: number) => void;
  maxRent: number;
  setMaxRent: (price: number) => void;
  minSales: number;
  setMinSales: (price: number) => void;
  maxSales: number;
  setMaxSales: (price: number) => void;
  isPoolAllowed: boolean;
  setIsPoolAllowed: (allowed: boolean) => void;
  // Additional state variables for Table.tsx
  selectedCompTables: string[];
  setSelectedCompTables: (tables: string[]) => void;
  realtorSingleFamilyTableSorter: Record<string, unknown>;
  setRealtorSingleFamilyTableSorter: (sorter: Record<string, unknown>) => void;
  secondaryPortalListingsColumnSettings: Record<string, unknown>[] | null;
  setSecondaryPortalListingsColumnSettings: (settings: Record<string, unknown>[] | null) => void;
}

const CompsContext = createContext<CompsContextType | undefined>(undefined);

export const CompsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentStatusMLS, setCurrentStatusMLS] = useState<string>('Closed');
  const [currentStartMLS, setCurrentStartMLS] = useState<string>(dayjs().subtract(365, 'day').format(dateFormat));
  const [currentEndMLS, setCurrentEndMLS] = useState<string>(dayjs().format(dateFormat));
  const [marketRentPreference, setMarketRentPreference] = useState<'mls' | 'sfr' | 'hotpads'>('mls');
  const [currentSchoolDistrictProperties, setCurrentSchoolDistrictProperties] = useState<schoolDistrictPolygon[]>([]);
  const [currentCountyData, setCurrentCountyData] = useState<countyPolygon[]>([]);
  const [currentZipCodeData, setCurrentZipCodeData] = useState<ZIPCodePolygon[]>([]);
  const [isSameSchoolDistrict, setIsSameSchoolDistrict] = useState<boolean>(false);
  const [isSameCounty, setIsSameCounty] = useState<boolean>(false);
  const [isSameZIPCode, setIsSameZIPCode] = useState<boolean>(false);
  const [minBeds, setMinBeds] = useState<number>(filtersDefault.minBeds);
  const [maxBeds, setMaxBeds] = useState<number>(filtersDefault.maxBeds);
  const [minBaths, setMinBaths] = useState<number>(filtersDefault.minBaths);
  const [maxBaths, setMaxBaths] = useState<number>(filtersDefault.maxBaths);
  const [minSqft, setMinSqft] = useState<number>(filtersDefault.minSqft);
  const [maxSqft, setMaxSqft] = useState<number>(filtersDefault.maxSqft);
  const [minLotSize, setMinLotSize] = useState<number>(filtersDefault.minLotSize);
  const [maxLotSize, setMaxLotSize] = useState<number>(filtersDefault.maxLotSize);
  const [minYearBuilt, setMinYearBuilt] = useState<number>(filtersDefault.minYearBuilt);
  const [maxYearBuilt, setMaxYearBuilt] = useState<number>(filtersDefault.maxYearBuilt);
  const [minCumulativeDaysOnMarket, setMinCumulativeDaysOnMarket] = useState<number>(filtersDefault.minCumulativeDaysOnMarket);
  const [maxCumulativeDaysOnMarket, setMaxCumulativeDaysOnMarket] = useState<number>(filtersDefault.maxCumulativeDaysOnMarket);
  const [minCoveredParking, setMinCoveredParking] = useState<number>(filtersDefault.minCoveredParking);
  const [maxCoveredParking, setMaxCoveredParking] = useState<number>(filtersDefault.maxCoveredParking);
  const [minRent, setMinRent] = useState<number>(filtersDefault.minRent);
  const [maxRent, setMaxRent] = useState<number>(filtersDefault.maxRent);
  const [minSales, setMinSales] = useState<number>(filtersDefault.minSales);
  const [maxSales, setMaxSales] = useState<number>(filtersDefault.maxSales);
  const [isPoolAllowed, setIsPoolAllowed] = useState<boolean>(filtersDefault.isPoolAllowed);
  // Additional state variables for Table.tsx
  const [selectedCompTables, setSelectedCompTables] = useState<string[]>([]);
  const [realtorSingleFamilyTableSorter, setRealtorSingleFamilyTableSorter] = useState<Record<string, unknown>>({});
  const [secondaryPortalListingsColumnSettings, setSecondaryPortalListingsColumnSettings] = useState<Record<string, unknown>[] | null>(null);

  return (
    <CompsContext.Provider value={{
      currentStatusMLS,
      setCurrentStatusMLS,
      currentStartMLS,
      setCurrentStartMLS,
      currentEndMLS,
      setCurrentEndMLS,
      marketRentPreference,
      setMarketRentPreference,
      currentSchoolDistrictProperties,
      setCurrentSchoolDistrictProperties,
      currentCountyData,
      setCurrentCountyData,
      currentZipCodeData,
      setCurrentZipCodeData,
      isSameSchoolDistrict,
      setIsSameSchoolDistrict,
      isSameCounty,
      setIsSameCounty,
      isSameZIPCode,
      setIsSameZIPCode,
      minBeds,
      setMinBeds,
      maxBeds,
      setMaxBeds,
      minBaths,
      setMinBaths,
      maxBaths,
      setMaxBaths,
      minSqft,
      setMinSqft,
      maxSqft,
      setMaxSqft,
      minLotSize,
      setMinLotSize,
      maxLotSize,
      setMaxLotSize,
      minYearBuilt,
      setMinYearBuilt,
      maxYearBuilt,
      setMaxYearBuilt,
      minCumulativeDaysOnMarket,
      setMinCumulativeDaysOnMarket,
      maxCumulativeDaysOnMarket,
      setMaxCumulativeDaysOnMarket,
      minCoveredParking,
      setMinCoveredParking,
      maxCoveredParking,
      setMaxCoveredParking,
      minRent,
      setMinRent,
      maxRent,
      setMaxRent,
      minSales,
      setMinSales,
      maxSales,
      setMaxSales,
      isPoolAllowed,
      setIsPoolAllowed,
      selectedCompTables,
      setSelectedCompTables,
      realtorSingleFamilyTableSorter,
      setRealtorSingleFamilyTableSorter,
      secondaryPortalListingsColumnSettings,
      setSecondaryPortalListingsColumnSettings,
    }}>
      {children}
    </CompsContext.Provider>
  );
};

export const useCompsContext = () => {
  const context = useContext(CompsContext);
  if (!context) {
    throw new Error('useCompsContext must be used within a CompsProvider');
  }
  return context;
}