import React from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { PersistQueryClientProvider } from '@tanstack/react-query-persist-client';
import { createRouter, RouterProvider } from '@tanstack/react-router';

import { icons } from '@/components/ui/icons';
import { Toaster } from '@/components/ui/sonner';

import { persister, queryClient } from '@/lib/config/tanstack-query';

import { AuthenticationProvider, useAuthentication } from '@/lib/auth';

import { TooltipProvider } from './components/ui/tooltip';
// Import the generated route tree
import { routeTree } from './routeTree.gen';
import { PanelStateProvider } from './contexts/PanelStateContext';
import { Authenticator } from '@aws-amplify/ui-react';

if (import.meta.env.MODE !== 'development') {
  console.error = function () { };
}

// Create a new router instance
const router = createRouter({
  routeTree,
  defaultPreload: false,
  scrollRestoration: true,
  context: {
    queryClient,
    auth: undefined!,
  },
  basepath: '/',
});

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}

// once app becomes stable and pushes become less frequent, we can use VITE_APP_VERSION
// which uses git commit hash to bust cache
const cacheBuster = import.meta.env.VITE_APP_VERSION;

// bust cache every month or on version change,
// - ie. just bump the version in package.json to bust cache if needed
// const date = new Date();
// const formattedDate = format(date, 'MMMM yyyy');
// const cacheBuster = `${packageJson.version}-${formattedDate}`;
const QueryProvider = (props: { children: React.ReactNode }) => {
  // if (import.meta.env.MODE === 'development') {
  return <QueryClientProvider client={queryClient}>{props.children}</QueryClientProvider>;
  // }
  return (
    <PersistQueryClientProvider
      client={queryClient}
      persistOptions={{
        persister,
        buster: cacheBuster,
        maxAge: 1000 * 60 * 60 * 24 * 24, // 24 days
      }}
    >
      {props.children}
    </PersistQueryClientProvider>
  );
};

export default function App() {
  return (
    <React.StrictMode>
      <Authenticator.Provider >
      {/* <AuthenticationProvider> */}
        <QueryProvider>
          <PanelStateProvider>
            <React.Suspense fallback={<FullPageLoadingSpinner />}>
              <AppRouter />
            </React.Suspense>
            <Toaster toastOptions={{}} richColors={true} position="top-center" />
          </PanelStateProvider>
        </QueryProvider>
      {/* </AuthenticationProvider> */}
      </Authenticator.Provider>
    </React.StrictMode>
  );
}

const AppRouter = () => {
  const auth = useAuthentication();
  return (
    <React.Fragment>
      <TooltipProvider>
        {auth.isPending ? (
          <FullPageLoadingSpinner />
        ) : (
          <RouterProvider router={router} context={{ auth: auth }} />
        )}
      </TooltipProvider>
    </React.Fragment>
  );
};

function FullPageLoadingSpinner() {
  return (
    <div className="grid min-h-dvh place-items-center bg-background">
      <icons.Loading className="h-24 w-24 animate-spin text-foreground" />
    </div>
  );
}
