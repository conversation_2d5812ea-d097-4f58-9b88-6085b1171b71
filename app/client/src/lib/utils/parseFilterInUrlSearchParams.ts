import { FiltersType } from "@/components/PropertyFilters";

const sampleFilterInUrlSearchParams = `beds>=4;baths>=2;yieldOnBidPrice>=0.03;basePrice>=300000;basePrice<400000;(cbsaName=='Minneapolis-St. Paul-Bloomington, MN-WI Metro',cbsaName=='Cape Coral-Fort Myers, FL Metro')`

export const parseFilterInUrlSearchParams = (filter: string): FiltersType => {
  if (!filter) {
    return {};
  }
  
  const filterDecoded = decodeURIComponent(filter);
  const result: FiltersType = {};
  
  // Handle market filters: market=in=('Birmingham','Gulf Coast','Huntsville','Tuscaloosa')
  if (filterDecoded.includes('market=in=')) {
    const marketMatches = [...filterDecoded.matchAll(/'([^']+)'/g)];
    if (marketMatches.length > 0) {
      result.selectedMarkets = marketMatches.map(match => match[1]);
    }
  }
  
  // Handle cbsaName filters (legacy format): cbsaName=='Minneapolis-St. Paul'
  if (filterDecoded.includes('cbsaName')) {
    const cbsaMatches = [...filterDecoded.matchAll(/cbsaName=='(.*?)'/g)];
    if (cbsaMatches.length > 0) {
      const cbsaMarkets = cbsaMatches.map(match => match[1]);
      result.selectedMarkets = result.selectedMarkets ? 
        [...result.selectedMarkets, ...cbsaMarkets] : cbsaMarkets;
    }
  }
  
  // Handle beds filter: beds>=4
  if (filterDecoded.includes('beds>=')) {
    const bedsMatch = filterDecoded.match(/beds>=([\d]+)/);
    if (bedsMatch) {
      result.minBeds = Number(bedsMatch[1]);
    }
  }
  
  // Handle baths filter: baths>=2
  if (filterDecoded.includes('baths>=')) {
    const bathsMatch = filterDecoded.match(/baths>=([\d]+)/);
    if (bathsMatch) {
      result.minBaths = Number(bathsMatch[1]);
    }
  }
  
  // Handle cap rate filter: yieldOnBidPrice>=0.03
  if (filterDecoded.includes('yieldOnBidPrice>=')) {
    const capRateMatch = filterDecoded.match(/yieldOnBidPrice>=([\d.]+)/);
    if (capRateMatch) {
      result.minCapRate = Number(capRateMatch[1]);
    }
  }
  
  // Handle price filters: basePrice>=100000 and basePrice<=200000
  const minPriceMatch = filterDecoded.match(/basePrice>=([\d]+)/);
  if (minPriceMatch) {
    result.minPrice = Number(minPriceMatch[1]);
  }
  
  const maxPriceMatch = filterDecoded.match(/basePrice<=([\d]+)/);
  if (maxPriceMatch) {
    result.maxPrice = Number(maxPriceMatch[1]);
  }
  return result;
}

export const generateUrlParamFilter = ({
  selectedMarkets,
  minBeds,
  minBaths,
  minPrice,
  maxPrice,
  minCapRate,
}: {
  selectedMarkets?: string[];
  minBeds?: number;
  minBaths?: number;
  minPrice?: number;
  maxPrice?: number;
  minCapRate?: number;
}) => {
  const searchParamsStringArray: string[] = [];
  if (selectedMarkets && selectedMarkets.length > 0) {
    // Use the new market format: market=in=('Market1','Market2')
    const marketsString = selectedMarkets.map(market => `'${market}'`).join(',');
    searchParamsStringArray.push(`market=in=(${marketsString})`);
  }
  if (minBeds && minBeds > 0) {
    searchParamsStringArray.push(`beds>=${minBeds}`);
  }
  if (minBaths && minBaths > 0) {
    searchParamsStringArray.push(`baths>=${minBaths}`);
  }
  if (minPrice || maxPrice) {
    if (minPrice && maxPrice && minPrice !== 0 && maxPrice !== 9999999) {
      searchParamsStringArray.push(`(basePrice>=${minPrice};basePrice<=${maxPrice})`);
    } else if (minPrice && minPrice !== 0) {
      searchParamsStringArray.push(`basePrice>=${minPrice}`);
    } else if (maxPrice && maxPrice !== 9999999) {
      searchParamsStringArray.push(`basePrice<=${maxPrice}`);
    }
  }
  if (minCapRate && minCapRate > 0) {
    searchParamsStringArray.push(`yieldOnBidPrice>=${minCapRate}`);
  }

  if (searchParamsStringArray.length === 0) {
    return ``;
  } else {
    return encodeURIComponent(`${searchParamsStringArray.join(';')}`);
  }
};