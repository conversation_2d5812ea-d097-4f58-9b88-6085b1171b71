export interface BookmarkState {
  propertyId: number;
  isBookmarked: boolean;
  timestamp: number;
  proFormData?: any;
}

const BOOKMARK_STORAGE_KEY = 'property_bookmarks';
const SYNC_TIMESTAMP_KEY = 'bookmarks_last_sync';

class BookmarkManager {
  private getStoredBookmarks(): Map<number, BookmarkState> {
    try {
      const stored = localStorage.getItem(BOOKMARK_STORAGE_KEY);
      if (!stored) return new Map();
      
      const data = JSON.parse(stored);
      return new Map(Object.entries(data).map(([id, state]) => [parseInt(id), state as BookmarkState]));
    } catch (error) {
      console.warn('Failed to parse stored bookmarks:', error);
      return new Map();
    }
  }

  private saveBookmarks(bookmarks: Map<number, BookmarkState>): void {
    try {
      const data = Object.fromEntries(bookmarks);
      localStorage.setItem(BOOKMARK_STORAGE_KEY, JSON.stringify(data));
      localStorage.setItem(SYNC_TIMESTAMP_KEY, Date.now().toString());
    } catch (error) {
      console.warn('Failed to save bookmarks to localStorage:', error);
    }
  }

  // Get bookmark state for a property (returns local state if available)
  isBookmarked(propertyId: number): boolean {
    const bookmarks = this.getStoredBookmarks();
    const bookmark = bookmarks.get(propertyId);
    return bookmark?.isBookmarked ?? false;
  }

  // Optimistically toggle bookmark (immediate UI update)
  toggleBookmark(propertyId: number, proFormData?: any): boolean {
    const bookmarks = this.getStoredBookmarks();
    const currentState = bookmarks.get(propertyId);
    const newState = !currentState?.isBookmarked;
    
    bookmarks.set(propertyId, {
      propertyId,
      isBookmarked: newState,
      timestamp: Date.now(),
      proFormData: newState ? proFormData : undefined,
    });
    
    this.saveBookmarks(bookmarks);
    return newState;
  }

  // Get all bookmarked property IDs
  getBookmarkedPropertyIds(): number[] {
    const bookmarks = this.getStoredBookmarks();
    return Array.from(bookmarks.values())
      .filter(bookmark => bookmark.isBookmarked)
      .map(bookmark => bookmark.propertyId);
  }

  // Get count of bookmarked properties
  getBookmarkCount(): number {
    return this.getBookmarkedPropertyIds().length;
  }

  // Sync local state with backend data (call after API fetch)
  syncWithBackend(backendBookmarkedProperties: any[]): void {
    const backendPropertyIds = new Set(
      backendBookmarkedProperties.map(prop => prop.property_id)
    );
    
    const localBookmarks = this.getStoredBookmarks();
    const lastSync = parseInt(localStorage.getItem(SYNC_TIMESTAMP_KEY) || '0');
    const now = Date.now();
    
    // Only update local state if it's not a recent change (give 30 seconds for API calls)
    for (const [propertyId, bookmark] of localBookmarks) {
      const isRecentChange = (now - bookmark.timestamp) < 30000; // 30 seconds
      const isBookmarkedInBackend = backendPropertyIds.has(propertyId);
      
      // Don't override recent local changes - they might be pending sync
      if (!isRecentChange && bookmark.isBookmarked !== isBookmarkedInBackend) {
        bookmark.isBookmarked = isBookmarkedInBackend;
        bookmark.timestamp = now;
      }
    }
    
    // Add any backend bookmarks not in local storage
    for (const property of backendBookmarkedProperties) {
      if (!localBookmarks.has(property.property_id)) {
        localBookmarks.set(property.property_id, {
          propertyId: property.property_id,
          isBookmarked: true,
          timestamp: now,
          proFormData: property.payload?.proforma,
        });
      }
    }
    
    this.saveBookmarks(localBookmarks);
  }

  // Get pending changes that need to be synced to backend
  getPendingChanges(): BookmarkState[] {
    const lastSync = parseInt(localStorage.getItem(SYNC_TIMESTAMP_KEY) || '0');
    const bookmarks = this.getStoredBookmarks();
    
    return Array.from(bookmarks.values()).filter(
      bookmark => bookmark.timestamp > lastSync
    );
  }

  // Clear all bookmark data (useful for logout)
  clear(): void {
    localStorage.removeItem(BOOKMARK_STORAGE_KEY);
    localStorage.removeItem(SYNC_TIMESTAMP_KEY);
  }

  // Debug: Force a property to be bookmarked (for testing)
  forceBookmark(propertyId: number, proFormData?: any): void {
    const bookmarks = this.getStoredBookmarks();
    bookmarks.set(propertyId, {
      propertyId,
      isBookmarked: true,
      timestamp: Date.now(),
      proFormData,
    });
    this.saveBookmarks(bookmarks);
  }
}

export const bookmarkManager = new BookmarkManager(); 