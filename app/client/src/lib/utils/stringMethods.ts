export const convertPriceToNumber = (price: string): number => {
  if (!price) {
    return 0;
  }
  // remove decimal point if there's no number after the decimal point
  if (price.includes('.') && price.indexOf('.') === price.length - 1) {
    return +price.replace(/,/g, '').replace('$', '').replace('.', '');
  } else {
    return +price.replace(/,/g, '').replace('$', '');
  }
};

export const convertPercentageToNumber = (percentage: string): number => {
  if (!percentage) {
    return 0;
  }
  const percentageNumber = +percentage.replace(/%/g, '');
  if (typeof percentageNumber === 'number') {
    return percentageNumber / 100;
  } else {
    return 0;
  }
};

export const convertPercentageToNumberPropertyTaxRateOnly = (percentage: string): number => {
  if (!percentage) {
    return 0;
  }
  const percentageNumber = +percentage.replace(/%/g, '');
  if (typeof percentageNumber === 'number') {
    // if the number > 0.1, it means it's a percentage, so we need to divide by 100
    if (percentageNumber > 0.1) {
      return percentageNumber / 100;
    } else {
      return percentageNumber;
    }
  } else {
    return 0;
  }
};