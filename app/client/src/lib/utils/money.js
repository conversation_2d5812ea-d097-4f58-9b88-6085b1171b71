export const handleNumberError = (num) => {
  if (num === null || num === undefined) return '-';
  if (typeof num === 'number') return num;

  const parseNum = num.replace(/[^\d.-]/g, '');
  if (
    !parseNum ||
    !Number.isFinite(+parseNum) ||
    parseNum === '' ||
    parseNum === null
  ) {
    return '-';
  }
  return num;
};

export const formatter = (num, decimal) => {
  if (['N/A', 'n/a', 'N/a'].includes(num)) {
    // leave N/A alone
    return num;
  } else if (
    typeof Number(num) === 'number' &&
    typeof num !== 'undefined' &&
    num !== null
  ) {
    // handle both number and string
    // if there is a decimal, use it; this is for price amounts in offers
    const decimalNum = decimal ? decimal : 0;
    return Number(num)
      .toFixed(decimalNum)
      .replace(/\d{1,3}(?=(\d{3})+(\.|$))/g, '$&,');
  } else {
    return '';
  }
};

export const formatterKM = (num) => {
  const si = [
    { value: 1, symbol: '' },
    { value: 1e3, symbol: 'k' },
    { value: 1e6, symbol: 'M' },
  ];
  const rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
  let i;
  for (i = si.length - 1; i > 0; i--) {
    if (num >= si[i].value) {
      break;
    }
  }
  let digits;
  if (num > 999999) {
    digits = 2;
  } else {
    digits = 0;
  }
  return (num / si[i].value).toFixed(digits).replace(rx, '$1') + si[i].symbol;
};

export const combineFormatter = (num) => {
  if (num > 99999) {
    return formatterKM(num);
  } else {
    return formatter(num);
  }
};

const DaysBetween = function (date1, date2) {
  var oneDay = 24 * 60 * 60 * 1000;
  return Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneDay));
};

// Change Date and Flow to date and value fields you use
const XNPV = function (rate, values) {
  var xnpv = 0.0;
  var firstDate = new Date(values[0].Date);
  for (var key in values) {
    var tmp = values[key];
    var value = tmp.Flow;
    var date = new Date(tmp.Date);
    xnpv += value / Math.pow(1 + rate, DaysBetween(firstDate, date) / 365);
  }
  return xnpv;
};

export const XIRR = function (values, guess) {
  // console.log('xirr param array', values);
  if (!guess) guess = 0.1;

  var x1 = 0.0;
  var x2 = guess;
  var f1 = XNPV(x1, values);
  var f2 = XNPV(x2, values);

  for (var i = 0; i < 100; i++) {
    if (f1 * f2 < 0.0) break;
    if (Math.abs(f1) < Math.abs(f2)) {
      f1 = XNPV((x1 += 1.6 * (x1 - x2)), values);
    } else {
      f2 = XNPV((x2 += 1.6 * (x2 - x1)), values);
    }
  }

  // debugger;

  if (f1 * f2 > 0.0) return null;

  var f = XNPV(x1, values);
  if (f < 0.0) {
    var rtb = x1;
    var dx = x2 - x1;
  } else {
    var rtb = x2;
    var dx = x1 - x2;
  }

  for (var i = 0; i < 100; i++) {
    dx *= 0.5;
    var x_mid = rtb + dx;
    var f_mid = XNPV(x_mid, values);
    if (f_mid <= 0.0) rtb = x_mid;
    if (Math.abs(f_mid) < 1.0e-6 || Math.abs(dx) < 1.0e-6) return x_mid;
  }

  return null;
};

export const PMT = function (rate, nper, pv, fv, type) {
  if (!fv) fv = 0;
  if (!type) type = 0;

  if (rate == 0) return -(pv + fv) / nper;

  var pvif = Math.pow(1 + rate, nper);
  var pmt = (rate / (pvif - 1)) * -(pv * pvif + fv);

  if (type == 1) {
    pmt /= 1 + rate;
  }

  return pmt;
};
