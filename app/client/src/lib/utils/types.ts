export interface Fund {
  id: number;
  fundName: string;
}

export interface MLSListingDataType {
  id: number;
  offerStatus: string | null;
  retsId: string | null;
  statusChangeTimestamp: string;
  associationType: string | null;
  backOnMarketDate: string | null;
  bathsFull: number;
  bathsHalf: number | null;
  bedsTotal: number;
  block: string | null;
  countyOrParish: string;
  lastChangeTimestamp: string | null;
  lastChangeType: string | null;
  lastListPrice: number | null;
  lastStatus: string | null;
  listPrice: number;
  listingContractDate: string | null;
  mlsNumber: string;
  matrixModifieddt: string | null;
  numberOfStories: number | null;
  offMarketDate: string | null;
  originalListPrice: number | null;
  parkingSpacesGarage: number | null;
  poolYN: string | null;
  propertySubType: string;
  propertyType: string;
  sqft: number;
  status: string;
  yearBuilt: number;
  floodZone: string | null;
  improvementRatio: number | null;
  crimeScore: number | null;
  city: string;
  state: string;
  postalCode: string;
  fullStreetAddress: string;
  latitude: string;
  longitude: string;
  placekey: string;
  listingKey: string;
  listAgentMui: string | null;
  listOfficeMui: string | null;
  cityCode: string;
  dispositions: string | null;
  offerSent: string | null;
  avmRatio: number | null;
  askingRatio: number | null;
  impRatio: number | null;
  firstname: string | null;
  lastname: string | null;
  elementary: number | null;
  middle: number | null;
  high: number | null;
  totalSchool: number | null;
  firstEntryTimestamp: string;
  waterSource: string;
  buyBoxName: string | null;
  payload: string | null;
  decision: string | null;
  rehabCost: number | null;
  yield: number | null;
  marketRent: number | null;
  marketSales: number | null;
  timestamp: string | null;
  underwritingId: string | null;
}

export interface ParcelDataType {
  fips: string;
  apn: string;
  placekey: string;
  year_built: number;
  stories: number;
  beds_count: number;
  baths: number;
  partial_baths_count: number;
  total_area_sq_ft: number;
  rent: number;
  sales: number;
  standardized_land_use_type: string;
  value: number | null;
  low: number | null;
  high: number | null;
  low_ratio: number | null;
  high_ratio: number | null;
  score_crime: number | null;
  elementary: number | null;
  middle: number | null;
  highschool: number | null;
  income: number | null;
  fiveyeargrowth: number | null;
  incomegrowth: number | null;
  fld_zone: string;
  legal_description: string;
  subdivision: string;
  hoa_fees: number;
  owner_occupied_sl: string | null;
  institution: string | null;
  marker: string;
  pbaplus: string | null;
  tax_rate_percent: number;
  tax_amount: number;
  total_value: number;
  deed_last_sale_date: string;
  deed_last_sale_price: number | null;
  rate: number;
  area_acres: number;
  deed_last_sale_data: string;
}

export interface DemographicsDataType {
  raw_fld_zone: string;
  fld_zone: string;
  medianhhincome: string;
  fiveyearpopgrowth: number;
  fiveyearincomegrowth: number;
  bachelorsandabove: number;
  crime_score: number;
  crime: number;
  white: number;
  black: number;
  asian: number;
  hispanic: number;
  school: {
    elem: {
      score_2022: number;
      score_2023: number;
      name: string;
      url: string | null;
    };
    high: {
      score_2022: number;
      score_2023: number;
      name: string;
      url: string | null;
    };
    middle: {
      score_2022: number;
      score_2023: number;
      name: string;
      url: string | null;
    };
  };
}

export type underwritingSource = "onmarket" | "offmarket";

export interface IntelligentCompingResponseDataType {
  data: {
    results: {
      subject_property: {
        state: string;
        remark: boolean;
        sales: number;
        rent: number;
        longitude: number;
        latitude: number;
        total_area_sq_ft: number;
        year_built: number;
        beds_count: number;
        schooldistrict: string | null;
        baths: number;
        placekey: string;
        zip_code: string;
        county: string;
        city: string;
        extra_data: {};
        owner_occupied: string;
        subdivision: string;
        formatted_street_address: string;
        stories: string;
        lot_size: number;
      };
      rent_comps: [];
      sales_comps: [];
      adjusted_rent: {
        source: string;
        criteria: {};
        calculation_method: string;
        adjusted_value: number;
      };
      adjusted_sales: {
        source: string;
        criteria: {};
        calculation_method: string;
        adjusted_value: number;
      };
    };
  };
  message: string;
  proccessed_for_date: string;
}

export type searchingModeType = 'Lease' | 'Sale' | 'Land';

export interface ProFormaValuesLennar {
  Address: string,
  City: string,
  State: string,
  'ZIP Code': string,
  County: string,
  'Property Type': string, // item.standardized_land_use_type,
  'Year Built': number,
  Sqft: number,
  Beds: number,
  Baths: number,
  Pool: string,
  'Flood Zone': string, // item.fld_zone,

  "Asking Price": number,
  "Market Value": number,
  "Bid Price": number,
  "Bid to Ask Ratio": number,
  "Total Acquisition Cost": number,
  "Projected Monthly Rent": number,
  "Other Monthly Income": number,
  "Total Annual Rental Income": number,
  "Net Annual Rental Income": number,
  "Property Tax": number,
  "Property Tax Coefficient": number,
  "Insurance": number,
  "Annual HOA Fees": number,
  "Repair & Maintenance": number,
  "Property Management Fees Coefficient": number,
  "Property Management Fees": number,
  "Vacancy Loss Coefficient": number,
  "Vacancy Loss": number,
  "Annual Community Development Fee": number,
  "Total Annual Expenses": number,
  "Loan to Value": number,
  "Mortgage Amount": number,
  "Mortgage Rate": number,
  "Mortgage Term": number,
  "Monthly Mortgage Payment": number,
  "Closing Costs": number,
  "Total Initial Investment": number,
  "Projected NOI": number,
  "Projected Yield on Bid Price": number,
  "Unlevered Cash Flow Yearly": number,
  "Unlevered Cash Flow Monthly": number,
  "Levered Cash Flow Yearly": number,
  "Levered Cash Flow Monthly": number,
  "Cash-on-Cash Return": number,

  "Annualized Return": number | null,
  "Total Return": number,
  "HPA 5Yr": number,
  "Years Holding": number,

  'Crime Score': number,
  Elementary: number,
  'Middle School': number,
  'High School': number,
  'Median HH Income': number,
  '5YR Pop. Growth': number,
  'Income Growth': number,
  "Bachelor's and Above": number,
}

interface PropertyMeta {
  active_adult: string;
  aerobics_studio: string;
  baseball_field: string;
  basement: string;
  basketball_court: string;
  bonus_room: string;
  club_house: string;
  community: string;
  "community#": string;
  community_type: string;
  communitydevelopmentfee: string;
  county: string | null; 
  division_name: string;
  elevation: string;
  estimated_coe: string;
  estimated_complete: string;
  garage: string;
  gate_community: string;
  golf_course: string;
  hoafeeannual: string;
  "homesite#": string;
  homesite_lat: string | null;  
  homesite_long: string | null; 
  lake: string;
  "lot#": string;
  master_comm: string;
  net_sales_price: string;
  park: string;
  picnic_area: string;
  placekey: string;
  plan: string;
  plan_name: string;
  playground: string;
  pool: string;
  school_district: string | null; 
  sl_status: string;
  soccer_field: string;
  solar: string;
  stories: string;
  taxrate: string;
  tennis: string;
  volleyball_court: string;
  walking_trails: string;
  waterfront: string;
  lot_size_acres?: number; 
}

export interface PropertyDetails {
  address: string;
  apn: string | null; 
  baths: number;
  beds: number;
  cbsaName: string;
  city: string;
  demographicData: any | null; 
  dispositions: any[] | null; 
  elem: number;
  fips: string | null; 
  high: number;
  lat: number;
  leaseComps: any[] | null; 
  lng: number;
  matchStatus: string;
  medianHhIncome: number;
  meta: PropertyMeta;
  middle: number;
  minDistance: number | null;  
  notes: string | null;  
  offerSent: string | null; 
  offerStatus: string | null; 
  onMls: boolean | null;  
  orderIndex: number;
  parcelData: any | null;  
  placekey: string;
  portfolioId: number;
  portfolioName: string;
  propertyId: number;
  propertyRawId: number;
  propertySource: string;
  rent: number;
  saleComps: any[] | null;  
  sales: number;
  savedProforma: any | null;  
  sqft: number;  
  stage: string;
  state: string;
  userId: string;
  yearbuilt: number;
  zipCode: string;
  lotSize?: number; 
}

export interface LennarSubjectPropertyDataType {
  apn: string | null;
  lat: number;
  lng: number;
  beds: number;
  city: string;
  elem: number;
  fips: string | null;
  high: number;
  meta: PropertyMeta;
  rent: number;
  sqft: number;
  baths: number;
  notes: string | null;
  onMls: boolean | null;
  sales: number;
  stage: string;
  state: string;
  middle: number;
  userId: string;
  address: string;
  zipCode: string;
  cbsaName: string;
  placekey: string;
  offerSent: string | null;
  saleComps: any[] | null;
  yearbuilt: number;
  leaseComps: any[] | null;
  orderIndex: number;
  parcelData: any | null;
  propertyId: number;
  matchStatus: string;
  minDistance: number | null;
  offerStatus: string | null;
  portfolioId: number;
  dispositions: any | null;
  portfolioName: string;
  propertyRawId: number;
  savedProforma: any | null;
  medianHhIncome: number;
  propertySource: string;
  demographicData: any | null;
}

export interface LennarSinglePropertyDataTypeAPIVersion2 {
  property_id: number;
  rets_id: string;
  buybox_id: number;
  city: string;
  full_address: string;
  postal_code: string;
  state: string;
  citycode: string;
  placekey: string;
  underwriting_id: number;
  step: number;
  source: underwritingSource;
  decisions: {
    id: number;
    decision: string;
    notes: string;
    payload: {
      proforma: {
        buyAndHold: ProFormaValuesLennar;
      }
      subjectProperty: LennarSubjectPropertyDataType;
    }
    timestamp: string;
    valid_for_min: number;
  }[];
  register_token: string;
  mls_status: string;
}

export interface LennarSinglePropertyDataType {
  buybox_id: number;
  city: string;
  citycode: string;
  decisions: {
    id: number;
    decision: string;
    notes: string;
    payload: {
      proforma: {
        buyAndHold: ProFormaValuesLennar;
      }
      subjectProperty: LennarSubjectPropertyDataType;
    }
  }[];
  full_address: string;
  notes: string;
  payload: {
    proforma: {
      buyAndHold: ProFormaValuesLennar;
    }
    subjectProperty: LennarSubjectPropertyDataType;
  }
  placekey: string;
  postal_code: string;
  property_id: number;
  register_token: string;
  rets_id: string;
  source: underwritingSource;
  state: string;
  step: number;
  timestamp: string;
  underwriting_decision_id: number;
  underwriting_id: number;
  valid_for_min: number;
}