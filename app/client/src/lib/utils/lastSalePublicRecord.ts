import { TAffordableHousing } from './../components/ResultTable/AffordableHousing/AHPanel';
export interface DataType {
  key: React.Key;
  address: string;
  distance: number;
  state: string;
  last_sale_date: Date | string;
  last_sale_price: number;
  year_built: number;
  bedroom: number;
  bathroom: number;
  propertysubtype: string;
  sqft: number;
  geom: {
    type: string;
    coordinates: number[];
  };
}
type PropertyData = {
  year_built: number;
  beds_count: number;
  baths: number;
  total_area_sq_ft: number;
};

type PropertyList = Array<{
  address: string;
  distance: number;
  last_sale_date: string;
  city: string;
  geom: {
    type: string;
    coordinates: number[];
  };
  zip_code: string;
  baths: number;
  last_sale_price: number;
  sqft: number;
  year_built: number;
  state: string;
  beds: number;
  propertysubtype: string;
}>;

interface GenerateSmartFilterExpressionParams {
  subjectPropertyData: PropertyData;
  dataSource: PropertyList;
}

export function convert(list: any[], dateRange: number | null): any[] {
  const dateDiff = new Date();
  if (dateRange) dateDiff.setDate(dateDiff.getDate() - dateRange);
  console.log('test list ', list);
  return list
    .filter((item: any) => {
      const saleDate = new Date(item.last_sale_date);
      return saleDate > dateDiff;
    })
    .map((item: any, idx: any) => ({
      // key: idx + 1,
      key: item.address + item.zip_code + item.last_sale_date,
      geom: item.geom,
      state: item.state,
      address: `${toTitleCase(item.address)}, ${toTitleCase(item.city)}, ${
        item.state
      }, ${item.zip_code}`,
      distance: item.distance,
      last_sale_date: item.last_sale_date,
      last_sale_price: item.last_sale_price,
      year_built: item.year_built ?? '',
      bedroom: item.beds,
      bathroom: item.baths,
      propertysubtype: item.propertysubtype,
      sqft: item.sqft,
    }));
}
export function toTitleCase(str: string): string {
  if (str) {
    if (str === 'SINGLE FAMILY RESIDENCE') return 'SFR';
    if (str.startsWith('DUPLEX')) return 'Duplex';
    return str
      .toLowerCase()
      .split(/\s+/)
      .map((word) => word.replace(/^\w/, (c) => c.toUpperCase()))
      .join(' ');
  } else return 'N/A';
}
export function formatCurrency(number: any) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(number);
}

export function simplifyPropertySubtypes(properties: any) {
  const simplified: any = [];

  const seen: { [key: string]: boolean } = {};

  properties.forEach((property: any) => {
    let originalSubtype = property.propertysubtype;
    if (originalSubtype) {
      originalSubtype = originalSubtype.replace(/\s*\([^)]*\)/, '').trim();

      const simplifiedSubtype = originalSubtype.split(' ')[0];

      if (!seen[simplifiedSubtype]) {
        seen[simplifiedSubtype] = true;

        simplified.push({
          text: toTitleCase(originalSubtype),
          value: originalSubtype,
        });
      }
    }
  });

  return simplified;
}

export function getAllSubtypes(properties: any) {
  const allSubtypes: string[] = [];
  const seen: { [key: string]: boolean } = {};

  properties.forEach((property: any) => {
    let originalSubtype = property.propertysubtype;
    if (originalSubtype) {
      originalSubtype = originalSubtype.replace(/\s*\([^)]*\)/, '').trim();

      const simplifiedSubtype = originalSubtype.split(' ')[0];

      if (!seen[simplifiedSubtype]) {
        seen[simplifiedSubtype] = true;

        allSubtypes.push(originalSubtype);
      }
    }
  });
  return allSubtypes;
}

export function calculateMedianLastSalePrice(
  selectedKeys: any[],
  properties: DataType[] | TAffordableHousing[],
  type: string = 'default',
) {
  const getSelectedPrices = (property: any) => {
    return type === 'ah' ? property.rent : property.last_sale_price;
  };

  const selectedPrices = properties
    .filter(
      (property) =>
        selectedKeys.includes(property.key) &&
        (type === 'ah' ? 'rent' in property : 'last_sale_price' in property),
    )
    .map(getSelectedPrices);

  selectedPrices.sort((a, b) => a - b);
  const n = selectedPrices.length;
  let medianPrice;

  if (n % 2 !== 0) {
    medianPrice = selectedPrices[Math.floor(n / 2)];
  } else {
    medianPrice = (selectedPrices[n / 2 - 1] + selectedPrices[n / 2]) / 2;
  }

  return medianPrice;
}

export function calculateMedianPricePerSqFt(
  selectedKeys: any[],
  properties: DataType[] | TAffordableHousing[],
  type: string = 'default',
): number | null {
  const getFilteredProperties = (property: any) =>
    selectedKeys.includes(property.key) &&
    property.sqft > 0 &&
    (type === 'ah' ? property.rent > 0 : property.last_sale_price > 0);

  const getPricesPerSqFt = (property: any) =>
    parseFloat(
      type === 'ah'
        ? property.rent / property.sqft
        : property.last_sale_price / property.sqft,
    );

  if (selectedKeys.length === 0) return null;
  const filteredProperties = properties.filter(getFilteredProperties);
  const pricesPerSqFt = filteredProperties.map(getPricesPerSqFt);

  if (pricesPerSqFt.length === 0) return null;

  pricesPerSqFt.sort((a, b) => a - b);

  const n = pricesPerSqFt.length;

  return n % 2 !== 0
    ? pricesPerSqFt[Math.floor(n / 2)]
    : (pricesPerSqFt[n / 2 - 1] + pricesPerSqFt[n / 2]) / 2;
}

export function convertToGeoJSONFormat(listOfObjects: any, listOfKeys: any) {
  console.log('test listOfObjects', listOfObjects);
  const filteredObjects = listOfObjects.filter((item: any) =>
    listOfKeys.includes(item.key),
  );
  return filteredObjects.map((item: any) => {
    const geom = item.geom;
    delete item.geom;
    return {
      type: 'Feature',
      geometry: geom,
      properties: { ...item },
    };
  });
}

export const generateSmartFilterExpression = ({
  subjectPropertyData,
  dataSource,
}: GenerateSmartFilterExpressionParams): PropertyList => {
  const { total_area_sq_ft, year_built, beds_count, baths } =
    subjectPropertyData;

  // Assuming propertiesToFilter is the list of properties you want to filter
  return dataSource.filter((property) => {
    const sizeWithinRange =
      total_area_sq_ft === null
        ? true
        : property.sqft >= Math.round((total_area_sq_ft * 0.75) / 10) * 10 &&
          property.sqft <= Math.round((total_area_sq_ft * 1.25) / 10) * 10;
    const yearBuiltWithinRange =
      property.year_built >= (year_built ? year_built - 30 : 1900) &&
      property.year_built <= (year_built ? year_built + 30 : 2022);
    const bathroomsWithinRange =
      property.baths >= (baths ? baths - 1 : 1) &&
      property.baths <= (baths ? baths + 1 : 99);
    const bedroomsWithinRange =
      property.beds >= (beds_count ? beds_count - 1 : 1) &&
      property.beds <= (beds_count ? beds_count + 1 : 99);

    return (
      sizeWithinRange &&
      yearBuiltWithinRange &&
      bathroomsWithinRange &&
      bedroomsWithinRange
    );
  });
};
