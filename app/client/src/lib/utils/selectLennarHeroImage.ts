import { singleImageLennarResponseType } from "@/lib/query/get-images-lennar";

export const selectLennarHeroImage = (images: singleImageLennarResponseType[]): string => {
  if (!images || images.length === 0) {
    return '';
  }

  switch (true) {
    case images.some(image => image.imageName.includes('Living_1')):
      return images.find(image => image.imageName.includes('Living_1'))?.url || '';
    case images.some(image => image.imageName.includes('Kitchen_1')):
      return images.find(image => image.imageName.includes('Kitchen_1'))?.url || '';
    case images.some(image => image.imageName.includes('Dining_1')):
      return images.find(image => image.imageName.includes('Dining_1'))?.url || '';
    case images.some(image => image.imageName.includes('OwnersSuite_1')):
      return images.find(image => image.imageName.includes('OwnersSuite_1'))?.url || '';
    default:
      return images[0]?.url;
  }
};