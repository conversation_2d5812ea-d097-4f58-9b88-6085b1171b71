import { LennarSinglePropertyDataType } from '../query/get-properties';

interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

/**
 * Filters properties to only include those within the specified map bounds
 * @param properties - Array of properties to filter
 * @param bounds - Map bounds with north, south, east, west coordinates
 * @returns Filtered array of properties within the bounds
 */
export const filterPropertiesByBounds = (
  properties: LennarSinglePropertyDataType[] | null,
  bounds: MapBounds | null
): LennarSinglePropertyDataType[] => {
  if (!properties || !bounds) {
    return properties || [];
  }

  return properties.filter(property => {
    const lat = property?.payload?.subjectProperty?.lat;
    const lng = property?.payload?.subjectProperty?.lng;

    // Skip properties without coordinates
    if (typeof lat !== 'number' || typeof lng !== 'number') {
      return false;
    }

    // Check if the property is within the bounds
    return (
      lat >= bounds.south &&
      lat <= bounds.north &&
      lng >= bounds.west &&
      lng <= bounds.east
    );
  });
};

/**
 * Converts Mapbox LngLatBounds to our MapBounds interface
 * @param mapboxBounds - Mapbox bounds array [[west, south], [east, north]]
 * @returns MapBounds object
 */
export const convertMapboxBoundsToMapBounds = (
  mapboxBounds: [[number, number], [number, number]]
): MapBounds => {
  const [[west, south], [east, north]] = mapboxBounds;
  return {
    north,
    south,
    east,
    west
  };
};

/**
 * Gets map bounds from a Mapbox map instance
 * @param map - Mapbox map instance
 * @returns MapBounds object or null if map is not available
 */
export const getMapBounds = (map: any): MapBounds | null => {
  if (!map || typeof map.getBounds !== 'function') {
    return null;
  }

  try {
    const bounds = map.getBounds();
    return {
      north: bounds.getNorth(),
      south: bounds.getSouth(),
      east: bounds.getEast(),
      west: bounds.getWest()
    };
  } catch (error) {
    console.error('Error getting map bounds:', error);
    return null;
  }
}; 