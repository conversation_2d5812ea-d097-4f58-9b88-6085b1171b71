import { postAreaDemographicCSV } from '../query/post-area-demographic-CSV';
import {  postMajorEmployersCSV } from '../query/post-major-employers-CSV';

interface ExportCSVOptions {
  filenamePrefix?: string;
  dateFormat?: boolean;
}

/**
 * Generic utility for exporting CSV data
 * @param fetchFunction - The API function to call to get CSV data
 * @param params - Parameters for the API call
 * @param coordinates - Lat/lng coordinates
 * @param options - Options for file naming etc.
 */
export const exportCSVFile = async <T extends object>(
  fetchFunction: (params: T) => Promise<string>,
  params: T,
  options: ExportCSVOptions = {}
): Promise<string | null> => {
  
  try {
    const response = await fetchFunction(params);
    
    if (response) {
      // Create blob from the CSV text response
      const blob = new Blob([response], {
        type: 'text/csv;charset=utf-8;',
      });

      // Create download URL
      const url = window.URL.createObjectURL(blob);

      // Create and trigger download
      const a = document.createElement('a');
      a.href = url;

      // Create filename with current date if requested
      const { filenamePrefix = "export", dateFormat = true } = options;
      let filename = filenamePrefix;
      
      if (dateFormat) {
        const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
        filename += `_${date}`;
      }
      
      a.download = `${filename}.csv`;

      // Trigger download
      document.body.appendChild(a);
      a.click();

      // Cleanup
      window.URL.revokeObjectURL(url);
      a.remove();
      
      return response;
    }
    return null;
  } catch (error) {
    console.error("CSV export failed:", error);
    throw error;
  }
};

export interface DistanceDriveModeParams {
    distance: number;
    mode: 'distance' | 'drive';
    time?: number;
}

export interface CSVRequestParams {
  body : {
    distance: number;
    mode: 'distance' | 'drive';
    time?: number;
    lat: number;
    lng: number;
  };
}


export const downloadDemographicsCSV = async (
  params: CSVRequestParams,
): Promise<string | null> => {
  try {
    return exportCSVFile(
    postAreaDemographicCSV,
    params,
    { filenamePrefix: "area_demographics" }
    );
  } catch (error) {
    console.error("Error validating coordinates:", error);
    throw error;
  }
  
};


export const downloadMajorEmployersToCSV = async (
  params: CSVRequestParams,
): Promise<string | null> => {
  try {
    return exportCSVFile(
      postMajorEmployersCSV,
      params,
      { filenamePrefix: "major_employers" }
    );
  } catch (error) {
    console.error("Error validating coordinates:", error);
    throw error;
  }
};

export const prepareCSVExportParams = (
  params: DistanceDriveModeParams,
  coordinates: { lat: number | null; lng: number | null }
): CSVRequestParams | Error => {
  // Validate coordinates
  if (coordinates.lat === null || coordinates.lng === null) {
    return new Error("Missing coordinates");
  }
  
  return {
    body: {
      ...params,
      lat: coordinates.lat,
      lng: coordinates.lng
    }
  };
};

export const handleCSVExport = async (
  params: DistanceDriveModeParams,
  coordinates: { lat: number | null; lng: number | null },
  downloadFunction: (params: CSVRequestParams) => Promise<string | null>,
  setLoadingState: React.Dispatch<React.SetStateAction<boolean>>
): Promise<void> => {
  setLoadingState(true);
  try {
    const result = prepareCSVExportParams(params, coordinates);
    
    if (result instanceof Error) {
      throw result;
    }
    
    await downloadFunction(result);
  } catch (error) {
    console.error("Error exporting CSV:", error);
  } finally {
    setLoadingState(false);
  }
};