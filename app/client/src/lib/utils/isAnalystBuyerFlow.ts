export const isAnalystBuyerFlow = (selectedUserGroup: string): boolean => {
  return (
    ['VentureREI', 'VentureREIDemo', 'Truehold', 'ShopRE'].includes(
      selectedUserGroup,
    ) || selectedUserGroup.includes('PortalsDemo')
  );
};

export const isVentureREI = (selectedUserGroup: string): boolean => {
  return (
    ['VentureREI', 'VentureREIDemo'].includes(selectedUserGroup) ||
    selectedUserGroup.includes('PortalsDemo')
  );
};

export const getCurrentClient = (
  selectedUserGroup: string,
  currentUserEmail: string,
  currentUrl: string,
): string => {
  switch (true) {
    case ['Truehold'].includes(selectedUserGroup):
      return 'Truehold';
    case currentUserEmail?.toLowerCase().includes('lennar'):
      return 'Lennar';
    case currentUserEmail?.toLowerCase().includes('remaxfinelocalinvestor'):
    case currentUrl?.toLowerCase().includes('remax'):
      return 'RE/MAX Fine Properties';
    case currentUserEmail?.toLowerCase().includes('mphlocalinvestor'):
    case currentUrl?.toLowerCase().includes('mph'):
    // case currentUrl?.toLowerCase().includes('localhost'): // TEMP
    case currentUserEmail === '<EMAIL>':
      return 'Marketplace Homes';
    case currentUrl?.toLowerCase().includes('eso'):
    case selectedUserGroup?.toLowerCase().includes('localinvestor'):
      // case currentUrl?.toLowerCase().includes('localhost'): // TEMP
      return 'ESO';
    default:
      return '';
  }
};
