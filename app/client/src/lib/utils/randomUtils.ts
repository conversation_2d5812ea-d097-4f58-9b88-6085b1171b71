// Helper function to get random integer
export const getRandomInt = (min: number, max: number): number => {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// Helper function to get random float with a specified number of decimals
export const getRandomFloat = (min: number, max: number, decimals: number): number => {
  const str = (Math.random() * (max - min) + min).toFixed(decimals);
  return parseFloat(str);
};

// Helper function to get a random boolean with a given probability of being true
export const getRandomBool = (probabilityTrue: number = 0.5): boolean => {
  return Math.random() < probabilityTrue;
};
