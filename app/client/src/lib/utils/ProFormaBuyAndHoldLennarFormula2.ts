import { convertPriceToNumber, convertPercentageToNumberPropertyTaxRateOnly } from '@/lib/utils/stringMethods';
import { convertFIPS } from '@/lib/utils/convertFIPS';
import { PMT } from '@/lib/utils/money';
import { calculateIRR } from '@/lib/utils/getIRR';
import { getMortgageRemainingBalance } from '@/lib/utils/getMortgageRemainingBalance';
import { ProFormaValuesLennar } from './types';


export const defaultUserInputValuesProFormaBuyAndHoldLennar = {
  userInputBidPrice: null,
  userInputProjectedRent: null,
  userInputOtherIncome: null,
  userInputPropertyTax: null,
  userInputInsurance: null,
  userInputRAndM: null,
  userInputPmFeesCoeff: null,
  userInputVacancyLossCoeff: null,
  userInputHoa: null,
  userInputAnnualCommunityDevelopmentFee: null,
  // appreciation
  userInputHPA5Yr: null,
  userInputYearsHolding: null,
};

// standardize pro forma input
export const standardizeProFormaBuyAndHoldLennarInput = (props) => {
  const { 
    parcelData, 
    subjectPropertyData, 
    demographicsData,
    savedProFormaValues, 
    hpa5Yr,
    ...rest 
  } = props;
  return {
    // from parcelData
    // latitude: parcelData.latitude,
    // longitude: parcelData.longitude,
    fips: parcelData.fips,
    apn: parcelData.apn,
    rentAVM: parcelData.rent,
    salesAVM: parcelData.sales,
    assessedValue: parcelData.total_value,
    // propertyTaxRate: parcelData.tax_rate_percent / 100 || 0,
    // propertyTaxAmount: parcelData.tax_amount,
    insuranceRate: parcelData.rate,
    hoaParam: parcelData.hoa_fees,
    poolType: parcelData.pool_type,
    // elementarySchoolScore: parcelData.elementary,
    // middleSchoolScore: parcelData.middle,
    // highSchoolScore: parcelData.highschool,
    propertyType: parcelData.standardized_land_use_type,
    // from subjectPropertyData
    latitude: subjectPropertyData?.lat,
    longitude: subjectPropertyData?.lng,
    beds: subjectPropertyData?.beds
      ? subjectPropertyData?.beds
      : subjectPropertyData?.bedsTotal,
    baths: subjectPropertyData?.baths
      ? subjectPropertyData?.baths
      : subjectPropertyData?.bathsFull,
    sqft: subjectPropertyData?.sqft,
    yearBuilt: subjectPropertyData?.yearbuilt
      ? subjectPropertyData?.yearbuilt
      : subjectPropertyData?.yearBuilt,
    streetAddress: subjectPropertyData?.address
      ? subjectPropertyData?.address
      : subjectPropertyData?.fullStreetAddress,
    city: subjectPropertyData?.city,
    state: subjectPropertyData?.state,
    zipCode: subjectPropertyData?.zipCode
      ? subjectPropertyData?.zipCode
      : subjectPropertyData?.postalCode,
    askingPriceFromProperty:
      +subjectPropertyData?.meta?.spec_price ||
      subjectPropertyData?.meta?.asking_price || subjectPropertyData?.listPrice || convertPriceToNumber(subjectPropertyData?.meta?.net_sales_price),
    hoaFromLennar: convertPriceToNumber(subjectPropertyData?.meta?.hoafeeannual),
    propertyTaxRate: convertPercentageToNumberPropertyTaxRateOnly(subjectPropertyData?.meta?.taxrate),
    annualSpecialAssessmentFees: convertPriceToNumber(subjectPropertyData?.meta?.annual_special_assessment_fees),
    // from demographicsData
    crimeScore: demographicsData?.crime_score,
    fiveYearGrowth: demographicsData?.fiveyearpopgrowth,
    incomeGrowth: demographicsData?.fiveyearincomegrowth,
    medianHHIncome: demographicsData?.medianhhincome,
    bachelorsOrAbove: demographicsData?.bachelorsandabove,
    floodZone: demographicsData?.fld_zone,
    elementarySchoolScore: demographicsData?.school?.elem?.score_2023,
    middleSchoolScore: demographicsData?.school?.middle?.score_2023,
    highSchoolScore: demographicsData?.school?.high?.score_2023,
    // from savedProFormaValues
    savedProFormaValues,
    // from hpa5Yr
    hpa5Yr,
    ...rest,
  };
};

export const getAllValues = ({
  // parcelData:
  fips,
  rentAVM, // rent AVM
  salesAVM, // sales AVM
  poolType,
  propertyType,
  // subjectPropertyData:
  beds,
  baths,
  sqft,
  yearBuilt,
  streetAddress,
  city,
  state,
  zipCode,
  askingPriceFromProperty,
  hoaFromLennar,
  propertyTaxRate, // property tax rate
  annualSpecialAssessmentFees,
  // demographicsData:
  crimeScore,
  fiveYearGrowth,
  incomeGrowth,
  medianHHIncome,
  bachelorsOrAbove,
  floodZone,
  elementarySchoolScore,
  middleSchoolScore,
  highSchoolScore,
  // userInput:
  userInputBidPrice,
  userInputClosingCosts,
  userInputProjectedRent,
  userInputOtherIncome,
  userInputPropertyTax,
  userInputInsurance,
  userInputHoa,
  userInputRAndM,
  userInputPmFeesCoeff,
  userInputPmFees,
  userInputVacancyLossCoeff,
  userInputVacancyLoss,
  userInputAnnualCommunityDevelopmentFee,
  userInputLoanToValue,
  userInputMortgageRate,
  userInputMortgageTerm,
  // userInput for appreciation
  userInputHPA5Yr, // home price appreciation (5 year history)
  userInputYearsHolding,
  // adjusted values
  adjustedRent,
  adjustedSales,
  // saved Pro Forma values,
  savedProFormaValues,
  hpa5Yr,
}: {
  // parcelData:
  fips: number;
  rentAVM: number;
  salesAVM: number;
  poolType: string;
  propertyType: string;
  // subjectPropertyData:
  beds: number;
  baths: number;
  sqft: number;
  yearBuilt: number;
  streetAddress: string;
  city: string;
  state: string;
  zipCode: string;
  askingPriceFromProperty: number;
  hoaFromLennar: number;
  propertyTaxRate: number;
  annualSpecialAssessmentFees: number;
  // demographicsData:
  crimeScore: number;
  fiveYearGrowth: number;
  incomeGrowth: number;
  medianHHIncome: number;
  bachelorsOrAbove: number;
  floodZone: string;
  elementarySchoolScore: number;
  middleSchoolScore: number;
  highSchoolScore: number;
  // userInput:
  userInputBidPrice: number | null;
  userInputClosingCosts: number | null;
  userInputProjectedRent: number | null;
  userInputOtherIncome: number | null;
  // userInputPropertyTaxCoeff: number | null;
  userInputPropertyTax: number | null;
  userInputInsurance: number | null;
  userInputRAndM: number | null;
  userInputHoa: number | null;
  userInputPmFeesCoeff: number | null;
  userInputPmFees: number | null;
  userInputVacancyLossCoeff: number | null;
  userInputVacancyLoss: number | null;
  userInputAnnualCommunityDevelopmentFee: number | null;
  userInputLoanToValue: number | null;
  userInputMortgageRate: number | null;
  userInputMortgageTerm: number | null;
  userInputHPA5Yr: number | null;
  userInputYearsHolding: number | null;
  // adjusted values
  adjustedRent: number;
  adjustedSales: number;
  // saved Pro Forma values,
  savedProFormaValues: ProFormaValuesLennar;
  hpa5Yr: number;
}) => {
  const getAskingPrice = () => {
    return askingPriceFromProperty;
  };

  const getMarketValue = () => {
    switch (true) {
      case adjustedSales && !isNaN(adjustedSales):
        return +adjustedSales;
      case salesAVM && !isNaN(salesAVM):
        return +salesAVM;
      default:
        return 0;
    }
  };

  const getBidPrice = () => {
    switch (true) {
      case userInputBidPrice !== null && !isNaN(userInputBidPrice):
        return userInputBidPrice;
      case savedProFormaValues &&
        savedProFormaValues?.['Bid Price'] &&
        !isNaN(savedProFormaValues?.['Bid Price']):
        return savedProFormaValues?.['Bid Price'];
      default:
        return getAskingPrice();
    }
  };

  const getFutureSalesPrice = ({
    yearsHolding,
  }: {
    yearsHolding: number;
  }) => {
    let futureSalesPrice: number;
    switch (true) {
      case userInputBidPrice !== null && !isNaN(userInputBidPrice):
        futureSalesPrice = userInputBidPrice;
        break;
      case savedProFormaValues &&
        savedProFormaValues?.['Bid Price'] &&
        !isNaN(savedProFormaValues?.['Bid Price']):
        futureSalesPrice = savedProFormaValues?.['Bid Price'];
        break;
      default:
        futureSalesPrice = getAskingPrice();
        break;
    }
    return futureSalesPrice * Math.pow(1 + getHPA5Yr(), yearsHolding - 1);
  };

  const getProjectedRent = ({
    yearNth
  }: {
    yearNth: number;
  }) => {
    let projectedRent: number;
    switch (true) {
      case userInputProjectedRent !== null && !isNaN(userInputProjectedRent):
        projectedRent = userInputProjectedRent;
        break;
      case savedProFormaValues &&
        savedProFormaValues?.['Projected Monthly Rent'] &&
        !isNaN(savedProFormaValues?.['Projected Monthly Rent']):
        projectedRent = savedProFormaValues?.['Projected Monthly Rent'];
        break;
      case adjustedRent && !isNaN(+adjustedRent):
        projectedRent = +adjustedRent;
        break;
      case rentAVM && !isNaN(+rentAVM):
        projectedRent = rentAVM;
        break;
      default:
        projectedRent = 0;
        break;
    }
    return projectedRent * Math.pow(1 + inflationRate, yearNth - 1);
  };

  const getOtherIncome = ({
    yearNth
  }: {
    yearNth: number;
  }) => {
    switch (true) {
      case userInputOtherIncome !== null && !isNaN(userInputOtherIncome):
        return userInputOtherIncome;
      case savedProFormaValues &&
        savedProFormaValues?.['Other Monthly Income Coefficient'] &&
        !isNaN(savedProFormaValues?.['Other Monthly Income Coefficient']):
        return savedProFormaValues?.['Other Monthly Income Coefficient'];
      default:
        return 0.05 * getProjectedRent({ yearNth });
    }
  };

  const getTotalAnnualRentalIncome = ({
    yearNth
  }: {
    yearNth: number;
  }) => {
    return (getProjectedRent({ yearNth }) + getOtherIncome({ yearNth })) * 12;
  };

  const getVacancyLossCoeff = (): number => {
    switch (true) {
      case userInputVacancyLossCoeff !== null && !isNaN(userInputVacancyLossCoeff):
        return userInputVacancyLossCoeff;
      case savedProFormaValues &&
        savedProFormaValues?.['Vacancy Loss Coefficient'] &&
        !isNaN(savedProFormaValues?.['Vacancy Loss Coefficient']):
        return savedProFormaValues?.['Vacancy Loss Coefficient'];
      case userInputVacancyLoss !== null && !isNaN(userInputVacancyLoss) && getTotalAnnualRentalIncome({ yearNth: 1 }) !== 0 && !isNaN(getTotalAnnualRentalIncome({ yearNth: 1 })):
        return userInputVacancyLoss / getTotalAnnualRentalIncome({ yearNth: 1 });
      default:
        return 0.05;
    }
  };

  const getVacancyLoss = ({
    yearNth
  }: {
    yearNth: number;
  }): number => {
    switch (true) {
      case userInputVacancyLoss !== null && !isNaN(userInputVacancyLoss):
        return userInputVacancyLoss;
      case savedProFormaValues &&
        savedProFormaValues?.['Vacancy Loss'] &&
        !isNaN(savedProFormaValues?.['Vacancy Loss']):
        return savedProFormaValues?.['Vacancy Loss'];
      default:
        return getTotalAnnualRentalIncome({ yearNth }) * getVacancyLossCoeff();
    }
  };

  const getNetAnnualRentalIncome = ({
    yearNth
  }: {
    yearNth: number;
  }) => {
    return getTotalAnnualRentalIncome({ yearNth }) - getVacancyLoss({ yearNth });
  };
  
  // const getPropertyTaxCoeff = () => {
  //   switch (true) {
  //     case userInputPropertyTaxCoeff !== null && !isNaN(userInputPropertyTaxCoeff):
  //       return userInputPropertyTaxCoeff;
  //     case savedProFormaValues &&
  //       savedProFormaValues?.['Property Tax Coefficient'] &&
  //       !isNaN(savedProFormaValues?.['Property Tax Coefficient']):
  //       return savedProFormaValues?.['Property Tax Coefficient'];
  //     default:
  //       return propertyTaxRate;
  //   }
  // };

  const getPropertyTax = ({
    yearNth
  }: {
    yearNth: number;
  }) => {
    let propertyTax: number;
    switch (true) {
      case userInputPropertyTax !== null && !isNaN(userInputPropertyTax):
        propertyTax = userInputPropertyTax;
        break;
      case savedProFormaValues &&
        savedProFormaValues?.['Property Tax'] &&
        !isNaN(savedProFormaValues?.['Property Tax']):
        propertyTax = savedProFormaValues?.['Property Tax'];
        break;
      default:
        propertyTax = getBidPrice() * propertyTaxRate;
        break;
    }
    return propertyTax * Math.pow(1 + inflationRate, yearNth - 1);
  };

  const getInsurance = ({
    yearNth
  }: {
    yearNth: number;
  }) => {
    let insurance: number;
    switch (true) {
      case userInputInsurance !== null && !isNaN(userInputInsurance):
        insurance = userInputInsurance;
        break;
      case savedProFormaValues &&
        savedProFormaValues?.['Insurance'] &&
        !isNaN(savedProFormaValues?.['Insurance']):
        insurance = savedProFormaValues?.['Insurance'];
        break;
      default:
        // for Florida and California, $1/sqft
        if (['fl', 'florida', 'ca', 'california'].includes(state?.toLowerCase())) {
          insurance = 1 * sqft;
        } else {
          insurance = 0.75 * sqft;
        }
        break;
    }
    return insurance * Math.pow(1 + inflationRate, yearNth - 1);
  };

  const getHOA = ({
    yearNth
  }: {
    yearNth: number;
  }) => {
    let hoa: number;
    switch (true) {
      case userInputHoa !== null && !isNaN(userInputHoa):
        hoa = userInputHoa;
        break;
      case savedProFormaValues &&
        savedProFormaValues?.['Annual HOA Fees'] &&
        !isNaN(savedProFormaValues?.['Annual HOA Fees']):
        hoa = savedProFormaValues?.['Annual HOA Fees'];
        break;
      default:
        hoa = hoaFromLennar;
        break;
    }
    return hoa * Math.pow(1 + inflationRate, yearNth - 1);
  };

  const getRAndM = ({
    yearNth
  }: {
    yearNth: number;
  }) => {
    let rAndM: number;
    switch (true) {
      case userInputRAndM !== null && !isNaN(userInputRAndM):
        rAndM = userInputRAndM;
        break;
      case savedProFormaValues &&
        savedProFormaValues?.['Repair & Maintenance'] &&
        !isNaN(savedProFormaValues?.['Repair & Maintenance']):
        rAndM = savedProFormaValues?.['Repair & Maintenance'];
        break;
      default:
        rAndM = 250;
        break;
    }
    return rAndM * Math.pow(1 + inflationRate, yearNth - 1);
  };

  const getPMFeesCoeff = (): number => {
    switch (true) {
      case userInputPmFeesCoeff !== null && !isNaN(userInputPmFeesCoeff):
        return userInputPmFeesCoeff;
      case savedProFormaValues &&
        savedProFormaValues?.['Property Management Fees Coefficient'] &&
        !isNaN(savedProFormaValues?.['Property Management Fees Coefficient']):
        return savedProFormaValues?.['Property Management Fees Coefficient'];
      case userInputPmFees !== null && !isNaN(userInputPmFees) && getNetAnnualRentalIncome({ yearNth: 1 }) !== 0 && !isNaN(getNetAnnualRentalIncome({ yearNth: 1 })):
        return userInputPmFees / getNetAnnualRentalIncome({ yearNth: 1 });
      default:
        return 0.08;
    }
  };

  const getPMFees = ({
    yearNth
  }: {
    yearNth: number;
  }): number => {
    switch (true) {
      case userInputPmFees !== null && !isNaN(userInputPmFees):
        return userInputPmFees;
      case savedProFormaValues &&
        savedProFormaValues?.['Property Management Fees'] &&
        !isNaN(savedProFormaValues?.['Property Management Fees']):
        return savedProFormaValues?.['Property Management Fees'];
      default:
        return getNetAnnualRentalIncome({ yearNth }) * getPMFeesCoeff();
    }
  };

  const getAnnualCommunityDevelopmentFee = ({
    yearNth
  }: {
    yearNth: number;
  }) => {
    let annualCommunityDevelopmentFee: number;
    switch (true) {
      case userInputAnnualCommunityDevelopmentFee !== null && !isNaN(userInputAnnualCommunityDevelopmentFee):
        annualCommunityDevelopmentFee = userInputAnnualCommunityDevelopmentFee;
        break;
      case savedProFormaValues &&
        savedProFormaValues?.['Annual Community Development Fee'] &&
        !isNaN(savedProFormaValues?.['Annual Community Development Fee']):
        annualCommunityDevelopmentFee = savedProFormaValues?.['Annual Community Development Fee'];
        break;
      default:
        annualCommunityDevelopmentFee = annualSpecialAssessmentFees;
        break;
    }
    return annualCommunityDevelopmentFee * Math.pow(1 + inflationRate, yearNth - 1);
  };

  const getLoanToValue = () => {
    switch (true) {
      case userInputLoanToValue !== null && !isNaN(userInputLoanToValue):
        return userInputLoanToValue;
      case savedProFormaValues &&
        savedProFormaValues?.['Loan to Value'] &&
        !isNaN(savedProFormaValues?.['Loan to Value']):
        return savedProFormaValues?.['Loan to Value'];
      default:
        return 0.75;
    }
  };

  const getDownPayment = () => {
    return getBidPrice() * (1 - getLoanToValue());
  };

  const getMortgageAmount = () => {
    return getBidPrice() - getDownPayment();
  };

  const getMortgageRate = () => {
    switch (true) {
      case userInputMortgageRate !== null && !isNaN(userInputMortgageRate):
        return userInputMortgageRate;
      case savedProFormaValues &&
        savedProFormaValues?.['Mortgage Rate'] &&
        !isNaN(savedProFormaValues?.['Mortgage Rate']):
        return savedProFormaValues?.['Mortgage Rate'];
      default:
        return 0.0499;
    }
  };

  const getMortgageTerm = () => {
    switch (true) {
      case userInputMortgageTerm !== null && !isNaN(userInputMortgageTerm):
        return userInputMortgageTerm;
      case savedProFormaValues &&
        savedProFormaValues?.['Mortgage Term'] &&
        !isNaN(savedProFormaValues?.['Mortgage Term']):
        return savedProFormaValues?.['Mortgage Term'];
      default:
        return 30;
    }
  };

  const getMortgagePayment = () => {
    // ATTN: the present value of the mortgage is negative, so we need to multiply the mortgage amount by -1
    return PMT(getMortgageRate() / 12, getMortgageTerm() * 12, getMortgageAmount()*(-1), 0, 0);
  };

  const getClosingCosts = () => {
    switch (true) {
      case userInputClosingCosts !== null && !isNaN(userInputClosingCosts):
        return userInputClosingCosts;
      case savedProFormaValues &&
        savedProFormaValues?.['Closing Costs'] &&
        !isNaN(savedProFormaValues?.['Closing Costs']):
        return savedProFormaValues?.['Closing Costs'];
      default:
        return 0.015 * getBidPrice();
    }
  };

  const getTotalAcquisitionCost = () => {
    return getBidPrice() + getClosingCosts();
  };

  const getTotalInitialInvestment = () => {
    return getDownPayment() + getClosingCosts();
  };
  
  const getTotalAnnualExpenses = ({
    yearNth
  }: {
    yearNth: number;
  }) => {
    return getPropertyTax({ yearNth }) + getInsurance({ yearNth }) + getHOA({ yearNth }) + getRAndM({ yearNth }) + getPMFees({ yearNth }) + getAnnualCommunityDevelopmentFee({ yearNth });
  };

  const getProjectedNOI = ({
    yearNth
  }: {
    yearNth: number;
  }) => {
    return getNetAnnualRentalIncome({ yearNth }) - getTotalAnnualExpenses({ yearNth });
  };

  // cash flow is only for year 1
  const getProjectedYieldOnBidPrice = () => {
    return getProjectedNOI({ yearNth: 1 }) / getBidPrice();
  };

  const getUnleveredCashFlowYearly = ({ 
    yearNth
  }: {
    yearNth: number;
  }) => {
    return getProjectedNOI({ yearNth });
  };

  const getUnleveredCashFlowMonthly = ({
    yearNth
  }: {
    yearNth: number;
  }) => { 
    return getUnleveredCashFlowYearly({ yearNth }) / 12;
  };

  const getLeveredCashFlowYearly = ({
    yearNth
  }: {
    yearNth: number;
  }) => {
      return getUnleveredCashFlowYearly({ yearNth }) - getMortgagePayment() * 12;
  };

  const getLeveredCashFlowMonthly = ({
    yearNth
  }: {
    yearNth: number;
  }) => {
    return getLeveredCashFlowYearly({ yearNth }) / 12;
  };

  const getCashOnCashReturn = ({
    yearNth
  }: {
    yearNth: number;
  }) => {
    return getLeveredCashFlowYearly({ yearNth }) / getTotalInitialInvestment();
  };

  // appreciation
  const inflationRate = 0.03;

  const getHPA5Yr = () => {
    switch (true) {
      case userInputHPA5Yr !== null && !isNaN(userInputHPA5Yr):
        return userInputHPA5Yr;
      case savedProFormaValues &&
        savedProFormaValues?.['HPA 5Yr'] &&
        !isNaN(savedProFormaValues?.['HPA 5Yr']):
        return savedProFormaValues?.['HPA 5Yr'];
      case hpa5Yr !== null && !isNaN(hpa5Yr):
        return hpa5Yr;
      default:
        return 0.05;
    }
  };

  const getYearsHolding = () => {
    switch (true) {
      case userInputYearsHolding !== null && !isNaN(userInputYearsHolding):
        return userInputYearsHolding;
      case savedProFormaValues &&
        savedProFormaValues?.['Years Holding'] &&
        !isNaN(savedProFormaValues?.['Years Holding']):
        return savedProFormaValues?.['Years Holding'];
      default:
        return 5;
    }
  };

  const getSalesClosingCosts = () => {
    return 0.035 * getFutureSalesPrice({ yearsHolding: getYearsHolding() });
  };

  const getIRR = ({
    yearsHolding
  }: {
    yearsHolding: number;
  }) => {
    const cashFlows = []
    for (let i = 0; i <= yearsHolding; i++) {
      if (i === 0) {
        // cashFlows.push(getLeveredCashFlowYearly({ yearNth: i + 1 }) - getTotalInitialInvestment());
        cashFlows.push(-getTotalInitialInvestment());
      } else if (i === yearsHolding) {
        const outstandingLoanBalance = getMortgageRemainingBalance({
          principal: getMortgageAmount(),
          monthlyInterestRate: getMortgageRate() / 12,
          monthsPaid: yearsHolding * 12,
          monthlyPayment: getMortgagePayment(),
        });
        cashFlows.push(getLeveredCashFlowYearly({ yearNth: i }) + getFutureSalesPrice({ yearsHolding }) - outstandingLoanBalance);
      } else {
        cashFlows.push(getLeveredCashFlowYearly({ yearNth: i }));
      }
    }
    return calculateIRR(cashFlows);
  };

  const getTotalReturn = ({
    yearsHolding
  }: {
    yearsHolding: number;
  }) => {
    // total cash flow for all years holding
    let totalCashFlow = 0;
    for (let i = 0; i < yearsHolding; i++) {
      totalCashFlow += getLeveredCashFlowYearly({ yearNth: i + 1 });
    }
    // get sales price at the end of the holding period
    const salesPrice = getFutureSalesPrice({ yearsHolding });
    // get outstanding loan balance at the end of the holding period
    const outstandingLoanBalance = getMortgageRemainingBalance({
      principal: getMortgageAmount(),
      monthlyInterestRate: getMortgageRate() / 12,
      monthsPaid: yearsHolding * 12,
      monthlyPayment: getMortgagePayment(),
    });
    // get total return
    const totalReturn = totalCashFlow + salesPrice - outstandingLoanBalance - getTotalInitialInvestment();
    return totalReturn;
  };
  
  return {
    Address: streetAddress,
    City: city,
    State: state,
    'ZIP Code': zipCode,
    County: convertFIPS(fips),
    'Property Type': propertyType, // item.standardized_land_use_type,
    'Year Built': +yearBuilt,
    Sqft: sqft,
    Beds: beds,
    Baths: baths,
    Pool: poolType ? poolType : 'NO',
    'Flood Zone': floodZone, // item.fld_zone,

    "Asking Price": getAskingPrice(),
    "Market Value": getMarketValue(),
    "Bid Price": getBidPrice(),
    "Bid to Ask Ratio": getBidPrice() / getAskingPrice(),
    "Total Acquisition Cost": getTotalAcquisitionCost(),
    "Projected Monthly Rent": getProjectedRent({ yearNth: 1 }),
    "Other Monthly Income": getOtherIncome({ yearNth: 1 }),
    "Total Annual Rental Income": getTotalAnnualRentalIncome({ yearNth: 1 }),
    "Net Annual Rental Income": getNetAnnualRentalIncome({ yearNth: 1 }),
    "Property Tax": getPropertyTax({ yearNth: 1 }),
    "Property Tax Coefficient": propertyTaxRate,
    "Insurance": getInsurance({ yearNth: 1 }),
    "Annual HOA Fees": getHOA({ yearNth: 1 }),
    "Repair & Maintenance": getRAndM({ yearNth: 1 }),
    "Property Management Fees Coefficient": getPMFeesCoeff(),
    "Property Management Fees": getPMFees({ yearNth: 1 }),
    "Vacancy Loss Coefficient": getVacancyLossCoeff(),
    "Vacancy Loss": getVacancyLoss({ yearNth: 1 }),
    "Annual Community Development Fee": getAnnualCommunityDevelopmentFee({ yearNth: 1 }),
    "Total Annual Expenses": getTotalAnnualExpenses({ yearNth: 1 }),
    "Loan to Value": getLoanToValue(),
    "Mortgage Amount": getMortgageAmount(),
    "Mortgage Rate": getMortgageRate(),
    "Mortgage Term": getMortgageTerm(),
    "Monthly Mortgage Payment": getMortgagePayment(),
    "Closing Costs": getClosingCosts(),
    "Total Initial Investment": getTotalInitialInvestment(),
    "Projected NOI": getProjectedNOI({ yearNth: 1}),
    "Projected Yield on Bid Price": getProjectedYieldOnBidPrice(),
    "Unlevered Cash Flow Yearly": getUnleveredCashFlowYearly({ yearNth: 1 }),
    "Unlevered Cash Flow Monthly": getUnleveredCashFlowMonthly({ yearNth: 1 }),
    "Levered Cash Flow Yearly": getLeveredCashFlowYearly({ yearNth: 1 }),
    "Levered Cash Flow Monthly": getLeveredCashFlowMonthly({ yearNth: 1 }),
    "Cash-on-Cash Return": getCashOnCashReturn({ yearNth: 1 }),

    "Annualized Return": getIRR({ yearsHolding: getYearsHolding() }),
    "Total Return": getTotalReturn({ yearsHolding: getYearsHolding() }),
    "HPA 5Yr": getHPA5Yr(),
    "Years Holding": getYearsHolding(),

    'Crime Score': +crimeScore,
    Elementary: +elementarySchoolScore,
    'Middle School': +middleSchoolScore,
    'High School': +highSchoolScore,
    'Median HH Income': +medianHHIncome,
    '5YR Pop. Growth': +fiveYearGrowth,
    'Income Growth': +incomeGrowth,
    "Bachelor's and Above": +bachelorsOrAbove,

    adjustedRent, // for offer email
  };
};
