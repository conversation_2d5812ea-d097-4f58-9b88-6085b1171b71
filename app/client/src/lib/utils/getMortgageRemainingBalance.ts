export type AmortizationEntry = {
  monthsPaid: number; // number of payments already made
  monthlyPayment: number; // monthly payment
  principalPaid: number; // the original principal loan amount
  monthlyInterestRate: number; // the monthly interest rate (annual interest rate divided by 12)
  remainingBalance: number; // remaining balance after this month
};

export const getMortgageRemainingBalance = ({
  principal,
  monthlyInterestRate,
  monthsPaid,
  monthlyPayment,
}: {
  principal: number;
  monthlyInterestRate: number;
  monthsPaid: number;
  monthlyPayment: number;
}) => {
  // console.log('principal', principal);
  // console.log('monthlyInterestRate', monthlyInterestRate);
  // console.log('monthsPaid', monthsPaid);
  // console.log('monthlyPayment', monthlyPayment);
  return principal * Math.pow((1 + monthlyInterestRate), monthsPaid) - ( monthlyPayment / monthlyInterestRate) * (Math.pow((1 + monthlyInterestRate), monthsPaid) - 1);
};