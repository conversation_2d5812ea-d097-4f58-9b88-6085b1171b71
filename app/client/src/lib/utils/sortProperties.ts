import { convertPriceToNumber } from "./stringMethods";
import { LennarSinglePropertyDataType } from "./types";
import { SortBy } from "@/routes/_authenticated/properties";

export const sortProperties = ({
  properties,
  sortBy,
  sortDirection,
}: {
  properties: LennarSinglePropertyDataType[];
  sortBy: SortBy;
  sortDirection: "asc" | "desc";
}) => {
  if (!properties) {
    return [];
  }

  if (sortBy === "price") {
    return properties.sort((a, b) => {
      const priceA = convertPriceToNumber(a.payload.subjectProperty.meta.net_sales_price);
      const priceB = convertPriceToNumber(b.payload.subjectProperty.meta.net_sales_price);
      return sortDirection === "asc" ? priceA - priceB : priceB - priceA;
    });
  } else if (sortBy === "capRate") {
    return properties.sort((a, b) => {
      const capRateA = a.payload.proforma.buyAndHold["Projected Yield on Bid Price"];
      const capRateB = b.payload.proforma.buyAndHold["Projected Yield on Bid Price"];
      return sortDirection === "asc" ? capRateA - capRateB : capRateB - capRateA;
    });
  } else if (sortBy === "estCompletion") {
    return properties.sort((a, b) => {
      const estCompletionA = a.payload.subjectProperty.meta.estimated_complete;
      const estCompletionB = b.payload.subjectProperty.meta.estimated_complete;
      return sortDirection === "asc" ? Date.parse(estCompletionA) - Date.parse(estCompletionB) : Date.parse(estCompletionB) - Date.parse(estCompletionA);
    });
  } else if (sortBy === "sqft") {
    return properties.sort((a, b) => {
      const sqftA = a.payload.subjectProperty.sqft;
      const sqftB = b.payload.subjectProperty.sqft;
      return sortDirection === "asc" ? sqftA - sqftB : sqftB - sqftA;
    });
  } else if (sortBy === "beds") {
    return properties.sort((a, b) => {
      const bedsA = a.payload.subjectProperty.beds;
      const bedsB = b.payload.subjectProperty.beds;
      return sortDirection === "asc" ? bedsA - bedsB : bedsB - bedsA;
    });
  } else if (sortBy === "baths") {
    return properties.sort((a, b) => {
      const bathsA = a.payload.subjectProperty.baths;
      const bathsB = b.payload.subjectProperty.baths;
      return sortDirection === "asc" ? bathsA - bathsB : bathsB - bathsA;
    });
  }

  return properties;
};