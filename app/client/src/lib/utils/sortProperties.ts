import { convertPriceToNumber } from "./stringMethods";
import { LennarSinglePropertyDataType } from "./types";
import { SortBy } from "@/routes/_authenticated/properties";

export const sortProperties = ({
  properties,
  sortBy,
  sortDirection,
}: {
  properties: LennarSinglePropertyDataType[];
  sortBy: SortBy;
  sortDirection: "asc" | "desc";
}) => {
  if (!properties) {
    return [];
  }

  console.log("Sorting properties:", { sortBy, sortDirection, propertiesCount: properties.length });

  if (sortBy === "basePrice") {
    return properties.sort((a, b) => {
      const priceA = a?.misc?.spec_price !== undefined ? convertPriceToNumber(a.misc.spec_price.toString()) : null;
      const priceB = b?.misc?.spec_price !== undefined ? convertPriceToNumber(b.misc.spec_price.toString()) : null;
      if (priceA == null && priceB == null) return 0;
      if (priceA == null) return 1;
      if (priceB == null) return -1;
      
      return sortDirection === "asc" ? priceA - priceB : priceB - priceA;
    });
  } else if (sortBy === "yieldOnBidPrice") {
    return properties.sort((a, b) => {
      const capRateA = a?.misc?.cap_rate
      const capRateB = b?.misc?.cap_rate
      if (capRateA == null && capRateB == null) return 0;
      if (capRateA == null) return 1;
      if (capRateB == null) return -1;
      
      return sortDirection === "asc" ? capRateA - capRateB : capRateB - capRateA;
    });
  } else if (sortBy === "estCompletion") {
    return properties.sort((a, b) => {
      const moveInA = a?.payload?.subjectProperty?.meta?.move_in_month;
      const moveInB = b?.payload?.subjectProperty?.meta?.move_in_month;
      if (!moveInA && !moveInB) return 0;
      if (!moveInA) return 1;
      if (!moveInB) return -1;
      return sortDirection === "asc" ? Date.parse(moveInA) - Date.parse(moveInB) : Date.parse(moveInB) - Date.parse(moveInA);
    });
  } else if (sortBy === "sqft") {
    return properties.sort((a, b) => {
      const sqftA = a?.payload?.subjectProperty?.sqft;
      const sqftB = b?.payload?.subjectProperty?.sqft;
      if (sqftA == null && sqftB == null) return 0;
      if (sqftA == null) return 1;
      if (sqftB == null) return -1;
      return sortDirection === "asc" ? sqftA - sqftB : sqftB - sqftA;
    });
  } else if (sortBy === "beds") {
    return properties.sort((a, b) => {
      const bedsA = a?.payload?.subjectProperty?.beds;
      const bedsB = b?.payload?.subjectProperty?.beds;
      if (bedsA == null && bedsB == null) return 0;
      if (bedsA == null) return 1;
      if (bedsB == null) return -1;
      return sortDirection === "asc" ? bedsA - bedsB : bedsB - bedsA;
    });
  } else if (sortBy === "baths") {
    return properties.sort((a, b) => {
      const bathsA = a?.payload?.subjectProperty?.baths;
      const bathsB = b?.payload?.subjectProperty?.baths;
      if (bathsA == null && bathsB == null) return 0;
      if (bathsA == null) return 1;
      if (bathsB == null) return -1;
      return sortDirection === "asc" ? bathsA - bathsB : bathsB - bathsA;
    });
  }

  return properties;
};