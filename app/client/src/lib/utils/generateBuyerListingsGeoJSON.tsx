export const getCoordinates = (property: any, dataType: 'offmarket' | 'onmarket' | 'MLSComps' | 'SFRComps' | 'HotPadsComps' | 'realtorDotComComps') => {
  if (dataType === 'offmarket') {
    return {
      latitude: property?.payload?.subjectProperty?.lat,
      longitude: property?.payload?.subjectProperty?.lng,
    }
  }
  return {
    latitude: property?.lat ?? property?.latitude ?? null,
    longitude: property?.lng ?? property?.longitude ?? null,
  };
};

export const generateBuyerListingsGeoJSON = ({
  dataSource,
  dataType,
}: {
  dataSource: any[];
  dataType: 'offmarket' | 'onmarket' | 'MLSComps' | 'SFRComps' | 'HotPadsComps' | 'realtorDotComComps';
}) => {
  let geojsonFeatures = [];
  if (dataSource && Array.isArray(dataSource) && dataSource.length > 0) {
    for (const property of dataSource) {
      // console.log('property', property);
      const { latitude, longitude } = getCoordinates(property, dataType);
      if (latitude && longitude) {
        geojsonFeatures.push({
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: [+longitude, +latitude],
          },
          properties: property,
        });
      }
    }
  }
  const buyerListingsGeoJSON = {
    type: 'FeatureCollection',
    features: geojsonFeatures,
  };
  return buyerListingsGeoJSON;
};
