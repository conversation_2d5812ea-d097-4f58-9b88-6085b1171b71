import { LennarSinglePropertyDataType } from './types';

const getMarketOptions = (properties: LennarSinglePropertyDataType[]): { value: string, label: string }[]  => {
  // Get unique city codes from properties that match enum values
  const uniqueCityCodes = Array.from(
    new Set(
      properties
        .map(p => p?.payload?.subjectProperty?.cbsaName)
        .filter(cbsaName => cbsaName !== 'Sherman-Denison, TX Metro')
    )
  );

  // Map to label using the enums
  return uniqueCityCodes.map(cbsaName => ({
    value: cbsaName,
    label: cbsaName
  }));
};

export default getMarketOptions;