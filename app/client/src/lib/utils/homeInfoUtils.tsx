import React from "react";
import {
  HomeInfoField,
  CommunityAmenityField,
  FieldConfig,
  Display,
  FALSY_STRING_VALUES
} from '../../constants/tabHomeInfoConstants'
import * as LucideIcons from "lucide-react";
import type { LucideProps } from "lucide-react";
import { MUI_SVG_ICONS } from '../../constants/tabHomeInfoConstants'
import { formatPrice, formatTaxRateToPercent, formatYearMonth, removeAllDecimal } from "./formatUtils";

type MuiIconName = keyof typeof MUI_SVG_ICONS;
type LucideIconComponent = React.ForwardRefExoticComponent<
  LucideProps & React.RefAttributes<SVGSVGElement>
>;

interface PropertyDetails {
  [key: string]: string | number | null;
}

export const formatValue = (rawValue: string | number | null, displayCondition: Display, fieldName: string): string | number | null => {
  if (displayCondition === Display.VALUE && fieldName === HomeInfoField.HoaFeeAnnual) {
    return formatPrice(rawValue);
  }
  if (displayCondition === Display.VALUE && fieldName === HomeInfoField.CommunityDevelopmentFee) {
    return formatPrice(rawValue);
  }
  if (displayCondition === Display.VALUE) return rawValue;

  const strValue = String(rawValue).trim().toLowerCase();
  if (displayCondition === Display.BOOLEAN_ALWAYS) {
    return FALSY_STRING_VALUES.includes(strValue) ? "No" : "Yes";
  }

  if (displayCondition === Display.BOOLEAN_CONDITIONAL) {
    return FALSY_STRING_VALUES.includes(strValue) ? null : "Yes";
  }

  if (displayCondition === Display.CONVERTED_PERCENTAGE) {
    return formatTaxRateToPercent(rawValue);
  }

  if (displayCondition === Display.REMOVE_DECIMAL) {
    return removeAllDecimal(rawValue == null ? undefined : String(rawValue));
  }

  if (displayCondition === Display.CONVERTED_YEAR_MONTH) {
    return formatYearMonth(rawValue == null ? undefined : String(rawValue));
  }


  // should never reach here, but just in case
  return rawValue;
};


export const createDisplayItems = <T extends HomeInfoField | CommunityAmenityField>(
  details: PropertyDetails,
  orderArray: T[],
  configObject: Record<T, FieldConfig>
) => {
  return orderArray
    .map(fieldName => {
      const fieldConfig = configObject[fieldName];
      if (!fieldConfig) return null;

      const rawValue = details[fieldName];
      const displayValue = formatValue(rawValue, fieldConfig.displayCondition, fieldName);

      if (displayValue === null) return null;

      return {
        field: fieldName,
        value: displayValue,
        config: fieldConfig
      };
    })
    .filter(item => item !== null);
};

export const getIconComponent = (iconName: string | undefined): React.ReactNode => {
  if (!iconName) return null;

  // Check if it's a MUI icon
  if (Object.keys(MUI_SVG_ICONS).includes(iconName)) {
    const MuiIcon = MUI_SVG_ICONS[iconName as MuiIconName];
    return (
      <MuiIcon
        sx={{
          fontSize: 16,
          transform: 'scale(0.85)',
        }}
        className="text-[var(--color-text-black)]"
      />
    );
  }

  // Try to find Lucide icon
  const LucideIcon = LucideIcons[iconName as keyof typeof LucideIcons] as LucideIconComponent | undefined;

  // Fallback to Info icon if not found
  if (!LucideIcon) {
    return <LucideIcons.Info size={16} strokeWidth={1.5} className="text-[var(--color-text-black)]" />;
  }

  // Return the found Lucide icon
  return <LucideIcon size={16} strokeWidth={1.5} className="text-[var(--color-text-black)]" />;
};



