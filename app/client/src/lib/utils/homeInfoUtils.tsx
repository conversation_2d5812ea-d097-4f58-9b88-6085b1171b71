import {
  HomeInfoField,
  CommunityAmenityField,
  FieldConfig,
  Display,
  FALSY_STRING_VALUES
} from '../../constants/tabHomeInfoConstants'
import { formatPrice, formatPriceWithDecimals, formatTaxRateToPercent, formatYearMonth, removeAllDecimal } from "./formatUtils";
import { PropertyMeta } from "./types";


export const formatValue = (rawValue: string | number | null, displayCondition: Display, fieldName: string): string | number | null => {
  if (displayCondition === Display.VALUE && fieldName === HomeInfoField.HoaFeeAnnual) {
    return formatPrice(rawValue);
  }
  if (displayCondition === Display.VALUE && fieldName === HomeInfoField.AnnualSpecialAssessmentFees) {
    return formatPriceWithDecimals(rawValue);
  }
  if (displayCondition === Display.VALUE) return rawValue;

  if (rawValue === null && displayCondition === Display.BOOLEAN_ALWAYS) {
    return "No";
  }

  const strValue = String(rawValue).trim().toLowerCase();
  if (displayCondition === Display.BOOLEAN_ALWAYS) {
    return FALSY_STRING_VALUES.includes(strValue) ? "No" : "Yes";
  }

  if (displayCondition === Display.BOOLEAN_CONDITIONAL) {
    return FALSY_STRING_VALUES.includes(strValue) ? null : "Yes";
  }

  if (displayCondition === Display.CONVERTED_PERCENTAGE) {
    return formatTaxRateToPercent(rawValue);
  }

  if (displayCondition === Display.REMOVE_DECIMAL) {
    return removeAllDecimal(rawValue == null ? undefined : String(rawValue));
  }

  if (displayCondition === Display.CONVERTED_YEAR_MONTH) {
    return formatYearMonth(rawValue == null ? undefined : String(rawValue));
  }


  // should never reach here, but just in case
  return rawValue;
};


export const createDisplayItems = <T extends HomeInfoField | CommunityAmenityField>(
  details: PropertyMeta,
  orderArray: T[],
  configObject: Record<T, FieldConfig>
) => {
  return orderArray
    .map(fieldName => {
      const fieldConfig = configObject[fieldName];
      if (!fieldConfig) return null;

      const rawValue = details[fieldName];
      const displayValue = formatValue(rawValue, fieldConfig.displayCondition, fieldName);

      if (displayValue === null) return null;

      return {
        field: fieldName,
        value: displayValue,
        config: fieldConfig
      };
    })
    .filter(item => item !== null);
};




