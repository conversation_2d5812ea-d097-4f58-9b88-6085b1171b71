
type OwnerCountItem = {
  owner_occupied_sl: 'Yes' | 'No';
  count: number;
}

export const getOwnerRenterCounts = (ownerCount: OwnerCountItem[] | undefined): { owner: number, renter: number } => {
  let owner = 0;
  let renter = 0;
  if (ownerCount && Array.isArray(ownerCount)) {
    ownerCount.forEach((item) => {
      if (item && item.owner_occupied_sl === 'Yes') {
        owner = item.count || 0;
      } else if (item) {
        renter = item.count || 0;
      }
    });
  }
  
  return { owner, renter };
};