import { LennarSinglePropertyDataType } from '@/lib/utils/types';
import { convertPriceToNumber } from '../utils/stringMethods';
import { formatPercentage } from '../utils/formatUtils';
import { BathroomMinValues, BedroomMinValues, PRICE_BOUNDARIES, PriceRangeValues } from '../../types/PropertiesFilterTypes';

interface PropertyFilterParams {
  properties: LennarSinglePropertyDataType[] | null;
  selectedMarket: string | "";
  selectedPrice: PriceRangeValues | "";
  selectedBeds: BedroomMinValues | "";
  selectedBaths: BathroomMinValues | "";
  minCapRate: string;
}

export const filterProperties = ({
  properties,
  selectedMarket,
  selectedPrice,
  selectedBeds,
  selectedBaths,
  minCapRate
}: PropertyFilterParams): LennarSinglePropertyDataType[] =>{

  if (!properties) {
    return [];
  }

  let filtered = properties;
  

  // Market filter
if (selectedMarket !== undefined && selectedMarket !== null && selectedMarket !== "") {
  filtered = filtered.filter(property => property?.payload?.subjectProperty?.cbsaName === selectedMarket || ( property?.payload?.subjectProperty?.cbsaName === 'Sherman-Denison, TX Metro' && selectedMarket === 'Dallas-Fort Worth-Arlington, TX Metro'));
}

  // Price filter
  if (selectedPrice !== undefined && selectedPrice !== null && selectedPrice !== "") {
    filtered = filtered.filter(property => {
      const priceString = property?.payload?.subjectProperty?.meta?.net_sales_price;
      if (!priceString) return true;

      const price = convertPriceToNumber(priceString);

      const selectedPriceValue = typeof selectedPrice === 'string' ?
        parseInt(selectedPrice) : selectedPrice;

      const boundary = PRICE_BOUNDARIES[selectedPriceValue];

      if (boundary && (price < boundary.min || price > boundary.max)) {
        return false;
      }

      return true;
    });
  }

  // Beds filter
  if (selectedBeds !== undefined && selectedBeds !== null && selectedBeds !== "") {
    filtered = filtered.filter(property => {
      const bedroomsString = property?.payload?.subjectProperty?.beds;
      if (!bedroomsString) return true;

      const bedrooms = parseInt(bedroomsString);
      if (isNaN(bedrooms)) return true;

      return bedrooms >= selectedBeds;
    });
  }

  // Baths filter
  if (selectedBaths !== undefined && selectedBaths !== null && selectedBaths !== "") {
    filtered = filtered.filter(property => {
      const bathroomsString = property?.payload?.subjectProperty?.baths;
      if (!bathroomsString) return true;

      const bathrooms = parseFloat(bathroomsString);
      if (isNaN(bathrooms)) return true;

      return bathrooms >= selectedBaths;
    });
  }

  // Min cap rate filter
  if (minCapRate !== undefined && minCapRate !== null && minCapRate !== "") {
    const minCapRateValue = Number(minCapRate);

    if (!isNaN(minCapRateValue) && minCapRateValue > 0) {
      filtered = filtered.filter(property => {
        const capRate = property?.payload?.proforma?.buyAndHold?.["Projected Yield on Bid Price"];
        if (capRate === undefined || capRate === null) return true;

        // Format the raw cap rate to percentage for consistent comparison
        const formattedCapRate = formatPercentage(capRate);
        const capRateValue = parseFloat(formattedCapRate);

        if (isNaN(capRateValue)) return true;

        return capRateValue >= minCapRateValue;
      });
    }
  }

  return filtered;
}