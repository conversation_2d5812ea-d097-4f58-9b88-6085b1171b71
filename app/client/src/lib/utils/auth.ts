import { fetchAuthSession, getCurrentUser, fetchUserAttributes } from 'aws-amplify/auth';

// https://docs.amplify.aws/react/build-a-backend/auth/manage-user-session/#refreshing-sessions

export const getUser = async () => {
  const user = await getCurrentUser();
  return user;
};

export const getUserToken = async (type: 'id' | 'access') => {
  try {
    if (type !== 'id' && type !== 'access') throw new Error('Invalid token type');

    const { idToken, accessToken } = (await fetchAuthSession()).tokens || {};
    if (idToken && type === 'id') {
      return idToken.toString() || '';
    } else if (accessToken && type === 'access') {
      return accessToken.toString() || '';
    }
  } catch (e) {
    console.error(e);
    console.log('Error in getUserToken');
  }
  return null;
};

export const getUserGroup = async () => {
  try {
    const { idToken } = (await fetchAuthSession()).tokens || {};
    if (!idToken || !idToken.payload) {
      console.warn('No valid idToken found');
      return [];
    }
    return idToken.payload['cognito:groups'] || [];
  } catch (e) {
    console.error('Error fetching user groups:', e);
    return [];
  }
};

export const getUserEmail = async (): Promise<string> => {
  try {
    const { idToken } = (await fetchAuthSession()).tokens || {};
    if (!idToken || !idToken.payload) {
      console.warn('No valid idToken found');
      return '';
    }
    return (idToken.payload['email'] as string) || '';
  } catch (e) {
    console.error('Error fetching user email:', e);
    return '';
  }
};

export const getUserAttributes = async () => {
  try {
    const attributes = await fetchUserAttributes();
    return attributes;
  } catch (e) {
    console.error('Error fetching user attributes:', e);
    return {};
  }
};

export const getUserFirstName = async (): Promise<string> => {
  try {
    const attributes = await fetchUserAttributes();
    return attributes.given_name || '';
  } catch (e) {
    console.error('Error fetching user first name:', e);
    return '';
  }
};

export const getUserLastName = async (): Promise<string> => {
  try {
    const attributes = await fetchUserAttributes();
    return attributes.family_name || '';
  } catch (e) {
    console.error('Error fetching user last name:', e);
    return '';
  }
};

export const getUserPhoneNumber = async (): Promise<string> => {
  try {
    const attributes = await fetchUserAttributes();
    return attributes.phone_number || '';
  } catch (e) {
    console.error('Error fetching user phone number:', e);
    return '';
  }
};

export const getUserRole = async (): Promise<string> => {
  try {
    const attributes = await fetchUserAttributes();
    return attributes['custom:role'] || '';
  } catch (e) {
    console.error('Error fetching user role:', e);
    return '';
  }
};