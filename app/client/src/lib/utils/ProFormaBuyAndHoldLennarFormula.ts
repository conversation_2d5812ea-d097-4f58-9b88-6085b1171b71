import { convertPriceToNumber, convertPercentageToNumberPropertyTaxRateOnly } from '@/lib/utils/stringMethods';
import { convertFIPS } from '@/lib/utils/convertFIPS';
import { PMT } from '@/lib/utils/money';


export const defaultUserInputValuesProFormaBuyAndHoldLennar = {
  userInputBidPrice: null,
  userInputProjectedRent: null,
  userInputOtherIncome: null,
  userInputPropertyTax: null,
  userInputInsurance: null,
  userInputRAndM: null,
  userInputPmFeesCoeff: null,
  userInputVacancyLossCoeff: null,
  userInputHoa: null,
  userInputAnnualCommunityDevelopmentFee: null,
};

// standardize pro forma input
export const standardizeProFormaBuyAndHoldLennarInput = (props) => {
  // console.log('props--->', props);
  const { 
    parcelData, 
    subjectPropertyData, 
    demographicsData, 
    savedProFormaValues, 
    ...rest 
  } = props;
  return {
    // from parcelData
    // latitude: parcelData.latitude,
    // longitude: parcelData.longitude,
    fips: parcelData.fips,
    apn: parcelData.apn,
    rentAVM: parcelData.rent,
    salesAVM: parcelData.sales,
    assessedValue: parcelData.total_value,
    // propertyTaxRate: parcelData.tax_rate_percent / 100 || 0,
    // propertyTaxAmount: parcelData.tax_amount,
    insuranceRate: parcelData.rate,
    hoaParam: parcelData.hoa_fees,
    poolType: parcelData.pool_type,
    // elementarySchoolScore: parcelData.elementary,
    // middleSchoolScore: parcelData.middle,
    // highSchoolScore: parcelData.highschool,
    propertyType: parcelData.standardized_land_use_type,
    // from subjectPropertyData
    latitude: subjectPropertyData.lat,
    longitude: subjectPropertyData.lng,
    beds: subjectPropertyData.beds
      ? subjectPropertyData.beds
      : subjectPropertyData.bedsTotal,
    baths: subjectPropertyData.baths
      ? subjectPropertyData.baths
      : subjectPropertyData.bathsFull,
    sqft: subjectPropertyData.sqft,
    yearBuilt: subjectPropertyData.yearbuilt
      ? subjectPropertyData.yearbuilt
      : subjectPropertyData.yearBuilt,
    streetAddress: subjectPropertyData.address
      ? subjectPropertyData.address
      : subjectPropertyData.fullStreetAddress,
    city: subjectPropertyData.city,
    state: subjectPropertyData.state,
    zipCode: subjectPropertyData.zipCode
      ? subjectPropertyData.zipCode
      : subjectPropertyData.postalCode,
    askingPriceFromProperty:
      subjectPropertyData?.meta?.asking_price || subjectPropertyData.listPrice || convertPriceToNumber(subjectPropertyData?.meta?.net_sales_price),
    hoa: convertPriceToNumber(subjectPropertyData?.meta?.hoafeeannual),
    propertyTaxRate: convertPercentageToNumberPropertyTaxRateOnly(subjectPropertyData?.meta?.taxrate),
    // from demographicsData
    crimeScore: demographicsData.crime_score,
    fiveYearGrowth: demographicsData.fiveyearpopgrowth,
    incomeGrowth: demographicsData.fiveyearincomegrowth,
    medianHHIncome: demographicsData.medianhhincome,
    bachelorsOrAbove: demographicsData.bachelorsandabove,
    floodZone: demographicsData.fld_zone,
    elementarySchoolScore: demographicsData?.school?.elem?.score_2023,
    middleSchoolScore: demographicsData?.school?.middle?.score_2023,
    highSchoolScore: demographicsData?.school?.high?.score_2023,
    // from savedProFormaValues
    savedProFormaValues,
    ...rest,
  };
};

const currentYear = new Date().getFullYear();

export const getAllValues = ({
  // parcelData:
  fips,
  rentAVM, // rent AVM
  salesAVM, // sales AVM
  poolType,
  propertyType,
  // subjectPropertyData:
  beds,
  baths,
  sqft,
  yearBuilt,
  streetAddress,
  city,
  state,
  zipCode,
  askingPriceFromProperty,
  hoa,
  propertyTaxRate, // property tax rate
  // demographicsData:
  crimeScore,
  fiveYearGrowth,
  incomeGrowth,
  medianHHIncome,
  bachelorsOrAbove,
  floodZone,
  elementarySchoolScore,
  middleSchoolScore,
  highSchoolScore,
  // userInput:
  userInputBidPrice,
  userInputClosingCosts,
  userInputProjectedRent,
  userInputOtherIncome,
  userInputPropertyTax,
  userInputInsurance,
  userInputHoa,
  userInputRAndM,
  userInputPmFeesCoeff,
  userInputPmFees,
  userInputVacancyLossCoeff,
  userInputVacancyLoss,
  userInputAnnualCommunityDevelopmentFee,
  userInputLoanToValue,
  userInputMortgageRate,
  userInputMortgageTerm,
  // adjusted values
  adjustedRent,
  adjustedSales,
  // saved Pro Forma values,
  savedProFormaValues,
}: {
  // parcelData:
  fips: number;
  rentAVM: number;
  salesAVM: number;
  poolType: string;
  propertyType: string;
  // subjectPropertyData:
  beds: number;
  baths: number;
  sqft: number;
  yearBuilt: number;
  streetAddress: string;
  city: string;
  state: string;
  zipCode: string;
  askingPriceFromProperty: number;
  hoa: number;
  propertyTaxRate: number;
  // demographicsData:
  crimeScore: number;
  fiveYearGrowth: number;
  incomeGrowth: number;
  medianHHIncome: number;
  bachelorsOrAbove: number;
  floodZone: string;
  elementarySchoolScore: number;
  middleSchoolScore: number;
  highSchoolScore: number;
  // userInput:
  userInputBidPrice: number | null;
  userInputClosingCosts: number | null;
  userInputProjectedRent: number | null;
  userInputOtherIncome: number | null;
  // userInputPropertyTaxCoeff: number | null;
  userInputPropertyTax: number | null;
  userInputInsurance: number | null;
  userInputRAndM: number | null;
  userInputHoa: number | null;
  userInputPmFeesCoeff: number | null;
  userInputPmFees: number | null;
  userInputVacancyLossCoeff: number | null;
  userInputVacancyLoss: number | null;
  userInputAnnualCommunityDevelopmentFee: number | null;
  userInputLoanToValue: number | null;
  userInputMortgageRate: number | null;
  userInputMortgageTerm: number | null;  
  // adjusted values
  adjustedRent: number;
  adjustedSales: number;
  // saved Pro Forma values,
  savedProFormaValues: any;
}) => {
  const getAskingPrice = () => {
    return askingPriceFromProperty;
  };

  const getMarketValue = () => {
    switch (true) {
      case adjustedSales && !isNaN(adjustedSales):
        return +adjustedSales;
      case salesAVM && !isNaN(salesAVM):
        return +salesAVM;
      default:
        return 0;
    }
  };

  const getBidPrice = () => {
    switch (true) {
      case userInputBidPrice !== null && !isNaN(userInputBidPrice):
        return userInputBidPrice;
      case savedProFormaValues &&
        savedProFormaValues?.['Bid Price'] &&
        !isNaN(savedProFormaValues?.['Bid Price']):
        return savedProFormaValues?.['Bid Price'];
      default:
        return getAskingPrice();
    }
  };

  const getProjectedRent = () => {
    switch (true) {
      case userInputProjectedRent !== null && !isNaN(userInputProjectedRent):
        return userInputProjectedRent;
      case savedProFormaValues &&
        savedProFormaValues?.['Projected Monthly Rent'] &&
        !isNaN(savedProFormaValues?.['Projected Monthly Rent']):
        return savedProFormaValues?.['Projected Monthly Rent'];
      case adjustedRent && !isNaN(+adjustedRent):
        return +adjustedRent;
      case rentAVM && !isNaN(+rentAVM):
        return rentAVM;
      default:
        return 0;
    }
  };

  const getOtherIncome = () => {
    switch (true) {
      case userInputOtherIncome !== null && !isNaN(userInputOtherIncome):
        return userInputOtherIncome;
      case savedProFormaValues &&
        savedProFormaValues?.['Other Monthly Income Coefficient'] &&
        !isNaN(savedProFormaValues?.['Other Monthly Income Coefficient']):
        return savedProFormaValues?.['Other Monthly Income Coefficient'];
      default:
        return 0.05 * getProjectedRent();
    }
  };

  const getVacancyLossCoeff = (): number => {
    switch (true) {
      case userInputVacancyLossCoeff !== null && !isNaN(userInputVacancyLossCoeff):
        return userInputVacancyLossCoeff;
      case savedProFormaValues &&
        savedProFormaValues?.['Vacancy Loss Coefficient'] &&
        !isNaN(savedProFormaValues?.['Vacancy Loss Coefficient']):
        return savedProFormaValues?.['Vacancy Loss Coefficient'];
      case userInputVacancyLoss !== null && !isNaN(userInputVacancyLoss) && getTotalAnnualRentalIncome() !== 0 && !isNaN(getTotalAnnualRentalIncome()):
        return userInputVacancyLoss / getTotalAnnualRentalIncome();
      default:
        return 0.05;
    }
  };

  const getVacancyLoss = (): number => {
    switch (true) {
      case userInputVacancyLoss !== null && !isNaN(userInputVacancyLoss):
        return userInputVacancyLoss;
      default:
        return getTotalAnnualRentalIncome() * getVacancyLossCoeff();
    }
  };

  const getTotalAnnualRentalIncome = () => {
    return (getProjectedRent() + getOtherIncome()) * 12;
  };

  const getNetAnnualRentalIncome = () => {
    return getTotalAnnualRentalIncome() - getVacancyLoss();
  };

  // const getPropertyTaxCoeff = () => {
  //   switch (true) {
  //     case userInputPropertyTaxCoeff !== null && !isNaN(userInputPropertyTaxCoeff):
  //       return userInputPropertyTaxCoeff;
  //     case savedProFormaValues &&
  //       savedProFormaValues?.['Property Tax Coefficient'] &&
  //       !isNaN(savedProFormaValues?.['Property Tax Coefficient']):
  //       return savedProFormaValues?.['Property Tax Coefficient'];
  //     default:
  //       return propertyTaxRate;
  //   }
  // };

  const getPropertyTax = () => {
    switch (true) {
      case userInputPropertyTax !== null && !isNaN(userInputPropertyTax):
        return userInputPropertyTax;
      case savedProFormaValues &&
        savedProFormaValues?.['Property Tax'] &&
        !isNaN(savedProFormaValues?.['Property Tax']):
        return savedProFormaValues?.['Property Tax'];
      default:
        return getBidPrice() * propertyTaxRate;
    }
  };

  const getInsurance = () => {
    switch (true) {
      case userInputInsurance !== null && !isNaN(userInputInsurance):
        return userInputInsurance;
      case savedProFormaValues &&
        savedProFormaValues?.['Insurance'] &&
        !isNaN(savedProFormaValues?.['Insurance']):
        return savedProFormaValues?.['Insurance'];
      default:
        return 0.75 * sqft;
    }
  };

  const getHOA = () => {
    switch (true) {
      case userInputHoa !== null && !isNaN(userInputHoa):
        return userInputHoa;
      case savedProFormaValues &&
        savedProFormaValues?.['Annual HOA Fees'] &&
        !isNaN(savedProFormaValues?.['Annual HOA Fees']):
        return savedProFormaValues?.['Annual HOA Fees'];
      default:
        return hoa;
    }
  };

  const getRAndM = () => {
    switch (true) {
      case userInputRAndM !== null && !isNaN(userInputRAndM):
        return userInputRAndM;
      case savedProFormaValues &&
        savedProFormaValues?.['Repair & Maintenance'] &&
        !isNaN(savedProFormaValues?.['Repair & Maintenance']):
        return savedProFormaValues?.['Repair & Maintenance'];
      default:
        return 250;
    }
  };

  const getPMFeesCoeff = (): number => {
    switch (true) {
      case userInputPmFeesCoeff !== null && !isNaN(userInputPmFeesCoeff):
        return userInputPmFeesCoeff;
      case savedProFormaValues &&
        savedProFormaValues?.['Property Management Fees Coefficient'] &&
        !isNaN(savedProFormaValues?.['Property Management Fees Coefficient']):
        return savedProFormaValues?.['Property Management Fees Coefficient'];
      case userInputPmFees !== null && !isNaN(userInputPmFees) && getTotalAnnualRentalIncome() !== 0 && !isNaN(getTotalAnnualRentalIncome()):
        return userInputPmFees / getTotalAnnualRentalIncome();
      default:
        return 0.08;
    }
  };

  const getPMFees = (): number => {
    switch (true) {
      case userInputPmFees !== null && !isNaN(userInputPmFees):
        return userInputPmFees;
      default:
        return getTotalAnnualRentalIncome() * getPMFeesCoeff();
    }
  };

  const getAnnualCommunityDevelopmentFee = () => {
    switch (true) {
      case userInputAnnualCommunityDevelopmentFee !== null && !isNaN(userInputAnnualCommunityDevelopmentFee):
        return userInputAnnualCommunityDevelopmentFee;
      case savedProFormaValues &&
        savedProFormaValues?.['Annual Community Development Fee'] &&
        !isNaN(savedProFormaValues?.['Annual Community Development Fee']):
        return savedProFormaValues?.['Annual Community Development Fee'];
      default:
        return 0;
    }
  };

  const getLoanToValue = () => {
    switch (true) {
      case userInputLoanToValue !== null && !isNaN(userInputLoanToValue):
        return userInputLoanToValue;
      default:
        return 0.8;
    }
  };

  const getDownPayment = () => {
    return (getBidPrice() + getClosingCosts()) * (1 - getLoanToValue());
  };

  const getMortgageAmount = () => {
    return (getBidPrice() + getClosingCosts()) - getDownPayment();
  };

  const getMortgageRate = () => {
    switch (true) {
      case userInputMortgageRate !== null && !isNaN(userInputMortgageRate):
        return userInputMortgageRate;
      default:
        return 0.06;
    }
  };

  const getMortgageTerm = () => {
    switch (true) {
      case userInputMortgageTerm !== null && !isNaN(userInputMortgageTerm):
        return userInputMortgageTerm;
      default:
        return 30;
    }
  };

  const getMortgagePayment = () => {
    // ATTN: the present value of the mortgage is negative, so we need to multiply the mortgage amount by -1
    return PMT(getMortgageRate() / 12, getMortgageTerm() * 12, getMortgageAmount()*(-1), 0, 0);
  };

  const getClosingCosts = () => {
    switch (true) {
      case userInputClosingCosts !== null && !isNaN(userInputClosingCosts):
        return userInputClosingCosts;
      default:
        return 0.015 * getBidPrice();
    }
  };

  const getTotalAcquisitionCost = () => {
    return getBidPrice() + getClosingCosts();
  };

  const getTotalInitialInvestment = () => {
    return getDownPayment() + getClosingCosts();
  };
  
  const getTotalAnnualExpenses = () => {
    return getPropertyTax() + getInsurance() + getHOA() + getRAndM() + getPMFees() + getAnnualCommunityDevelopmentFee();
  };

  const getProjectedNOI = () => {
    return getNetAnnualRentalIncome() - getTotalAnnualExpenses();
  };

  const getProjectedYieldOnBidPrice = () => {
    return getProjectedNOI() / getBidPrice();
  };

  const getUnleveredCashFlowYearly = () => {
    return getProjectedNOI();
  };

  const getUnleveredCashFlowMonthly = () => { 
    return getUnleveredCashFlowYearly() / 12;
  };

  const getLeveredCashFlowYearly = () => {
      return getUnleveredCashFlowYearly() - getMortgagePayment() * 12;
  };

  const getLeveredCashFlowMonthly = () => {
    return getLeveredCashFlowYearly() / 12;
  };

  const getCashOnCashReturn = () => {
    return getLeveredCashFlowYearly() / getDownPayment();
  };
  

  return {
    Address: streetAddress,
    City: city,
    State: state,
    'ZIP Code': zipCode,
    County: convertFIPS(fips),
    'Property Type': propertyType, // item.standardized_land_use_type,
    'Year Built': +yearBuilt,
    Sqft: sqft,
    Beds: beds,
    Baths: baths,
    Pool: poolType ? poolType : 'NO',
    'Flood Zone': floodZone, // item.fld_zone,

    "Asking Price": getAskingPrice(),
    "Market Value": getMarketValue(),
    "Bid Price": getBidPrice(),
    "Bid to Ask Ratio": getBidPrice() / getAskingPrice(),
    "Total Acquisition Cost": getTotalAcquisitionCost(),
    "Projected Monthly Rent": getProjectedRent(),
    "Other Monthly Income": getOtherIncome(),
    "Total Annual Rental Income": getTotalAnnualRentalIncome(),
    "Net Annual Rental Income": getNetAnnualRentalIncome(),
    "Property Tax": getPropertyTax(),
    "Property Tax Coefficient": propertyTaxRate,
    "Insurance": getInsurance(),
    "Annual HOA Fees": getHOA(),
    "Repair & Maintenance": getRAndM(),
    "Property Management Fees Coefficient": getPMFeesCoeff(),
    "Property Management Fees": getPMFees(),
    "Vacancy Loss Coefficient": getVacancyLossCoeff(),
    "Vacancy Loss": getVacancyLoss(),
    "Annual Community Development Fee": getAnnualCommunityDevelopmentFee(),
    "Total Annual Expenses": getTotalAnnualExpenses(),
    "Loan to Value": getLoanToValue(),
    "Mortgage Amount": getMortgageAmount(),
    "Mortgage Rate": getMortgageRate(),
    "Mortgage Term": getMortgageTerm(),
    "Monthly Mortgage Payment": getMortgagePayment(),
    "Closing Costs": getClosingCosts(),
    "Total Initial Investment": getTotalInitialInvestment(),
    "Projected NOI": getProjectedNOI(),
    "Projected Yield on Bid Price": getProjectedYieldOnBidPrice(),
    "Unlevered Cash Flow Yearly": getUnleveredCashFlowYearly(),
    "Unlevered Cash Flow Monthly": getUnleveredCashFlowMonthly(),
    "Levered Cash Flow Yearly": getLeveredCashFlowYearly(),
    "Levered Cash Flow Monthly": getLeveredCashFlowMonthly(),
    "Cash-on-Cash Return": getCashOnCashReturn(),

    'Crime Score': +crimeScore,
    Elementary: +elementarySchoolScore,
    'Middle School': +middleSchoolScore,
    'High School': +highSchoolScore,
    'Median HH Income': +medianHHIncome,
    '5YR Pop. Growth': +fiveYearGrowth,
    'Income Growth': +incomeGrowth,
    "Bachelor's and Above": +bachelorsOrAbove,
  };
};
