import {
  OWNER_COLOR,
  HOA_FEE_COLOR,
  dallasZipCodes,
  houstonZipCodes,
  sanantonioZipCodes,
  austinZipCodes,
  carolinaZipCodes,
  realtracZipCodes,
  atlantaZipCodes,
  tampa_orlandoZipCodes,
  cincinnatiZipCodes,
  columbusOhioZipCodes,
  oklahomaCityZipCodes,
  tulsaCityZipCodes,
  nashvilleZipCodes,
  minneapolisZipCodes,
  detroitZipCodes,
  phoenixZipCodes,
} from '@/constants';
import { point } from '@turf/helpers';
import { default as turf_distance } from '@turf/distance';
import cityCodes from '@/lib/utils/citycodes.json';
import { default as turf_centerOfMass } from '@turf/center-of-mass';
import { polygon } from '@turf/helpers';
import userGroupAccess from '@/lib/utils/userGroupAccess.json';
import allMetrosWithMLSData from '@/lib/utils/proFormaPropertySubTypesByMetro.json';

export const getMLSImageFolderBasedOnMetro = (metro) => {
  const metroLowerCase = metro.toLowerCase();

  if (metroLowerCase.includes('jacksonville')) {
    return 'jacksonville';
  } else if (metroLowerCase.includes('houston')) {
    return 'houston';
  } else if (metroLowerCase.includes('san antonio')) {
    return 'sanantonio';
  } else if (metroLowerCase.includes('stellar')) {
    return 'stellar';
  } else if (metroLowerCase.includes('carolina')) {
    // covers North Carolina
    return 'carolina';
  } else if (metroLowerCase.includes('atlanta')) {
    return 'atlanta';
  } else if (metroLowerCase.includes('dallas')) {
    return 'dallas';
  } else if (metroLowerCase.includes('austin')) {
    return 'austin';
  } else if (metroLowerCase.includes('nashville')) {
    return 'realtrac';
  } else if (metroLowerCase.includes('minneapolis')) {
    return 'minneapolis';
  } else if (metroLowerCase.includes('detroit')) {
    return 'detroit';
  } else if (metroLowerCase.includes('phoenix')) {
    return 'phoenix';
  } else {
    return metro.toLowerCase();
  }
};

export const cityBasedZipCode = (zipcode) => {
  const code = Number(zipcode.substring(0, 3));

  if (dallasZipCodes.includes(code)) {
    return 'dallas';
  } else if (houstonZipCodes.includes(code)) {
    return 'houston';
  } else if (sanantonioZipCodes.includes(code)) {
    return 'sanantonio';
  } else if (austinZipCodes.includes(code)) {
    return 'austin';
  } else if (carolinaZipCodes.includes(code)) {
    return 'carolina';
  } else if (realtracZipCodes.includes(code)) {
    return 'realtrac';
  } else if (atlantaZipCodes.includes(code)) {
    return 'atlanta';
  } else if (tampa_orlandoZipCodes.includes(code)) {
    return 'stellar';
  } else if (nashvilleZipCodes.includes(code)) {
    return 'nashville';
  } else if (minneapolisZipCodes.includes(code)) {
    return 'minneapolis';
  } else if (detroitZipCodes.includes(code)) {
    return 'detroit';
  } else if (phoenixZipCodes.includes(code)) {
    return 'phoenix';
  } else if (
    cincinnatiZipCodes.includes(code) ||
    cincinnatiZipCodes.includes(+zipcode)
  ) {
    return 'cincinnati';
  } else if (
    columbusOhioZipCodes.includes(code) ||
    columbusOhioZipCodes.includes(+zipcode)
  ) {
    return 'columbus';
  } else if (
    oklahomaCityZipCodes.includes(code) ||
    oklahomaCityZipCodes.includes(+zipcode)
  ) {
    return 'oklahomacity';
  } else if (
    tulsaCityZipCodes.includes(code) ||
    tulsaCityZipCodes.includes(+zipcode)
  ) {
    return 'tulsa';
  } else {
    console.log('ZipCode not found in cityBasedZipCode()');
  }
};

export const getCityCodeOfMetro = (metro) => {
  const value = metro.trim().toLowerCase();
  switch (value) {
    case 'dallas':
      return '01';
    case 'houston':
      return '02';
    case 'atlanta':
      return '03';
    case 'austin':
      return '04';
    case 'stellar':
      return '05';
    case 'nashville':
      return '06';
    case 'san antonio':
    case 'sanantonio':
      return '07';
    case 'north carolina':
    case 'carolina':
      return '08';
    case 'jacksonville':
      return '09';
    case 'minneapolis':
      return '10';
    case 'detroit':
      return '11';
    case 'phoenix':
      return '12';
    case 'realtrac':
      return '';
    case 'cincinnati':
      return '';
    case 'columbus':
      return '';
    case 'oklahomacity':
      return '';
    case 'tulsa':
      return '';
    default:
      console.log('metro ' + value + ' not found in getCityCodeOfMetro()');
  }
};

export const cityCodeBasedZipCode = (zipcode) => {
  const city = cityBasedZipCode(zipcode);
  switch (city) {
    case 'dallas':
      return '01';
    case 'houston':
      return '02';
    case 'atlanta':
      return '03';
    case 'austin':
      return '04';
    case 'stellar':
      return '05';
    case 'nashville':
      return '06';
    case 'sanantonio':
      return '07';
    case 'carolina':
      return '08';
    case 'minneapolis':
      return '10';
    case 'detroit':
      return '11';
    case 'realtrac':
      return '06';
    case 'phoenix':
      return '12';
    case 'cincinnati':
      return '';
    case 'columbus':
      return '';
    case 'oklahomacity':
      return '';
    case 'tulsa':
      return '';
    default:
      console.log('ZipCode not found in cityCodeBasedZipCode()');
  }
};

export const getMetroNameFromCityCode = (cityCode) => {
  const metroObject = allMetrosWithMLSData.find(
    (item) => item.cityCode === cityCode,
  );
  if (metroObject) {
    return metroObject.metro;
  } else {
    console.log('cityCode not found in getMetroNameFromCityCode()');
    return '';
  }
};

export const getMetroNameFromCityCodeForBatchImgAPI = (cityCode) => {
  const metroObject = allMetrosWithMLSData.find(
    (item) => item.cityCode === cityCode,
  );
  if (metroObject) {
    return metroObject.imgFolder;
  } else {
    console.log(
      'cityCode not found in getMetroNameFromCityCodeForBatchImgAPI()',
    );
    return '';
  }
};

export const getMetroAbbreviation = (cityCode) => {
  const metro = cityCodes.find((item) => item.value === cityCode);
  if (metro) {
    const metroAbbreviation = metro.abbrev;
    if (!metroAbbreviation) {
      console.log(
        'metroAbbreviation not found in getMetroAbbreviation()',
        cityCode,
      );
      return '';
    }
    return metroAbbreviation;
  } else {
    return '';
  }
};

export const getMetroNameForParam = (MLSData, isRealtrac, isPriceHistory) => {
  if (MLSData && Object.hasOwn(MLSData, 'metro') && MLSData.metro) {
    // if metro field is available in MLS data, use it
    const metroName = convertMetroNameForParam(MLSData.metro, isRealtrac, isPriceHistory);
    return metroName;
  } else if (MLSData && MLSData?.cityCode) {
    // if metro field is not available in MLS data, use city code to find metro
    const cityCode = MLSData.cityCode || MLSData.city_code || MLSData.citycode;
    const metroObject = allMetrosWithMLSData.find(
      (item) => item.cityCode === cityCode,
    );
    if (metroObject) {
      const metroName = convertMetroNameForParam(metroObject.metro, isRealtrac, isPriceHistory);
      return metroName;
    } else {
      console.log('metro not found in getMetroNameForParam()', MLSData);
      return '';
    }
  } else if (
    MLSData &&
    (MLSData?.zipCode ||
      MLSData?.zipcode ||
      MLSData?.postalCode ||
      MLSData?.postal_code)
  ) {
    const ZIPCode =
      MLSData.zipCode ||
      MLSData.zipcode ||
      MLSData.postalCode ||
      MLSData.postal_code;
    // if metro field is not available in MLS data, use zipcode to find metro
    const metroObject = getCurrentMetroObjectViaZIPCode(ZIPCode);
    if (metroObject) {
      const metroName = convertMetroNameForParam(metroObject.metro, isRealtrac, isPriceHistory);
      return metroName;
    } else {
      console.log('metro not found in getMetroNameForParam()', MLSData);
      return '';
    }
  } else {
    console.log('metro not found in getMetroNameForParam()', MLSData);
    return '';
  }
};

export const getCurrentMetroObjectViaZIPCode = (ZIPCode) => {
  if (ZIPCode) {
    const zipCodeFirst3Digits = Number(ZIPCode.substring(0, 3));
    const metroObject = allMetrosWithMLSData.find((item) =>
      item.zipcode.includes(zipCodeFirst3Digits),
    );
    // console.log('metroObject', metroObject);
    if (metroObject) {
      return metroObject;
    } else {
      console.log(
        'metro not found in getCurrentMetroObjectViaZIPCode()',
        ZIPCode,
      );
      return '';
    }
  } else {
    console.log(
      'metro not found in getCurrentMetroObjectViaZIPCode()',
      ZIPCode,
    );
    return '';
  }
};

export const convertMetroNameForParam = (metro, isRealtrac, isPriceHistory) => {
  switch (metro) {
    case 'North Carolina':
      return 'carolina';
    case 'San Antonio':
      return 'sanantonio';
    case 'Nashville':
      return isRealtrac ? 'realtrac' : 'nashville';
    case 'Oklahoma City':
      return 'oklahoma';
    default:
      if (isPriceHistory) {
        return metro;
      } else if (metro.toLowerCase().trim().includes(' ')) {
        return metro.toLowerCase().trim().split(' ').join('_');
      } else {
        return metro.toLowerCase();
      }
  }
};

export const getMetroNameForParamViaZIPCode = (ZIPCode, isRealtrac) => {
  const metroObject = getCurrentMetroObjectViaZIPCode(ZIPCode);
  if (metroObject && metroObject.metro) {
    const metroName = convertMetroNameForParam(metroObject.metro, isRealtrac);
    return metroName;
  } else {
    console.log('metro not found in getMetroNameForParamViaZIPCode()', ZIPCode);
    return '';
  }
};

export const setPropertyOwnerColor = (property) => {
  if (property['owner_occupied_sl'] === 'Yes') {
    property.ownerColor = OWNER_COLOR.ownerOccupiedColor;
  } else {
    if (property.institution === 'AH4R') {
      property.ownerColor = OWNER_COLOR.AH4RColor;
    } else if (property.institution === 'Cerberus') {
      property.ownerColor = OWNER_COLOR.cerberusColor;
    } else if (property.institution === 'Invitation Homes') {
      property.ownerColor = OWNER_COLOR.invitationHomes;
    } else if (property.institution === 'Progress Residential') {
      property.ownerColor = OWNER_COLOR.progressResColor;
    } else if (property.institution === 'Other') {
      property.ownerColor = OWNER_COLOR.othersColor;
    } else {
      property.ownerColor = OWNER_COLOR.momAndPopColor;
    }
  }
  return property;
};

export const setHOAFeeColor = (property) => {
  switch (true) {
    case property.hoa_fees >= 1001:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange5Color;
      break;
    case property.hoa_fees >= 601:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange4Color;
      break;
    case property.hoa_fees >= 201:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange3Color;
      break;
    case property.hoa_fees > 0:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange2Color;
      break;
    default:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange1Color;
  }
  return property;
};

/**
 * Checks if a coordinate is within a specified distance
 * @param {Array}  coordinateA  Coordinates of a point
 * @param {Array}  coordinateB  Coordinates of a point
 * @param {number} distance     The distance to check for in meters
 * @return {boolean}  True if distance between two points are within distance
 */
export const isWithinDistance = ({ coordinateA, coordinateB, distance }) => {
  const pointA = point(coordinateA);
  const pointB = point(coordinateB);
  const options = { units: 'kilometers' };

  const measurement = turf_distance(pointA, pointB, options) * 1000;

  return measurement < distance;
};

export const getMetroLabel = (cityCode) => {
  const metroLabelArray = cityCodes.filter((item) => item.value === cityCode);
  // console.log('metroLabelArray', metroLabelArray);
  if (metroLabelArray.length > 0) {
    const metroLabel = metroLabelArray[0].label;
    return metroLabel;
  } else {
    return '';
  }
};

export const getTimeZoneIdentifier = (citycode) => {
  const metro = cityCodes.find((item) => item.value === citycode);
  if (metro) {
    // console.log('tz', metro.tz);
    return metro.tz;
  } else {
    return '';
  }
};

export const withinSubscribedMetro = (
  response,
  userGroup,
  // metrosAllowedOnIndividualAccountLevel,
) => {
  console.log(
    'response',
    response,
    'userGroup',
    userGroup,
    // 'metrosAllowedOnIndividualAccountLevel',
    // metrosAllowedOnIndividualAccountLevel,
  );
  if (!response) return false;

  let zipcode;

  for (let i = 0; i < response.features.length; i++) {
    const feature = response.features[i];
    if (feature.id.includes('postcode')) {
      zipcode = feature.text;
      break;
    }
  }

  if (!zipcode) return false; // for when the user clicks outside the US

  // const code = Number(zipcode.substring(0, 3));

  // if (userGroup.includes('Nhimble')) {
  //   return atlantaZipCodes.includes(code) ? true : false;
  // }

  const resultCityCode = cityCodeBasedZipCode(zipcode);
  // if (userGroup.includes('demo-CMA-DFW-only')) {
  //   return metrosAllowedOnIndividualAccountLevel.includes(resultCityCode);
  // } else {
  //   return (
  //     userGroupAccess[userGroup[0]] &&
  //     userGroupAccess[userGroup[0]].metro.includes(resultCityCode)
  //   );
  // }
  // for moms and pops investors that each is in a unique userGroup
  if (Object.hasOwn(userGroupAccess, userGroup[0])) {
    return true;
  }
  return (
    userGroupAccess[userGroup[0]] &&
    userGroupAccess[userGroup[0]].metro.includes(resultCityCode)
  );
};

export const polygonWithinMetro = async (polygonData, userGroup) => {
  if (polygonData && polygonData.length > 0) {
    const polyGeoJSON = polygon([polygonData]);
    const center = turf_centerOfMass(polyGeoJSON);

    const lng = center.geometry.coordinates[0];
    const lat = center.geometry.coordinates[1];
    const response = await (
      await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=pk.eyJ1Ijoic3hieGNoZW4iLCJhIjoiYjRhNWMyMmI0NzVjZjEzZjYyZGUzZDM0NmFhZTcyNjEifQ.-T2S1ZeAEBGxjC4rC0CZzA`,
      )
    ).json();

    return withinSubscribedMetro(response, userGroup);
  }
  return false;
};
