/**
 * Calculates the IRR (Internal Rate of Return) for a series of cash flows.
 * @param cashFlows - An array of cash flows where:
 *   - cashFlows[0] is the initial investment (negative number),
 *   - subsequent elements are periodic returns or final sale proceeds.
 * @param guess - Initial guess for IRR, default is 0.1 (10%)
 * @returns IRR as a decimal (e.g., 0.12 means 12%)
 */

export function calculateIRR(cashFlows: number[], guess: number = 0.1): number | null {
  const maxIterations = 1000;
  const precision = 1e-6;

  let rate = guess;

  for (let iter = 0; iter < maxIterations; iter++) {
    let npv = 0;
    let derivative = 0;

    for (let t = 0; t < cashFlows.length; t++) {
      const cf = cashFlows[t];
      npv += cf / Math.pow(1 + rate, t);
      if (t > 0) {
        derivative -= t * cf / Math.pow(1 + rate, t + 1);
      }
    }

    const newRate = rate - npv / derivative;
    if (Math.abs(newRate - rate) < precision) {
      return parseFloat(newRate.toFixed(6));
    }
    rate = newRate;
  }

  return null; // IRR did not converge
}

// example
// const cashFlows = [-100000, 10000, 12000, 15000, 15000, 60000]; // Investment + 5 years of income + resale
// const irr = calculateIRR(cashFlows);

// if (irr !== null) {
//   console.log(`IRR: ${(irr * 100).toFixed(2)}%`);
// } else {
//   console.log("IRR calculation did not converge.");
// }