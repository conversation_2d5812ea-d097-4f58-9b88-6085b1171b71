export declare const handleNumberError: (num: string | number | null | undefined) => string | number;
export declare const formatter: (num: string | number | null | undefined, decimal?: number) => string;
export declare const formatterKM: (num: number) => string;
export declare const combineFormatter: (num: number) => string;
export declare const XIRR: (values: Array<{Date: string | Date, Flow: number}>, guess?: number) => number | null;
export declare const PMT: (rate: number, nper: number, pv: number, fv?: number, type?: number) => number; 