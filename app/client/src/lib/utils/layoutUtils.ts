interface ShowBannerParams {
  /** The width of the container in pixels */
  containerWidth: number;
  /** The index of the current property in the list */
  index: number;
  /** The total padding of the container (default: 32px for 16px each side) */
  containerPadding?: number;
}

/**
 * Determines whether to show a banner after a specific property based on container width
 *
 * @param containerWidth - The width of the container in pixels
 * @param index - The index of the current property in the list
 * @param containerPadding - The total padding of the container (default: 32px for 16px each side)
 * @returns boolean - Whether to show the banner after this property
 *
 * With box-sizing: border-box, we add container padding to breakpoints
 * to properly match @container query behavior.
 */

export const shouldShowBanner = ({
  containerWidth,
  index,
  containerPadding = 32,
}: ShowBannerParams): boolean => {
  const lgBreakpoint = 512; // @lg
  const xlBreakpoint = 768; // @3xl

  // Add padding to breakpoints to match container query behavior
  const lgWithPadding = lgBreakpoint + containerPadding;
  const xlWithPadding = xlBreakpoint + containerPadding;

  return (
    (containerWidth < lgWithPadding && index === 0) || // 1 column layout
    (containerWidth >= lgWithPadding &&
      containerWidth < xlWithPadding &&
      index === 1) || // 2 column layout
    (containerWidth >= xlWithPadding && index === 2) // 3 column layout
  );
};
