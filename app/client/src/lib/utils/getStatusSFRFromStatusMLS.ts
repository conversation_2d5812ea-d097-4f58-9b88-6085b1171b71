import { MLSListingStatusType, SFRListingStatusType, HotPadsListingStatusType } from "@/components/PropertyDetails/TabComps/utils/types";

export const getStatusSFRFromStatusMLS = (statusMLS: MLSListingStatusType): SFRListingStatusType => {
  switch (statusMLS) {
    case 'Closed':
      return 'Closed';
    case 'Active':
      return 'Active';
    case 'Pending':
      return 'Pending';
    case 'status':
      return 'exists';
    default:
      return 'Closed';
  }
};

export const getStatusHotPadsFromStatusMLS = (statusMLS: MLSListingStatusType): HotPadsListingStatusType => {
  switch (statusMLS) {
    case 'Closed':
      return false;
    case 'Active':
      return true;
    case 'status':
      return 'exists';
    default:
      return null;
  }
};
