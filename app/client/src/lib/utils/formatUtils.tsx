import type { MarketConditionData } from "../../hooks/useMarketConditionData";
import { CompListingProps } from "../../types/PropertyDetailPage.types"

export const formatPrice = (price: string | number | null | undefined): string => {
  if (price === undefined || price === null || price === '') return '';

  let priceString = typeof price === 'string' ? price : String(price);

  // Remove '$' if present
  if (priceString.includes('$')) {
    priceString = priceString.replace(/\$/g, '');
  }

  const numericPrice = parseFloat(priceString);

  // Check if parsing resulted in a valid number
  if (isNaN(numericPrice)) {
    return ''; // Or handle invalid input as appropriate
  }

  if (numericPrice === Infinity || numericPrice === -Infinity) {
    return 'N/A';
  }

  // Use 'en-US' locale and options to control decimal places
  // +0 because Math.round(-0.5) returns -0, +0 will convert -0 to 0
  return `$${(Math.round(numericPrice) + 0).toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
};

export const formatPercentage = (value: string | number | undefined | null, decimalPlaces: number = 1): string => {
  if (!value) return '';

  if (value === Infinity || value === -Infinity) {
    return 'N/A';
  }

  const numericValue = typeof value === 'string' ? parseFloat(value) : value;
  // +0 because Math.round(-0.5) returns -0, +0 will convert -0 to 0
  return `${(Math.round(numericValue * (10 ** (decimalPlaces + 2))) / (10 ** decimalPlaces) + 0).toFixed(decimalPlaces)}%`;
};

export const formatDate = (dateString: string) => {
  try {
    // Check if string matches YYYY-MM-DD format
    const isoFormatRegex = /^(\d{4})-(\d{2})-(\d{2})$/;
    const match = dateString.match(isoFormatRegex);

    if (match) {
      // Direct parsing of YYYY-MM-DD format to avoid timezone issues
      const year = match[1].slice(-2); // Get last 2 digits of year
      const month = match[2];
      const day = match[3];
      return `${month}/${day}/${year}`;
    }

    // For other formats, use the Date object
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      // Use local date parts to avoid timezone shifts
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const year = String(date.getFullYear()).slice(-2);
      return `${month}/${day}/${year}`;
    }

    // Return the original string if we can't parse it
    return dateString;
  } catch {
    return dateString; // Fallback to original string if any error occurs
  }
};
// TODO: Remove this function once all components are updated to use formatPercentage2
export const formatPercentage2 = (
  value: number | string | undefined | null,
  isDecimal: boolean = false
): string => {
  if (value === undefined || value === null || value === '') {
    return 'N/A';
  }

  // Convert to number if it's a string
  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  // Check if it's a valid number
  if (isNaN(numValue)) {
    return 'N/A';
  }

  // If the value is already a percentage (like 36.455), don't divide by 100
  // If it's a decimal (like 0.36455), multiply by 100
  const percentageValue = isDecimal ? numValue * 100 : numValue;

  // Round to 2 decimal places and add % symbol
  return `${percentageValue.toFixed(1)}%`;
};

export const formatNumber = (value: string | number | undefined): string => {
  if (!value) return '';
  const numericValue = typeof value === 'string' ? parseFloat(value) : value;
  return Math.round(numericValue).toLocaleString('en-US', { maximumFractionDigits: 0 });
};

export const calculateMedianRent = (
  listings: CompListingProps[],
  selectedListingAddresses: string[]
): number | string => {
  const selectedRents = listings
    .filter(listing => selectedListingAddresses.includes(listing.address))
    .map(listing => typeof listing.rent === 'string' ? parseFloat(listing.rent.replace(/[^0-9.-]+/g, "")) : listing.rent)
    .filter(rent => typeof rent === 'number' && !isNaN(rent))
    .sort((a, b) => a - b);

  if (selectedRents.length === 0) {
    return 'N/A';
  }

  const mid = Math.floor(selectedRents.length / 2);
  return selectedRents.length % 2 !== 0
    ? selectedRents[mid]
    : (selectedRents[mid - 1] + selectedRents[mid]) / 2;
};

// TODO: Not sure original data format for this function, so make sure meters or any other input is a number
/**
 * Converts a distance from meters to miles and formats with 1 decimal place
 * 
 * @param meters - Distance in meters
 * @returns Formatted distance in miles (e.g., "2.3 mi")
 */
export const formatDistanceToMiles = (
  meters: number | undefined | null
): string => {
  if (meters === undefined || meters === null) return 'N/A';

  // Convert meters to miles (1 meter = 0.000621371 miles)
  const miles = meters * 0.000621371;

  // Format to 1 decimal place
  return `${miles.toFixed(1)} mi`;
};


export const formatMiles = (
  miles: number | undefined | null
): string => {
  if (miles === undefined || miles === null) return 'N/A';
  return `${miles.toFixed(1)} mi`;
};


export function convertCapRateToNumber(capRate: string): number {
  if (capRate === null || capRate === undefined || capRate === '') {
    return 0;
  }

  const cleanedValue = capRate.replace('%', '').trim();
  const numericValue = parseFloat(cleanedValue);

  if (isNaN(numericValue)) {
    return 0;
  }

  // If the value is greater than 1, assume it's a percentage and divide by 100
  return numericValue > 1 ? numericValue / 100 : numericValue;
}

export function removeAllDecimal(value: string | undefined): string {
  if (typeof value !== "string" || value.trim() === "") return "";
  const num = parseFloat(String(value));
  if (isNaN(num)) return "";
  return Math.trunc(num).toString();
}

export function formatTaxRateToPercent(
  value: string | number | null | undefined
): string {
  if (value === null || value === undefined || value === "") return "";
  const num = typeof value === "number" ? value : parseFloat(value);
  if (isNaN(num)) return "";
  const percentValue = num > 0.1 ? num / 100 : num;
  return `${(percentValue * 100).toFixed(2)}%`;
}

export const getFormattedAddress = (
  addressComponents: {
    streetAddress?: string | null;
    city?: string | null;
    state?: string | null;
    postalCode?: string | null;
  }
): string => {
  const { streetAddress, city, state, postalCode } = addressComponents;

  const firstLine = streetAddress || '';

  const locationParts = [];
  if (city) locationParts.push(city);
  if (state) locationParts.push(state);
  if (postalCode) locationParts.push(postalCode);

  const locationLine = locationParts.join(', ');

  // Combine parts, filtering out empty strings
  return [firstLine, locationLine]
    .filter(part => part && part.length > 0)
    .join(', ');
};

export const formatSegmentName = (input: string): string => {
  try {
    if (!input || !input.includes('_-_')) {
      throw new Error('Invalid input format');
    }

    const [prefix, ...descriptionParts] = input.split('_-_');
    const description = descriptionParts.join('_-_');

    return `${prefix.toUpperCase()} - ${description
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')}`;
  } catch (error) {
    console.error('Error formatting segment name:', error);
    return input;
  }
}

export const formatMetroName = (metroName: string | undefined): string => {
  if (!metroName) return '';
  if (metroName === 'Sherman-Denison, TX Metro') {
    return 'Dallas-Fort Worth-Arlington, TX';
  }
  return metroName.replace(/ Metro$/, '');
}

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const getFileType = (fileName: string): string => {
  if (fileName.toLowerCase().endsWith('.pdf')) return 'PDF';
  if (fileName.toLowerCase().match(/\.(jpg|jpeg|png|gif)$/)) return 'Image';
  if (fileName.toLowerCase().endsWith('.xlsx')) return 'Excel';
  return 'Other';
};

export const calculatePercentage = (value: number, total: number): string => {
  if (total === 0) return "0%";
  return `${((value / total) * 100).toFixed(0)}%`;
};

export const formatPercentageRoundUp = (
  value: number
): string => {
  if (value === undefined || value === null) {
    return '';
  }

  // Check if it's a valid number
  if (isNaN(value)) {
    return '';
  }

  // Round up to the nearest whole number
  const roundedValue = Math.round(value);

  // Return the formatted percentage
  return `${roundedValue}%`;
};

export const createChartData = (metricData: MarketConditionData[] | undefined, metricType: string) => {
  if (!metricData || !metricData.length) return [];

  // Find the object with the selected metric type
  const metric = metricData.find(item => item.type === metricType);
  if (!metric) return [];

  // Format value based on metric type and ensure it's not null
  const formatValue = (value: number | null | undefined): number => {
    if (value === null || value === undefined) return 0; // Default to 0 for null/undefined values

    if (metricType.includes('_median_psf')) {
      // Format price per square foot with 2 decimal places
      return parseFloat(value.toFixed(2));
    } else if (metricType.includes('_price')) {
      // Format prices: round to nearest integer
      return Math.round(value);
    } else if (metricType === 'months_of_inventory') {
      // Format months of inventory: 2 decimal places
      return parseFloat(value.toFixed(2));
    } else {
      // For other metrics (active, closed, median_dom), round to nearest integer
      return Math.round(value);
    }
  };

  // Transform the data into the format needed by VerticalBarChart with formatted values
  return [
    { name: "Area of Interest", value: formatValue(metric.aoi) },
    { name: "ZIP Code", value: formatValue(metric.zipcode) },
    { name: "School District", value: formatValue(metric.schoolDistrict) },
    { name: "County", value: formatValue(metric.county) },
    { name: "Metro", value: formatValue(metric.metro) }
  ];
};

export const formatDisplayValue = (
  value: number,
  metricType: string,
  valuePrefix: string = '',
  valueSuffix: string = ''
): string => {
  let formattedValue: string;

  if (metricType.includes('_median_psf')) {
    // Price per square foot: show 2 decimal places
    formattedValue = value.toFixed(2);
  } else if (metricType.includes('_price')) {
    // Prices: use toLocaleString for thousands separators
    formattedValue = value.toLocaleString('en-US');
  } else if (metricType === 'months_of_inventory') {
    // Months of inventory: show 2 decimal places
    formattedValue = value.toFixed(2);
  } else {
    // Other metrics: simple toString
    formattedValue = value.toString();
  }

  return `${valuePrefix}${formattedValue}${valueSuffix}`;
};

export const formatYearMonth = (dateString: string | null | undefined): string => {
  if (!dateString) return '';

  try {
    const [year, month] = dateString.split('-').map(part => parseInt(part, 10));

    if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
      return dateString;
    }

    const monthNames = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];

    return `${monthNames[month - 1]}, ${year}`;
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

export const formatPhoneNumber = (phoneNumber: string | number | null | undefined): string => {
  if (!phoneNumber) return '';

  const cleaned = String(phoneNumber).replace(/\D/g, '');

  if (cleaned.length !== 10) {
    return String(phoneNumber);
  }

  return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
};