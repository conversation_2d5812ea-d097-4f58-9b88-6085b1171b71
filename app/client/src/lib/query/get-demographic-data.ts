import { getUserToken } from "@/lib/utils/auth";

export interface DemographicDataType {
  raw_fld_zone: string;
  fld_zone: string;
  medianhhincome: string;
  fiveyearpopgrowth: number;
  fiveyearincomegrowth: number;
  bachelorsandabove: number;
  crime_score: number;
  crime: number;
  white: number;
  black: number;
  asian: number;
  hispanic: number;
  school: {
    elem: {
      score_2022: number;
      score_2023: number;
      name: string;
      url: string;
    };
    high: {
      score_2022: number;
      score_2023: number;
      name: string;
      url: string;
    };
    middle: {
      score_2022: number;
      score_2023: number;
      name: string;
      url: string;
    };
  };
}

export const getDemographicData = async ({
  lat,
  lng
}: {
  lat: number;
  lng: number;
}): Promise<DemographicDataType[]> => {
  try {
    const token = await getUserToken('access');
    const response = await fetch(`/api/cma/prod/demographic?lat=${lat}&lng=${lng}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    if (!response.ok) {
      console.error(`Failed to fetch demographic data: ${response.status} ${response.statusText}`);
      return []; // Return empty array instead of throwing
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching demographic data:", error);
    return []; // Return empty array on any error
  }
};