import { getUserToken } from "@/lib/utils/auth";
import { sfrAndHotPadsCompData, SFRListingStatusType } from "@/components/PropertyDetails/TabComps/utils/types";

export const getSFRCompsData = async ({
  exists, // status
  startDate,
  endDate,
  distance,
  lng,
  lat,
}: {
  exists: SFRListingStatusType;
  startDate: string;
  endDate: string;
  distance: number;
  lng: number;
  lat: number;
}): Promise<sfrAndHotPadsCompData[]> => {
  const token = await getUserToken('access');
  const response = await fetch(`/api/cma/prod/realestate?exists=${exists}&startDate=${startDate}&endDate=${endDate}&distance=${distance}&lng=${lng}&lat=${lat}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch SFR comps data: ${response.status} ${response.statusText}`);
  }

  return response.json();
};