import { getUserToken } from "@/lib/utils/auth";

export interface ParcelDataType {
  fips: string | null;
  apn: string | null;
  placekey: string;
  year_built: number;
  stories: number | null;
  beds_count: number;
  baths: number;
  partial_baths_count: number | null;
  total_area_sq_ft: number;
  rent: number | null;
  sales: number | null;
  standardized_land_use_type: string | null;
  value: number | null;
  low: number | null;
  high: number | null;
  low_ratio: number | null;
  high_ratio: number | null;
  score_crime: number | null;
  elementary: string | null;
  middle: string | null;
  highschool: string | null;
  income: number | null;
  fiveyeargrowth: number | null;
  incomegrowth: number | null;
  fld_zone: string | null;
  legal_description: string | null;
  subdivision: string | null;
  hoa_fees: number | null;
  owner_occupied_sl: string | null;
  institution: string | null;
  marker: string | null;
  pbaplus: string | null;
  tax_rate_percent: number | null;
  tax_amount: number | null;
  total_value: number | null;
  deed_last_sale_date: string | null;
  deed_last_sale_price: number | null;
  rate: number | null;
  area_acres: number | null;
  deed_last_sale_data: string | null;
}

export const getParcelData = async ({
  placekey,
  lat,
  lng,
  streetnum,
}: {
  placekey: string;
  lat: number;
  lng: number;
  streetnum: string;
}): Promise<ParcelDataType[]> => {
  try {
    const token = await getUserToken('access');
    const response = await fetch(`/api/cma/prod/parcel?placekey=${placekey}&lat=${lat}&lng=${lng}&streetnum=${streetnum}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    if (!response.ok) {
      console.error(`Failed to fetch parcel data: ${response.status} ${response.statusText}`);
      return [];
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching parcel data:", error);
    return [];
  }
};