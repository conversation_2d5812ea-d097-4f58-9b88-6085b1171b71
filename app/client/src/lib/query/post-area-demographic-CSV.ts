import { getUserToken } from "@/lib/utils/auth";
import { CSVRequestParams } from "./post-major-employers-CSV";

const serverType = "prod";

interface CustomRequestParams {
  method: string;
  headers?: Record<string, string>;
  data?: unknown;
}

const customRequest = async (url: string, parameter: CustomRequestParams) => {
  try {
    const accessToken = await getUserToken('access');
    
    const fetchOptions: RequestInit = {
      method: parameter.method,
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        ...parameter.headers
      }
    };
    
    if (parameter.data) {
      fetchOptions.body = JSON.stringify(parameter.data);
    }
    
    const response = await fetch(url, fetchOptions);
    
    if (!response.ok) {
      console.error(`Request failed: ${response.status} ${response.statusText}`);
      return null;
    }

    // For CSV endpoints, return text content
    if (url.includes('/csv/')) {
      return await response.text();
    }
    
    // Otherwise try to parse as JSON
    return await response.json();
    
  } catch (error) {
    console.error("Error in customRequest:", error);
    return null;
  }
};


export const postAreaDemographicCSV = async (params: CSVRequestParams) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/csv/area-demographic`,
    // `http://localhost:9003/api/v1/csv/building-permit`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};