import { getUserToken } from "@/lib/utils/auth";
import { underwritingSource, LennarSinglePropertyDataType } from "@/lib/utils/types";


export const getSubmittedProperties = async (source: underwritingSource): Promise<LennarSinglePropertyDataType[]> => {
  try {
    const token = await getUserToken('access');
    const response = await fetch(`/api/underwriting/prod/history/properties?source=${source}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch properties: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // remove properties with no subjectProperty in payload
    const propertiesWithSubjectProperty = data.filter((property: LennarSinglePropertyDataType) => property?.payload?.subjectProperty);

    // TEMP - when /properties api duplicates issue is fixed, remove this deduping
    // properties are duplicated
    // properties with the same property_id have different underwriting_decision_ids
    // only keep the property with the highest underwriting_decision_id
    function dedupeItems(items: LennarSinglePropertyDataType[]): LennarSinglePropertyDataType[] {
      const map = new Map<number, LennarSinglePropertyDataType>();
    
      for (const item of items) {
        const existing = map.get(item.property_id);
        if (!existing || item.underwriting_decision_id > existing.underwriting_decision_id) {
          map.set(item.property_id, item);
        }
      }
    
      return Array.from(map.values());
    }

    return dedupeItems(propertiesWithSubjectProperty);
    
  } catch (error) {
    console.error("Error fetching properties:", error);
    throw error;
  }
};