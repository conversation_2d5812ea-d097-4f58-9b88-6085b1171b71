import { getUserToken } from "@/lib/utils/auth";

export type underwritingSource = "onmarket" | "offmarket";

export interface LennarSinglePropertyDataType {
  buybox_id: number;
  city: string;
  citycode: string;
  decision: string;
  filter_results: any[];
  full_address: string;
  notes: string;
  payload: any;
  placekey: string;
  postal_code: string;
  property_id: number;
  register_token: string;
  rets_id: string;
  source: underwritingSource;
  state: string;
  step: number;
  timestamp: string;
  underwriting_decision_id: number;
  underwriting_id: number;
  valid_for_min: number;
}

export const getSubmittedProperties = async (source: underwritingSource): Promise<LennarSinglePropertyDataType[]> => {
  try {
    const token = await getUserToken('access');
    const response = await fetch(`/api/underwriting/prod/history/properties?source=${source}`, {
    // const response = await fetch(`/api/underwriting/exp/properties?source=${source}&api-version=v2`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch properties: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // remove properties with no subjectProperty in payload
    const propertiesWithSubjectProperty = data.filter((property: LennarSinglePropertyDataType) => property?.payload?.subjectProperty);

    // TEMP - when /properties api duplicates issue is fixed, remove this deduping
    // properties are duplicated
    // properties with the same property_id have different underwriting_decision_ids
    // only keep the property with the highest underwriting_decision_id
    function dedupeItems(items: LennarSinglePropertyDataType[]): LennarSinglePropertyDataType[] {
      const map = new Map<number, LennarSinglePropertyDataType>();
    
      for (const item of items) {
        const existing = map.get(item.property_id);
        if (!existing || item.underwriting_decision_id > existing.underwriting_decision_id) {
          map.set(item.property_id, item);
        }
      }
    
      return Array.from(map.values());
    }

    return dedupeItems(propertiesWithSubjectProperty);
    
  } catch (error) {
    console.error("Error fetching properties:", error);
    throw error;
  }
};