import { getUserToken } from "@/lib/utils/auth";
import { IntelligentCompingResponseDataType } from "@/lib/utils/types";

export interface IntelligentCompingRequestBodyType {
  address: string;
  city: string;
  state: string;
  zip_code: string;
  latitude: string;
  longitude: string;
  beds: string;
  baths: string;
  sqft: string;
  yearbuilt: string;
}

export const getIntelligentCompsData = async (requestBody: IntelligentCompingRequestBodyType): Promise<IntelligentCompingResponseDataType | null> => {
  try {
    const token = await getUserToken('access');
    const response = await fetch('/api/elixir/prod/intelligent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(requestBody),
    });

    const responseData = await response.json();

    if (responseData.message !== 'Single property processed successfully') {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // round off adjusted_rent and adjusted_sales to integer
    responseData.data.results.adjusted_rent.adjusted_value = Math.round(responseData.data.results.adjusted_rent.adjusted_value);
    responseData.data.results.adjusted_sales.adjusted_value = Math.round(responseData.data.results.adjusted_sales.adjusted_value);

    return responseData;
  } catch (error) {
    console.error("Error fetching intelligent comps data:", error);
    return null;
  }
};