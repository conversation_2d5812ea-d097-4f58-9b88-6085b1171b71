import { BulkSubmissionUserInfo, CartItem, BulkOfferSubmission } from '../../types/cartTypes';
import { SimplifiedOffer } from '../../types/offerTypes';
import { submitOffer } from './submit-offer';
import { sendOfferData } from './send-offer-data';
import { getUserToken } from '../utils/auth';

// const API_BASE_URL = 'http://localhost:9003/api/v1/lennar';
const API_BASE_URL = '/api/sbs/prod/api/v1/lennar';

// Helper function to convert CartItem to SimplifiedOffer format
function cartItemToSimplifiedOffer(
  cartItem: CartItem, 
  userInfo: BulkSubmissionUserInfo
): SimplifiedOffer {
  // Use metadata stored in cart item
  const specPrice = cartItem.specPrice;
  const bidPrice = specPrice ? parseFloat(specPrice) : (cartItem.offerPrice || 0);
  
  const specNumber = cartItem.homesiteNumber || '';
  const communityName = cartItem.communityName || '';
  const floorplanName = cartItem.floorplanName || '';
  const divisionName = cartItem.divisionName || '';
  const originalRent = cartItem.originalRent || '';
  const buyerFinalRent = cartItem.buyerFinalRent || '';

  return {
    propertyId: cartItem.propertyId,
    cartItemId: cartItem.id,
    address: cartItem.propertyAddress,
    email: userInfo.email,
    phone: userInfo.phone || '',
    firstName: userInfo.firstName || '',
    lastName: userInfo.lastName || '',
    offerPrice: cartItem.offerPrice || 0,
    listPrice: specPrice || '',
    comments: cartItem.comments || '',
    userRole: userInfo.userRole,
    bid_price: bidPrice,
    spec_number: specNumber,
    community_name: communityName,
    homesite_number: specNumber, // homesite number is the same as spec_number
    floorplan_name: floorplanName,
    division_name: divisionName,
    original_rent: originalRent,
    buyer_final_rent: buyerFinalRent,
    ...(userInfo.userRole === 'agent' 
      ? { compensation: userInfo.compensation }
      : { financingRequired: userInfo.financingRequired, PMRequired: userInfo.PMRequired }
    ),
  };
}

// Submit bulk offers using the new bulk API endpoint
export async function submitBulkOffersAPI(
  confirmedItems: CartItem[],
  userInfo: BulkSubmissionUserInfo
): Promise<BulkOfferSubmission> {
  const token = await getUserToken('access');
  if (!token) {
    throw new Error("User not authenticated.");
  }

  if (confirmedItems.length === 0) {
    throw new Error("No properties to submit");
  }

  // Validate that all items have offer prices
  const itemsWithoutPrices = confirmedItems.filter(item => !item.offerPrice || item.offerPrice <= 0);
  if (itemsWithoutPrices.length > 0) {
    throw new Error('All selected properties must have valid offer prices');
  }

  // Convert cart items to offer format
  const offers = confirmedItems.map(item => {
    return cartItemToSimplifiedOffer(item, userInfo);
  });

  // Create bulk submission payload for the bulk offers endpoint
  const bulkPayload = {
    offer: offers,
    userInfo: {
      email: userInfo.email,
      phone: userInfo.phone,
      firstName: userInfo.firstName,
      lastName: userInfo.lastName,
      userRole: userInfo.userRole,
      comments: userInfo.comments,
      financingRequired: userInfo.financingRequired,
      PMRequired: userInfo.PMRequired
    }
  };

  try {
    // Send bulk format to the correct bulk submission endpoint
    const response = await fetch(`${API_BASE_URL}/cart/submit-lennar-bulk-offers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(bulkPayload)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to submit bulk offers: ${response.status}`);
    }

    const result = await response.json();
    
    // Send additional offer data for each property after successful bulk submission
    for (const item of confirmedItems) {
      try {
        await sendOfferData({
          source: 'offmarket',
          propertyId: item.propertyId.toString(),
          body: {
            decision: 'approve',
            notes: '',
            payload: {
              offerForm: {
                firstName: userInfo.firstName || '',
                lastName: userInfo.lastName || '',
                offerPrice: item.offerPrice || 0,
                comments: item.comments || '',
                questions: item.comments || '',
                financing: userInfo.financingRequired ? 'yes' : 'no',
                PM: userInfo.PMRequired ? 'yes' : 'no',
                email: userInfo.email,
                phone: userInfo.phone || '',
                userRole: userInfo.userRole,
              },
              proforma: {
                buyAndHold: item?.payload?.proforma || {},
              },
              subjectProperty: {
                propertyId: item.propertyId,
                address: item.propertyAddress,
                ...item?.payload?.subjectProperty
              },
            }
          }
        });
      } catch (sendError) {
        console.warn(`Failed to send additional offer data for property ${item.propertyId}:`, sendError);
        // Don't fail the whole submission for this
      }
    }
    
    return {
      cartId: result.cartId || 0,
      userId: result.userId || 'current-user',
      totalProperties: confirmedItems.length,
      successfulSubmissions: result.successfulSubmissions || confirmedItems.length,
      failedSubmissions: result.failedSubmissions || 0,
      submissionStatus: 'completed',
      userEmail: userInfo.email,
      userPhone: userInfo.phone,
      userFirstName: userInfo.firstName,
      userLastName: userInfo.lastName,
      userRole: userInfo.userRole,
      createdAt: new Date().toISOString(),
      completedAt: new Date().toISOString(),
    };

  } catch (error) {
    console.error('Bulk submission failed:', error);
    throw error; // Don't fall back, let the error bubble up
  }
}

// Submit offers for all confirmed cart items
export async function submitBulkOffersSequential(
  confirmedItems: CartItem[],
  userInfo: BulkSubmissionUserInfo,
  onProgress?: (completed: number, total: number, currentProperty: string) => void
): Promise<BulkOfferSubmission> {
  const token = await getUserToken('access');
  if (!token) {
    throw new Error("User not authenticated.");
  }

  const totalProperties = confirmedItems.length;
  let successfulSubmissions = 0;
  let failedSubmissions = 0;
  const results: Array<{
    propertyId: number;
    status: 'success' | 'failed';
    error?: string;
    responseId?: string;
  }> = [];

  // Process each property sequentially to avoid overwhelming the API
  for (let i = 0; i < confirmedItems.length; i++) {
    const item = confirmedItems[i];
    
    try {
      // Report progress
      onProgress?.(i, totalProperties, item.propertyAddress);
      
      // Convert cart item to offer format
      const offer = cartItemToSimplifiedOffer(item, userInfo);
      
      // Submit the offer using the existing API
      const response = await submitOffer(offer);
      
      // Try to send additional offer data if successful
      try {
        await sendOfferData({
          source: 'offmarket',
          propertyId: item.propertyId.toString(),
          body: {
            decision: 'approve',
            notes: '',
            payload: {
              offerForm: {
                firstName: userInfo.firstName || '',
                lastName: userInfo.lastName || '',
                offerPrice: item.offerPrice || 0,
                comments: item.notes || '',
                questions: item.notes || '',
                financing: userInfo.financingRequired ? 'yes' : 'no',
                PM: userInfo.PMRequired ? 'yes' : 'no',
                email: userInfo.email,
                phone: userInfo.phone || '',
                userRole: userInfo.userRole,
              },
              proforma: {
                buyAndHold: {},
              },
              subjectProperty: {
                propertyId: item.propertyId,
                address: item.propertyAddress,
              },
            }
          }
        });
      } catch (sendError) {
        console.warn('Failed to send additional offer data:', sendError);
        // Don't fail the whole submission for this
      }
      
              results.push({
          propertyId: item.propertyId,
          status: 'success',
          responseId: response.headers?.get('x-request-id') || undefined,
        });
      
      successfulSubmissions++;
      
    } catch (error) {
      console.error(`Failed to submit offer for property ${item.propertyId}:`, error);
      
      results.push({
        propertyId: item.propertyId,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      failedSubmissions++;
    }
    
    // Small delay between requests to be respectful to the API
    if (i < confirmedItems.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  // Final progress update
  onProgress?.(totalProperties, totalProperties, 'Completed');

  // Create bulk submission record
  const bulkSubmission: BulkOfferSubmission = {
    cartId: 0, // This would be set by the backend
    userId: 'current-user', // This would be set by the backend
    totalProperties,
    successfulSubmissions,
    failedSubmissions,
    submissionStatus: failedSubmissions === 0 ? 'completed' : 'completed', // Even partial success is completed
    userEmail: userInfo.email,
    userPhone: userInfo.phone,
    userFirstName: userInfo.firstName,
    userLastName: userInfo.lastName,
    userRole: userInfo.userRole,
    createdAt: new Date().toISOString(),
    completedAt: new Date().toISOString(),
  };

  // Log the submission results for debugging
  console.log('Bulk submission completed:', {
    totalProperties,
    successfulSubmissions,
    failedSubmissions,
    results,
  });

  return bulkSubmission;
}

// Submit offers in parallel (use with caution for API rate limits)
export async function submitBulkOffersParallel(
  confirmedItems: CartItem[],
  userInfo: BulkSubmissionUserInfo,
  concurrency: number = 3
): Promise<BulkOfferSubmission> {
  const totalProperties = confirmedItems.length;
  
  // Create chunks for parallel processing
  const chunks: CartItem[][] = [];
  for (let i = 0; i < confirmedItems.length; i += concurrency) {
    chunks.push(confirmedItems.slice(i, i + concurrency));
  }

  let successfulSubmissions = 0;
  let failedSubmissions = 0;

  // Process chunks sequentially, but items within chunks in parallel
  for (const chunk of chunks) {
    const chunkPromises = chunk.map(async (item) => {
              try {
          const offer = cartItemToSimplifiedOffer(item, userInfo);
          await submitOffer(offer);
          return { propertyId: item.propertyId, status: 'success' as const };
      } catch (error) {
        console.error(`Failed to submit offer for property ${item.propertyId}:`, error);
        return { 
          propertyId: item.propertyId, 
          status: 'failed' as const,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });

    const chunkResults = await Promise.all(chunkPromises);
    
    chunkResults.forEach(result => {
      if (result.status === 'success') {
        successfulSubmissions++;
      } else {
        failedSubmissions++;
      }
    });

    // Delay between chunks
    if (chunks.indexOf(chunk) < chunks.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  return {
    cartId: 0,
    userId: 'current-user',
    totalProperties,
    successfulSubmissions,
    failedSubmissions,
    submissionStatus: failedSubmissions === 0 ? 'completed' : 'completed',
    userEmail: userInfo.email,
    userPhone: userInfo.phone,
    userFirstName: userInfo.firstName,
    userLastName: userInfo.lastName,
    userRole: userInfo.userRole,
    createdAt: new Date().toISOString(),
    completedAt: new Date().toISOString(),
  };
}

// Default export uses the new bulk API with sequential fallback
export { submitBulkOffersAPI as submitBulkOffers }; 