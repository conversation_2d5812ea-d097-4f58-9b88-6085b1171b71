import { MAPBOX_TOKEN } from "@/constants";
import { getUserToken } from "@/lib/utils/auth";

const serverType = "prod";

export interface AreaDemographicDataType {
  five_year_hh_income_growth: number;
  five_year_pop_growth: number;
  median_hh_income: number;
  total_households: number;
  total_population: number;
}

export interface AreaDemographicDistanceParams {
  lat: number;
  lng: number;
  distance: number;
}

export interface AreaDemographicDriveTimeParams {
  body: {
    lat: number;
    lng: number;
    drivetime: number;
  };
}

const customRequest = async (
  url: string, 
  options: { 
    method: string; 
    data?: AreaDemographicDistanceParams | AreaDemographicDriveTimeParams['body'];
  }
): Promise<AreaDemographicDataType | null> => {
  try {
    const token = await getUserToken('access');
    
    const fetchOptions: RequestInit = {
      method: options.method,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
    
    if (options.data) {
      fetchOptions.body = JSON.stringify(options.data);
    }
    
    const response = await fetch(`/${url}`, fetchOptions);
    
    if (!response.ok) {
      console.error(`Failed to fetch area demographic data: ${response.status} ${response.statusText}`);
      return null;
    }

    const data = await response.json();
    return data[0];
  } catch (error) {
    console.error("Error in customRequest:", error);
    return null;
  }
};

export const getAreaDemographicInDistance = async (
  params: AreaDemographicDistanceParams
): Promise<AreaDemographicDataType | null> => {
  try {
    return await customRequest(
      `api/cma/${serverType}/area-demographic/distance?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
      { method: 'GET' }
    );
  } catch (error) {
    console.error("Error fetching area demographic data by distance:", error);
    return null;
  }
};


export const getAreaDemographicInDriveTime = async (
  params: AreaDemographicDriveTimeParams
): Promise<AreaDemographicDataType | null> => {
  try {
    // Get polygon from Mapbox
    const polygon = await fetchMapboxPolygon(
      params.body.lng,
      params.body.lat,
      params.body.drivetime
    ).catch(error => {
      console.error("Error fetching polygon from Mapbox:", error);
      return null;
    });
    
    const requestBody = {
      ...params.body,
      polygon
    };
    
    return await customRequest(
      `api/cma/${serverType}/area-demographic/drivetime`,
      { 
        method: 'POST',
        data: requestBody
      }
    );
  } catch (error) {
    console.error("Error fetching area demographic data by drive time:", error);
    return null;
  }
};

// Helper function to fetch polygon from Mapbox
const fetchMapboxPolygon = async (lng: number, lat: number, drivetime: number) => {
  
  const mapboxResult = await fetch(
    `https://api.mapbox.com/isochrone/v1/mapbox/driving-traffic/${lng}%2C${lat}?contours_minutes=${drivetime}&polygons=true&denoise=1&access_token=${MAPBOX_TOKEN}`,
  );
  
  if (!mapboxResult.ok) {
    console.error(`Failed to fetch isochrone from Mapbox: ${mapboxResult.status} ${mapboxResult.statusText}`);
    return null;
  }
  
  const data = await mapboxResult.json();
  
  // Extract polygon coordinates from GeoJSON response
  if (data?.features?.[0]?.geometry?.coordinates?.[0]) {
    return data.features[0].geometry.coordinates[0];
  }
  
  return null;
};