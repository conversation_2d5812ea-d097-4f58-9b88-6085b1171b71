import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { Cart, CartItem, BulkOfferSubmission, BulkSubmissionUserInfo, PropertyToCartConverter } from '../../types/cartTypes';
import { getUserToken } from '../utils/auth';
import { ProFormaValuesLennar } from '../utils/types';


const API_BASE_URL = '/api/sbs/prod/api/v1/lennar';
// const API_BASE_URL = 'http://localhost:9003/api/v1/lennar';

// Helper function to get authenticated headers
async function getAuthHeaders() {
  const token = await getUserToken('access');
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };
}

// Helper function to handle API responses
async function handleResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
  }
  return response.json();
}

// Cart CRUD Operations
export async function getUserCart(): Promise<Cart | null> {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/cart`, {
      method: "GET",
      headers,
    });

    if (response.status === 404) {
      return null; // No cart exists for user
    }

    return handleResponse<Cart>(response);
  } catch (error) {
    console.error('Error fetching user cart:', error);
    throw error;
  }
}

export async function createCart(): Promise<Cart> {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/cart`, {
      method: "POST",
      headers,
      body: JSON.stringify({}),
    });

    return handleResponse<Cart>(response);
  } catch (error) {
    console.error('Error creating cart:', error);
    throw error;
  }
}

export async function addItemToCart(propertyData: PropertyToCartConverter): Promise<CartItem> {
  try {
    const headers = await getAuthHeaders();
    console.log("addItemToCart: ", propertyData);
    // Convert property data to cart item format with correct field names
    const cartItemData = {
      propertyId: propertyData.propertyId,
      propertyAddress: `${propertyData.address}, ${propertyData.city}, ${propertyData.state} ${propertyData.postalCode}`,
      propertyCity: propertyData.city,
      propertyState: propertyData.state,
      propertyPostalCode: propertyData.postalCode,
      listedPrice: propertyData.price,
      beds: propertyData.beds,
      baths: propertyData.baths,
      sqft: propertyData.sqft,
      placekey: propertyData.placekey,
      offerPrice: propertyData.offerPrice, // Include purchase price from Pro Forma
      isConfirmed: true, // Select by default
      // Include metadata for offer submissions (using correct field names)
      communityName: propertyData.communityName,
      homesiteNumber: propertyData.homesiteNumber,
      floorplanName: propertyData.floorplanName,
      specPrice: propertyData.specPrice,
      basePrice: propertyData.basePrice,
      divisionName: propertyData.divisionName,
      originalRent: propertyData.originalRent || propertyData.payload?.proforma?.adjustedRent,
      buyerFinalRent: propertyData.buyerFinalRent || propertyData.payload?.proforma?.['Projected Monthly Rent'],
      payload: propertyData.payload
    };

    console.log('test21 📤 Sending cart item data to backend:', cartItemData);

    const response = await fetch(`${API_BASE_URL}/cart/items`, {
      method: 'POST',
      headers,
      body: JSON.stringify(cartItemData)
    });

    if (!response.ok) {
      throw new Error(`Failed to add item to cart: ${response.status}`);
    }

    const result = await response.json();
    console.log('test21 📥 Received cart item from backend:', result);

    return result;
  } catch (error) {
    console.error('Error adding item to cart:', error);
    throw error;
  }
}

export async function removeItemFromCart(propertyId: number): Promise<void> {
  try {
    const token = await getUserToken('access');
    if (!token) {
      throw new Error("User not authenticated.");
    }
    
    const headers = {
      Authorization: `Bearer ${token}`,
      // Don't set Content-Type for DELETE requests without body
    };
    
    const response = await fetch(`${API_BASE_URL}/cart/items/${propertyId}`, {
      method: "DELETE",
      headers,
    });

    if (!response.ok) {
      throw new Error(`Failed to remove item from cart: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Error removing item from cart:', error);
    throw error;
  }
}

export async function updateCartItem(propertyId: number, updates: Partial<CartItem>): Promise<CartItem> {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/cart/items/${propertyId}`, {
      method: "PATCH",
      headers,
      body: JSON.stringify(updates),
    });

    return handleResponse<CartItem>(response);
  } catch (error) {
    console.error('Error updating cart item:', error);
    throw error;
  }
}

export async function clearCart(): Promise<void> {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/cart/clear`, {
      method: "DELETE",
      headers,
      body: JSON.stringify({}) // Add empty JSON body to fix parsing error
    });

    if (!response.ok) {
      throw new Error(`Failed to clear cart: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Error clearing cart:', error);
    throw error;
  }
}

// Note: Bulk offer submission is now handled in bulk-submit-offers.ts

// Get bulk submission status
export async function getBulkSubmissionStatus(submissionId: number): Promise<BulkOfferSubmission> {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/bulk-submissions/${submissionId}`, {
      method: "GET",
      headers,
    });

    return handleResponse<BulkOfferSubmission>(response);
  } catch (error) {
    console.error('Error fetching bulk submission status:', error);
    throw error;
  }
}

// Utility functions for property conversion
export function convertPropertyToCartItem(property: any, purchasePrice?: number, originalRent?: number, buyerFinalRent?: number, payload?: { subjectProperty?: LennarSinglePropertyDataType, proforma?: ProFormaValuesLennar }): PropertyToCartConverter {

  // Handle both LennarSinglePropertyDataType and other property formats
  const getPrice = () => {
    // If purchasePrice is provided, use it (from Pro Forma)
    // if (purchasePrice !== undefined && purchasePrice > 0) {
    //   return purchasePrice;
    // }
    
    // if (property.payload?.subjectProperty?.meta?.base_price) {
    //   return property.payload.subjectProperty.meta.base_price;
    // }
    // return property.price || property.listPrice || 0;
    return property.payload?.subjectProperty?.meta?.spec_price || 0;
  };

  const getBeds = () => {
    if (property.payload?.subjectProperty?.beds) {
      return property.payload.subjectProperty.beds;
    }
    return property.beds || property.bedsTotal || 0;
  };

  const getBaths = () => {
    if (property.payload?.subjectProperty?.baths) {
      return property.payload.subjectProperty.baths;
    }
    return property.baths || property.bathsFull || 0;
  };

  const getSqft = () => {
    if (property.payload?.subjectProperty?.sqft) {
      return property.payload.subjectProperty.sqft;
    }
    return property.sqft || property.total_area_sq_ft || 0;
  };

  // Extract metadata for offer submissions
  const meta = property.payload?.subjectProperty?.meta;
  const getCommunityName = () => meta?.community || '';
  const getHomesiteNumber = () => meta?.spec_number || '';
  const getFloorplanName = () => meta?.plan_name || '';
  const getSpecPrice = () => meta?.spec_price || '';
  const getBasePrice = () => meta?.base_price || '';
  const getDivisionName = () => meta?.division_name || '';
  
  console.log("test21 📥 property: ", property)

  console.log(`test21 🏠 Converting property ${property.property_id || property.id} to cart item:`, {
    hasPayload: !!property.payload,
    hasSubjectProperty: !!property.payload?.subjectProperty,
    hasMeta: !!meta,
    metaKeys: meta ? Object.keys(meta) : [],
    extractedMetadata: {
      communityName: getCommunityName(),
      homesiteNumber: getHomesiteNumber(),
      floorplanName: getFloorplanName(),
      specPrice: getSpecPrice(),
      basePrice: getBasePrice(),
      divisionName: getDivisionName(),
      originalRent: originalRent,
      buyerFinalRent: buyerFinalRent
    }
  });

  return {
    propertyId: property.property_id || property.id,
    address: property.full_address || property.address || property.fullStreetAddress || '',
    city: property.city || '',
    state: property.state || '',
    postalCode: property.postal_code?.toString() || property.postalCode || property.zipCode || '',
    price: getPrice(),
    beds: getBeds(),
    baths: getBaths(),
    sqft: getSqft(),
    placekey: property.placekey || '',
    offerPrice: purchasePrice, // Set offer price from Pro Forma if provided
    // Store metadata for offer submissions
    communityName: getCommunityName(),
    homesiteNumber: getHomesiteNumber(),
    floorplanName: getFloorplanName(),
    specPrice: getSpecPrice(),
    basePrice: getBasePrice(),
    divisionName: getDivisionName(),
    originalRent: originalRent,
    buyerFinalRent: buyerFinalRent,
    payload: payload
  };
}

// Get cart item count from localStorage (for immediate UI updates)
export function getCartItemCountFromStorage(): number {
  try {
    const cartData = localStorage.getItem('lennar_cart');
    if (cartData) {
      const cart = JSON.parse(cartData);
      return cart.totalItems || 0;
    }
    return 0;
  } catch (error) {
    console.error('Error reading cart from localStorage:', error);
    return 0;
  }
}

// Save cart to localStorage (for persistence across sessions)
export function saveCartToStorage(cart: Cart): void {
  try {
    localStorage.setItem('lennar_cart', JSON.stringify(cart));
  } catch (error) {
    console.error('Error saving cart to localStorage:', error);
  }
}

// Load cart from localStorage
export function loadCartFromStorage(): Cart | null {
  try {
    const cartData = localStorage.getItem('lennar_cart');
    if (cartData) {
      return JSON.parse(cartData);
    }
    return null;
  } catch (error) {
    console.error('Error loading cart from localStorage:', error);
    return null;
  }
}

// Clear cart from localStorage
export function clearCartFromStorage(): void {
  try {
    localStorage.removeItem('lennar_cart');
  } catch (error) {
    console.error('Error clearing cart from localStorage:', error);
  }
} 