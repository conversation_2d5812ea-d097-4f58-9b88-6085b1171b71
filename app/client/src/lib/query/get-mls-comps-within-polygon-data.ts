import { getUserToken } from "@/lib/utils/auth";
import { mlsCompData, MLSListingStatusType, constructionType } from "@/components/PropertyDetails/TabComps/utils/types";

export const getMLSCompsWithinPolygonData = async ({
  status,
  propertyType,
  startDate,
  endDate,
  body,
  constructionType,
}: {
  status: MLSListingStatusType;
  propertyType: 'Residential Lease' | 'Residential';
  startDate: string;
  endDate: string;
  body: Array<any>;
  constructionType: constructionType;
}): Promise<mlsCompData[]> => {
  const token = await getUserToken('access');
  const response = await fetch(`/api/cma/prod/mls/multipolygon?status=${status}&propertyType=${propertyType}&startDate=${startDate}&endDate=${endDate}&constructionType=${constructionType}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(body),
    method: 'POST',
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch MLS comps data: ${response.status} ${response.statusText}`);
  }

  return response.json();
};