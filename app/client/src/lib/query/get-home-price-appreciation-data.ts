import { getUserToken } from "@/lib/utils/auth";

export const getHomePriceAppreciationData = async ({
  zipCode,
}: {
  zipCode: string;
}) => {
  const token = await getUserToken('access');
  const response = await fetch(`/api/cma/exp/parcel/cagr?zipcode=${zipCode}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    // handle error so that it doesn't crash the app
    // throw new Error(`Failed to fetch home appreciation data: ${response.status} ${response.statusText}`);
    return null;
  }

  return response.json();
};