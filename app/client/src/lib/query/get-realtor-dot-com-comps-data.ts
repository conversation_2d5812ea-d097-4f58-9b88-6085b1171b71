import { getUserToken } from "@/lib/utils/auth";
import { realtorDotComCompData } from "@/components/PropertyDetails/TabComps/utils/types";

export const getRealtorDotComCompsData = async ({
  lng,
  lat,
  distance,
  startDate,
}: {
  lng: number;
  lat: number;
  distance: number;
  startDate: string;
}): Promise<realtorDotComCompData[]> => {
  const token = await getUserToken('access');
  const response = await fetch(`/api/cma/prod/realestate/realtor/singlefamily?lng=${lng}&lat=${lat}&distance=${distance}&startDate=${startDate}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch realtor dot com comps data: ${response.status} ${response.statusText}`);
  }

  return response.json();
};