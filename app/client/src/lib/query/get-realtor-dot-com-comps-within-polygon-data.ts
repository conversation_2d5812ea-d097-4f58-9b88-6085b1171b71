import { getUserToken } from "@/lib/utils/auth";
import { realtorDotComCompData } from "@/components/PropertyDetails/TabComps/utils/types";

export interface RealtorDotComCompsWithinPolygonDataRequestBody {
  body: Record<string, unknown>[]; // drawn custom polygons
  startDate: string;
}

export const getRealtorDotComCompsWithinPolygonData = async ({
  body,
  startDate,
}: RealtorDotComCompsWithinPolygonDataRequestBody): Promise<realtorDotComCompData[]> => {
  const token = await getUserToken('access');
  const response = await fetch(`/api/cma/prod/realestate/realtor/singlefamily/multipolygon?startDate=${startDate}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(body),
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch realtor dot com comps within polygon data: ${response.status} ${response.statusText}`);
  }

  return response.json();
};