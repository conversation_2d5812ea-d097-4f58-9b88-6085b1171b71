import { getUserToken } from "@/lib/utils/auth";
import { underwritingSource } from "../utils/types";

export const sendOfferData = async ({
  source,
  propertyId,
  body
}: {
  source: underwritingSource;
  propertyId: string;
  body: any;
}) => {
  const token = await getUserToken('access');
  const response = await fetch(`/api/underwriting/prod/underwriting?source=${source}&propertyId=${propertyId}&fundId=1`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(body)
  });
  return response.json();
};