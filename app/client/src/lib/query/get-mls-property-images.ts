import { getUserToken } from '@/lib/utils/auth';

export const getMLSPropertyImages = async ({
  listingKey,
  city,
}: {
  listingKey: string;
  city: string;
}) => {
  const token = await getUserToken('access');
  const response = await fetch(`/api/cma/prod/batch/img?key=${listingKey}&city=${city}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to fetch MLS property images');
  }

  const data = await response.json();
  if (data && Array.isArray(data) && data.length > 0) {
    const processedImages = [
      ...data.map(
        (src) => `https://spatiallaserserver-img.s3.amazonaws.com/${src}`,
      ),
    ];

    const imageCanBeLoaded = (url) => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.src = url;
        img.onload = function () {
          resolve(true);
        };
        img.onerror = function () {
          resolve(false);
        };
      });
    };

    for (let i = 0; i < processedImages.length; i++) {
      const canBeLoaded = await imageCanBeLoaded(processedImages[i]);

      if (
        !processedImages[i].includes('null.jpeg') &&
        !processedImages[i].includes('null.jpg') &&
        canBeLoaded
      ) {
        return processedImages[i];
      }
    }
  }

  return '';
};