import { getUserToken } from "@/lib/utils/auth";

export const sendSinglePropertyLogData = async ({
  propertyId,
  address
}: {
  propertyId: number;
  address: string;
}) => {
  const token = await getUserToken('access');
  if (!token) {
    return;
  }

  const response = await fetch(`/api/sbs/exp/api/v1/lennar/logs`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      propertyId,
      address,
    }),
  });

  if (!response.ok) {
    console.error('Failed to send single property log data', response);
    return null;
  }

  const contentType = response.headers.get('content-type');
  if (!contentType || !contentType.includes('application/json')) {
    return null;
  }

  try {
    return await response.json();
  } catch (error) {
    console.error('Failed to parse JSON response from log API:', error);
    return null; 
  }
};