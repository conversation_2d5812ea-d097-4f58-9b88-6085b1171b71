import { getUserToken } from "../../lib/utils/auth";


const serverType = "prod";

export interface ParcelOwnerRequestParams {
  lng: number;
  lat: number;
  distance: number;
}

type OwnerCountItem = {
  owner_occupied_sl: 'Yes' | 'No';
  count: number;
}

type InstitutionCountItem = {
  institution: string;
  count: number;
}

export type ParcelOwnerData = {
  institution_count: InstitutionCountItem[];
  owner_count: OwnerCountItem[];
  total_parcels: number;
};

const customRequest = async (
  url: string, 
  options: { 
    method: string;
    data?: ParcelOwnerRequestParams;
  }
): Promise<ParcelOwnerData | null> => {
  try {
    const token = await getUserToken('access');
    
    const fetchOptions: RequestInit = {
      method: options.method,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
    
    if (options.data) {
      fetchOptions.body = JSON.stringify(options.data);
    }
    
    const response = await fetch(`/${url}`, fetchOptions);
    
    if (!response.ok) {
      console.error(`Failed to fetch parcel owner summary: ${response.status} ${response.statusText}`);
      return null;
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error in customRequest:", error);
    return null;
  }
};

export async function getParcelOwnerSummary(params: ParcelOwnerRequestParams): Promise<ParcelOwnerData | null> {
  try {
    return await customRequest(
      `api/cma/${serverType}/mls/summary?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
      { method: 'GET' }
    );
  } catch (error) {
    console.error("Error fetching parcel owner summary:", error);
    return null;
  }
}