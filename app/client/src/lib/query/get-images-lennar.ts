import { getUserToken } from "@/lib/utils/auth";

export interface singleImageLennarResponseType {
  imageName: string;
  url: string;
}

export interface getImagesLennarResponseType {
  images: singleImageLennarResponseType[];
  count: number;
}

export interface getImagesLennarResponseTypeWithPropertyId {
  images: singleImageLennarResponseType[];
  count: number;
  project: string;
  code: string;
}

export const getImagesLennar = async ({
  project,
  code
}: {
  project: string;
  code: string;
}): Promise<getImagesLennarResponseType> => {
  const token = await getUserToken('access');
  const response = await fetch(`/api/sbs/prod/api/v1/lennar/interior-renders?project=${project}&code=${code}`, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });
  if (response.status !== 200) {
    throw new Error("Failed to fetch images");
  }
  return response.json();
};