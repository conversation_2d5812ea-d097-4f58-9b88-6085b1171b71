import { getUserToken } from "@/lib/utils/auth";
import { sfrAndHotPadsCompData, HotPadsListingStatusType } from "@/components/PropertyDetails/TabComps/utils/types";

export const getHotPadsCompsWithinPolygonData = async ({
  exists, // status
  startDate,
  endDate,
  body,
}: {
  exists: HotPadsListingStatusType;
  startDate: string;
  endDate: string;
  body: Array<any>;
}): Promise<sfrAndHotPadsCompData[]> => {
  const token = await getUserToken('access');
  const response = await fetch(`/api/cma/prod/realestate/hotpads/multipolygon?exists=${exists}&startDate=${startDate}&endDate=${endDate}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: 'POST',
    body: JSON.stringify(body),
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch HotPads comps within polygon data: ${response.status} ${response.statusText}`);
  }

  return response.json();
};