import { getUserToken } from "../../lib/utils/auth";

const serverType = "prod";

export interface ParcelUnitMixRequestParams {
  lat: number;
  lng: number;
  radius: number; // Note: this uses 'radius' instead of 'distance'
}

export interface PropertyDataPoint {
  x: string;
  y: number;
  type: 'Bedrooms' | 'Bathrooms' | 'Square Feet' | 'Year Built';
  percent: number;
}

export type PropertyDataPointWithFlag = PropertyDataPoint & {
  isSubjectProperty: boolean;
};

export interface ParcelUnitMixData {
  bathrooms: PropertyDataPoint[];
  bedrooms: PropertyDataPoint[];
  sqft: PropertyDataPoint[];
  year: PropertyDataPoint[];
}

const customRequest = async (
  url: string, 
  options: { 
    method: string;
    data?: ParcelUnitMixRequestParams;
  }
): Promise<ParcelUnitMixData | null> => {
  try {
    const token = await getUserToken('access');
    
    const fetchOptions: RequestInit = {
      method: options.method,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
    
    if (options.data) {
      fetchOptions.body = JSON.stringify(options.data);
    }
    
    const response = await fetch(`/${url}`, fetchOptions);
    
    if (!response.ok) {
      console.error(`Failed to fetch parcel unit mix data: ${response.status} ${response.statusText}`);
      return null;
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error in customRequest:", error);
    return null;
  }
};

export async function getParcelUnitMixData(params: ParcelUnitMixRequestParams): Promise<ParcelUnitMixData | null> {
  try {
    return await customRequest(
      `api/cma/${serverType}/parcel/mix?lat=${params.lat}&lng=${params.lng}&radius=${params.radius}`,
      { method: 'GET' }
    );
  } catch (error) {
    console.error("Error fetching parcel unit mix data:", error);
    return null;
  }
}