import { getUserToken } from "@/lib/utils/auth";
import { LennarSinglePropertyDataTypeAPIVersion2, underwritingSource, LennarSinglePropertyDataType } from "../utils/types";


export const getProperties = async (source: underwritingSource): Promise<LennarSinglePropertyDataTypeAPIVersion2[]> => {
  try {
    const token = await getUserToken('access');
    // const response = await fetch(`/api/underwriting/prod/properties?source=${source}`, {
    const response = await fetch(`/api/underwriting/exp/properties?source=${source}&api-version=v2`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch properties: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // add a field called `payload` to each property
    // find the item in `decisions` field with the largest `id`
    // copy the item, spread it into the property
    // change the `id` to `underwriting_decision_id`
    const dataConvertedToAPIVersion1DataSchema = data.map((item: LennarSinglePropertyDataTypeAPIVersion2): LennarSinglePropertyDataType => {
      const largestDecisionId = Math.max(...item.decisions.map(decision => decision.id));
      const largestDecisionItem = item.decisions.find(decision => decision.id === largestDecisionId);
      const { id, ...rest } = largestDecisionItem;
      const largestDecisionItemWithIdConverted = {
        ...rest,
        underwriting_decision_id: id
      }
      return {
        ...item,
        ...largestDecisionItemWithIdConverted
      };
    });



    // remove properties with sl_status: 'Closed'
    const dataWithClosedListingsRemoved = dataConvertedToAPIVersion1DataSchema.filter((item: LennarSinglePropertyDataType) => item?.payload?.subjectProperty?.meta?.sl_status !== 'Closed');

    // remove properties with meta?.master_comm
    // because they are old listings
    const dataWithMasterCommRemoved = dataWithClosedListingsRemoved.filter((item: LennarSinglePropertyDataType) => !item?.payload?.subjectProperty?.meta?.master_comm);

    

    console.log('dataConvertedToAPIVersion1DataSchema', dataConvertedToAPIVersion1DataSchema);

    return dataWithMasterCommRemoved;
  } catch (error) {
    console.error("Error fetching properties:", error);
    throw error;
  }
};