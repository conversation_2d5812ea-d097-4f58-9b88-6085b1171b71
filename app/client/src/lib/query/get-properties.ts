import { getUserToken } from "@/lib/utils/auth";
import {
  LennarSinglePropertyDataTypeAPIVersion2,
  underwritingSource,
  LennarSinglePropertyDataType,
} from "../utils/types";
import { isEmpty } from "lodash";

const DEFAULT_PROPERTIES_API_LIMIT = 5000;

export const getProperties = async ({
  source,
  filter,
  sort,
  direction,
  limit = DEFAULT_PROPERTIES_API_LIMIT,
  offset = 0,
}: {
  source: underwritingSource;
  filter?: string;
  sort?: "basePrice" | "yieldOnBidPrice";
  direction?: "asc" | "desc";
  limit?: number;
  offset?: number;
}): Promise<LennarSinglePropertyDataType[]> => {
  try {
    const token = await getUserToken("access");

    const filterParams = filter ? `&filter=${filter}` : "";
    const sortParams = sort ? `&sort=${sort}` : "";
    const directionParams = direction ? `&direction=${direction}` : "";

    // TODO: Someone needs to dynamically handle whether prod/exp underwritting server needs to be used. Im hardcoding exp..
    const response = await fetch(
      `/api/underwriting/prod/v2/properties?source=${source}&limit=${limit}&offset=${offset}${filterParams}${sortParams}${directionParams}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch properties: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();

    // add a field called `payload` to each property
    // find the item in `decisions` field with the largest `id`
    // copy the item, spread it into the property
    // change the `id` to `underwriting_decision_id`
    const dataConvertedToAPIVersion1DataSchema = data.map(
      (
        item: LennarSinglePropertyDataTypeAPIVersion2
      ): LennarSinglePropertyDataType => {
        const largestDecisionId = Math.max(
          ...item.decisions.map((decision) => decision.id)
        );
        const largestDecisionItem = item.decisions.find(
          (decision) => decision.id === largestDecisionId
        );

        if (!largestDecisionItem) {
          throw new Error(`No decision found for property ${item.property_id}`);
        }

        const { id, ...rest } = largestDecisionItem;
        const largestDecisionItemWithIdConverted = {
          ...rest,
          underwriting_decision_id: id,
        };

        // check if misc is present
        // if so, use values inside misc to update the property and the most recent decision
        if (item?.misc && !isEmpty(item?.misc) && largestDecisionItemWithIdConverted?.payload) {
          // update pro forma values
          if (
            item.misc.cap_rate &&
            largestDecisionItemWithIdConverted.payload?.proforma?.buyAndHold
          ) {
            largestDecisionItemWithIdConverted.payload.proforma.buyAndHold[
              "Projected Yield on Bid Price"
            ] = item.misc.cap_rate;
          }
          if (
            item.misc.projected_monthly_rent &&
            largestDecisionItemWithIdConverted.payload?.proforma?.buyAndHold
          ) {
            largestDecisionItemWithIdConverted.payload.proforma.buyAndHold[
              "Projected Monthly Rent"
            ] = item.misc.projected_monthly_rent;
          }
          if (
            item.misc.spec_price &&
            largestDecisionItemWithIdConverted.payload?.proforma?.buyAndHold
          ) {
            largestDecisionItemWithIdConverted.payload.proforma.buyAndHold[
              "Asking Price"
            ] = item.misc.spec_price;
          }
          if (
            item.misc.tax_rate &&
            largestDecisionItemWithIdConverted.payload?.proforma?.buyAndHold
          ) {
            largestDecisionItemWithIdConverted.payload.proforma.buyAndHold[
              "Property Tax Coefficient"
            ] = item.misc.tax_rate;
          }
          if (
            item.misc.hoafeeannual &&
            largestDecisionItemWithIdConverted.payload?.proforma?.buyAndHold
          ) {
            largestDecisionItemWithIdConverted.payload.proforma.buyAndHold[
              "Annual HOA Fees"
            ] = item.misc.hoafeeannual;
          }
          if (
            item.misc.annual_special_assessment_fees &&
            largestDecisionItemWithIdConverted.payload?.proforma?.buyAndHold
          ) {
            largestDecisionItemWithIdConverted.payload.proforma.buyAndHold[
              "Annual Community Development Fee"
            ] = item.misc.annual_special_assessment_fees;
          }
          // update subject property
          if (
            item.misc.spec_price &&
            largestDecisionItemWithIdConverted.payload?.subjectProperty?.meta
          ) {
            largestDecisionItemWithIdConverted.payload.subjectProperty.meta.spec_price =
              item.misc.spec_price.toString();
          }
          if (
            item.misc.tax_rate &&
            largestDecisionItemWithIdConverted.payload?.subjectProperty?.meta
          ) {
            largestDecisionItemWithIdConverted.payload.subjectProperty.meta.taxrate =
              item.misc.tax_rate.toString();
          }
          if (
            item.misc.hoafeeannual &&
            largestDecisionItemWithIdConverted.payload?.subjectProperty?.meta
          ) {
            largestDecisionItemWithIdConverted.payload.subjectProperty.meta.hoafeeannual =
              item.misc.hoafeeannual.toString();
          }
          if (
            item.misc.annual_special_assessment_fees &&
            largestDecisionItemWithIdConverted.payload?.subjectProperty?.meta
          ) {
            largestDecisionItemWithIdConverted.payload.subjectProperty.meta.annual_special_assessment_fees =
              item.misc.annual_special_assessment_fees.toString();
          }
        }

        return {
          ...item,
          ...largestDecisionItemWithIdConverted,
        };
      }
    );

    // console.log(
    //   "dataConvertedToAPIVersion1DataSchema",
    //   dataConvertedToAPIVersion1DataSchema
    // );

    console.log('dataConvertedToAPIVersion1DataSchema', dataConvertedToAPIVersion1DataSchema);

    // remove properties with sl_status: 'Closed'
    const dataWithClosedListingsRemoved =
      dataConvertedToAPIVersion1DataSchema.filter(
        (item: LennarSinglePropertyDataType) =>
          (item?.payload?.subjectProperty?.meta as any)?.sl_status !== "Closed"
      );

    // remove properties with meta?.master_comm
    // because they are old listings
    const dataWithMasterCommRemoved = dataWithClosedListingsRemoved.filter(
      (item: LennarSinglePropertyDataType) =>
        !(item?.payload?.subjectProperty?.meta as any)?.master_comm
    );

    // dedupe by payload.subjectProperty.meta.spec_number
    const dedupedData = dataWithMasterCommRemoved.filter(
      (
        item: LennarSinglePropertyDataType,
        index: number,
        self: LennarSinglePropertyDataType[]
      ) =>
        index ===
        self.findIndex(
          (t) =>
            t?.payload?.subjectProperty?.meta?.spec_number ===
            item?.payload?.subjectProperty?.meta?.spec_number
        )
    );

    // console.log("dedupedData", dedupedData);

    return dedupedData;
  } catch (error) {
    console.error("Error fetching properties:", error);
    throw error;
  }
};
