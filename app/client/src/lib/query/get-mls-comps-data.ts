import { getUserToken } from "@/lib/utils/auth";
import { mlsCompData, MLSListingStatusType, constructionType } from "@/components/PropertyDetails/TabComps/utils/types";
import { processMLSProperties } from "@/lib/utils/processAPIResponses.js";

export const getMLSCompsData = async ({
  status,
  propertyType,
  startDate,
  endDate,
  lng,
  lat,
  distance,
  constructionType,
}: {
  status: MLSListingStatusType;
  propertyType: 'Residential Lease' | 'Residential';
  startDate: string;
  endDate: string;
  lng: number;
  lat: number;
  distance: number;
  constructionType: constructionType;
}): Promise<mlsCompData[]> => {
  const token = await getUserToken('access');
  const response = await fetch(`/api/cma/prod/mls?status=${status}&propertyType=${propertyType}&startDate=${startDate}&endDate=${endDate}&lng=${lng}&lat=${lat}&distance=${distance}&constructionType=${constructionType}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch MLS comps data: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  const dataProcessed = processMLSProperties(data, status);
  return dataProcessed;
};