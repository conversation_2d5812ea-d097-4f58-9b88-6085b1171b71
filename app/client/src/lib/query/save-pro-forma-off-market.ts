import { getUserToken } from "@/lib/utils/auth";

export const saveSingleProForma = async ({
  propertyId,
  source = 'offmarket',
  proFormaValues,
}: {
  propertyId: number;
  source: 'offmarket' | 'onmarket';
  proFormaValues: any;
}) => {
  const token = await getUserToken('access');
  const response = await fetch(`/api/offMarket/exp/proforma/save`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      propertyId,
      source,
      payload: proFormaValues,
    }),
  });
  if (response?.status === 201) {
    // const status = await response?.json();
    const status = 'success';
    return status;
  } else {
    return null;
  }
};

export const getSavedSingleProForma = async (propertyId: string) => {
  const token = await getUserToken('access');
  const response = await fetch(`/api/offMarket/exp/proforma?propertyId=${propertyId}&source=offmarket`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  }); 
  if (response && response?.status === 200) {
    const data = await response.json();
    return data?.payload;
  }
  return null;
};

