import { getUserToken } from "@/lib/utils/auth";
import { underwritingSource } from "../utils/types";

// it's the same api request as sendOfferData
// the difference is the decision
// for bookmark, decision is `approve with condition`
// for sendOfferData, decision is `approve`
export const saveToBookmarkData = async ({
  source,
  propertyId,
  body
}: {
  source: underwritingSource;
  propertyId: string;
  body: any;
}) => {
  const token = await getUserToken('access');
  const response = await fetch(`/api/underwriting/prod/underwriting?source=${source}&propertyId=${propertyId}&fundId=1`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(body)
  });
  return response.json();
};