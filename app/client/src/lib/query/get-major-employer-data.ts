import { getUserToken } from "@/lib/utils/auth";
import { MAPBOX_TOKEN } from "@/constants";
import { MajorEmployerDataProps } from "@/types/PropertyDetailPage.types";

const serverType = "prod";

export interface MajorEmployersDistanceParams {
  lat: number;
  lng: number;
  distance: number;
}

export interface MajorEmployersDriveTimeParams {
  body: {
    lat: number;
    lng: number;
    drivetime: number;
}
}

const customRequest = async (
  url: string, 
  options: { 
    method: string; 
    data?: MajorEmployersDistanceParams | MajorEmployersDriveTimeParams['body'];
  }
): Promise<MajorEmployerDataProps[] | null> => {
  try {
    const token = await getUserToken('access');
    
    const fetchOptions: RequestInit = {
      method: options.method,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
    
    if (options.data) {
      fetchOptions.body = JSON.stringify(options.data);
    }
    
    const response = await fetch(`/${url}`, fetchOptions);
    
    if (!response.ok) {
      console.error(`Failed to fetch area demographic data: ${response.status} ${response.statusText}`);
      return null;
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error in customRequest:", error);
    return null;
  }
};


export const getMajorEmployersInDistance = async (
  params: MajorEmployersDistanceParams
): Promise<MajorEmployerDataProps[] | null> => {
  try {
      return await customRequest(
      `api/cma/${serverType}/major-employers/distance?lng=${params.lat}&lat=${params.lng}&distance=${params.distance}`,
      { method: 'GET' },
      );
  } catch (error) {
    console.error("Error fetching area major employers data by distance:", error);
    return null;
  }
};

export const getMajorEmployersInDriveTime = async (params: MajorEmployersDriveTimeParams): Promise<MajorEmployerDataProps[] | null> => {

  try {
    // Get polygon from Mapbox
    const polygon = await fetchMapboxPolygon(
      params.body.lng,
      params.body.lat,
      params.body.drivetime
    ).catch(error => {
      console.error("Error fetching polygon from Mapbox:", error);
      return null;
    });
    
    const requestBody = {
      ...params.body,
      polygon
    };
    
    return await customRequest(
      `api/cma/${serverType}/major-employers/drivetime`,
      { 
        method: 'POST',
        data: requestBody
      }
    );
  } catch (error) {
    console.error("Error fetching area demographic data by drive time:", error);
    return null;
  }
};

// Helper function to fetch polygon from Mapbox
const fetchMapboxPolygon = async (lng: number, lat: number, drivetime: number) => {
  
  const mapboxResult = await fetch(
    `https://api.mapbox.com/isochrone/v1/mapbox/driving-traffic/${lng}%2C${lat}?contours_minutes=${drivetime}&polygons=true&denoise=1&access_token=${MAPBOX_TOKEN}`,
  );
  
  if (!mapboxResult.ok) {
    console.error(`Failed to fetch isochrone from Mapbox: ${mapboxResult.status} ${mapboxResult.statusText}`);
    return null;
  }
  
  const data = await mapboxResult.json();
  
  // Extract polygon coordinates from GeoJSON response
  if (data?.features?.[0]?.geometry?.coordinates?.[0]) {
    return data.features[0].geometry.coordinates[0];
  }
  
  return null;
};