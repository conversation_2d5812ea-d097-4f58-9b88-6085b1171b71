// import { getSLUserToken } from '@spatiallaser/sl-auth';
import { getUserToken } from "@/lib/utils/auth";
import { MAX_PRICE, MIN_PRICE } from '../../hooks/useMarketConditionData';

interface MarketConditionAPIOptions {
  serverType: 'exp' | 'prod';
  propertyType: string;
  year: number;
  month: number;
  numberOfMonths: number;
  lng: number;
  lat: number;
  distance: number;
  bedrooms: string;
  newConstruction: 'true' | 'false' | '';
  minPrice?: number;
  maxPrice?: number;
  signal?: AbortSignal;
  date: string;
}

export const getMarketConditionAOIData = async (
  options: MarketConditionAPIOptions,
) => {
  const { minPrice = MIN_PRICE, maxPrice = MAX_PRICE } = options;
  return await fetch(
    `/api/cma/${options.serverType}/mls-listing-summary/point-radius-new?propertyType=${options.propertyType}&date=${options.date}&month=${options.numberOfMonths}&numberOfMonths=${options.numberOfMonths}&lng=${options.lng}&lat=${options.lat}&distance=${options.distance}&numberOfBedrooms=${options.bedrooms}&newConstruction=${options.newConstruction}&minPrice=${minPrice}&maxPrice=${maxPrice}`,
    {
      method: 'GET',
      headers: { Authorization: `Bearer ${await getUserToken('access')}` },
      signal: options.signal,
    },
  );
};


export const getMarketConditionZIPCodeData = async (
  options: Omit<MarketConditionAPIOptions, 'distance'>,
) => {
  const { minPrice = MIN_PRICE, maxPrice = MAX_PRICE } = options;
  return await fetch(
    `/api/cma/${options.serverType}/mls-listing-summary/zipcode-new?propertyType=${options.propertyType}&date=${options.date}&month=${options.numberOfMonths}&numberOfMonths=${options.numberOfMonths}&lng=${options.lng}&lat=${options.lat}&numberOfBedrooms=${options.bedrooms}&newConstruction=${options.newConstruction}&minPrice=${minPrice}&maxPrice=${maxPrice}`,
    {
      method: 'GET',
      headers: { Authorization: `Bearer ${await getUserToken('access')}` },
      signal: options.signal,
    },
  );
};

export const getMarketConditionDistrictData = async (
  options: Omit<MarketConditionAPIOptions, 'distance'>,
) => {
  const { minPrice = MIN_PRICE, maxPrice = MAX_PRICE } = options;
  return await fetch(
    `/api/cma/${options.serverType}/mls-listing-summary/school-district-new?propertyType=${options.propertyType}&date=${options.date}&month=${options.numberOfMonths}&numberOfMonths=${options.numberOfMonths}&lng=${options.lng}&lat=${options.lat}&numberOfBedrooms=${options.bedrooms}&newConstruction=${options.newConstruction}&minPrice=${minPrice}&maxPrice=${maxPrice}`,
    {
      method: 'GET',
      headers: { Authorization: `Bearer ${await getUserToken('access')}` },
      signal: options.signal,
    },
  );
};

export const getMarketConditionCountyData = async (
  options: Omit<MarketConditionAPIOptions, 'distance'>,
) => {
  const { minPrice = MIN_PRICE, maxPrice = MAX_PRICE } = options;
  return await fetch(
    `/api/cma/${options.serverType}/mls-listing-summary/county-new?propertyType=${options.propertyType}&date=${options.date}&month=${options.numberOfMonths}&numberOfMonths=${options.numberOfMonths}&lng=${options.lng}&lat=${options.lat}&numberOfBedrooms=${options.bedrooms}&newConstruction=${options.newConstruction}&minPrice=${minPrice}&maxPrice=${maxPrice}`,
    {
      method: 'GET',
      headers: { Authorization: `Bearer ${await getUserToken('access')}` },
      signal: options.signal,
    },
  );
};

export const getMarketConditionMetroData = async (
  options: Omit<MarketConditionAPIOptions, 'distance'>,
) => {
  const { minPrice = MIN_PRICE, maxPrice = MAX_PRICE } = options;
  return await fetch(
    `/api/cma/${options.serverType}/mls-listing-summary/metro-new?propertyType=${options.propertyType}&date=${options.date}&month=${options.numberOfMonths}&numberOfMonths=${options.numberOfMonths}&lng=${options.lng}&lat=${options.lat}&numberOfBedrooms=${options.bedrooms}&newConstruction=${options.newConstruction}&minPrice=${minPrice}&maxPrice=${maxPrice}`,
    {
      method: 'GET',
      headers: { Authorization: `Bearer ${await getUserToken('access')}` },
      signal: options.signal,
    },
  );
};
