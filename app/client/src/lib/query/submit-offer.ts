import { SimplifiedOffer } from "../../types/offerTypes";
import { getUserToken } from "../utils/auth";

export async function submitOffer(offer: SimplifiedOffer): Promise<Response> {
    const token = await getUserToken('access');

    if (!token) {
        throw new Error("User not authenticated.");
    }

    const response = await fetch(
        `/api/sbs/prod/api/v1/lennar/create-offer`, 
        // `http://localhost:9003/api/v1/lennar/create-offer`, 
        
        {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(offer),
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to submit offer.");
    }

    return response;
} 