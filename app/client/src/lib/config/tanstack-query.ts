import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';
import { QueryClient } from '@tanstack/react-query';

// Create a client for react-query
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: Infinity,
      gcTime: 1000 * 60 * 60 * 24 * 24, // 24 days
    },
  },
});

export const persister = createSyncStoragePersister({
  storage: window.localStorage,
});
