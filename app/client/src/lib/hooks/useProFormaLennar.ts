import { useState, useEffect } from "react";
import { useMarketplaceMapContext } from "@/contexts/MarketplaceMapContext";
import { getRouteApi, useSearch } from "@tanstack/react-router";
import { ProFormaValuesLennar } from "../utils/types";
import { isEmpty, isEqual } from "lodash";
import { getAllValues, standardizeProFormaBuyAndHoldLennarInput } from "../utils/ProFormaBuyAndHoldLennarFormula2";

export const useProFormaLennar = () => {
  const { 
    setProFormaAllValues,
    proFormaInputsLennar,
    setProFormaInputsLennar,
    prevSubjectPropertyId,
    setPrevSubjectPropertyId,
  } = useMarketplaceMapContext();

  const routeApiSingleProperty = getRouteApi('/_authenticated/properties/$id');
  const { parcelData, demographicData, homePriceAppreciationData } = routeApiSingleProperty.useLoaderData();

  const routeApiPropertyList = getRouteApi('/_authenticated/properties');
  const { properties, propertiesSubmitted, propertiesBookmarked } = routeApiPropertyList.useLoaderData();

  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;

  const currentProperties = listType === 'listings' ? properties : listType === 'submitted' ? propertiesSubmitted : propertiesBookmarked;

  const currentPropertyId = routeApiSingleProperty.useParams().id;
  // then use the id to find the property data in the property list data
  const subjectPropertyData = currentProperties.find((property) => property.property_id === +currentPropertyId);

  const [prevAllValues, setPrevAllValues] = useState<ProFormaValuesLennar | null>(null);

  // after subject property has changed, reset the pro forma
  useEffect(() => {
    if (subjectPropertyData?.property_id && subjectPropertyData.property_id !== prevSubjectPropertyId) {
      setPrevSubjectPropertyId(subjectPropertyData?.property_id || null);
      // if there are saved user input values, set the pro forma values to the saved user input values
      if (!isEmpty(subjectPropertyData?.payload?.proforma?.userInputs)) {
        setProFormaInputsLennar({
          propertyId: subjectPropertyData.property_id,
          source: 'offmarket',
          proFormaValues: subjectPropertyData?.payload?.proforma?.userInputs,
        });
      } else {
        setProFormaInputsLennar({
          propertyId: subjectPropertyData.property_id,
          source: 'offmarket',
          proFormaValues: {
            userInputBidPrice: null,
            userInputClosingCosts: null,
            userInputProjectedRent: null,
            userInputOtherIncome: null,
            userInputPropertyTax: null,
            userInputInsurance: null,
            userInputHoa: null,
            userInputRAndM: null,
            userInputPmFeesCoeff: null,
            userInputPmFees: null,
            userInputVacancyLossCoeff: null,
            userInputVacancyLoss: null,
            userInputAnnualCommunityDevelopmentFee: null,
            userInputLoanToValue: null,
            userInputMortgageRate: null,
            userInputMortgageTerm: null,
            userInputHPA5Yr: null,
            userInputYearsHolding: null,
          },
        });
      }
    }
  }, [subjectPropertyData?.property_id]);
  
  const proFormaInputs = standardizeProFormaBuyAndHoldLennarInput({
    parcelData: parcelData[0],
    subjectPropertyData: subjectPropertyData?.payload?.subjectProperty,
    demographicsData: demographicData[0],
    // adjustedRent: intelligentCompsData?.data?.results?.adjusted_rent?.adjusted_value,
    // adjustedSales: intelligentCompsData?.data?.results?.adjusted_sales?.adjusted_value,
    adjustedRent: subjectPropertyData?.payload?.proforma?.buyAndHold?.['Projected Monthly Rent'] || 0,
    adjustedSales: subjectPropertyData?.payload?.proforma?.buyAndHold?.['Market Value'] || 0,
    hpa5Yr: homePriceAppreciationData,
    ...proFormaInputsLennar?.proFormaValues,
  });

  const allValues = getAllValues(proFormaInputs);

  // Update context with pro forma values whenever they change
  // if (!isEqual(allValues, prevAllValues)) {
  //   setProFormaAllValues(allValues);
  //   setPrevAllValues(allValues);
  // }
  useEffect(() => {
    if (!isEqual(allValues, prevAllValues)) {
      setProFormaAllValues(allValues);
      setPrevAllValues(allValues);
    }
  }, [allValues]);

  const resetProForma = () => {
    if (subjectPropertyData && subjectPropertyData?.property_id) {
      setProFormaInputsLennar({
        propertyId: subjectPropertyData.property_id,
        source: 'offmarket',
        proFormaValues: {
          userInputBidPrice: null,
          userInputClosingCosts: null,
          userInputProjectedRent: null,
          userInputOtherIncome: null,
          userInputPropertyTax: null,
          userInputInsurance: null,
          userInputHoa: null,
          userInputRAndM: null,
          userInputPmFeesCoeff: null,
          userInputPmFees: null,
          userInputVacancyLossCoeff: null,
          userInputVacancyLoss: null,
          userInputAnnualCommunityDevelopmentFee: null,
          userInputLoanToValue: null,
          userInputMortgageRate: null,
          userInputMortgageTerm: null,
          userInputHPA5Yr: null,
          userInputYearsHolding: null,
        },
      });
    }
  };

  return { allValues, resetProForma };
};