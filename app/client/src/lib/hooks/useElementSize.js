import { useState, useEffect } from 'react';

export const useElementSize = (ref) => {
  const [width, setWidth] = useState();
  const [height, setHeight] = useState();

  useEffect(() => {
    if (!ref.current) return;

    const resizeObserver = new ResizeObserver(() => {
      if (!ref.current) return;
      setWidth(ref.current.offsetWidth);
      setHeight(ref.current.offsetHeight);
    });

    resizeObserver.observe(ref.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [ref.current]);

  return { width, height };
};

export default useElementSize;
