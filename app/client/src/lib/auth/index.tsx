import {
  Authenticator,
  AuthenticatorProps,
  useAuthenticator,
  type UseAuthenticator,
} from '@aws-amplify/ui-react';
import { Amplify } from 'aws-amplify';

Amplify.configure({
  Auth: {
    Cognito: {
      userPoolId: import.meta.env.VITE_COGNITO_USER_POOL_ID,
      userPoolClientId: import.meta.env.VITE_COGNITO_CLIENT_ID,
      loginWith: {
        username: false,
        email: true,
      },
    },
  },
});

export type AuthType = UseAuthenticator;

export const AuthenticationProvider = (props: { children: React.ReactNode }) => (
  <Authenticator.Provider {...props} />
);

export const useAuthentication = () => useAuthenticator();

export const Authentication = (props: AuthenticatorProps) => <Authenticator {...props} />;

export const useAuthUsername = () => {
  const auth = useAuthenticator();
  return auth.user?.signInDetails?.loginId;
};
