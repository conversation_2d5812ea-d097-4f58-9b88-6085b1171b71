import { Dispatch, SetStateAction } from "react";

export interface Option {
  value: number | string;
  label: string;
}

export interface BaseFilterConfig<T> {
  label: string;
  options: Option[];
  value: T;
}

export type FilterConfig = MarketFilterConfig | PriceFilterConfig | BedFilterConfig | BathFilterConfig;
export type FilterValue = string[] | PriceRangeValues | BedroomMinValues | BathroomMinValues | "";

export interface MarketFilterConfig extends BaseFilterConfig<string[]> {
  onChange: Dispatch<SetStateAction<string[]>>;
  multiSelect?: boolean;
}
export interface PriceFilterConfig extends BaseFilterConfig<PriceRangeValues | ""> {
  onChange: Dispatch<SetStateAction<PriceRangeValues | "">>;
}

export interface BedFilterConfig extends BaseFilterConfig<BedroomMinValues | ""> {
  onChange: Dispatch<SetStateAction<BedroomMinValues | "">>;
}

export interface BathFilterConfig extends BaseFilterConfig<BathroomMinValues | ""> {
  onChange: Dispatch<SetStateAction<BathroomMinValues | "">>;
}

export enum PriceRangeValues {
  UNDER_100K,
  RANGE_100K,
  RANGE_200K,
  RANGE_300K,
  RANGE_400K_PLUS,
  RANGE_0K_PLUS,
}

export enum BedroomMinValues {
  ONE_PLUS = 1,
  TWO_PLUS = 2,
  THREE_PLUS = 3,
  FOUR_PLUS = 4,
  FIVE_PLUS = 5,
}

export enum BathroomMinValues {
  TWO_PLUS = 2,
  THREE_PLUS = 3,
  FOUR_PLUS = 4,
  FIVE_PLUS = 5,
}

export const priceOptions: Option[] = [
  { value: PriceRangeValues.RANGE_0K_PLUS, label: "No Min - No Max" },
  { value: PriceRangeValues.UNDER_100K, label: "< $100K" },
  { value: PriceRangeValues.RANGE_100K, label: "$100k - $200k" },
  { value: PriceRangeValues.RANGE_200K, label: "$200k - $300k" },
  { value: PriceRangeValues.RANGE_300K, label: "$300k - $400k" },
  { value: PriceRangeValues.RANGE_400K_PLUS, label: "$400k+" },
];

export const bedsOptions: Option[] = [
  { value: BedroomMinValues.ONE_PLUS, label: "1+" },
  { value: BedroomMinValues.TWO_PLUS, label: "2+" },
  { value: BedroomMinValues.THREE_PLUS, label: "3+" },
  { value: BedroomMinValues.FOUR_PLUS, label: "4+" },
  { value: BedroomMinValues.FIVE_PLUS, label: "5+" },
];

export const bathsOptions: Option[] = [
  { value: BathroomMinValues.TWO_PLUS, label: "2+" },
  { value: BathroomMinValues.THREE_PLUS, label: "3+" },
  { value: BathroomMinValues.FOUR_PLUS, label: "4+" },
  { value: BathroomMinValues.FIVE_PLUS, label: "5+" },
];

interface PriceBoundary {
  min: number;
  max: number;
}

export const PRICE_BOUNDARIES: Record<number, PriceBoundary> = {
  [PriceRangeValues.RANGE_0K_PLUS]: { min: 0, max: Infinity },
  [PriceRangeValues.UNDER_100K]: { min: 0, max: 99999 },
  [PriceRangeValues.RANGE_100K]: { min: 100000, max: 199999 },
  [PriceRangeValues.RANGE_200K]: { min: 200000, max: 299999 },
  [PriceRangeValues.RANGE_300K]: { min: 300000, max: 399999 },
  [PriceRangeValues.RANGE_400K_PLUS]: { min: 400000, max: Infinity },
};