export interface PropertyDetailProps {
  id: number;
  title: string;
  address: string;
  price: number;
  beds: number;
  baths: number;
  sqft: number;
  imageUrl: string;
  latitude: number;
  longitude: number;
  source: string;
  thumbnail: string;
  lotNumber: string;
  full_address?: string;
  stories?: number;
  garage?: number;
}

// Define the structure for GeneralDetails
export interface GeneralDetailsProps {
  homeDetails?: Record<string, string | number | boolean>;
  communityAndAmenityDetails?: Record<string, string | number | boolean>;
}

// Define the structure for ProFormaDetails
export interface ProFormaDetails {
  projectedNOI?: number;
  projectedYield?: number;
  expenses?: {
    propertyTax?: { percentage: number; amount: number };
    insurance?: number;
    hoaFees?: number;
    repairMaintenance?: number;
    propertyManagement?: number;
    vacancyLoss?: { percentage: number; amount: number };
    communityDevelopmentFee?: { percentage: number; amount: number };
    totalAnnualExpenses?: number;
  };
  bidPrice?: {
    askingPrice?: number;
    bidPrice?: number;
  };
  rentalIncome?: {
    projectedRent?: number;
    otherMonthlyIncome?: number;
    totalAnnualRentalIncome?: number;
  };
  cashFlow?: number; // Consider refining this type if more detail is needed
  mortgage?: {
    amount?: number;
    interestRate?: number;
    loanTerm?: number;
    monthlyPayment?: number;
  };
  closingCosts?: number;
  totalAcquisitionCost?: number;
  loanToValue?: number;
  communityDevelopmentFeePercentage?: number; // Check if this duplicates the one in expenses
  netOperatingIncome?: number;
  cashOnCashReturn?: number;
}

export interface ImageProps {
  imageName: string;
  url: string;
}

// Define the structure for CompsDetails
export interface CompListingProps {
  address: string;
  distance: string;
  status: string;
  rent: string;
  built: string;
  bedsBaths: string; // Combined as "Bd/Ba" in the image
  sqFt: string;
  closedDate: string; // "Closed" column in the image
  source?: string; // Add source property for combined view
}

export interface CompSectionProps {
  medianRentOfSelected: string;
  total: number;
  listings: CompListingProps[];
}

export interface CompsDetailsProps {
  estimatedMarketRent: string;
  mls?: CompSectionProps;
  nationalSfr?: CompSectionProps;
  rentalPortal?: CompSectionProps;
  rentalPortal2?: CompSectionProps; // Assuming this was added based on mockComps
}

export interface SubjectPropertyCharacteristics {
  beds: number;
  baths: number;
  sqft: number;
  lotSize?: number; 
  yearBuilt?: number; 
}

// Define the structure for School and Demographics data
export interface GreatSchoolsScoresProps {
  elementary: number;
  middle: number;
  high: number;
}

export interface DemographicsDataProps {
  five_year_hh_income_growth: number;
  five_year_pop_growth: number;
  median_hh_income: number;
  total_households: number;
  total_population: number;
}

export interface MajorEmployerDataProps {
  companyName: string;
  locationEmployeeSizeActual: number;
  subcategory: string;
  distance: number;
}

export interface FavorableBrandProps {
  chain_name: string;
  distance: number;
}

export interface HouseholdCompositionProps {
  column_name: string;
  percentage: number;
  percentage2024: number;
  percentage2022: number;
}
