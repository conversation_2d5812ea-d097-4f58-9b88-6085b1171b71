declare module '@spatiallaser/map' {
  interface NearbyChainStore {
    hash_id: string;
    chain_id: string;
    chain_name: string;
    category: string;
    address: string;
    first_appeared: string;
    distance: number;
    geometry: any;
  }
  const useNearbyChainStore: () => {
    data: null | {
      favorable: Array<NearbyChainStore>;
      unfavorable: Array<NearbyChainStore>;
    };
    isLoading: boolean;
    isError: any;
  };
}

// Osano Cookie Consent Management
declare global {
  interface Window {
    Osano?: {
      cm?: {
        showDrawer?: (dialogType?: string) => void;
        addEventListener?: (event: string, callback: () => void) => void;
      };
    };
  }
}