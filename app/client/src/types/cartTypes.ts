import { LennarSinglePropertyDataType, ProFormaValuesLennar } from "@/lib/utils/types";

export interface CartItem {
  id?: number;
  cartId?: number;
  propertyId: number;
  
  // Property details (snapshot for display)
  propertyAddress: string;
  propertyCity?: string;
  propertyState?: string;
  propertyPostalCode?: string;
  propertyImage?: string;
  listedPrice?: number;
  beds?: number;
  baths?: number;
  sqft?: number;
  placekey?: string;
  
  // Property metadata for offers (using correct field names)
  communityName?: string;
  homesiteNumber?: string;
  floorplanName?: string;
  specPrice?: string;
  basePrice?: string;
  divisionName?: string;
  originalRent?: number;
  buyerFinalRent?: number;
  // Cart-specific data
  offerPrice?: number;
  isConfirmed: boolean;
  comments?: string;
  
  // Metadata
  createdAt?: string;
  updatedAt?: string;

  // payload
  payload?: {
    subjectProperty?: LennarSinglePropertyDataType,
    proforma?: ProFormaValuesLennar,
  }
}

export interface Cart {
  id?: number;
  userId: string;
  status: 'active' | 'archived' | 'submitted';
  totalItems: number;
  items: CartItem[];
  createdAt?: string;
  updatedAt?: string;
}

export interface BulkOfferSubmission {
  id?: number;
  cartId: number;
  userId: string;
  totalProperties: number;
  successfulSubmissions: number;
  failedSubmissions: number;
  submissionStatus: 'pending' | 'processing' | 'completed' | 'failed';
  
  // User contact info snapshot
  userEmail: string;
  userPhone?: string;
  userFirstName?: string;
  userLastName?: string;
  userRole: 'agent' | 'investor' | 'agent_investor';
  
  createdAt?: string;
  completedAt?: string;
}

export interface BulkOfferResult {
  id?: number;
  bulkSubmissionId: number;
  cartItemId: number;
  propertyId: number;
  offerPrice: number;
  submissionStatus: 'pending' | 'success' | 'failed';
  apiResponseId?: string;
  errorMessage?: string;
  submittedAt?: string;
}

export interface CartContextType {
  // Cart state
  cart: Cart | null;
  isLoading: boolean;
  error: string | null;
  
  // Cart operations
  addToCart: (propertyData: any, purchasePrice?: number, originalRent?: number, buyerFinalRent?: number, payload?: { subjectProperty?: LennarSinglePropertyDataType, proforma?: ProFormaValuesLennar }) => Promise<void>;
  removeFromCart: (propertyId: number) => Promise<void>;
  updateCartItem: (propertyId: number, updates: Partial<CartItem>) => Promise<void>;
  clearCart: () => Promise<void>;
  
  // Cart item operations
  toggleItemConfirmation: (propertyId: number) => Promise<void>;
  updateOfferPrice: (propertyId: number, offerPrice: number) => Promise<void>;
  
  // Bulk submission
  submitSelectedOffers: (userInfo: BulkSubmissionUserInfo) => Promise<BulkOfferSubmission>;
  
  // Utility functions
  getCartItemCount: () => number;
  getConfirmedItemCount: () => number;
  isPropertyInCart: (propertyId: number) => boolean;
  getCartItem: (propertyId: number) => CartItem | undefined;
  selectAllItems: () => Promise<void>;
  deselectAllItems: () => Promise<void>;
  
  // Modal state
  isCartModalOpen: boolean;
  setIsCartModalOpen: (open: boolean) => void;
  isOfferFlowOpen: boolean;
  setIsOfferFlowOpen: (open: boolean) => void;
}

export interface BulkSubmissionUserInfo {
  email: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  userRole: 'agent' | 'investor' | 'agent_investor';
  comments?: string; // For agents
  financingRequired?: boolean; // For investors
  PMRequired?: boolean; // For investors
}

// For converting from LennarSinglePropertyDataType to CartItem
export interface PropertyToCartConverter {
  propertyId: number;
  address: string;
  city?: string;
  state?: string;
  postalCode?: string;
  price?: number;
  beds?: number;
  baths?: number;
  sqft?: number;
  placekey?: string;
  offerPrice?: number; // Purchase price from Pro Forma
  // Metadata for offer submissions (using correct field names)
  communityName?: string;
  homesiteNumber?: string;
  floorplanName?: string;
  specPrice?: string;
  basePrice?: string;
  divisionName?: string;
  originalRent?: number;
  buyerFinalRent?: number;
  payload?: {
    subjectProperty?: LennarSinglePropertyDataType,
    proforma?: ProFormaValuesLennar,
  }
}

export type CartActionType = 
  | 'ADD_TO_CART'
  | 'REMOVE_FROM_CART'
  | 'UPDATE_CART_ITEM'
  | 'CLEAR_CART'
  | 'TOGGLE_CONFIRMATION'
  | 'UPDATE_OFFER_PRICE'
  | 'SET_LOADING'
  | 'SET_ERROR'
  | 'SET_CART'; 