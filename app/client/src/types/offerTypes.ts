import { LennarSinglePropertyDataType } from "@/lib/utils/types";

export enum OfferSubmissionStep {
    START = 'START',
    SUBMIT = 'SUBMIT',
    SUCCESS = 'SUCCESS'
}

// Define valid user role values
export type UserRole = "agent" | "investor" | "agent_investor";

export interface SimplifiedOffer {
    selectedBuyersViewRecord?: LennarSinglePropertyDataType;
    propertyId: number;
    address: string;
    questions: string;
    email: string;
    phone: string;
    firstName: string;
    lastName: string;
    offerPrice: number;
    comments: string;
    userRole: UserRole;
    financingRequired?: boolean;
    compensation?: string;
}