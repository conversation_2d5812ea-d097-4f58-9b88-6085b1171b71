import { LennarSinglePropertyDataType } from "@/lib/utils/types";

export enum OfferSubmissionStep {
    START = 'START',
    SUBMIT = 'SUBMIT',
    SUCCESS = 'SUCCESS'
}

// Define valid user role values
export type UserRole = "agent" | "investor" | "agent_investor";

export interface SimplifiedOffer {
    selectedBuyersViewRecord?: LennarSinglePropertyDataType;
    propertyId: number;
    cartItemId?: number;
    address: string;
    email: string;
    phone: string;
    firstName: string;
    lastName: string;
    offerPrice: number;
    comments?: string;
    userRole: UserRole;
    financingRequired?: boolean;
    PMRequired?: boolean;
    compensation?: string;
    bid_price?: number;
    spec_number?: string;
    community_name?: string;
    homesite_number?: string;
    floorplan_name?: string;
    division_name?: string;
    original_rent?: string | number;
    buyer_final_rent?: string | number;
    listPrice?: string | number;
}