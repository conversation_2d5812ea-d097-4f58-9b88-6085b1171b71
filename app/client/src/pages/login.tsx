import { withAuthenticator } from '@aws-amplify/ui-react';
import '@aws-amplify/ui-react/styles.css';
import { useNavigate } from '@tanstack/react-router';
import { useEffect } from 'react';

function LoginPage() {
  const navigate = useNavigate();

  useEffect(() => {
    // If user is authenticated, redirect to home
    navigate({ to: '/' });
  }, [navigate]);

  return null; // The Authenticator UI will be rendered by the HOC
}

export default withAuthenticator(LoginPage, {
  // hideSignUp: true,
  variation: 'modal',
}); 