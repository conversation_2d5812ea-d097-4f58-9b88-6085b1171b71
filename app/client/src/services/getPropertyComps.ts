import { CompsDetailsProps } from '../types/PropertyDetailPage.types';
import { getMockedComps } from './mockedData/mockComps';

// Simulate an async API call to fetch comps details for a property
const getPropertyComps = async (propertyId: string): Promise<CompsDetailsProps> => {
  console.log(`Fetching comps details for property ${propertyId}...`);
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500)); // Simulate delay

  // Generate mock comps
  const comps = getMockedComps(propertyId);
  console.log(`Fetched comps details.`);

  // Simulate a potential error randomly (e.g., 10% chance)
  // if (Math.random() < 0.10) {
  //   console.error("Simulated network error fetching comps.");
  //   throw new Error("Failed to fetch comps due to simulated network error.");
  // }

  return comps;
};

export default getPropertyComps;
