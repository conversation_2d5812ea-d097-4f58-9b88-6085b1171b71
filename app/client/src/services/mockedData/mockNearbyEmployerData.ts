import { MajorEmployerProps } from '../../types/PropertyDetailPage.types';
import { getRandomInt, getRandomFloat } from '../../lib/utils/randomUtils';

// Generates mock major employer data based on distance or drive time
export const getMockedNearbyEmployers = (
  propertyId: string, // Keep propertyId for potential future seeding/variation
  value: number,      // Represents either minutes or miles
  isDistanceMode: boolean // Determines if 'value' is distance (true) or time (false)
): MajorEmployerProps[] => {
  console.log(`Fetching nearby employers for property ${propertyId}...`);
  const count = getRandomInt(5, 15); // Generate 5 to 15 employers
  const employers: MajorEmployerProps[] = [];
  const categories = ["Engineering and Management Services", "Logistics", "Education", "Healthcare", "Retail"];
  const companyNames = ["Company A", "Company B", "Company C", "Company D", "Company E", "Company F", "Company G", "Company H"];

  // Estimate max distance based on mode
  // If distance mode, value is miles.
  // If time mode, estimate miles (e.g., 0.8 miles per minute)
  const maxDistance = isDistanceMode ? value : value * 0.8;
  // Ensure minimum distance is reasonable
  const minDistance = 0.5;

  for (let i = 0; i < count; i++) {
    employers.push({
      companyName: companyNames[getRandomInt(0, companyNames.length - 1)],
      // Generate distance within the calculated range
      distance: `${getRandomFloat(minDistance, Math.max(minDistance, maxDistance), 1)} mi`,
      employees: getRandomInt(50, 5000), // Wider range for employees
      category: categories[getRandomInt(0, categories.length - 1)],
    });
  }
  return employers;
};