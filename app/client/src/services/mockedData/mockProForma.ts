import { ProFormaDetails } from '../../types/PropertyDetailPage.types';
import { getRandomInt, getRandomFloat } from '@/lib/utils/randomUtils';

// Function to generate mock pro forma details for a given property ID
export const getMockedProForma = (propertyId: string): ProFormaDetails => {
  console.log(`Fetching pro forma details for property ${propertyId}...`);
  // Use propertyId to potentially vary the data slightly if needed
  // For now, we'll use the same logic as in mockPropertyList.js,
  // but ideally, this could be made more dynamic based on the ID.

  const askingPrice = getRandomInt(240000, 250000); // Example range
  const bidPriceVal = askingPrice * getRandomFloat(0.98, 1.0, 4); // Bid slightly around asking
  const projectedRent = getRandomInt(1500, 1800);
  const totalAnnualRentalIncome = projectedRent * 12;
  const propertyTaxPercentage = getRandomFloat(0.025, 0.028, 4);
  const propertyTaxAmount = askingPrice * propertyTaxPercentage;
  const insurance = getRandomInt(1000, 1200);
  const hoaFees = getRandomInt(0, 300); // Some might not have HOA
  const repairMaintenance = getRandomInt(200, 400);
  const propertyManagement = totalAnnualRentalIncome * getRandomFloat(0.08, 0.10, 2); // 8-10% of rent
  const vacancyLossPercentage = getRandomFloat(0.05, 0.08, 3); 
  const vacancyLossAmount = totalAnnualRentalIncome * vacancyLossPercentage;
  const communityDevelopmentFeePercentage = 0; // Example, adjust if needed
  const communityDevelopmentFeeAmount = askingPrice * communityDevelopmentFeePercentage;

  const totalAnnualExpenses =
    propertyTaxAmount +
    insurance +
    hoaFees +
    repairMaintenance +
    propertyManagement +
    vacancyLossAmount +
    communityDevelopmentFeeAmount;

  const netOperatingIncome = totalAnnualRentalIncome - totalAnnualExpenses;
  const projectedYield = netOperatingIncome / bidPriceVal;

  const loanToValue = getRandomFloat(0.65, 0.80, 2); // LTV 65-80%
  const mortgageAmount = bidPriceVal * loanToValue;
  const interestRate = getRandomFloat(0.055, 0.075, 3); // Interest rate 5.5-7.5%
  const loanTerm = 30; // Years
  // Simple mortgage calculation (approximation, real calc is more complex)
  // P = L * [c(1 + c)^n] / [(1 + c)^n – 1] where c = monthly interest rate, n = number of payments
  const monthlyInterestRate = interestRate / 12;
  const numberOfPayments = loanTerm * 12;
  const monthlyPayment = mortgageAmount * (monthlyInterestRate * Math.pow(1 + monthlyInterestRate, numberOfPayments)) / (Math.pow(1 + monthlyInterestRate, numberOfPayments) - 1);

  const annualDebtService = monthlyPayment * 12;
  const cashFlow = netOperatingIncome - annualDebtService; // Simplified cash flow before tax

  const closingCosts = getRandomInt(4000, 6000);
  const totalAcquisitionCost = bidPriceVal + closingCosts;
  const equityInvested = totalAcquisitionCost - mortgageAmount;
  const cashOnCashReturn = equityInvested > 0 ? cashFlow / equityInvested : 0;


  return {
    projectedNOI: netOperatingIncome, // Use calculated NOI
    projectedYield: projectedYield, // Use calculated yield
    expenses: {
      propertyTax: { percentage: propertyTaxPercentage, amount: propertyTaxAmount },
      insurance: insurance,
      hoaFees: hoaFees,
      repairMaintenance: repairMaintenance,
      propertyManagement: propertyManagement,
      vacancyLoss: { percentage: vacancyLossPercentage, amount: vacancyLossAmount },
      communityDevelopmentFee: { percentage: communityDevelopmentFeePercentage, amount: communityDevelopmentFeeAmount },
      totalAnnualExpenses: totalAnnualExpenses,
    },
    bidPrice: { askingPrice: askingPrice, bidPrice: bidPriceVal},
    rentalIncome: { projectedRent: projectedRent, otherMonthlyIncome: 0, totalAnnualRentalIncome: totalAnnualRentalIncome },
    // Note: cashFlow in mockPropertyList seems inconsistent with NOI/Debt. Using calculated value.
    cashFlow: cashFlow,
    mortgage: { amount: mortgageAmount, interestRate: interestRate, loanTerm: loanTerm, monthlyPayment: monthlyPayment },
    closingCosts: closingCosts,
    totalAcquisitionCost: totalAcquisitionCost,
    loanToValue: loanToValue,
    // communityDevelopmentFeePercentage was used above, maybe this is a different field? Assuming 0 for now.
    communityDevelopmentFeePercentage: 0,
    netOperatingIncome: netOperatingIncome, // Use calculated NOI
    cashOnCashReturn: cashOnCashReturn, // Use calculated CoC return
  };
};

// Helper function to get random integer (if not available globally)
// const getRandomInt = (min: number, max: number): number =>
//   Math.floor(Math.random() * (max - min + 1)) + min;

// Helper function to get random float (if not available globally)
// const getRandomFloat = (min: number, max: number, decimals: number): number =>
//   parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
