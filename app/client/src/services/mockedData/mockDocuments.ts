import { DocumentProps } from '../../types/PropertyDetailPage.types';

// Function to generate mock documents for a given property ID
export const getMockedDocuments = (propertyId: string, countPerCategory: number = 2): DocumentProps[] => {
  const documents: DocumentProps[] = [];
  const categories = ['other', 'interior images', 'exterior images'];
  const fileDetails: Record<string, { name: string; ext: string }[]> = {
    'other': [
      { name: 'InspectionReport', ext: 'pdf' },
      { name: 'Appraisal', ext: 'pdf' },
      { name: 'Survey', ext: 'pdf' },
      { name: 'TitlePolicy', ext: 'pdf' }
    ],
    'interior images': [
      { name: 'LivingRoom', ext: 'jpg' },
      { name: 'Kitchen', ext: 'jpg' },
      { name: 'Bedroom1', ext: 'jpg' },
      { name: 'Bathroom', ext: 'jpg' }
    ],
    'exterior images': [
      { name: 'FrontView', ext: 'jpg' },
      { name: 'Backyard', ext: 'jpg' },
      { name: 'SideView', ext: 'jpg' },
      { name: 'Garage', ext: 'jpg' }
    ]
  };

  let docCounter = 1;
  // Use propertyId to slightly vary the generated data if needed, e.g., in file names or links
  const propertyIdentifier = propertyId.substring(propertyId.length - 4); // Example: use last 4 chars

  categories.forEach(category => {
    const availableFiles = fileDetails[category];
    // Ensure we don't try to generate more unique docs than available per category
    const numToGenerate = Math.min(countPerCategory, availableFiles.length);

    for (let j = 0; j < numToGenerate; j++) {
      // Use modulo to cycle through available files if countPerCategory > availableFiles.length
      const fileInfo = availableFiles[j % availableFiles.length];
      const fileName = `${fileInfo.name}_${propertyIdentifier}_${docCounter}.${fileInfo.ext}`;
      // Simulate a download link structure
      const downloadLink = `https://example.com/download/${propertyId}/${category.replace(/\s+/g, '-')}/${fileName}`;

      documents.push({
        id: `doc-${propertyId}-${docCounter++}`, // Unique ID per document
        category: category,
        fileName: fileName,
        downloadLink: downloadLink,
      });
    }
  });
  return documents;
};
