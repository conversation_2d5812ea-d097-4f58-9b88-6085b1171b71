import { getRandomInt, getRandomFloat } from '../../lib/utils/randomUtils';

// Updated signature to accept value and isDistanceMode, matching the pattern
export const getMockedDemographicsData = (propertyId: string, value: number, isDistanceMode: boolean) => {
  // Use value to affect the data ranges, assuming value represents scale (time or distance)
  const multiplier = value / 10; // Base calculations on 10 (minutes or miles)
  console.log(`Fetching demographics data for property ${propertyId} on ${isDistanceMode?'distance mode': 'drive time mode'}...`);

  return {
    demographics: {
      totalPopulation: getRandomInt(50000 * multiplier, 100000 * multiplier).toLocaleString(),
      totalHousehold: getRandomInt(20000 * multiplier, 40000 * multiplier).toLocaleString(),
      populationGrowth5Years: `${getRandomFloat(0.5, 2.0, 2)}%`,
      householdIncomeGrowth5Years: `${getRandomFloat(10.0, 15.0, 2)}%`,
      medianHouseholdIncome: `$${getRandomFloat(45000, 60000, 2).toLocaleString()}`,
    },
    // majorEmployers removed
    favorableBrandsNearby: [], // Placeholder retained
    householdComposition: [], // Placeholder retained
  };
};

// Removed the generateMockMajorEmployers function definition from this file
