import fallbackImage from '../../assets/images/listing_fallback_4.png';
import { LennarSinglePropertyDataType } from '@/lib/utils/types'; // Import the existing type

// Define the type for the mocked property data, aligning with LennarSinglePropertyDataType
interface MockPropertyData extends LennarSinglePropertyDataType {}

function getMockedPropertyGeneralData(i: number): MockPropertyData {
  const getRandomInt = (min: number, max: number): number =>
    Math.floor(Math.random() * (max - min + 1)) + min;
  const getRandomFloat = (min: number, max: number, decimals: number): number =>
    parseFloat((Math.random() * (max - min) + min).toFixed(decimals));

  // Generate mock data, ensuring types match MockPropertyData (and thus LennarSinglePropertyDataType)
  return {
    thumbnail: fallbackImage,
    id: 201862 + i,
    source: "MLS", // Changed to string literal "MLS"
    citycode: "NBR", // Changed to string literal "NBR"
    city: "New Braunfels", // Changed to string literal "New Braunfels"    
    lotNumber: getRandomInt(100, 110),
    full_address: `${4023 + i} Barrow Trail, San Antonio, TX 78211`,
    price: String(getRandomInt(250000, 300000)), // Ensure price is a string
    beds: getRandomInt(2, 5),
    baths: getRandomInt(1, 3),
    sqft: getRandomInt(1200, 1600),
    capRate: String(getRandomFloat(0.04, 0.06, 3)), // Ensure capRate is a string
    estimated_complete: "2025-05-06",
    stories: 1,
    garage: 2,
    latitude: 29.325741611790725,
    longitude: -98.56613042494898,
    // communityDetails and homeDetails are optional in LennarSinglePropertyDataType,
    // so they can be omitted here or included with appropriate types if needed.
  };
}

export function getMockedHPropertyList(count: number): MockPropertyData[] {
  console.log('getMockedHPropertyList', count);
  const mockedHomePagePropertyDataArray: MockPropertyData[] = [];
  for (let i = 0; i < count; i++) {
    console.log('getMockedPropertyGeneralData(i)', getMockedPropertyGeneralData(i));
    mockedHomePagePropertyDataArray.push(getMockedPropertyGeneralData(i));
  }
  return mockedHomePagePropertyDataArray;
}
