import { GeneralDetailsProps } from "../../types/PropertyDetailPage.types";
import { getRandomInt, getRandomBool } from "../../lib/utils/randomUtils"; // Assuming random utils exist

// Function to generate mock general details for a given property ID
export const getMockedDetails = (propertyId: string): GeneralDetailsProps => {
  // Use propertyId to seed or vary data if needed, here just using simple random generation
  const seed = parseInt(propertyId.slice(-3), 16); // Example seed based on ID

  return {
    homeDetails: {
      floorplanName: `Plan ${String.fromCharCode(65 + (seed % 5))}`, // Plan A, B, C, D, E
      estimatedCloseOfEscrowDate: `2025-0${getRandomInt(5, 9)}-${getRandomInt(10, 28)}`,
      solar: getRandomBool(0.2) ? "Yes" : "No", // 20% chance of solar
      basement: getRandomBool(0.1) ? "Yes" : "No", // 10% chance of basement
      bonusRoom: getRandomBool(0.3) ? "Yes" : "No", // 30% chance of bonus room
      lotNumber: `${getRandomInt(100, 999)}`,
    },
    communityAndAmenityDetails: {
      name: `Community ${String.fromCharCode(88 + (seed % 3))}`, // Community X, Y, Z
      type: "Single Family",
      county: ["Comal", "Bexar", "Guadalupe"][seed % 3],
      schoolDistrict: ["Comal ISD", "Northside ISD", "East Central ISD"][
        seed % 3
      ],
      activeAdult: getRandomBool(0.05) ? "Yes" : "No", // 5% chance
      gatedCommunity: getRandomBool(0.15) ? "Yes" : "No", // 15% chance
      golf: getRandomBool(0.1) ? "Yes" : "No", // 10% chance
      pool: getRandomBool(0.6) ? "Yes" : "No", // 60% chance
      clubhouse: getRandomBool(0.4) ? "Yes" : "No", // 40% chance
      gym: getRandomBool(0.3) ? "Yes" : "No", // 30% chance
      park: getRandomBool(0.7) ? "Yes" : "No", // 70% chance
    },
  };
};
