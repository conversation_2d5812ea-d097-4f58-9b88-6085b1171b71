import { HouseholdCompositionProps } from '../../types/PropertyDetailPage.types';

export const getMockedHouseholdCompositionData = (
  propertyId: string,
  driveTime: number,
  isDistanceMode: boolean
): HouseholdCompositionProps[] => {
  console.log(`Generating mocked household composition data for property ${propertyId} with drive time/distance ${driveTime} ${isDistanceMode ? 'miles' : 'mins'}`);

  // Basic mock data - vary based on driveTime and isDistanceMode
  if (isDistanceMode) {
    if (driveTime <= 5) {
      return [
        { segment: 'P06 - Cultural Collection', percentage: '40.23%', link: '#' },
        { segment: 'B05 - Young Gamers', percentage: '30.15%', link: '#' },
        { segment: 'P07 - Fashion Followers', percentage: '25.50%', link: '#' },
        { segment: 'C06 - Family and Faith', percentage: '20.80%', link: '#' },
      ];
    } else if (driveTime <= 15) {
      return [
        { segment: 'P06 - Cultural Collection', percentage: '35.10%', link: '#' },
        { segment: 'B05 - Young Gamers', percentage: '28.90%', link: '#' },
        { segment: 'P07 - Fashion Followers', percentage: '22.30%', link: '#' },
        { segment: 'C06 - Family and Faith', percentage: '18.75%', link: '#' },
        { segment: 'A01 - Urban Professionals', percentage: '15.20%', link: '#' },
      ];
    } else {
      return [
        { segment: 'P06 - Cultural Collection', percentage: '30.00%', link: '#' },
        { segment: 'B05 - Young Gamers', percentage: '25.00%', link: '#' },
        { segment: 'P07 - Fashion Followers', percentage: '20.00%', link: '#' },
        { segment: 'C06 - Family and Faith', percentage: '15.00%', link: '#' },
        { segment: 'A01 - Urban Professionals', percentage: '12.00%', link: '#' },
        { segment: 'D10 - Suburban Families', percentage: '10.00%', link: '#' },
      ];
    }
  } else { // Drive Time mode
    if (driveTime <= 10) {
      return [
        { segment: 'P06 - Cultural Collection', percentage: '45.50%', link: '#' },
        { segment: 'B05 - Young Gamers', percentage: '35.20%', link: '#' },
        { segment: 'P07 - Fashion Followers', percentage: '30.10%', link: '#' },
      ];
    } else if (driveTime <= 30) {
      return [
        { segment: 'P06 - Cultural Collection', percentage: '40.00%', link: '#' },
        { segment: 'B05 - Young Gamers', percentage: '32.00%', link: '#' },
        { segment: 'P07 - Fashion Followers', percentage: '28.00%', link: '#' },
        { segment: 'C06 - Family and Faith', percentage: '22.00%', link: '#' },
      ];
    } else {
      return [
        { segment: 'P06 - Cultural Collection', percentage: '35.00%', link: '#' },
        { segment: 'B05 - Young Gamers', percentage: '28.00%', link: '#' },
        { segment: 'P07 - Fashion Followers', percentage: '24.00%', link: '#' },
        { segment: 'C06 - Family and Faith', percentage: '18.00%', link: '#' },
        { segment: 'A01 - Urban Professionals', percentage: '15.00%', link: '#' },
        { segment: 'D10 - Suburban Families', percentage: '12.00%', link: '#' },
        { segment: 'E15 - Rural Lifestyles', percentage: '8.00%', link: '#' },
      ];
    }
  }
};
