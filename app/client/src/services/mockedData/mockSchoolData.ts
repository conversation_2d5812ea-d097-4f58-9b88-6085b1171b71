import { getRandomInt } from '../../lib/utils/randomUtils';

export const getMockedSchoolData = (propertyId: string) => {
  console.log(`Fetching school data for property ${propertyId}...`);
  
  return {
    greatSchoolsScores: {
      elementary: getRandomInt(4, 9),
      middle: getRandomInt(5, 10),
      high: getRandomInt(4, 8),
    },
    floodZone: getRandomBool(0.05) ? 'Yes' : 'No',
    bachelorsOrAbove: `${getRandomInt(30, 60)}%`,
    crimeScore: `${getRandomInt(1, 5)}/10`,
    populationGrowth5Yr: `${getRandomInt(10, 30)}%`,
    incomeGrowth5Yr: `${getRandomInt(5, 20)}%`,
    medianHHIncome: `$${getRandomInt(60000, 90000).toLocaleString()}`,
  };
};

function getRandomBool(probability: number): boolean {
  return Math.random() < probability;
}
