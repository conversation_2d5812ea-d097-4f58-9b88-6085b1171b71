import { CompsDetailsProps, CompListingProps } from '../../types/PropertyDetailPage.types';
import { getRandomInt, getRandomFloat } from '../../lib/utils/randomUtils'; // Assuming random utils exist

function generateMockCompListings(count: number, baseAddressPrefix: number, cityState: string): CompListingProps[] {
  const listings: CompListingProps[] = [];
  const statuses = ["Closed", "Active", "Pending"];
  const baseYear = 2018;
  const baseRent = 1400;
  const baseSqFt = 1250;

  for (let k = 0; k < count; k++) {
    const beds = getRandomInt(2, 4); // 2-4 beds
    const baths = getRandomInt(2, 3); // 2-3 baths
    listings.push({
      address: `${baseAddressPrefix + k} Example St, ${cityState}`,
      distance: `${getRandomFloat(0.1, 1.5, 1)} mi`, // 0.1 to 1.6 mi
      status: statuses[k % statuses.length],
      rent: `$${baseRent + k * 50}`,
      built: `${baseYear + k}`,
      bedsBaths: `${beds}/${baths}`,
      sqFt: `${baseSqFt + k * 50}`,
      closedDate: `2025-0${getRandomInt(1, 9)}-${getRandomInt(10, 28)}` // Example date
    });
  }
  return listings;
}

// Function to generate mock comps details for a given property ID
export const getMockedComps = (propertyId: string): CompsDetailsProps => {
  // Use propertyId to seed or vary data if needed
  const seed = parseInt(propertyId.slice(-3), 16); // Example seed based on ID

  const mlsListings = generateMockCompListings(getRandomInt(3, 6), 1490 + seed, "New Braunfels, TX");
  const nationalSfrListings = generateMockCompListings(getRandomInt(1, 3), 1500 + seed, "New Braunfels, TX");
  const rentalPortalListings = generateMockCompListings(getRandomInt(1, 4), 1510 + seed, "New Braunfels, TX");

  return {
    estimatedMarketRent: `$${getRandomInt(1500, 2000).toLocaleString()}`,
    mls: {
      medianRentOfSelected: `$${getRandomInt(1400, 1800).toLocaleString()}`,
      total: mlsListings.length,
      listings: mlsListings
    },
    nationalSfr: {
      medianRentOfSelected: `$${getRandomInt(1400, 1800).toLocaleString()}`,
      total: nationalSfrListings.length,
      listings: nationalSfrListings
    },
    rentalPortal: {
      medianRentOfSelected: `$${getRandomInt(1400, 1800).toLocaleString()}`,
      total: rentalPortalListings.length,
      listings: rentalPortalListings
    },
    rentalPortal2: { // Add mock data for secondary rental portal
      medianRentOfSelected: `$${getRandomInt(1300, 1700).toLocaleString()}`,
      total: rentalPortalListings.length, // Can adjust count if needed
      listings: generateMockCompListings(getRandomInt(1, 3), 1520 + seed, "New Braunfels, TX") // Generate new listings
    }
  };
};
