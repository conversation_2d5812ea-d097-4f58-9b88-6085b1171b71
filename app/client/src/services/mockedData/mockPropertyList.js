import fallbackImage from '../../assets/images/listing_fallback_4.png';

function getMockedPropertyGeneralData(i) {
  const getRandomInt = (min, max) =>
    Math.floor(Math.random() * (max - min + 1)) + min;
  const getRandomFloat = (min, max, decimals) =>
    parseFloat((Math.random() * (max - min) + min).toFixed(decimals));

  return {
    thumbnail: fallbackImage,
    id: 201862 + i,
    source: "offmarket",
    citycode: "07",
    city: "San Antonio",
    lotNumber: getRandomInt(100, 110),
    full_address: `${4022 + i} Barrow Trail, San Antonio, TX 78211`,
    price: getRandomInt(250000, 300000),
    beds: getRandomInt(2, 5),
    baths: getRandomInt(1, 3),
    sqft: getRandomInt(1200, 1600),
    capRate: getRandomFloat(0.04, 0.06, 3),
    estimated_complete: "2025-05-06",
    stories: 1,
    garage: 2,
    // Note: Overwriting source, citycode, city for consistency with image example
    source: "MLS",
    citycode: "NBR",
    city: "New Braunfels",
    latitude: 29.3256 - i * 0.00005,
    longitude: -98.56613042494898 + i * 0.0002,
  };
}

export function getMockedHPropertyList(count) {
  const mockedHomePagePropertyDataArray = [];
  for (let i = 0; i < count; i++) {
    mockedHomePagePropertyDataArray.push(getMockedPropertyGeneralData(i));
  }
  return mockedHomePagePropertyDataArray;
}

