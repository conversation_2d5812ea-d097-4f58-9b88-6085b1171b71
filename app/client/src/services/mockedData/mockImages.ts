// Import the PropertyImage type. Adjust the path if necessary based on your project structure.
import { ImageProps } from '../../types/PropertyDetailPage.types';
import listingImage from '../../assets/images/image_sample.png';

// Function to generate mock images for a given property ID
export const getMockedImages = (propertyId: string, count: number = 5): ImageProps[] => {
  const mockedImages: ImageProps[] = [];
  const imageTypes = [
    { type: 'livingroom', title: 'Living Room' },
    { type: 'kitchen', title: 'Kitchen' },
    { type: 'bedroom', title: 'Master Bedroom' },
    { type: 'bathroom', title: 'Bathroom' },
    { type: 'exterior', title: 'Exterior View' },
    { type: 'patio', title: 'Patio Area' },
    { type: 'dining', title: 'Dining Area' },
    { type: 'office', title: 'Home Office' },
  ];

  for (let i = 0; i < count; i++) {
    // Select image type based on index and seed to vary images per property
    const imageInfo = imageTypes[i];
    // Generate a slightly different image URL based on index and seed
    mockedImages.push({
      id: `img-${propertyId}-${i + 1}`, // Unique ID per image
      url: listingImage,
      alt: `${imageInfo.title} for property ${propertyId}`,
      title: imageInfo.title, // Optional: Add title if needed by PropertyImage type
    });
  }

  return mockedImages;
};