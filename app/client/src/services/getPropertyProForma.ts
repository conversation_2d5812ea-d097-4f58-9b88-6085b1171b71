import { ProFormaDetails } from '../types/PropertyDetailPage.types';
import { getMockedProForma } from './mockedData/mockProForma';

// Simulate an async API call to fetch pro forma details for a property
const getPropertyProForma = async (propertyId: string): Promise<ProFormaDetails> => {
  console.log(`Fetching pro forma details for property ${propertyId}...`);
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 550)); // Slightly different delay

  // Generate mock pro forma details for the given property ID
  const proFormaData = getMockedProForma(propertyId);
  console.log(`Fetched pro forma details.`);
  console.log(proFormaData);

  // Simulate a potential error randomly (e.g., 10% chance)
  // if (Math.random() < 0.10) {
  //   console.error("Simulated network error fetching pro forma details.");
  //   throw new Error("Failed to fetch pro forma details due to simulated network error.");
  // }

  return proFormaData;
};

export default getPropertyProForma;
