import { MajorEmployerProps, DemographicsDataProps, GreatSchoolsScoresProps, HouseholdCompositionProps } from '../types/PropertyDetailPage.types';
import { getMockedSchoolData } from './mockedData/mockSchoolData';
import { getMockedDemographicsData } from './mockedData/mockDemographicsData';
import { getMockedNearbyEmployers } from './mockedData/mockNearbyEmployerData';
import { getMockedHouseholdCompositionData } from './mockedData/mockHouseholdCompositionData'; // Import the new mock data

// --- New Functions ---

// Fetches only school-related data
export const getPropertySchoolData = async (
  propertyId: string
): Promise<{
  greatSchoolsScores: GreatSchoolsScoresProps;
  floodZone: string; // Assuming floodZone is static school/property info
  bachelorsOrAbove: string; // Assuming bachelorsOrAbove is static school/property info
  crimeScore: string; // Changed to string to match mock data
  populationGrowth5Yr: string;
  incomeGrowth5Yr: string;
  medianHHIncome: string;
}> => {
  console.log(`Fetching school data for property ${propertyId}...`);
  await new Promise(resolve => setTimeout(resolve, 200)); // Simulate network delay
  const schoolData = getMockedSchoolData(propertyId); // This mock currently returns more than just school scores
  // Extract only the relevant parts if needed, or adjust mock data structure
  return {
    greatSchoolsScores: schoolData.greatSchoolsScores,
    floodZone: schoolData.floodZone,
    bachelorsOrAbove: schoolData.bachelorsOrAbove,
    crimeScore: schoolData.crimeScore,
    populationGrowth5Yr: schoolData.populationGrowth5Yr,
    incomeGrowth5Yr: schoolData.incomeGrowth5Yr,
    medianHHIncome: schoolData.medianHHIncome,
  };
};

// Fetches only demographics data based on drive time/distance
export const getPropertyDemographics = async (
  propertyId: string,
  driveTime: number,
  isDistanceMode: boolean
): Promise<{
  demographics: DemographicsDataProps;
  // Removed populationGrowth5Yr, incomeGrowth5Yr, medianHHIncome
  // Removed favorableBrands as it's not generated by the mock
  householdComposition: any; // Placeholder type
}> => {
  console.log(`Fetching demographics (${driveTime} ${isDistanceMode ? 'miles' : 'mins'}) for property ${propertyId}...`);
  await new Promise(resolve => setTimeout(resolve, 400)); // Simulate network delay
  // Pass demo-specific parameters to demographics mock
  const demographicsData = getMockedDemographicsData(propertyId, driveTime, isDistanceMode);
  return {
    demographics: demographicsData.demographics,
    householdComposition: demographicsData.householdComposition, // Keep placeholder if needed
  };
};

// Fetches only nearby employer data based on drive time/distance
export const getPropertyNearbyEmployers = async (
  propertyId: string,
  driveTime: number,
  isDistanceMode: boolean
): Promise<MajorEmployerProps[]> => {
  console.log(`Fetching employers (${driveTime} ${isDistanceMode ? 'miles' : 'mins'}) for property ${propertyId}...`);
  await new Promise(resolve => setTimeout(resolve, 300)); // Simulate network delay
  // Pass employer-specific parameters to the new employer mock
  const majorEmployers = getMockedNearbyEmployers(propertyId, driveTime, isDistanceMode);
  return majorEmployers;
};

// Fetches household composition data based on drive time/distance
export const getPropertyHouseholdComposition = async (
  propertyId: string,
  driveTime: number,
  isDistanceMode: boolean
): Promise<HouseholdCompositionProps[]> => {
  console.log(`Fetching household composition (${driveTime} ${isDistanceMode ? 'miles' : 'mins'}) for property ${propertyId}...`);
  await new Promise(resolve => setTimeout(resolve, 350)); // Simulate network delay
  const householdCompositionData = getMockedHouseholdCompositionData(propertyId, driveTime, isDistanceMode);
  return householdCompositionData;
};
