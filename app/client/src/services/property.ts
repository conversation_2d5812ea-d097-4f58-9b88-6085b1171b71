import { LennarSinglePropertyDataType } from "@/lib/utils/types"; // Ensure type is imported
import { getMockedHPropertyList } from "./mockedData/mockPropertyList";
import { getUserToken } from "@/lib/auth";

// Generate a larger list for pagination simulation if needed
const allMockedPropertyList = getMockedHPropertyList(50); // Increased size for testing pagination

// Define a type for the paginated response
interface PaginatedProperties {
  properties: LennarSinglePropertyDataType[];
  totalCount: number;
}

// Mock function to get property detail by ID
export const getPropertyDetail = async (id: number) => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 100));
  console.log(`Searching for property with ID: ${id}`);
  const property = allMockedPropertyList.find(p => p.id === id);
  console.log("Found property:", property);
  return property;
};


// --- Updated Data Fetching Functions with Pagination ---

const PAGE_SIZE = 10; // Define page size constant

// Simulates fetching listing data with pagination
export const getListingData = async (page: number = 1): Promise<PaginatedProperties> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 200)); // Simulate network latency

  // Filter or select data appropriate for "Listings" if necessary
  // For this mock, we'll use the first part of the list
  const listingSourceData = allMockedPropertyList.slice(0, 35); // Example: Listings are the first 35 items
  const totalCount = listingSourceData.length;
  const startIndex = (page - 1) * PAGE_SIZE;
  const endIndex = startIndex + PAGE_SIZE;
  const properties = listingSourceData.slice(startIndex, endIndex);

  console.log(`Fetched Listings Page: ${page}, Count: ${properties.length}, Total: ${totalCount}`);

  return { properties, totalCount };
};

// Simulates fetching submitted data with pagination
export const getSubmittedData = async (page: number = 1): Promise<PaginatedProperties> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 200)); // Simulate network latency

  // Filter or select data appropriate for "Submitted"
  // For this mock, we'll use a different part of the list
  const submittedSourceData = allMockedPropertyList.slice(35, 50); // Example: Submitted are items 35-49
  const totalCount = submittedSourceData.length;
  const startIndex = (page - 1) * PAGE_SIZE;
  const endIndex = startIndex + PAGE_SIZE;
  const properties = submittedSourceData.slice(startIndex, endIndex);

  console.log(`Fetched Submitted Page: ${page}, Count: ${properties.length}, Total: ${totalCount}`);

  return { properties, totalCount };
};

export const getListingData2 = async (page: number = 1): Promise<PaginatedProperties> => {
  // Simulate API call delay
  // await new Promise(resolve => setTimeout(resolve, 200)); // Simulate network latency

  const token = await getUserToken('access');
  console.log('token', token);

  const response = await fetch(`https://ukquo0kwrb.execute-api.us-east-1.amazonaws.com/production/properties?source=offmarket`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  const data = await response.json();
  console.log('data', data);

  const properties = data;
  const totalCount = data?.length;

  // Filter or select data appropriate for "Listings" if necessary
  // For this mock, we'll use the first part of the list
  // const listingSourceData = allMockedPropertyList.slice(0, 35); // Example: Listings are the first 35 items
  // const totalCount = listingSourceData.length;
  // const startIndex = (page - 1) * PAGE_SIZE;
  // const endIndex = startIndex + PAGE_SIZE;
  // const properties = listingSourceData.slice(startIndex, endIndex);

  console.log(`Fetched Listings: Count: ${properties.length}, Total: ${totalCount}`);

  return { properties, totalCount };
};