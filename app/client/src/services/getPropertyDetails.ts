import { GeneralDetailsProps } from '../types/PropertyDetailPage.types';
import { getMockedDetails } from './mockedData/mockDetails';

// Simulate an async API call to fetch general details for a property
const getPropertyDetails = async (propertyId: string): Promise<GeneralDetailsProps> => {
  console.log(`Fetching general details for property ${propertyId}...`);
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 350)); // Simulate delay

  // Generate mock details
  const details = getMockedDetails(propertyId);
  console.log(`Fetched general details.`);

  // Simulate a potential error randomly (e.g., 10% chance)
  // if (Math.random() < 0.10) {
  //   console.error("Simulated network error fetching details.");
  //   throw new Error("Failed to fetch details due to simulated network error.");
  // }

  return details;
};

export default getPropertyDetails;
