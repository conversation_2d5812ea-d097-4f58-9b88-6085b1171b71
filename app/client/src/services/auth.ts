import { Amplify } from 'aws-amplify';
import { signIn, signOut, fetchAuthSession } from 'aws-amplify/auth';

// Configure Amplify
Amplify.configure({
  Auth: {
    Cognito: {
      userPoolId: import.meta.env.VITE_COGNITO_USER_POOL_ID,
      userPoolClientId: import.meta.env.VITE_COGNITO_CLIENT_ID,
    }
  }
});

export const getCurrentSession = async () => {
  try {
    const session = await fetchAuthSession();
    const token = session.tokens?.accessToken.toString();
    if (token) {
      localStorage.setItem('authToken', token);
    }
    return token;
  } catch (error) {
    console.error('Error getting current session:', error);
    return null;
  }
};

export const signInUser = async (username: string, password: string) => {
  try {
    const { isSignedIn } = await signIn({ username, password });
    await getCurrentSession();
    return isSignedIn;
  } catch (error) {
    console.error('Error signing in:', error);
    throw error;
  }
};

export const signOutUser = async () => {
  try {
    await signOut();
    localStorage.removeItem('authToken');
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
}; 