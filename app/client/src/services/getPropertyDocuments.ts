// Import the DocumentProps type. Adjust the path if necessary based on your project structure.
import { DocumentProps } from '../types/PropertyDetailPage.types';
import { getMockedDocuments } from './mockedData/mockDocuments'; // Corrected import path

// Simulate an async API call to fetch documents for a property
const getPropertyDocuments = async (propertyId: string): Promise<DocumentProps[]> => {
  console.log(`Fetching documents for property ${propertyId}...`);
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 450)); // Slightly different delay than images

  // Generate mock documents for the given property ID
  // Let's generate 2 documents per category for variety
  const documents = getMockedDocuments(propertyId, 2);
  console.log(`Fetched ${documents.length} documents.`);

  // Simulate a potential error randomly (e.g., 15% chance)
  // if (Math.random() < 0.15) {
  //   console.error("Simulated network error fetching documents.");
  //   throw new Error("Failed to fetch documents due to simulated network error.");
  // }

  return documents;
};

export default getPropertyDocuments;
