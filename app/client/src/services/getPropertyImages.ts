// Import the PropertyImage type. Adjust the path if necessary based on your project structure.
import { ImageProps } from '../types/PropertyDetailPage.types.ts';
import { getMockedImages } from './mockedData/mockImages.ts';

// Simulate an async API call
const getPropertyImages = async (propertyId: string): Promise<ImageProps[]> => {
  console.log(`Fetching images for property ${propertyId}...`);
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 300));
  // Generate 8 mock images for the given property ID
  const images = getMockedImages(propertyId, 8);
  console.log(`Fetched ${images.length} images.`);
  // Simulate a potential error randomly (e.g., 10% chance)
  // if (Math.random() < 0.1) {
  //   console.error("Simulated network error fetching images.");
  //   throw new Error("Failed to fetch images due to simulated network error.");
  // }
  return images;
}

export default getPropertyImages;