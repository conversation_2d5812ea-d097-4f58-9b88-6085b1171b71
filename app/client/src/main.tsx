import React from 'react';
import ReactDOM from 'react-dom/client';

import App from './App';

import './index.css';

const rootElement = document.getElementById('root')!;

if (!rootElement) {
  throw new Error('No root element found');
}
import * as Sentry from "@sentry/react";

// Determine the environment for Sentry
const getSentryEnvironment = () => {
  const mode = import.meta.env.MODE;
  if (mode === 'development') {
    return 'local-prod';
  }
  return 'production';
};

Sentry.init({ 
  dsn: "https://<EMAIL>/4", 
  environment: getSentryEnvironment() 
});

ReactDOM.createRoot(rootElement).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
