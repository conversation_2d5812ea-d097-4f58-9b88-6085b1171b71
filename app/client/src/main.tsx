import React from 'react';
import ReactDOM from 'react-dom/client';

import App from './App';

import './index.css';

const rootElement = document.getElementById('root')!;

if (!rootElement) {
  throw new Error('No root element found');
}
// import * as Sentry from "@sentry/react";
// Sentry.init({ dsn: "http://25ecb02572844ff69a452e1484e27499@3.235.170.15:9041/4", environment: "development" });

ReactDOM.createRoot(rootElement).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
