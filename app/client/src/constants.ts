export const dateFormat = 'YYYY-MM-DD';

export const MAPBOX_TOKEN =
  'pk.**************************************************************************.-T2S1ZeAEBGxjC4rC0CZzA';

export const MAPBOX_STREET = 'sxbxchen/clb6ws0po002x15qpswyiwpov';
export const MAPBOX_SATELLITE = 'sxbxchen/clb6wti4f000014p25xhg3y2e';
export const MAPBOX_MONOCHROME = 'sxbxchen/clb6wpbs4003g14nzv7b2x5cx';
export const MAPBOX_TERRAIN = 'sxbxchen/clba2z24b001n14qr5h90kmxj';

export const MAP_LAYER_NAME_BASE = {
  parcel: 'parcelMapScope',
  parcelBoundary: 'parcelBoundaryMapScope',
  district: 'districtMapScope',
  activityCenter: 'activityCenterMapScope',
  county: 'county',
  zipcode: 'zipcode',
  opportunityZone: 'opportunityZone',
  attendance: 'attendance',
  cbsa: 'cbsa',
  floodZone: 'floodZone',
  poiChainLocation: 'poiChainLocation',
  circle: 'circle',
  BTOwned: 'BTOwned',
  multiFamily: 'multiFamily',
  mls: 'MLS',
  nationalOperator: 'nationalOperators',
  hotPads: 'HotPads',
  padSplit: 'PadSplit',
  kml: 'KML',
  iso: 'iso',
  mobile: 'mobile',
  newbuilds: 'newbuilds',
  publicRecord: 'publicRecord',
  affordableHousing: 'affordable-housing',
  landComp: 'land-comp',
  landMF: 'land-mf',
  landSF: 'land-sf',
  buyerListings: 'buyerListings',
  singlePortfolio: 'singlePortfolio',
};

export const OWNER_COLOR = {
  ownerOccupiedColor: '#8e8d8f',
  AH4RColor: '#f5222d',
  amherstColor: '#3d9146',
  cerberusColor: '#1890ff',
  invitationHomes: '#52c41a',
  progressResColor: '#08979c',
  triconColor: '#5047b9',
  momAndPopColor: '#8c8c8c',
  othersColor: '#faad14',
};

export const HOA_FEE_COLOR = {
  haoFeeRange1Color: '#4daf4a',
  haoFeeRange2Color: '#a8a802',
  haoFeeRange3Color: '#faad14',
  haoFeeRange4Color: '#ff7f00',
  haoFeeRange5Color: '#e41a1c',
};

export const ACTIVITY_CENTER_COLOR = {
  majorColor: '#FF9E1B',
  minorColor: '#F5CC00',
  monoColor: '#3182bd',
};

export const PARCEL_TYPE = {
  hoaFee: 'hoa_fees',
  owner: 'owners',
  subdivision: 'subdivision',
};

// zipcodes
function arrayUnique(array: any[]) {
  const a = array.concat();
  for (let i = 0; i < a.length; ++i) {
    for (let j = i + 1; j < a.length; ++j) {
      if (a[i] === a[j]) a.splice(j--, 1);
    }
  }
  return a;
}

export const dallasZipCodes = [
  750, 751, 752, 753, 754, 756, 757, 760, 761, 762, 763,
];
export const houstonZipCodes = [770, 771, 772, 773, 774, 775, 776, 777];
export const sanantonioZipCodes = [780, 781, 782, 788];
export const austinZipCodes = [765, 786, 787, 789];
export const carolinaZipCodes = [280, 281, 282, 283, 297];
export const realtracZipCodes = [370, 371, 372];
export const atlantaZipCodes = [300, 301, 302, 303, 305, 306, 310, 318];
export const nashvilleZipCodes = [370, 371, 372, 384, 385, 421, 422];
export const phoenixZipCodes = [850, 851, 852, 853, 856];
export const jacksonvilleZipCodes = [315, 316, 320, 321, 322, 326];
export const minneapolisZipCodes = [
  553, 562, 550, 563, 554, 551, 564, 560, 540, 548, 547, 566, 561, 559, 557,
  565
];
export const bakersfieldZipCodes = [932, 933];
export const miamiZipCodes = [330, 331, 332, 333, 334];

const tampa = [335, 336, 337, 338, 342, 344, 346];
const orlando = [321, 327, 328, 329, 335, 338, 344, 347, 349];
const lakelandWinter = [335, 338, 342, 347];
const homosassaSprings = [335, 344, 346];
const theVillages = [321, 335, 338, 344, 346, 347];
const northPort = [335, 338, 339, 342];
// [335, 336, 337, 338, 342, 344, 346, 321, 327, 328, 329, 347, 349, 339];
export const tampa_orlandoZipCodes = arrayUnique(
  tampa
    .concat(orlando)
    .concat(lakelandWinter)
    .concat(homosassaSprings)
    .concat(theVillages)
    .concat(northPort),
);

export const cincinnatiZipCodes = [
  403, 410, 450, 451, 452, 453, 454, 456, 470, 473,
];
export const columbusOhioZipCodes = [
  430, 431, 432, 433, 437, 438, 448, 449, 453, 456, 457,
];
export const oklahomaCityZipCodes = [730, 731, 740, 748];

export const tulsaCityZipCodes = [670, 671, 673, 740, 741, 743, 744, 746, 748];
export const detroitZipCodes = [480, 481, 482, 483, 484, 487, 488];
export const tulsaZipCodes = [740, 741, 744, 746];
export const stlouisZipCodes = [
  620, 622, 623, 625, 626, 628, 630, 631, 633, 636, 650, 654,
];

export const indianapolisZipCodes = [460, 461, 462, 472, 474];

export const triangleZipCodes = [272, 273, 275, 276, 277];

export const columnbiaSCZipCodes = [290, 291, 292];

export const huntsvilleZipCodes = [356, 357, 358];

export const kansasCityZipCodes = [640, 641, 644, 646, 647, 653];

export const memphisZipCodes = [380, 381];

export const pittsburghZipCodes = [
  150, 151, 152, 153, 154, 156, 157, 159, 160, 161, 162,
];

export const lasVegasZipCodes = [890, 891];

export const zipCodesAllMetros = [
  ...dallasZipCodes,
  ...houstonZipCodes,
  ...sanantonioZipCodes,
  ...austinZipCodes,
  ...carolinaZipCodes,
  // ...realtracZipCodes,
  ...atlantaZipCodes,
  ...nashvilleZipCodes,
  ...phoenixZipCodes,
  ...jacksonvilleZipCodes,
  ...minneapolisZipCodes,
  ...tampa_orlandoZipCodes,
  ...detroitZipCodes,
  ...bakersfieldZipCodes,
  ...miamiZipCodes,
  ...indianapolisZipCodes,
  ...triangleZipCodes,
  ...columnbiaSCZipCodes,
  ...oklahomaCityZipCodes,
  ...tulsaZipCodes,
  ...huntsvilleZipCodes,
  ...kansasCityZipCodes,
  ...memphisZipCodes,
  ...pittsburghZipCodes,
  ...lasVegasZipCodes,
];

export const groupOneChainIds = [
  '4539',
  '1058',
  '3937',
  '88',
  '4978',
  '5295',
  '3535',
];
export const groupTwoChainIds = ['1463', '1709', '1465', '2078'];

export const geojsonTemplate = {
  type: 'FeatureCollection',
  features: [],
};

export const LAND_DEVELOPMENT_FILTERS = {
  lotSize: {
    isChecked: true,
    type: '<->',
    min: 20,
    max: 100,
  },
  schoolScore: {
    isChecked: true,
    type: '<->',
    min: 4,
    max: 10,
  },
  improvementRatio: {
    isChecked: true,
    type: '<->',
    min: 0,
    max: 20,
  },
  medianRent: {
    isChecked: true,
    type: '<->',
    min: 1500,
    max: 3000,
  },
  homeValue: {
    isChecked: true,
    type: '<->',
    min: 250000,
    max: 600000,
  },
  householdIncome: {
    isChecked: true,
    type: '<->',
    min: 70000,
    max: 120000,
  },
  population: {
    isChecked: true,
    type: '<->',
    min: 20000,
    max: 80000,
  },
  tax_rate: {
    isChecked: false,
    type: '<->',
    min: 0,
    max: 1.5,
  },
  assessmentValuePerAcre: {
    isChecked: false,
    type: '<=',
    max: 20000,
  },
  nearestHighway: {
    isChecked: false,
    type: '<=',
    max: 0.5,
  },
  nearestPowerLine: {
    isChecked: false,
    type: '>=',
    min: 0.5,
  },
  floodCoverageWithin50: {
    type: 'boolean',
    isChecked: false,
  },
  buildingCoverage: {
    isChecked: false,
    type: '<->',
    min: 0,
    max: 20,
  },
  withinOpportunityZone: {
    type: 'boolean',
    isChecked: false,
  },
  withinResidentialDevelopment: {
    type: 'boolean',
    isChecked: false,
  },
  notSoldRecently: {
    type: 'string',
    isChecked: true,
    value: '1 year',
  },
  landUseCategory: {
    type: 'string[]',
    isChecked: false,
    value: [],
  },
  landUseType: {
    type: 'string[]',
    isChecked: false,
    value: [],
  },
  boundaryLength: {
    isChecked: false,
    type: '<->',
    min: 500,
    max: 1000,
  },
  boundaryWidth: {
    isChecked: false,
    type: '<->',
    min: 500,
    max: 1000,
  },
  boundaryLxWRatio: {
    isChecked: true,
    type: '<=',
    max: 2,
  },
  listingType: {
    isChecked: true,
    type: 'string',
    value: 'mls',
  },
  mlsListing: {
    isChecked: true,
    filters: {
      status: {
        isChecked: true,
        type: 'string',
        value: 'Active',
      },
      lotSize: {
        isChecked: false,
        type: '<->',
        min: 0,
        max: 100,
      },
      price: {
        isChecked: false,
        type: '<->',
        min: 0,
        max: 500000,
      },
    },
  },
  showcase: {
    isChecked: true,
    filters: {
      lotSize: {
        isChecked: false,
        type: '<->',
        min: 0,
        max: 100,
      },
      for_type: {
        isChecked: false,
        type: 'string',
        value: [],
      },
      sub_type: {
        isChecked: false,
        type: 'string',
        value: [],
      },
      sale_price: {
        isChecked: false,
        type: '<->',
        min: 0,
        max: 500000,
      },
    },
  },
};
