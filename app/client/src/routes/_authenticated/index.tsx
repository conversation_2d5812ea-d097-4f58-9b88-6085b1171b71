import { createFileRoute, redirect } from '@tanstack/react-router';
import { underwritingSource } from '@/lib/utils/types';

export const Route = createFileRoute('/_authenticated/')({
  beforeLoad: async () => {
    return redirect({
      from: '/',
      to: '/properties',
      search: {
        source: "offmarket" as underwritingSource,
        listType: "listings" as "submitted" | "listings" | "bookmarked",
        viewStyle: "grid" as "grid" | "list",
      },
    });
  },
});
