import { createFileRoute, Outlet, useLoaderData } from '@tanstack/react-router';
import { getProperties } from '@/lib/query/get-properties';
import { getSubmittedProperties } from '@/lib/query/get-properties-submitted';
import { getUserToken } from '@/lib/utils/auth';
import { useEffect } from 'react';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { underwritingSource } from '@/lib/utils/types';

export const Route = createFileRoute('/_authenticated/properties')({
  validateSearch: (search: { source: underwritingSource, listType: "submitted" | "listings", viewStyle: "grid" | "list" }) => {
    if (!search.source || !search.listType || !search.viewStyle) {
      return {
        source: "offmarket" as underwritingSource,
        listType: "listings" as "submitted" | "listings",
        viewStyle: "grid" as "grid" | "list",
      };
    }
    return {
      source: search.source as underwritingSource,
      listType: search.listType as "submitted" | "listings",
      viewStyle: search.viewStyle as "grid" | "list",
    };
  },
  loaderDeps: ({ search: { source, listType, viewStyle } }) => {
    return {
      source,
      listType,
      viewStyle,
    };
  },
  loader: async ({ deps: { source, listType } }) => {
    try {
      const properties = await getProperties(source);
      const propertiesSubmitted = await getSubmittedProperties(source);
      return {
        properties,
        propertiesSubmitted,
        error: null,
      };
    } catch (err) {
      return {
        properties: [],
        propertiesSubmitted: [],
        error: err instanceof Error ? err.message : "Failed to load properties.",
      };
    }
  },
  component: PropertyListLayoutComponent,
});

// Extend Window interface for spatiallaser
declare global {
  interface Window {
    spatiallaser?: {
      getUserToken: typeof getUserToken;
    };
  }
}

if (!window.spatiallaser) {
  window.spatiallaser = {
    getUserToken,
  };
}

function PropertyListLayoutComponent() {
  const { properties, propertiesSubmitted } = useLoaderData({ from: '/_authenticated/properties' });
  const { setAllProperties, setAllPropertiesSubmitted } = useMarketplaceMapContext();

  useEffect(() => {
    if (properties && Array.isArray(properties)) {
      setAllProperties(properties); // for map
    }
    else {
      setAllProperties([]);
    }
  }, [properties]);

  useEffect(() => {
    if (propertiesSubmitted && Array.isArray(propertiesSubmitted)) {
      setAllPropertiesSubmitted(propertiesSubmitted); // for map
    } else {
      setAllPropertiesSubmitted([]);
    }
  }, [propertiesSubmitted]);

  return (
    <div
      key='main-layout-container'
      className='flex h-full w-full max-w-[1024px] overflow-hidden justify-center items-center mx-auto'
    >
      <Outlet />
    </div>
  )
}