import { createFileRoute, Outlet, useLoaderData } from '@tanstack/react-router';
import { getProperties } from '@/lib/query/get-properties';
import { getSubmittedProperties } from '@/lib/query/get-properties-submitted';
import { getUserToken } from '@/lib/utils/auth';
import { useEffect } from 'react';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { underwritingSource } from '@/lib/utils/types';
import { bookmarkManager } from '@/lib/utils/bookmarkManager';
import SkeletonCards from '@/components/Common/SkeletonCards';

export const Route = createFileRoute('/_authenticated/properties')({
  validateSearch: (search: {
    source: underwritingSource,
    listType: "submitted" | "listings" | "bookmarked",
    viewStyle: "grid" | "list",
    offset?: number,
    filter?: string,
    sort?: "basePrice" | "yieldOnBidPrice",
    direction?: "asc" | "desc",
  }) => {
    if (!search.source || !search.listType || !search.viewStyle) {
      return {
        source: "offmarket" as underwritingSource,
        listType: "listings" as "submitted" | "listings" | "bookmarked",
        viewStyle: "grid" as "grid" | "list",
      };
    }
    return {
      source: search.source as underwritingSource,
      listType: search.listType as "submitted" | "listings" | "bookmarked",
      viewStyle: search.viewStyle as "grid" | "list",
      offset: search.offset,
      filter: search.filter,
      sort: search.sort,
      direction: search.direction,
    };
  },
  loaderDeps: ({ search: { source } }) => {
    return {
      source,
      // Removed filter, sort, direction, offset - we only fetch all data once and filter on frontend
    };
  },
  staleTime: Infinity, // Never automatically refetch - only when source changes
  gcTime: 1000 * 60 * 30, // Keep in cache for 30 minutes
  loader: async ({ deps: { source } }) => {
    try {
      // make getProperties and getSubmittedProperties requests in parallel
      const [properties, propertiesSubmittedAll] = await Promise.all([
        getProperties({
          source,
          // Removed filter, sort, direction, offset - get all data and filter on frontend
        }),
        getSubmittedProperties(source),
      ]);
      const propertiesSubmitted = propertiesSubmittedAll.filter((property) => property.decision === 'approve');
      
      const propertiesBookmarked = propertiesSubmittedAll.filter((property) => 
        property.decision === 'approve with condition' && 
        property.payload?.proforma?.buyAndHold !== null
      );

      // Sync backend data with local storage for offline/optimistic updates
      bookmarkManager.syncWithBackend(propertiesBookmarked);

      // Get optimistic bookmark list (includes local changes not yet synced)
      const localBookmarkIds = bookmarkManager.getBookmarkedPropertyIds();
      const localBookmarkSet = new Set(localBookmarkIds);
      
      const optimisticBookmarked = [
        // Keep backend bookmarks that are still bookmarked locally OR not in local storage (untouched)
        ...propertiesBookmarked.filter(prop => 
          !localBookmarkSet.has(prop.property_id) || // Not in local storage (untouched by user)
          bookmarkManager.isBookmarked(prop.property_id) // Still bookmarked locally
        ),
        // Add locally bookmarked properties that aren't in backend yet
        ...properties.filter(prop => 
          localBookmarkIds.includes(prop.property_id) && 
          !propertiesBookmarked.some(bp => bp.property_id === prop.property_id)
        )
      ];

      // dedupe: remove properties in `properties` that are also in `optimisticBookmarked` 
      const propertiesDeduped = properties.filter((property) => !optimisticBookmarked.some((bookmarkedProperty) => bookmarkedProperty.property_id === property.property_id));

      // Always count total properties since filtering is now done on frontend
      const totalNumberOfProperties = propertiesDeduped.length;

      return {
        properties: propertiesDeduped,
        propertiesSubmitted,
        propertiesBookmarked: optimisticBookmarked,
        totalNumberOfProperties,
        error: null,
      };
    } catch (err) {
      return {
        properties: [],
        propertiesSubmitted: [],
        propertiesBookmarked: [],
        totalNumberOfProperties: 0,
        error: err instanceof Error ? err.message : "Failed to load properties.",
      };
    }
  },
  shouldReload: false, // Never reload automatically - only when source changes via loaderDeps
  // No pendingComponent - we don't want loading states when only filters change
  component: PropertyListLayoutComponent,
});

// Extend Window interface for spatiallaser
declare global {
  interface Window {
    spatiallaser?: {
      getUserToken: typeof getUserToken;
    };
  }
}

if (!window.spatiallaser) {
  window.spatiallaser = {
    getUserToken,
  };
}

function PropertyListLayoutComponent() {
  const { properties, propertiesSubmitted, propertiesBookmarked, totalNumberOfProperties } = useLoaderData({ from: '/_authenticated/properties' });
  const { setAllProperties, setAllPropertiesSubmitted, setAllPropertiesBookmarked, setTotalNumberOfProperties } = useMarketplaceMapContext();

  useEffect(() => {
    if (properties && Array.isArray(properties)) {
      setAllProperties(properties); // for map
    }
    else {
      setAllProperties([]);
    }
  }, [properties]);

  useEffect(() => {
    if (propertiesSubmitted && Array.isArray(propertiesSubmitted)) {
      setAllPropertiesSubmitted(propertiesSubmitted); // for map
    } else {
      setAllPropertiesSubmitted([]);
    }
  }, [propertiesSubmitted]);

  useEffect(() => {
    if (propertiesBookmarked && Array.isArray(propertiesBookmarked)) {
      setAllPropertiesBookmarked(propertiesBookmarked); // for map
    } else {
      setAllPropertiesBookmarked([]);
    }
  }, [propertiesBookmarked]);

  useEffect(() => {
    if (totalNumberOfProperties) {
      setTotalNumberOfProperties(totalNumberOfProperties);
    }
  }, [totalNumberOfProperties]);

  return (
    <div
      key='main-layout-container'
      className='flex h-full w-full max-w-[1024px] overflow-hidden justify-center items-center mx-auto'
    >
      <Outlet />
    </div>
  )
}