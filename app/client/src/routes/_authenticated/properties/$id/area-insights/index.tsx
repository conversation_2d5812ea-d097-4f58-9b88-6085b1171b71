import { createFileRoute, getRouteApi } from '@tanstack/react-router'
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { defaultDistance } from '@/constants/tabInsights';
import { TabInsights } from '@/components/PropertyDetails/TabInsights/TabInsights';
import { getOwnerRenterCounts } from '@/lib/utils/getOwnerRenterCounts';
import { useCompositionData } from '@/hooks/useCompositionData';

export const Route = createFileRoute('/_authenticated/properties/$id/area-insights/')(
  {
    component: RouteComponent,
  },
)

function RouteComponent() {
  const { setPropertyModalTabKey } = useMarketplaceMapContext();
  setPropertyModalTabKey('area-insights');

  const routeApiSingleProperty = getRouteApi('/_authenticated/properties/$id');
  const { areaInsightsData, parcelData } = routeApiSingleProperty.useLoaderData();

  const parcelOwnerSummary = areaInsightsData?.parcelOwnerSummary;
  const rawBathroomData = areaInsightsData?.unitMixData?.bathrooms;
  const rawBedroomData = areaInsightsData?.unitMixData?.bedrooms;
  const rawSqftData = areaInsightsData?.unitMixData?.sqft;
  const rawYearData = areaInsightsData?.unitMixData?.year;

  let ownershipData = [{ name: "Owner/Renter", owner: 0, renter: 0 }];
  let total = 0;

  if (parcelOwnerSummary) {
    const { owner, renter } = getOwnerRenterCounts(parcelOwnerSummary.owner_count || []);
    ownershipData = [{ name: "Owner/Renter", owner, renter }];
    total = parcelOwnerSummary.total_parcels || 0;
  }

  const {
    bedroomData,
    bathroomData,
    squareFeetData,
    yearBuiltData,
    isLoading,
  } = useCompositionData(
    rawBedroomData,
    rawBathroomData,
    rawSqftData,
    rawYearData,
    parcelData
  );

  return (
    <>
      <TabInsights
        ownershipData={ownershipData}
        totalParcels={total}
        distance={defaultDistance}
        bedroomData={bedroomData}
        bathroomData={bathroomData}
        squareFeetData={squareFeetData}
        yearBuiltData={yearBuiltData}
        isLoading={isLoading}
      />
    </>
  )
}
