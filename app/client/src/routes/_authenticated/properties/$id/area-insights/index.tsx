import React from 'react';
import { createFileRoute, getRouteApi, useSearch } from '@tanstack/react-router'
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { defaultDistance } from '@/constants/tabInsights';
import { TabInsights } from '@/components/PropertyDetails/TabInsights/TabInsights';
import { getOwnerRenterCounts } from '@/lib/utils/getOwnerRenterCounts';
import { useCompositionData } from '@/hooks/useCompositionData';

export const Route = createFileRoute('/_authenticated/properties/$id/area-insights/')(
  {
    component: RouteComponent,
  },
)

function RouteComponent() {
  const { setPropertyModalTabKey } = useMarketplaceMapContext();
  React.useEffect(() => {
    setPropertyModalTabKey('area-insights');
  }, []);

  const routeApiPropertyList = getRouteApi('/_authenticated/properties');
  const routeApiSingleProperty = getRouteApi('/_authenticated/properties/$id');
  const { areaInsightsData, parcelData } = routeApiSingleProperty.useLoaderData();
  const { properties, propertiesSubmitted, propertiesBookmarked } = routeApiPropertyList.useLoaderData();

  const currentPropertyId = routeApiSingleProperty.useParams().id;
  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;

  // Get the current property data
  const currentProperties = listType === 'listings' ? properties : listType === 'submitted' ? propertiesSubmitted : propertiesBookmarked;
  const selectedBuyersViewRecord = currentProperties.find(
    (property) => property.property_id === +currentPropertyId
  );

  const propertyBeds = selectedBuyersViewRecord?.payload?.subjectProperty?.beds;
  const propertyBaths = selectedBuyersViewRecord?.payload?.subjectProperty?.baths;
  const propertySqft = selectedBuyersViewRecord?.payload?.subjectProperty?.sqft;

  const parcelOwnerSummary = areaInsightsData?.parcelOwnerSummary;
  const rawBathroomData = areaInsightsData?.unitMixData?.bathrooms;
  const rawBedroomData = areaInsightsData?.unitMixData?.bedrooms;
  const rawSqftData = areaInsightsData?.unitMixData?.sqft;
  const rawYearData = areaInsightsData?.unitMixData?.year;

  let ownershipData = [{ name: "Owner/Renter", owner: 0, renter: 0 }];
  let total = 0;

  if (parcelOwnerSummary) {
    const { owner, renter } = getOwnerRenterCounts(parcelOwnerSummary.owner_count || []);
    ownershipData = [{ name: "Owner/Renter", owner, renter }];
    total = parcelOwnerSummary.total_parcels || 0;
  }

  const {
    bedroomData,
    bathroomData,
    squareFeetData,
    yearBuiltData,
    isLoading,
  } = useCompositionData(
    rawBedroomData,
    rawBathroomData,
    rawSqftData,
    rawYearData,
    parcelData,
    propertyBeds,
    propertyBaths,
    propertySqft
  );

  return (
    <>
      <TabInsights
        ownershipData={ownershipData}
        totalParcels={total}
        distance={defaultDistance}
        bedroomData={bedroomData}
        bathroomData={bathroomData}
        squareFeetData={squareFeetData}
        yearBuiltData={yearBuiltData}
        isLoading={isLoading}
      />
    </>
  )
}
