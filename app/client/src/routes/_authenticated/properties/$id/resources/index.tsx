import React from 'react';
import { createFileRoute, getRouteApi, useSearch } from '@tanstack/react-router'
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import TabResources from '../../../../../components/PropertyDetails/TabResources/TabResources';
import { getFormattedPropertyManagements } from '@/lib/utils/formatUtils';

export const Route = createFileRoute(
  '/_authenticated/properties/$id/resources/',
)({

  component: RouteComponent,
})

function RouteComponent() {
  const routeApiPropertyList = getRouteApi('/_authenticated/properties');
  const { properties, propertiesSubmitted, propertiesBookmarked } = routeApiPropertyList.useLoaderData();
  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;
  const currentProperties = listType === 'listings' ? properties : listType === 'submitted' ? propertiesSubmitted : propertiesBookmarked;
  const routeApiSingleProperty = getRouteApi('/_authenticated/properties/$id');
  const currentPropertyId = routeApiSingleProperty.useParams().id;
  const currentPropertyData = currentProperties.find((property) => property.property_id === +currentPropertyId);
  const data = currentPropertyData?.payload?.subjectProperty.meta.pm_fee_by_market || null;

  const formatData = getFormattedPropertyManagements(data);
  const { setPropertyModalTabKey } = useMarketplaceMapContext();
  React.useEffect(() => {
    setPropertyModalTabKey('resources');
  }, []);

  return (
    <>
      <TabResources data={formatData} />
    </>
  )

}
