import { createFileRoute, getRoute<PERSON>pi, useSearch } from '@tanstack/react-router'
import type { GreatSchoolsScoresProps } from '@/types/PropertyDetailPage.types';
import TabDemographics, { SummaryCardsProps } from '@/components/PropertyDetails/TabDemographics/TabDemographics';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { DemographicsProvider } from '@/contexts/DemographicsContext';


export const Route = createFileRoute(
  '/_authenticated/properties/$id/demographics/',
)({
  component: RouteComponent,
})

function RouteComponent() {

  const routeApiPropertyList = getRouteApi('/_authenticated/properties');
  const { properties, propertiesSubmitted } = routeApiPropertyList.useLoaderData();

  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;

  const currentProperties = listType === 'listings' ? properties : propertiesSubmitted;

  const routeApiSingleProperty = getRouteApi('/_authenticated/properties/$id');
  const currentPropertyId = routeApiSingleProperty.useParams().id;
  const { demographicData } = routeApiSingleProperty.useLoaderData();

  const currentPropertyData = currentProperties.find((property) => property.property_id === +currentPropertyId);

  const { setPropertyModalTabKey } = useMarketplaceMapContext();
  setPropertyModalTabKey('demographics');

  // TODO: score_2023 and score_2022 exists. This might be change over time, so we should handle it dynamically in the future.
  const greatSchoolsScores: GreatSchoolsScoresProps = {
    elementary: demographicData[0].school.elem.score_2023,
    middle: demographicData[0].school.middle.score_2023,
    high: demographicData[0].school.high.score_2023,
  }

  const incomeGrowth5Yr = demographicData[0].fiveyearincomegrowth;
  const populationGrowth5Yr = demographicData[0].fiveyearpopgrowth;
  const floodZone = demographicData[0].fld_zone;
  const bachelorsOrAbove = demographicData[0].bachelorsandabove;
  const crimeScore = demographicData[0].crime_score;
  const medianHHIncome = demographicData[0].medianhhincome;


  const SummaryCards: SummaryCardsProps = {
    greatSchoolsScores,
    incomeGrowth5Yr,
    populationGrowth5Yr,
    floodZone,
    bachelorsOrAbove,
    crimeScore,
    medianHHIncome,
  };

  return (
    <DemographicsProvider>
      {currentPropertyData &&
        <TabDemographics
          summaryCardsData={SummaryCards}
        />}
    </DemographicsProvider>
  );
}
