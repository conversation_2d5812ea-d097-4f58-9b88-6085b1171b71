import React from 'react';
import { createFileRoute, getRouteApi, useSearch } from '@tanstack/react-router'
import ProFormaLennar from '@/components/PropertyDetails/TabProForma2/ProFormaLennar2';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
// import { getHomePriceAppreciationData } from '@/lib/query/get-home-price-appreciation-data';

export const Route = createFileRoute(
  '/_authenticated/properties/$id/proForma/',
)({
  // validateSearch: (search) => {
  //   return {
  //     zip_code: search.zip_code as string,
  //   };
  // },
  // loaderDeps: ({ search: { zip_code } }) => { 
  //   return {
  //     zip_code,
  //   };
  // },
  // loader: async ({ deps: { zip_code } }) => {
  //   const homePriceAppreciationData = await getHomePriceAppreciationData({ zipCode: zip_code });
  //   // console.log('homePriceAppreciationData', homePriceAppreciationData);
  //   return { homePriceAppreciationData };
  // },
  component: ComponentProForma,
});

function ComponentProForma() {
  const routeApiSingleProperty = getRouteApi('/_authenticated/properties/$id');

  const routeApiPropertyList = getRouteApi('/_authenticated/properties');
  const { properties, propertiesSubmitted, propertiesBookmarked } = routeApiPropertyList.useLoaderData();

  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;

  const currentProperties = listType === 'listings' ? properties : listType === 'submitted' ? propertiesSubmitted : propertiesBookmarked;

  const currentPropertyId = routeApiSingleProperty.useParams().id;
  // then use the id to find the property data in the property list data
  const currentPropertyData = currentProperties.find((property) => property.property_id === +currentPropertyId);

  // const { homePriceAppreciationData } = useLoaderData({ from: '/_authenticated/properties/$id/proForma/' });

  const { setPropertyModalTabKey } = useMarketplaceMapContext();

  React.useEffect(() => {
    setPropertyModalTabKey('proForma');
  }, []);

  return (
    <ProFormaLennar
      subjectPropertyData={currentPropertyData}
    />
  )
};
