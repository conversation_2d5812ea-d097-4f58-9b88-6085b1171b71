import { createFile<PERSON>oute, getRoute<PERSON><PERSON>, Outlet, useSearch, useMatch } from '@tanstack/react-router'
import { getParcelData } from '@/lib/query/get-parcel-data';
import { getDemographicData } from '@/lib/query/get-demographic-data';
import { useEffect, useState } from 'react';
import PropertyOverview from '@/components/PropertyDetails/PropertyOverview';
import OfferSubmissionFlow from '@/components/Modal/OfferSubmissionFlow';
import { useAuthenticator } from '@aws-amplify/ui-react';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { CompsProvider } from '@/contexts/CompsContext';
import { FormSubmissionProvider } from '@/contexts/FormSubmissionContext';
import { formatPrice, getFormattedAddress, removeAllDecimal } from '@/lib/utils/formatUtils';
import { usePropertyImages } from '@/hooks/usePropertyImages';
import { LennarSinglePropertyDataType } from '@/lib/utils/types';
import { getParcelOwnerSummary } from '@/lib/query/get-parcel-owner-summary';
import { defaultDistance } from '@/constants/tabInsights';
import { getParcelUnitMixData } from '@/lib/query/get-parcel-unit-mix-data';
import { getHomePriceAppreciationData } from '@/lib/query/get-home-price-appreciation-data';
import { Skeleton } from '@/components/ui/skeleton';
import { sendSinglePropertyLogData } from '@/lib/query/send-single-property-log';
import { usePanelState } from '@/hooks/usePanelState';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import MobileFooter from '@/components/MobileFooter';
import { ScrollProvider } from '@/contexts/ScrollContext';
import { useScroll } from '@/hooks/useScroll';

export interface singlePropertySearchParams {
  placekey: string;
  lat: number;
  lng: number;
  streetnum: string;
  source: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  latitude: string;
  longitude: string;
  beds: string;
  baths: string;
  sqft: string;
  yearbuilt: string;
}

const SkeletonProFormaItems = () => {
  const items = Array.from({ length: 16 }, (_, index) => (
    <Skeleton className='w-[calc(50%-16px)] h-[32px]' key={index} />
  ));

  return (
    <div className='flex gap-8 flex-wrap'>
      {items}
    </div>
  )
}

export const Route = createFileRoute('/_authenticated/properties/$id')({
  validateSearch: (search) => {
    return {
      placekey: search.placekey as string,
      lat: search.lat as number,
      lng: search.lng as number,
      streetnum: search.streetnum as string,
      source: search.source as string,
      address: search.address as string,
      city: search.city as string,
      state: search.state as string,
      zip_code: search.zip_code as string,
      latitude: search.latitude as string,
      longitude: search.longitude as string,
      beds: search.beds as string,
      baths: search.baths as string,
      sqft: search.sqft as string,
      yearbuilt: search.yearbuilt as string,
    };
  },
  loaderDeps: ({ search: { placekey, lat, lng, streetnum, address, city, state, zip_code, latitude, longitude, beds, baths, sqft } }) => {
    return {
      placekey,
      lat,
      lng,
      streetnum,
      address,
      city,
      state,
      zip_code,
      latitude,
      longitude,
      beds,
      baths,
      sqft,
    };
  },
  loader: async ({ deps: { placekey, lat, lng, streetnum, address, city, state, zip_code, latitude, longitude, beds, baths, sqft }, location }) => {
    // make all requests in parallel
    // const currentYear = new Date().getFullYear();
    // console.log('location', location);
    const segments = location.pathname.split('/').filter(Boolean); // remove empty strings
    const propsIndex = segments.indexOf('properties');
    const propertyId = propsIndex !== -1 ? segments[propsIndex + 1] : null;
    const [
      parcelData,
      demographicData,
      // intelligentCompsData,
      homePriceAppreciationData,
      parcelOwnerSummary,
      unitMixData,
    ] = await Promise.all([
      getParcelData({ placekey, lat, lng, streetnum }),
      getDemographicData({ lat, lng }),
      // getIntelligentCompsData({
      //   address,
      //   city,
      //   state,
      //   zip_code,
      //   latitude,
      //   longitude,
      //   beds: beds?.toString(),
      //   baths: baths?.toString(),
      //   sqft: sqft?.toString(),
      //   yearbuilt: currentYear.toString(),
      // }),
      getHomePriceAppreciationData({ zipCode: zip_code }),
      getParcelOwnerSummary({
        lng,
        lat,
        distance: defaultDistance * 1609.34 // Convert miles to meters
      }),
      getParcelUnitMixData({
        lng,
        lat,
        radius: defaultDistance
      }),
      // only send log data if propertyId is available
      ...(propertyId ? [
        sendSinglePropertyLogData({
          propertyId: +propertyId,
          address: address + ', ' + city + ', ' + state + ' ' + zip_code || '',
        }),
      ] : []),
    ]);
    return {
      parcelData,
      demographicData,
      // intelligentCompsData,
      homePriceAppreciationData,
      areaInsightsData: {
        parcelOwnerSummary: parcelOwnerSummary || null,
        unitMixData: unitMixData || null,
      },
      coordinates: { lat, lng }
    };
  },
  pendingComponent: () => (
    <div className='w-full h-full flex flex-col gap-8 p-8'>
      <div className='w-full h-auto flex justify-between items-center gap-8'>
        <Skeleton className='w-[175px] h-[140px] rounded-lg' />
        <div className='flex flex-col gap-4 flex-1'>
          <Skeleton className='w-full h-[36px]' />
          <Skeleton className='w-full h-[20px]' />
          <div className='flex gap-4 flex-wrap'>
            <Skeleton className='w-[20%] h-[50px] flex-1' />
            <Skeleton className='w-[20%] h-[50px] flex-1' />
            <Skeleton className='w-[20%] h-[50px] flex-1' />
            <Skeleton className='w-[20%] h-[50px] flex-1' />
            <Skeleton className='w-[20%] h-[50px] flex-1' />
          </div>
        </div>
      </div>
      <Skeleton className='w-full h-[36px] rounded-lg' />
      <SkeletonProFormaItems />
    </div>
  ),
  // shouldReload: () => false,
  component: () => (
    <ScrollProvider>
      <SinglePropertyLayoutComponent />
    </ScrollProvider>
  ),
})

function SinglePropertyLayoutComponent() {
  const { status } = useMatch({ from: '/_authenticated/properties/$id' });

  const routeApiPropertyList = getRouteApi('/_authenticated/properties');
  const { properties, propertiesSubmitted, propertiesBookmarked } = routeApiPropertyList.useLoaderData();
  const routeApiSingleProperty = getRouteApi('/_authenticated/properties/$id');
  const { user } = useAuthenticator((context) => [context.user]);

  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;

  const currentProperties = listType === 'listings' ? properties : listType === 'submitted' ? propertiesSubmitted : propertiesBookmarked;

  const currentPropertyId = routeApiSingleProperty.useParams().id;
  const selectedBuyersViewRecord = currentProperties.find((property) => property.property_id === +currentPropertyId);

  // Extract images directly from property meta data in the specified order
  const { images: orderedImages, loading: imagesLoading, error: imagesError } = usePropertyImages(selectedBuyersViewRecord as LennarSinglePropertyDataType);

  // Select hero image from ordered images (elevation images will be first, so they get priority)
  const heroImage = imagesLoading ? '' : (orderedImages.length > 0 ? orderedImages[0].url : '');


  const { setSelectedBuyersViewRecord, setEventCoordinates, setIsBookmarked } = useMarketplaceMapContext();
  const { setIsLeftPanelOpen } = usePanelState();
  const { isMobile } = useBreakpoint();
  const [prevId, setPrevId] = useState<string | null>(null);

  useEffect(() => {
    if (prevId !== currentPropertyId) {
      setSelectedBuyersViewRecord(selectedBuyersViewRecord || null);
      setEventCoordinates([
        +(selectedBuyersViewRecord?.payload?.subjectProperty?.lng || 0),
        +(selectedBuyersViewRecord?.payload?.subjectProperty?.lat || 0),
      ]);
      setPrevId(currentPropertyId);
      if (listType === 'bookmarked') {
        setIsBookmarked(selectedBuyersViewRecord?.decision === 'approve with condition' && selectedBuyersViewRecord?.payload?.proforma?.buyAndHold !== null);
      } else {
        setIsBookmarked(false);
      }

      // Automatically show property details panel on mobile when navigating to property page
      if (isMobile && currentPropertyId && selectedBuyersViewRecord) {
        setIsLeftPanelOpen(false); // Hide left panel (property list) to show right panel (property details)
      }
    }
  }, [currentPropertyId, isMobile, setIsLeftPanelOpen]);



  const full_address = selectedBuyersViewRecord?.full_address;
  const city = selectedBuyersViewRecord?.city;
  const state = selectedBuyersViewRecord?.state;
  const postal_code = selectedBuyersViewRecord?.postal_code;
  const beds = selectedBuyersViewRecord?.payload?.subjectProperty?.beds;
  const spec_price = selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.spec_price;
  const stories = selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.stories;
  const garage = selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.garage;
  const baths = selectedBuyersViewRecord?.payload?.subjectProperty?.baths;
  const sqft = selectedBuyersViewRecord?.payload?.subjectProperty?.sqft;
  const lotNumber = selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.["homesite#"];
  const loginEmail = user?.signInDetails?.loginId || '';
  const agentPhone = selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.agent_phone;
  const divisionPhoneSupport = selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.division_support_phone;
  const decision = selectedBuyersViewRecord?.decision;


  const formattedPrice = spec_price !== undefined && spec_price !== null ? formatPrice(spec_price) : "";
  const formattedPostalCode = postal_code ? removeAllDecimal(postal_code) : "";
  const formattedStories = stories ? removeAllDecimal(stories) : "";
  const formattedGarage = garage ? removeAllDecimal(garage) : "";

  const address = getFormattedAddress({
    streetAddress: full_address,
    city: city,
    state: state,
    postalCode: formattedPostalCode
  });

  const [isModalOpen, setIsModalOpen] = useState(false);
  const { scrollContainerRef } = useScroll();

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  if (status === 'pending') {
    return <div>Loading...</div>;
  }

  return (
    <FormSubmissionProvider>
      <div className="flex flex-col gap-4 w-full h-full p-4">
        <PropertyOverview
          thumbnail={heroImage}
          thumbnailLoading={imagesLoading}
          thumbnailError={imagesError}
          formattedPrice={formattedPrice}
          address={address}
          beds={beds}
          baths={baths}
          sqft={sqft}
          stories={formattedStories}
          garage={formattedGarage}
          phone={divisionPhoneSupport}
          decision={decision}
        />
        <OfferSubmissionFlow
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          address={address}
          lotNumber={lotNumber}
          loginEmail={loginEmail}
          agentPhone={agentPhone}
        />
        <div ref={scrollContainerRef} className="overflow-y-scroll pb-70" >
          <CompsProvider>
            <Outlet />
            {isMobile && <MobileFooter />}
          </CompsProvider>
        </div>
      </div>
    </FormSubmissionProvider>
  );
}
