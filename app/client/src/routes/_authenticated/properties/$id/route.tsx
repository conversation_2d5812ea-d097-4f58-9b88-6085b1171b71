import { createFile<PERSON>out<PERSON>, getRoute<PERSON><PERSON>, Outlet, useSearch } from '@tanstack/react-router'
import { getParcelData } from '@/lib/query/get-parcel-data';
import { getDemographicData } from '@/lib/query/get-demographic-data';
import { selectLennarHeroImage } from '@/lib/utils/selectLennarHeroImage';
import { useEffect, useState } from 'react';
import PropertyOverview from '@/components/PropertyDetails/PropertyOverview';
import OfferSubmissionFlow from '@/components/Modal/OfferSubmissionFlow';
import { useAuthenticator } from '@aws-amplify/ui-react';
import { getIntelligentCompsData } from '@/lib/query/get-intelligent-comps-data';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { CompsProvider } from '@/contexts/CompsContext';
import { formatPrice, getFormattedAddress, removeAllDecimal } from '@/lib/utils/formatUtils';
import { usePropertyImages } from '@/hooks/usePropertyImages';
import { LennarSinglePropertyDataType } from '@/lib/utils/types';
import { getParcelOwnerSummary } from '@/lib/query/get-parcel-owner-summary';
import { defaultDistance } from '@/constants/tabInsights';
import { getParcelUnitMixData } from '@/lib/query/get-parcel-unit-mix-data';

export interface singlePropertySearchParams {
  placekey: string;
  lat: number;
  lng: number;
  streetnum: string;
  source: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  latitude: string;
  longitude: string;
  beds: string;
  baths: string;
  sqft: string;
  yearbuilt: string;
}

export const Route = createFileRoute('/_authenticated/properties/$id')({
  validateSearch: (search) => {
    return {
      placekey: search.placekey as string,
      lat: search.lat as number,
      lng: search.lng as number,
      streetnum: search.streetnum as string,
      source: search.source as string,
      address: search.address as string,
      city: search.city as string,
      state: search.state as string,
      zip_code: search.zip_code as string,
      latitude: search.latitude as string,
      longitude: search.longitude as string,
      beds: search.beds as string,
      baths: search.baths as string,
      sqft: search.sqft as string,
      yearbuilt: search.yearbuilt as string,
    };
  },
  loaderDeps: ({ search: { placekey, lat, lng, streetnum, address, city, state, zip_code, latitude, longitude, beds, baths, sqft } }) => {
    return {
      placekey,
      lat,
      lng,
      streetnum,
      address,
      city,
      state,
      zip_code,
      latitude,
      longitude,
      beds,
      baths,
      sqft,
    };
  },
  loader: async ({ deps: { placekey, lat, lng, streetnum, address, city, state, zip_code, latitude, longitude, beds, baths, sqft } }) => {
    const parcelData = await getParcelData({ placekey, lat, lng, streetnum });
    const demographicData = await getDemographicData({ lat, lng });
    const currentYear = new Date().getFullYear();
    const intelligentCompsData = await getIntelligentCompsData({
      address,
      city,
      state,
      zip_code,
      latitude,
      longitude,
      beds: beds?.toString(),
      baths: baths?.toString(),
      sqft: sqft?.toString(),
      yearbuilt: currentYear.toString(),
    });

    let areaInsightsData = null;
    try {
      // Fetch data needed by the area-insights route
      const [parcelOwnerSummary, unitMixData] = await Promise.all([
        getParcelOwnerSummary({
          lng,
          lat,
          distance: defaultDistance * 1609.34 // Convert miles to meters
        }),
        getParcelUnitMixData({
          lng,
          lat,
          radius: defaultDistance
        })
      ]);

      areaInsightsData = {
        parcelOwnerSummary,
        unitMixData
      };

    } catch (error) {
      console.error("Error fetching area insights data:", error);
      // Set default values in case of error
      areaInsightsData = {
        parcelOwnerSummary: null,
        unitMixData: null
      };
    }

    return {
      parcelData,
      demographicData,
      intelligentCompsData,
      areaInsightsData,
      coordinates: { lat, lng }
    };
  },
  component: SinglePropertyLayoutComponent,
})

function SinglePropertyLayoutComponent() {
  const routeApiPropertyList = getRouteApi('/_authenticated/properties');
  const { properties, propertiesSubmitted } = routeApiPropertyList.useLoaderData();
  const routeApiSingleProperty = getRouteApi('/_authenticated/properties/$id');
  const { user } = useAuthenticator((context) => [context.user]);

  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;

  const currentProperties = listType === 'listings' ? properties : propertiesSubmitted;

  const currentPropertyId = routeApiSingleProperty.useParams().id;
  const selectedBuyersViewRecord = currentProperties.find((property) => property.property_id === +currentPropertyId);

  // Extract images directly from property meta data in the specified order
  const { images: orderedImages, loading: imagesLoading, error: imagesError } = usePropertyImages(selectedBuyersViewRecord as LennarSinglePropertyDataType);
  
  // Select hero image from ordered images (elevation images will be first, so they get priority)
  const heroImage = imagesLoading ? '' : (orderedImages.length > 0 ? orderedImages[0].url : '');


  const { setSelectedBuyersViewRecord, setEventCoordinates } = useMarketplaceMapContext();
  const [prevId, setPrevId] = useState<string | null>(null);

  useEffect(() => {
    if (prevId !== currentPropertyId) {
      setSelectedBuyersViewRecord(selectedBuyersViewRecord || null);
      setEventCoordinates([
        +selectedBuyersViewRecord?.payload?.subjectProperty?.lng,
        +selectedBuyersViewRecord?.payload?.subjectProperty?.lat,
      ]);
      setPrevId(currentPropertyId);
    }
  }, [currentPropertyId]);

  const full_address = selectedBuyersViewRecord?.full_address;
  const city = selectedBuyersViewRecord?.city;
  const state = selectedBuyersViewRecord?.state;
  const postal_code = selectedBuyersViewRecord?.postal_code;
  const beds = selectedBuyersViewRecord?.payload?.subjectProperty?.beds;
  const price = selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.base_price;
  const stories = selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.stories;
  const garage = selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.garage;
  const baths = selectedBuyersViewRecord?.payload?.subjectProperty?.baths;
  const sqft = selectedBuyersViewRecord?.payload?.subjectProperty?.sqft;
  const lotNumber = selectedBuyersViewRecord?.payload?.subjectProperty?.lotNumber;
  const loginEmail = user?.signInDetails?.loginId || '';


  const formattedPrice = price !== undefined && price !== null ? formatPrice(price) : "";
  const formattedPostalCode = postal_code ? removeAllDecimal(postal_code) : "";
  const formattedStories = stories ? removeAllDecimal(stories) : "";
  const formattedGarage = garage ? removeAllDecimal(garage) : "";

  const address = getFormattedAddress({
    streetAddress: full_address,
    city: city,
    state: state,
    postalCode: formattedPostalCode
  });

  const [isModalOpen, setIsModalOpen] = useState(false);

  const onStartOfferClick = () => {
    setIsModalOpen(true);
  }

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <div className="flex flex-col gap-4 w-full h-full p-4">
      <PropertyOverview
        thumbnail={heroImage}
        thumbnailLoading={imagesLoading}
        thumbnailError={imagesError}
        formattedPrice={formattedPrice}
        address={address}
        beds={beds}
        baths={baths}
        sqft={sqft}
        stories={formattedStories}
        garage={formattedGarage}
        onStartOfferClick={onStartOfferClick}
      />
      <OfferSubmissionFlow
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        address={address}
        lotNumber={lotNumber}
        loginEmail={loginEmail}
      />
      <div className="overflow-y-scroll">
        <CompsProvider>
          <Outlet />
        </CompsProvider>
      </div>
    </div>
  );
}
