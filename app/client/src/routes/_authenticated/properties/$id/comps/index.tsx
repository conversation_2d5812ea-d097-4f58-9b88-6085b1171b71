import React from 'react';
import { createFileRoute, getRouteApi, useSearch } from '@tanstack/react-router'
import TabComps from '../../../../../components/PropertyDetails/TabComps/TabComps';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';

export const Route = createFileRoute('/_authenticated/properties/$id/comps/')({
  component: RouteComponent,
})

function RouteComponent() {
  const routeApiPropertyList = getRouteApi('/_authenticated/properties');
  const { properties, propertiesSubmitted, propertiesBookmarked } = routeApiPropertyList.useLoaderData();

  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;

  const currentProperties = listType === 'listings' ? properties : listType === 'submitted' ? propertiesSubmitted : propertiesBookmarked;

  const routeApiSingleProperty = getRouteApi('/_authenticated/properties/$id');
  const currentPropertyId = routeApiSingleProperty.useParams().id;
  const currentPropertyData = currentProperties.find((property) => property.property_id === +currentPropertyId);

  const { setPropertyModalTabKey } = useMarketplaceMapContext();
  React.useEffect(() => {
    setPropertyModalTabKey('comps');
  }, []);

  return (
    <>
      {currentPropertyData && <TabComps subjectProperty={currentPropertyData.payload?.subjectProperty || {}} />}
    </>
  )
}
