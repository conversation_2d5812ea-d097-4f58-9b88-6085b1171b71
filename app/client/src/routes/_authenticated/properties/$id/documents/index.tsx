import TabDocuments from '@/components/PropertyDetails/TabDocuments/TabDocuments';
import { createFileRoute, getRouteApi, useSearch } from '@tanstack/react-router'
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';

export const Route = createFileRoute(
  '/_authenticated/properties/$id/documents/',
)({
  component: RouteComponent,
})

function RouteComponent() {
  const routeApiPropertyList = getRouteApi('/_authenticated/properties');
  const { properties, propertiesSubmitted } = routeApiPropertyList.useLoaderData();

  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;

  const currentProperties = listType === 'listings' ? properties : propertiesSubmitted;

  const routeApiSingleProperty = getRouteApi('/_authenticated/properties/$id');
  const currentPropertyId = routeApiSingleProperty.useParams().id;
  const currentPropertyData = currentProperties.find((property) => property.property_id === +currentPropertyId);

  const { setPropertyModalTabKey } = useMarketplaceMapContext();
  setPropertyModalTabKey('documents');

  return (
    <>
      {currentPropertyData && <TabDocuments />}
    </>
  )
}
