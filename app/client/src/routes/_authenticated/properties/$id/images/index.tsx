import TabImages from '@/components/PropertyDetails/TabImage/TabImages';
import { createFileRoute, getRouteApi, useSearch } from '@tanstack/react-router'
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { usePropertyImages } from '@/hooks/usePropertyImages';
import { LennarSinglePropertyDataType } from '@/lib/utils/types';

export const Route = createFileRoute('/_authenticated/properties/$id/images/')({
  component: RouteComponent,
})

function RouteComponent() {
  const routeApiPropertyList = getRouteApi('/_authenticated/properties');
  const { properties, propertiesSubmitted } = routeApiPropertyList.useLoaderData();

  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;

  const currentProperties = listType === 'listings' ? properties : propertiesSubmitted;

  const routeApiSingleProperty = getRouteApi('/_authenticated/properties/$id');
  const currentPropertyId = routeApiSingleProperty.useParams().id;

  const currentPropertyData = currentProperties.find((property) => property.property_id === +currentPropertyId);

  // Extract images and virtual tour URL directly from property meta data
  const { images: orderedImages, virtualTourUrl, loading: imagesLoading, error: imagesError } = usePropertyImages(currentPropertyData as LennarSinglePropertyDataType);

  const { setPropertyModalTabKey } = useMarketplaceMapContext();
  setPropertyModalTabKey('images');

  return (
    <>
      {currentPropertyData && <TabImages images={orderedImages} virtualTourUrl={virtualTourUrl} isLoading={imagesLoading} error={imagesError} />}
    </>
  )
}
