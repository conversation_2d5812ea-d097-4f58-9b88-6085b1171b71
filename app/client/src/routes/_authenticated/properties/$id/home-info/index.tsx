import React from 'react';
import TabHomeInfo from '@/components/PropertyDetails/TabHomeInfo/TabHomeInfo';
import { createFileRoute, getRouteApi, useSearch } from '@tanstack/react-router';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';

export const Route = createFileRoute(
  '/_authenticated/properties/$id/home-info/',
)({
  component: RouteComponent,
})

function RouteComponent() {
  // the data used in home info is from the data that we use to populate property list
  // it's fetched in app/client/src/routes/_authenticated/properties/route.tsx line 28
  // because it's fetched in Tanstack Router loadery route loader
  // getRouteApi is the way to access the data that is fetched in the route loader
  const routeApiPropertyList = getRouteApi('/_authenticated/properties');
  const { properties, propertiesSubmitted, propertiesBookmarked } = routeApiPropertyList.useLoaderData();

  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;

  const currentProperties = listType === 'listings' ? properties : listType === 'submitted' ? propertiesSubmitted : propertiesBookmarked;

  // we use getRouteApi to get the id that is in the url
  const routeApiSingleProperty = getRouteApi('/_authenticated/properties/$id');
  const currentPropertyId = routeApiSingleProperty.useParams().id;
  // then use the id to find the property data in the property list data
  const currentPropertyData = currentProperties.find((property) => property.property_id === +currentPropertyId);
  const data = currentPropertyData?.payload?.subjectProperty.meta;

  const { setPropertyModalTabKey } = useMarketplaceMapContext();
  React.useEffect(() => {
    setPropertyModalTabKey('home-info');
  }, []);

  return (
    <>
      <TabHomeInfo details={data} />
    </>
  )
}
