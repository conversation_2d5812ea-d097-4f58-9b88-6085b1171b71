import { useState, useEffect, useMemo } from "react";
import {
  createFile<PERSON>out<PERSON>,
  getRoute<PERSON><PERSON>,
  useSearch,
  useRouter,
} from "@tanstack/react-router";
import { usePanelState } from "../../../hooks/usePanelState";
import { useMarketplaceMapContext } from "../../../contexts/MarketplaceMapContext";
import PropertyListHeader from "../../../components/PropertyList/PropertyListHeader";
import ErrorMessage from "@/components/Common/ErrorMessage";
import PropertyListTable from "../../../components/PropertyList/PropertyListTable";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationLink,
} from "@/components/ui/pagination";
import { LennarSinglePropertyDataType } from "@/lib/utils/types";
import { isEqual } from "lodash";
import { useContainerWidth } from "@/hooks/useContainerWidth";
import LegalDisclaimerModal from "@/components/Modal/LegalDisclaimerModal";
import PropertyCardWithBanner from "@/components/PropertyCardWithBanner";
import MobileFooter from "@/components/MobileFooter";
import { useBreakpoint } from "@/hooks/useBreakpoint";
import { parseFilterInUrlSearchParams } from "@/lib/utils/parseFilterInUrlSearchParams";
import { sortProperties } from "@/lib/utils/sortProperties";

export type SortBy = "basePrice" | "yieldOnBidPrice";

export const Route = createFileRoute("/_authenticated/properties/")({
  component: PropertyListComponent,
});

function PropertyListComponent() {
  const routeApi = getRouteApi("/_authenticated/properties");
  const { properties, propertiesSubmitted, propertiesBookmarked, error } =
    routeApi.useLoaderData();
  // const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // const [isLoading, setIsLoading] = useState(true);

  const { isLeftPanelOpen } = usePanelState();
  const { containerRef, containerWidth } = useContainerWidth(isLeftPanelOpen);
  const {
    // map,
    // selectedBuyersViewRecord,
    viewportFilteredProperties,
    viewportFilteredPropertiesSubmitted,
    viewportFilteredPropertiesBookmarked,
    isViewportFilteringEnabled,
    setSelectedBuyersViewRecord,
    setAllProperties,
  } = useMarketplaceMapContext();

  // Clear selected property when returning to the list
  useEffect(() => {
    setSelectedBuyersViewRecord(null);
  }, [setSelectedBuyersViewRecord]);

  const router = useRouter();
  const search = useSearch({ from: "/_authenticated/properties" });

  // Parse URL filters and apply frontend filtering
  const urlFilters = useMemo(() => {
    const filters = parseFilterInUrlSearchParams(search.filter || "");
    return filters;
  }, [search.filter]);

  // Apply frontend filtering to properties based on URL params
  const filteredPropertiesByUrlParams = useMemo(() => {
    const baseProperties =
      search.listType === "listings"
        ? properties
        : search.listType === "submitted"
          ? propertiesSubmitted
          : propertiesBookmarked;

    if (
      !baseProperties ||
      !urlFilters ||
      Object.keys(urlFilters).length === 0
    ) {
      return baseProperties;
    }

    const filtered = baseProperties.filter((property) => {
      // Market filter - only check lennar_metro
      if (urlFilters.selectedMarkets && urlFilters.selectedMarkets.length > 0) {
        const divisionName = property?.meta?.lennar_metro?.trim();

        const marketMatches = urlFilters.selectedMarkets.some(
          (selectedMarket) => {
            if (divisionName === selectedMarket) return true;

            if (
              divisionName &&
              divisionName.toLowerCase() === selectedMarket.toLowerCase()
            )
              return true;
            const legacyMappings: Record<string, string> = {
              "Austin / Central Texas": "Austin",
              "Dallas / Ft. Worth": "Dallas/Ft. Worth",
              "Fresno / Bakersfield": "Central Valley",
            };
            const mappedDivision = legacyMappings[selectedMarket];
            if (mappedDivision && divisionName === mappedDivision) return true;
            if (
              divisionName &&
              divisionName.toLowerCase().includes(selectedMarket.toLowerCase())
            )
              return true;

            return false;
          }
        );

        if (!marketMatches) {
          return false;
        }
      }

      // Price filter
      if (
        urlFilters.minPrice !== undefined ||
        urlFilters.maxPrice !== undefined
      ) {
        const priceString =
          property?.payload?.subjectProperty?.meta?.base_price;
        if (priceString) {
          const price = parseInt(priceString.replace(/[^0-9]/g, ""));
          if (urlFilters.minPrice && price < urlFilters.minPrice) return false;
          if (urlFilters.maxPrice && price > urlFilters.maxPrice) return false;
        } else if (urlFilters.minPrice || urlFilters.maxPrice) {
          return false;
        }
      }

      // Beds filter
      if (urlFilters.minBeds !== undefined && urlFilters.minBeds > 0) {
        const bedroomCount = property?.payload?.subjectProperty?.beds;
        if (bedroomCount === undefined || bedroomCount < urlFilters.minBeds)
          return false;
      }

      // Baths filter
      if (urlFilters.minBaths !== undefined && urlFilters.minBaths > 0) {
        const bathroomsCount = property?.payload?.subjectProperty?.baths;
        if (
          bathroomsCount === undefined ||
          bathroomsCount < urlFilters.minBaths
        )
          return false;
      }

      // Min cap rate filter
      if (urlFilters.minCapRate !== undefined && urlFilters.minCapRate > 0) {
        const capRate =
          property?.payload?.proforma?.buyAndHold?.[
          "Projected Yield on Bid Price"
          ];
        if (capRate === undefined || capRate === null) return false;

        // Convert cap rate to comparable format
        let capRateValue: number;
        if (typeof capRate === "number") {
          capRateValue = capRate;
        } else {
          capRateValue = parseFloat(String(capRate));
        }

        if (isNaN(capRateValue) || capRateValue < urlFilters.minCapRate)
          return false;
      }

      return true;
    });

    return filtered;
  }, [
    properties,
    propertiesSubmitted,
    propertiesBookmarked,
    search.listType,
    urlFilters,
  ]);

  // Initialize state from URL search params to avoid mismatches
  const [activeButton, setActiveButton] = useState<
    "listings" | "submitted" | "bookmarked"
  >(search.listType || "listings");
  const [viewStyle, setViewStyle] = useState<"grid" | "list">(
    search.viewStyle || "grid"
  );

  // Initialize sorting from URL parameters
  const [sortBy, setSortBy] = useState<SortBy>(search.sort || "basePrice");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">(
    search.direction || "desc"
  );

  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageProperties, setCurrentPageProperties] = useState<
    LennarSinglePropertyDataType[]
  >([]);

  const [preProperties, setPreProperties] = useState<
    LennarSinglePropertyDataType[]
  >([]);

  const { isMobile } = useBreakpoint();

  // Update map context with filtered properties for map display
  useEffect(() => {
    const hasUrlFilters = urlFilters && Object.keys(urlFilters).length > 0;
    if (
      search.listType === "listings" &&
      hasUrlFilters &&
      filteredPropertiesByUrlParams
    ) {
      // Only update map when we have URL filters and we're on listings tab
      setAllProperties(filteredPropertiesByUrlParams);
    } else if (search.listType === "listings" && !hasUrlFilters && properties) {
      // Reset to all properties when no filters
      setAllProperties(properties);
    }
  }, [
    search.listType,
    search.filter,
    filteredPropertiesByUrlParams,
    properties,
    setAllProperties,
  ]);

  // Determine which properties to display in the table/grid
  let currentProperties;
  const hasUrlFilters = urlFilters && Object.keys(urlFilters).length > 0;

  if (hasUrlFilters) {
    // URL filters are applied - use filtered properties and ignore viewport filtering for the table
    currentProperties = filteredPropertiesByUrlParams;
  } else if (isViewportFilteringEnabled) {
    // No URL filters - use viewport filtering as before
    const viewportFiltered =
      search.listType === "listings"
        ? viewportFilteredProperties
        : search.listType === "submitted"
          ? viewportFilteredPropertiesSubmitted
          : viewportFilteredPropertiesBookmarked;
    currentProperties =
      viewportFiltered !== null
        ? viewportFiltered
        : search.listType === "listings"
          ? properties
          : search.listType === "submitted"
            ? propertiesSubmitted
            : propertiesBookmarked;
  } else {
    // No filtering at all
    currentProperties =
      search.listType === "listings"
        ? properties
        : search.listType === "submitted"
          ? propertiesSubmitted
          : propertiesBookmarked;
  }

  // Apply sorting if URL parameters specify sorting
  const sortedProperties = useMemo(() => {
    if (!currentProperties || !search.sort || !search.direction) {
      return currentProperties || [];
    }

    return sortProperties({
      properties: [...currentProperties],
      sortBy: search.sort,
      sortDirection: search.direction,
    });
  }, [currentProperties, search.sort, search.direction]);

  useEffect(() => {
    if (search.listType !== activeButton) {
      setActiveButton(
        search.listType as "listings" | "submitted" | "bookmarked"
      );
    }
  }, [search.listType]);

  useEffect(() => {
    if (search.viewStyle !== viewStyle) {
      setViewStyle(search.viewStyle as "grid" | "list");
    }
  }, [search.viewStyle]);

  // Update sorting state when URL parameters change
  useEffect(() => {
    if (search.sort && search.sort !== sortBy) {
      setSortBy(search.sort);
    }
    if (search.direction && search.direction !== sortDirection) {
      setSortDirection(search.direction);
    }
  }, [search.sort, search.direction, sortBy, sortDirection]);

  // load the first 50 properties after initial load for the 1st page
  useEffect(() => {
    if (!isEqual(preProperties, sortedProperties)) {
      if (sortedProperties && sortedProperties.length > 0) {
        setPreProperties(sortedProperties);
        setCurrentPageProperties(sortedProperties?.slice(0, 50));
        setCurrentPage(1);
      } else {
        setCurrentPageProperties([]);
        setPreProperties([]);
        setCurrentPage(1);
      }
    }
  }, [sortedProperties, preProperties]);

  // Update currentPageProperties when sortedProperties changes (due to viewport filtering or sorting)
  useEffect(() => {
    if (sortedProperties) {
      setCurrentPage(1); // Reset to first page when properties change
      setCurrentPageProperties(sortedProperties.slice(0, PAGE_SIZE));
    }
  }, [sortedProperties]);

  const onListTypeChange = (type: "listings" | "submitted" | "bookmarked") => {
    setActiveButton(type);
    router.navigate({
      to: "/properties",
      search: {
        ...search,
        listType: type,
      },
    });
  };

  const onViewStyleChange = (style: "grid" | "list") => {
    setViewStyle(style);
    router.navigate({
      to: "/properties",
      search: {
        ...search,
        viewStyle: style,
      },
    });
  };

  const PAGE_SIZE = 50;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    const startIndex = (page - 1) * PAGE_SIZE;
    const endIndex = startIndex + PAGE_SIZE;
    setCurrentPageProperties(sortedProperties.slice(startIndex, endIndex));

    if (containerRef.current) {
      containerRef.current.scrollTop = 0;
    }
  };

  return (
    <div className="flex flex-col h-full w-full overflow-hidden p-4">
      {/* Header  */}
      <PropertyListHeader
        activeButton={activeButton}
        onListTypeChange={onListTypeChange}
        viewStyle={viewStyle}
        onViewStyleChange={onViewStyleChange}
        isLoading={false}
        sortBy={sortBy}
        setSortBy={setSortBy}
        sortDirection={sortDirection}
        setSortDirection={setSortDirection}
        numberOfSubmittedProperties={propertiesSubmitted?.length}
        numberOfListingsProperties={
          search.listType === "listings"
            ? properties?.length
            : search.listType === "submitted"
              ? propertiesSubmitted?.length
              : propertiesBookmarked?.length
        }
        numberOfFilteredProperties={sortedProperties?.length}
        numberOfBookmarkedProperties={propertiesBookmarked?.length}
      />

      {/* Error Message */}
      {error && (
        <ErrorMessage message="Failed to load properties. Please try again later." />
      )}

      {/* Main Content */}
      {!error && (
        <>
          <div
            className={`@container ${viewStyle === "grid" ? "overflow-y-auto" : ""} p-4 w-full h-full`}
            ref={containerRef}
          >
            <div
              className={`${viewStyle === "grid"
                ? `grid grid-cols-1 @lg:grid-cols-2 @3xl:grid-cols-3 gap-5`
                : "flex flex-col space-y-0"
                }
          `}
            >
              {viewStyle === "list" ? (
                <PropertyListTable properties={currentPageProperties} />
              ) : (
                currentPageProperties &&
                currentPageProperties.map((property, index) => (
                  <PropertyCardWithBanner
                    key={property.property_id}
                    property={property}
                    index={index}
                    totalCount={sortedProperties?.length}
                    containerWidth={containerWidth}
                    onSeeDetailsClick={() => setIsModalOpen(true)}
                  />
                ))
              )}
            </div>
            {/* Pagination Component */}
            <div className="flex justify-center my-3">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() =>
                        handlePageChange(Math.max(currentPage - 1, 1))
                      }
                      aria-disabled={currentPage === 1}
                      tabIndex={currentPage === 1 ? -1 : 0}
                    />
                  </PaginationItem>
                  <PaginationItem>
                    <PaginationLink isActive>{currentPage}</PaginationLink>
                  </PaginationItem>
                  {/* No total page count, so no ellipsis or last page */}
                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(currentPage + 1)}
                    // We can't know if it's the last page, so always enabled
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
            {isMobile && <MobileFooter />}
          </div>
        </>
      )}

      <LegalDisclaimerModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Legal Disclaimer"
      />
    </div>
  );
}
