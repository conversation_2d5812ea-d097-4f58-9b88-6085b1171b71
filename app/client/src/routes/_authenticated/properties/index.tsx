import { useState, useMemo, useEffect } from 'react';
import { createFileRoute, getRoute<PERSON>pi, useSearch, useRouter } from '@tanstack/react-router';
import { usePanelState } from '../../../hooks/usePanelState';
import { usePropertiesFilterState } from '../../../hooks/usePropertiesFilter';
import { filterProperties } from '../../../lib/utils/filterProperties';
import { sortProperties } from '../../../lib/utils/sortProperties';
import { useMarketplaceMapContext } from '../../../contexts/MarketplaceMapContext';
import PropertyListHeader from '../../../components/PropertyList/PropertyListHeader';
import ErrorMessage from '@/components/Common/ErrorMessage';
import PropertyCard from '../../../components/PropertyList/PropertyCard';
import PropertyListTable from '../../../components/PropertyList/PropertyListTable';

export type SortBy = "price" | "capRate" | "estCompletion" | "sqft" | "beds" | "baths";

export const Route = createFileRoute('/_authenticated/properties/')({
  component: PropertyListComponent,
});

function PropertyListComponent() {
  const routeApi = getRouteApi('/_authenticated/properties');
  const { properties, propertiesSubmitted, error } = routeApi.useLoaderData();

  const { selectedMarket, selectedPrice, selectedBeds, selectedBaths, minCapRate } = usePropertiesFilterState();
  const { isLeftPanelOpen } = usePanelState();
  const { 
    viewportFilteredProperties, 
    viewportFilteredPropertiesSubmitted, 
    isViewportFilteringEnabled 
  } = useMarketplaceMapContext();

  const [activeButton, setActiveButton] = useState<'listings' | 'submitted'>('listings');
  const [viewStyle, setViewStyle] = useState<"grid" | "list">('grid');

  const [sortBy, setSortBy] = useState<SortBy>("capRate");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  const router = useRouter();
  const search = useSearch({ from: '/_authenticated/properties' });

  const filteredProperties = useMemo(() => {
    // Use viewport-filtered properties if viewport filtering is enabled and available
    if (isViewportFilteringEnabled) {
      const viewportProps = search.listType === 'listings' ? viewportFilteredProperties : viewportFilteredPropertiesSubmitted;
      if (viewportProps !== null) {
        return viewportProps;
      }
    }
    
    // Fallback to regular filtering
    return filterProperties({
      properties: search.listType === 'listings' ? properties : propertiesSubmitted,
      selectedMarket,
      selectedPrice,
      selectedBeds,
      selectedBaths,
      minCapRate
    });
  }, [
    properties, 
    propertiesSubmitted, 
    selectedMarket, 
    selectedPrice, 
    selectedBeds, 
    selectedBaths, 
    minCapRate, 
    search.listType,
    isViewportFilteringEnabled,
    viewportFilteredProperties,
    viewportFilteredPropertiesSubmitted
  ]);

  const sortedProperties = useMemo(() => {
    return sortProperties({
      properties: filteredProperties,
      sortBy,
      sortDirection,
    });
  }, [filteredProperties, sortBy, sortDirection]);

  if (search.listType !== activeButton) {
    setActiveButton(search.listType as 'listings' | 'submitted');
  }

  const onListTypeChange = (type: 'listings' | 'submitted') => {
    setActiveButton(type);
    router.navigate({
      to: '/properties',
      search: {
        ...search,
        listType: type,
      },
    });
  };

  if (search.viewStyle !== viewStyle) {
    setViewStyle(search.viewStyle as "grid" | "list");
  }

  const onViewStyleChange = (style: "grid" | "list") => {
    setViewStyle(style);
    router.navigate({
      to: '/properties',
      search: {
        ...search,
        viewStyle: style,
      },
    });
  };

  return (
    <div className="flex flex-col h-full w-full overflow-hidden p-4">
      {/* Header  */}
      <PropertyListHeader
        activeButton={activeButton}
        onListTypeChange={onListTypeChange}
        viewStyle={viewStyle}
        onViewStyleChange={onViewStyleChange}
        isLoading={false}
        sortBy={sortBy}
        setSortBy={setSortBy}
        sortDirection={sortDirection}
        setSortDirection={setSortDirection}
        numberOfSubmittedProperties={propertiesSubmitted?.length}
        numberOfListingsProperties={properties?.length}
        numberOfFilteredProperties={filteredProperties?.length}
      />

      {/* Error Message */}
      {error && (
        <ErrorMessage message="Failed to load properties. Please try again later." />
      )}

      {/* Main Content */}
      {!error && (
        <>
          {/* No Properties Found Message */}
          {sortedProperties.length === 0 ? (
            <div className="flex flex-1 justify-center items-center text-center text-[var(--color-dark-gray)] py-8">
              {properties.length === 0
                ? "There are currently no properties to display."
                : "No properties match your current filters."}
            </div>
          ) : (
            <div className={`
            overflow-y-auto p-4 w-full
            ${viewStyle === "grid"
                ? `grid grid-cols-1 ${isLeftPanelOpen ? 'md:grid-cols-2' : 'md:grid-cols-3'} gap-5`
                : 'flex flex-col space-y-0'}
          `}>
              {viewStyle === "list" ? (
                <PropertyListTable properties={filteredProperties} />
              ) : (
                sortedProperties.map((property, index) => (
                  <PropertyCard
                    key={property.property_id}
                    property={property}
                    index={index}
                    totalCount={filteredProperties.length}
                  />
                ))
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
}

