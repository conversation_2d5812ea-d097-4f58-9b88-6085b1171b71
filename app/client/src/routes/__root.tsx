import React from 'react';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { createRootRouteWithContext, ErrorComponent, Outlet } from '@tanstack/react-router';

import { TanStackRouterDevtools } from '@/components/router-devtools';
import { TailwindScreenDevTool } from '@/components/tailwind-screen-dev-tool';

import { useDocumentTitle } from '@/lib/hooks/useDocumentTitle';

import { UI_APPLICATION_NAME } from '@/lib/utils/constants';
import { titleMaker } from '@/lib/utils/title-maker';

import { queryClient } from '@/lib/config/tanstack-query';

import { type AuthType } from '@/lib/auth';

import { Container } from './-components/container';
import { PageNotFound } from './-components/page-not-found';

interface RouterContext {
  queryClient: typeof queryClient;
  auth: AuthType;
}

export const Route = createRootRouteWithContext<RouterContext>()({
  beforeLoad: () => ({ getTitle: () => UI_APPLICATION_NAME }), // Set default title
  component: () => {
    return (
      <RootDocument>
        <Outlet />
      </RootDocument>
    );
  },
  errorComponent: (props) => {
    return (
      <RootDocument>
        <Container>
          <ErrorComponent {...props} />
        </Container>
      </RootDocument>
    );
  },
  notFoundComponent: () => {
    useDocumentTitle(titleMaker('Not Found'));

    return (
      <RootDocument>
        <Container className="flex">
          <PageNotFound className="mt-20 px-2" />
        </Container>
      </RootDocument>
    );
  },
});

function RootDocument({ children }: { children: React.ReactNode }) {
  return (
    <React.Fragment>
      {children}
      <TailwindScreenDevTool />
      <ReactQueryDevtools initialIsOpen={false} position="bottom" buttonPosition="bottom-right" />
      <TanStackRouterDevtools position="bottom-right" />
    </React.Fragment>
  );
}
