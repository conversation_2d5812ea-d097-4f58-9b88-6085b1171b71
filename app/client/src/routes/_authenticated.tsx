import { useEffect } from 'react';
import { MapProvider } from '@spatiallaser/map';
import { createFileRoute, Outlet, redirect, useLoaderData } from '@tanstack/react-router';
import { Toaster } from 'react-hot-toast';
import { getUserGroup, getUserEmail } from '@/lib/utils/auth';
import { MarketplaceMapProvider } from '@/contexts/MarketplaceMapContext';
import { CartProvider } from '../contexts/CartContext';
import CartModal from '../components/Cart/CartModal';
import CartOfferSubmissionFlow from '../components/Cart/CartOfferSubmissionFlow';
import PropertiesWrapper from '../components/PropertiesWrapper';
import * as Sentry from '@sentry/react';

export const Route = createFileRoute('/_authenticated')({
  beforeLoad: async ({ context, location }) => {
    if (!context.auth.user) {
      throw redirect({
        to: '/login',
        search: {
          ...location.search,
          redirect: location.pathname + (Object.keys(location.search).length > 0 ? '?' + new URLSearchParams(location.search).toString() : ''),
        },
      });
    }
  },

  loader: async () => {
    // Fetch user speicifc data like profile, permission, config, etc..
    // queryClient.ensureQueryData
    const userGroup = await getUserGroup();
    const userEmail = await getUserEmail();
    return {
      userGroup,
      userEmail,
    };
  },
  component: AuthenticatedLayout,
});

function AuthenticatedLayout() {
  const { userEmail } = useLoaderData({ from: '/_authenticated' });

  useEffect(() => {
    Sentry.setUser({
      email: userEmail,
    });
  }, [userEmail]);

  // if user email does not include allcommonsenses, suppress console.log
  if (!userEmail.includes('allcommonsenses')) {
    console.log = function () {};
  }

  return (
    <div className="flex flex-col h-screen w-full">
      <MapProvider>
        <MarketplaceMapProvider>
          <CartProvider>
            <MapProvider>
              <PropertiesWrapper>
                <Outlet />
              </PropertiesWrapper>
            </MapProvider>
            <CartModal />
            <CartOfferSubmissionFlow />
          </CartProvider>
        </MarketplaceMapProvider>
      </MapProvider>
      <Toaster />
    </div >
  )
}