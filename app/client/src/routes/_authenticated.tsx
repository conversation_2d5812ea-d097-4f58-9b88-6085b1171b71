import { MapProvider } from '@spatiallaser/map';
import { createFileRoute, Outlet, redirect } from '@tanstack/react-router';
import { Toaster } from 'react-hot-toast';
import { getUserGroup, getUserEmail } from '@/lib/utils/auth';
import { MarketplaceMapProvider } from '@/contexts/MarketplaceMapContext';
import PropertiesWrapper from '../components/PropertiesWrapper';

export const Route = createFileRoute('/_authenticated')({
  beforeLoad: async ({ context, location }) => {
    if (!context.auth.user) {
      throw redirect({
        to: '/login',
        search: {
          redirect: location.href,
        },
      });
    }
  },

  loader: async () => {
    // Fetch user speicifc data like profile, permission, config, etc..
    // queryClient.ensureQueryData
    const userGroup = await getUserGroup();
    const userEmail = await getUserEmail();
    return {
      userGroup,
      userEmail,
    };
  },
  component: AuthenticatedLayout,
});

function AuthenticatedLayout() {

  return (
    <div className="flex flex-col h-screen w-full">
      <MapProvider>
        <MarketplaceMapProvider>
          <MapProvider>
            <PropertiesWrapper>
              <Outlet />
            </PropertiesWrapper>
          </MapProvider>
        </MarketplaceMapProvider>
      </MapProvider>
      <Toaster />
    </div >
  )
}