import { useEffect, useRef } from "react";
import { useRouter, createLazyFileRoute } from "@tanstack/react-router";

import "@aws-amplify/ui-react/styles.css";

import { useAuthentication } from "@/lib/auth";

import img from "../../assets/hero_bg.jpg";
import SignUp from "./-components/TOUModal";
import { withAuthenticator } from "@aws-amplify/ui-react";

const formFields = {
  signUp: {
    given_name: {
      label: "First Name",
      placeholder: "Enter your First name",
      order: 1,
      isRequired: true,
    },
    family_name: {
      label: "Last Name",
      placeholder: "Enter your Last name",
      order: 2,
      isRequired: true,
    },
    email: {
      label: "Email",
      placeholder: "Enter Your Email",
      order: 3,
      isRequired: true,
    },
    password: {
      order: 4,
    },
    confirm_password: {
      order: 5,
    },
  },
};

function InnerComponent() {
  const router = useRouter();
  const searchParams = Route.useSearch() as { redirect?: string; mode?: string };
  const { user } = useAuthentication();

  useEffect(() => {
    if (user) {
      const redirectPath = searchParams.redirect || "/";
      router.history.push(redirectPath);
    }
  }, [user]);

  if (user) return null;
  return (
    <div className="relative flex h-full flex-row">
      <div className="h-full w-full">
        <img src={img} className="h-full w-full object-cover" />
      </div>
      <div className="absolute left-1/2 top-[20%] -translate-x-1/2 z-10" />
    </div>
  );
}

const AuthComponent = () => {
  const searchParams = Route.useSearch() as { redirect?: string; mode?: string };
  const containerRef = useRef<HTMLDivElement>(null);

  let initialState: "signIn" | "signUp" = "signUp";
  if (searchParams.mode === "login") {
    initialState = "signIn";
  } else if (searchParams.mode === "signup") {
    initialState = "signUp";
  }
  const handleCreateAccountClick = (formData: any) => {
    console.log("Create account button clicked!", formData);

    return formData;
  };

  // Event delegation approach for button click handling  
  useEffect(() => {
    const handleButtonClick = (event: Event) => {
      const target = event.target as HTMLElement;
      if (target.matches('button[type="submit"]')) {
        const form = target.closest("form");
        const currentView =
          form?.closest("[data-amplify-authenticator-signup]") ||
          form?.closest('[data-amplify-router="signUp"]');
        if (currentView) {
          console.log("Create Account button clicked!");
          const formData = new FormData(form as HTMLFormElement);
          const formObject: Record<string, string> = {};

          formData.forEach((value, key) => {
            formObject[key] = value.toString();
          });

          let hasValidationErrors = false;
          
          if (!formObject['custom:role']) {
            console.error('Validation Error: Account type is required');
            hasValidationErrors = true;
          }
          
          if (!formObject.acknowledgement) {
            console.error('Validation Error: You must agree to the Terms of Use');
            hasValidationErrors = true;
          }
          
          if (hasValidationErrors) {
            event.preventDefault();
            event.stopPropagation();
            return;
          }

          handleCreateAccountClick(formObject);

          // Google Tag Manager dataLayer push
          if (typeof window !== "undefined" && (window as any).dataLayer) {
            const eventData = {
              event: "form_submit",
              form_type: "signup",
              timestamp: new Date().toISOString(),
            };
            
            (window as any).dataLayer.push(eventData);
          } else {
            console.warn("DataLayer not found - make sure Google Tag Manager is loaded");
          }
        }
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener("click", handleButtonClick, true);

      return () => {
        container.removeEventListener("click", handleButtonClick, true);
      };
    }
  }, []);

  const services = {};

  const AuthenticatorComponent = withAuthenticator(InnerComponent, {
    initialState,
    hideSignUp: false,
    signUpAttributes: [
      "given_name",
      "family_name",
      "email",
    ],
    formFields: formFields,
    services: services, // Add the custom services
    components: {
      SignUp: {
        FormFields: () => <SignUp />,
      },
      SignIn: {},
    },
  });

  return (
    <div ref={containerRef} className="relative w-full h-full">
      {/* Background */}
      <div className="absolute inset-0 w-full h-full">
        <img src={img} className="h-full w-full object-cover" />
      </div>
      {/* Authenticator Component */}
      <div className="relative z-10">
        <AuthenticatorComponent />
      </div>
    </div>
  );
};

export const Route = createLazyFileRoute("/_public/login")({
  component: AuthComponent,
});