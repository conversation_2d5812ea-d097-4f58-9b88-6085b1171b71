import { useEffect } from 'react';
import { useRouter, createLazyFileRoute } from '@tanstack/react-router';

import '@aws-amplify/ui-react/styles.css';

import { useAuthentication } from '@/lib/auth';

import img from '../../assets/hero_bg.jpg';
import SignUp from './-components/TOUModal';
import { withAuthenticator } from '@aws-amplify/ui-react';

const formFields = {
  signUp: {
    given_name: {
      label: 'First Name',
      placeholder: 'Enter your first name',
      order: 1,
      isRequired: true,
    },
    family_name: {
      label: 'Last Name',
      placeholder: 'Enter your last name',
      order: 2,
      isRequired: true,
    },
    email: {
      label: 'Email',
      order: 3,
      isRequired: true,
    },
    password: {
      order: 4,
    },
    confirm_password: {
      order: 5,
    },
  },
};

function InnerComponent() {
  const router = useRouter();
  const searchParams = Route.useSearch() as { redirect?: string; mode?: string };
  const { user } = useAuthentication();

  useEffect(() => {
    if (user) {
      router.history.push(searchParams.redirect || '/');
    }
  }, [user]);

  if (user) return null;
  return (
    <div className="relative flex h-full flex-row">
      <div className="h-full w-full">
        <img src={img} className="h-full w-full object-cover" />
      </div>
      <div className="absolute left-1/2 top-[20%] -translate-x-1/2 z-10" />
    </div>
  )
};

// IMPORTANT: AWS Amplify UI Authenticator does not support custom components for SignUp.
// So we need to use the withAuthenticator HOC to wrap the InnerComponent.
// AWS DOC IS COMPLETELY WRONG ON THIS POINT (retrieved 2025-06-20)
const AuthComponent = () => {
  const searchParams = Route.useSearch() as { redirect?: string; mode?: string };
  
  // Determine initial state based on URL parameter
  let initialState: 'signIn' | 'signUp' = 'signUp'; // default
  if (searchParams.mode === 'login') {
    initialState = 'signIn';
  } else if (searchParams.mode === 'signup') {
    initialState = 'signUp';
  }

  const AuthenticatorComponent = withAuthenticator(InnerComponent, {
    initialState,
    hideSignUp: false,
    signUpAttributes: ['given_name', 'family_name', 'email', 'custom:role' as any],
    formFields: formFields,
    components: {
      SignUp: {
        FormFields: () => <SignUp />
      },
    },
  });

  return <AuthenticatorComponent />;
};

export const Route = createLazyFileRoute('/_public/login')({
  component: AuthComponent,
});