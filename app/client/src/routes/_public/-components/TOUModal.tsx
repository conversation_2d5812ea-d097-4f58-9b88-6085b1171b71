import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Authenticator,
  CheckboxField,
  SelectField,
  TextField,
  PasswordField,
  useAuthenticator,
} from '@aws-amplify/ui-react';
import { Button } from '@/components/ui/button';
import { Printer, X } from 'lucide-react';

import VOW from './VOW';
import { useBreakpoint } from '@/hooks/useBreakpoint';

// Custom hook to get URL parameters
const useURLParams = () => {
  const [params, setParams] = useState<URLSearchParams>(new URLSearchParams());
  
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    setParams(urlParams);
  }, []);
  
  const getParam = (key: string) => params.get(key);
  
  return { getParam, params };
};

const SCROLL_THRESHOLD = 20; // Increased threshold for better detection

const SignUp = () => {
  const { validationErrors } = useAuthenticator();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [consentChecked, setConsentChecked] = useState(false);
  const [contentLoaded, setContentLoaded] = useState(false);
  const vowRef = useRef<HTMLDivElement>(null);
  const resizeObserver = useRef<ResizeObserver | null>(null);
  const { isMobile } = useBreakpoint();
  const { getParam } = useURLParams();

  const checkScrollPosition = useCallback(() => {
    const container = vowRef.current;
    if (!container) return;

    const { scrollHeight, scrollTop, clientHeight } = container;

    // Calculate how far we are from the bottom
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

    // Consider it "scrolled to bottom" if we're within the threshold
    const isAtBottom = distanceFromBottom <= SCROLL_THRESHOLD;

    if (isAtBottom !== isScrolledToBottom) {
      setIsScrolledToBottom(isAtBottom);
    }
  }, [isScrolledToBottom]);

  // Handle content loading
  useEffect(() => {
    if (!isModalOpen) return;

    let checkTimeout: NodeJS.Timeout;
    const container = vowRef.current;
    if (!container) return;

    // Function to check scroll after content changes
    const checkAfterDelay = () => {
      clearTimeout(checkTimeout);
      checkTimeout = setTimeout(() => {
        setContentLoaded(true);
        checkScrollPosition();
      }, 100);
    };

    // Create a new ResizeObserver
    resizeObserver.current = new ResizeObserver(checkAfterDelay);

    // Observe the container
    resizeObserver.current.observe(container);

    // Use MutationObserver to detect when content is fully loaded
    const observer = new MutationObserver(checkAfterDelay);

    observer.observe(container, {
      childList: true,
      subtree: true,
      characterData: true
    });

    // Initial check
    checkAfterDelay();

    return () => {
      clearTimeout(checkTimeout);
      observer.disconnect();
      if (resizeObserver.current) {
        resizeObserver.current.disconnect();
      }
    };
  }, [isModalOpen, checkScrollPosition]);

  // Reset states when modal opens
  const showModal = () => {
    setIsModalOpen(true);
    setIsScrolledToBottom(false);
    setIsChecked(false);
    setConsentChecked(false);
    setContentLoaded(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    e.preventDefault();
    checkScrollPosition();
  }, [checkScrollPosition]);

  const handleAgree = () => {
    setIsChecked(true);
    handleCancel();
  };

  const printContent = () => {
    const content = vowRef.current;
    if (!content) return;

    const windowPrint = window.open(
      '',
      '',
      'left=0,top=0,width=800,height=900,toolbar=0,scrollbars=0,status=0',
    );

    if (!windowPrint) return;

    windowPrint.document.write(content.innerHTML);
    windowPrint.document.title = 'Platlabs.com VOW Terms of Use';
    windowPrint.document.close();
    windowPrint.focus();
    windowPrint.print();
    windowPrint.close();
  };

  return (
    <>
      {/* Render standard fields manually to avoid custom field conflicts */}
      <TextField
        name="given_name"
        label="First Name"
        placeholder="Enter Your First Name"
        isRequired
        errorMessage={validationErrors.given_name as string}
        hasError={!!validationErrors.given_name}
      />
      <TextField
        name="family_name"
        label="Last Name"
        placeholder="Enter Your Last Name"
        isRequired
        errorMessage={validationErrors.family_name as string}
        hasError={!!validationErrors.family_name}
      />
      <TextField
        name="email"
        label="Email"
        type="email"
        placeholder="Enter Your Email"
        isRequired
        errorMessage={validationErrors.email as string}
        hasError={!!validationErrors.email}
      />
      <PasswordField
        name="password"
        label="Password"
        // type="password"
        placeholder="Enter Your Password"
        isRequired
        errorMessage={validationErrors.password as string}
        hasError={!!validationErrors.password}
      />
      <PasswordField
        name="confirm_password"
        label="Confirm Password"
        placeholder="Confirm Your Password"
        // type="password"
        isRequired
        errorMessage={validationErrors.confirm_password as string}
        hasError={!!validationErrors.confirm_password}
      />
      <SelectField
        name="custom:role"
        label="Account Type"
        placeholder="Select Your Role"
        isRequired
        errorMessage={validationErrors['custom:role'] as string}
        hasError={!!validationErrors['custom:role']}
      >
        <option value="Investor">Investor</option>
        <option value="Agent">Agent</option>
        <option value="Investor and Agent">Investor and Agent</option>
      </SelectField>
      <CheckboxField
        errorMessage={validationErrors.acknowledgement as string}
        hasError={!!validationErrors.acknowledgement}
        name="acknowledgement"
        value="yes"
        label="I agree with the Terms of Use"
        checked={isChecked}
        readOnly
        onClick={showModal}
      />
      {/* Hidden field to save consent to Cognito */}
      <input
        type="hidden"
        name="custom:consent"
        value={consentChecked ? "yes" : "no"}
      />
            <input
        type="hidden"
        name="custom:source"
        value={getParam('utm_source') || 'unknown'}
      />
      {isModalOpen && (
        <div
          className="fixed top-0 left-0 w-full h-full bg-black/50 z-50 flex justify-center items-center"
        >
          <div className={`${isMobile ? 'px-4 py-6' : 'px-12 py-8'} w-[90%] max-w-[600px] bg-white rounded-lg shadow-lg border border-medium-gray-20`}>
            <Button onClick={handleCancel} className="absolute top-0 right-0 p-4">
              <X />
            </Button>
            <div
              ref={vowRef}
              className="overflow-y-auto max-h-[50vh]"
              style={{
                scrollBehavior: 'smooth'
              }}
              onScroll={handleScroll}
            >
              <VOW />
            </div>
            <div className={`flex ${isMobile ? 'flex-col' : 'flex-row'}  justify-between mt-3 pt-3 border-t`}>
              <Button
                variant="outline"
                onClick={printContent}
                className="flex items-center gap-2"
              >
                <Printer className="h-4 w-4" /> Print
              </Button>
              <div className={`flex ${isMobile ? 'flex-col justfy-start gap-2 mt-2' : 'flex-row justify-center gap-2'}  `}>
                <CheckboxField
                  name="consent"
                  label="I consent"
                  checked={consentChecked}
                  onChange={(e) => setConsentChecked(e.target.checked)}
                  disabled={!isScrolledToBottom || !contentLoaded}
                />
                <Button
                  onClick={handleAgree}
                  disabled={!isScrolledToBottom || !contentLoaded}
                  className="flex items-center gap-2"
                >
                  I agree with the Terms of Use
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SignUp;
