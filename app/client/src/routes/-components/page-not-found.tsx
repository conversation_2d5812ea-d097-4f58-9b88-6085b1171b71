import React from 'react';
import { Link } from '@tanstack/react-router';

import { buttonVariants } from '@/components/ui/button';
import { icons } from '@/components/ui/icons';

import { cn } from '@/lib/utils/styles';

export const PageNotFound = ({
  children,
  className,
}: {
  children?: React.ReactNode;
  className?: string;
}) => {
  return (
    <React.Fragment>
      <section className={cn('flex grow flex-col items-center justify-center gap-2', className)}>
        <p className="text-4xl font-semibold uppercase tracking-wide text-primary">404</p>
        <h1 className="mt-2 text-2xl font-semibold tracking-tight text-foreground sm:text-4xl">
          Page not found.
        </h1>
        <p className="mt-2 text-center text-base text-foreground/80">
          Sorry, we couldn't find the page you're looking for.
        </p>
        {children || (
          <p className="mt-4">
            <Link to="/" className={cn(buttonVariants())}>
              <icons.ChevronLeft className="mr-2 h-4 w-4" />
              <span>Go Back</span>
            </Link>
          </p>
        )}
      </section>
    </React.Fragment>
  );
};
