@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Reckless Font Family */
@font-face {
  font-family: 'Reckless';
  src: url('./assets/fonts/Reckless/Reckless-Light.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Reckless';
  src: url('./assets/fonts/Reckless/Reckless-Regular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Reckless';
  src: url('./assets/fonts/Reckless/Reckless-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Reckless';
  src: url('./assets/fonts/Reckless/Reckless-SemiBold.otf') format('opentype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Mabry Font Family */
@font-face {
  font-family: 'Mabry';
  src: url('./assets/fonts/Mabry/mabry-light-pro.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Mabry';
  src: url('./assets/fonts/Mabry/mabry-regular-pro.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Mabry';
  src: url('./assets/fonts/Mabry/mabry-medium-pro.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

:root {
  --font-body: 'Mabry', sans-serif;
  --font-heading: 'Reckless', serif;
  
  font-family: var(--font-body);
  line-height: 1.5;
  font-weight: 400;

  color: #2D2D2D; /* Default text color */
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Global Colors */
  --color-super-light-gray: #f5f5f5;
  --color-light-gray: #fafafa;
  --color-white: #ffffff;
  --color-red: #C3342E;
  --color-dark-gray: #2D2D2D;
  --color-text-black: #222222;
  --color-button-blue: #1890ff;


  --color-lennar-blue-light: #e6f4ff;
  --color-lennar-blue-dark: #315d9e;
  --color-dark-green: #008000;
  --color-light-green: #e7ffe7;
  --radius:
    0.625rem;
  --background:
    oklch(1 0 0);
  --foreground:
    oklch(0.145 0 0);
  --card:
    oklch(1 0 0);
  --card-foreground:
    oklch(0.145 0 0);
  --popover:
    oklch(1 0 0);
  --popover-foreground:
    oklch(0.145 0 0);
  --primary:
    oklch(0.205 0 0);
  --primary-foreground:
    oklch(0.985 0 0);
  --secondary:
    oklch(0.97 0 0);
  --secondary-foreground:
    oklch(0.205 0 0);
  --muted:
    oklch(0.97 0 0);
  --muted-foreground:
    oklch(0.556 0 0);
  --accent:
    oklch(0.97 0 0);
  --accent-foreground:
    oklch(0.205 0 0);
  --destructive:
    oklch(0.577 0.245 27.325);
  --border:
    oklch(0.922 0 0);
  --input:
    oklch(0.922 0 0);
  --ring:
    oklch(0.708 0 0);
  --chart-1:
    oklch(0.646 0.222 41.116);
  --chart-2:
    oklch(0.6 0.118 184.704);
  --chart-3:
    oklch(0.398 0.07 227.392);
  --chart-4:
    oklch(0.828 0.189 84.429);
  --chart-5:
    oklch(0.769 0.188 70.08);
  --sidebar:
    oklch(0.985 0 0);
  --sidebar-foreground:
    oklch(0.145 0 0);
  --sidebar-primary:
    oklch(0.205 0 0);
  --sidebar-primary-foreground:
    oklch(0.985 0 0);
  --sidebar-accent:
    oklch(0.97 0 0);
  --sidebar-accent-foreground:
    oklch(0.205 0 0);
  --sidebar-border:
    oklch(0.922 0 0);
  --sidebar-ring:
    oklch(0.708 0 0);
}

html,
body,
#root {
  height: 100%;
  width: 100%; /* Ensure full width */
  margin: 0;
  padding: 0;
  overflow: hidden; /* Prevent body scroll */
  box-sizing: border-box; /* Apply globally for easier layout */
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

@theme {
  /* Lennar */
  /* breakpoint */
  --breakpoint-sm: 375px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1440px;
  --breakpoint-xl: 1920px;
  --breakpoint-ul: 2560px;
  
  /* Color */
  /* Primary */
  --color-green-primary: #406855;
  --color-blue-20: #D3DEED;
  --color-dark-gray: #2D2D2D;
  --color-medium-gray: #767676;
  --color-medium-gray-20: #D5D5D5;
  --color-light-gray: #FAFAFA;
  --color-white: #FFFFFF;
  --color-blue: #235CA3;
  /* Secondary */
  --color-red: #C3342E;
  --color-green-secondary: #409125;
  --color-light-blue: #F2F4FF;
  /* Lennar */


  /* Original Colors will be replaced */
  --color-super-light-gray: #f5f5f5;
  --color-button-blue: #1890ff;
  
  --color-text-black: #222222;
  --color-lennar-blue-light: #e6f4ff;
  --color-lennar-blue-dark: #315d9e;
  --color-lennar-blue-light-opacity-75: #e6f4ff75;
  --color-dark-green: #008000;
  --color-light-green: #e7ffe7;
  /* --color-white: #fefefe; */

  /* Custom sizes */
  --font-size-tiny: 0.625rem;   /* 10px */
  --font-size-small: 0.75rem; /* 12px */
  /* Rest uses default values */

  /* Size utilities */
  --text-tiny: var(--font-size-tiny);
  --text-small: var(--font-size-small);
  /* Rest uses default values */  

  /* Animation definitions */
  --animate-fade-in: fade-in 0.3s ease-in-out;

  @keyframes fade-in {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

}

@theme inline {
  --radius-sm:
    calc(var(--radius) - 4px);
  --radius-md:
    calc(var(--radius) - 2px);
  --radius-lg:
    var(--radius);
  --radius-xl:
    calc(var(--radius) + 4px);
  --color-background:
    var(--background);
  --color-foreground:
    var(--foreground);
  --color-card:
    var(--card);
  --color-card-foreground:
    var(--card-foreground);
  --color-popover:
    var(--popover);
  --color-popover-foreground:
    var(--popover-foreground);
  --color-primary:
    var(--primary);
  --color-primary-foreground:
    var(--primary-foreground);
  --color-secondary:
    var(--secondary);
  --color-secondary-foreground:
    var(--secondary-foreground);
  --color-muted:
    var(--muted);
  --color-muted-foreground:
    var(--muted-foreground);
  --color-accent:
    var(--accent);
  --color-accent-foreground:
    var(--accent-foreground);
  --color-destructive:
    var(--destructive);
  --color-border:
    var(--border);
  --color-input:
    var(--input);
  --color-ring:
    var(--ring);
  --color-chart-1:
    var(--chart-1);
  --color-chart-2:
    var(--chart-2);
  --color-chart-3:
    var(--chart-3);
  --color-chart-4:
    var(--chart-4);
  --color-chart-5:
    var(--chart-5);
  --color-sidebar:
    var(--sidebar);
  --color-sidebar-foreground:
    var(--sidebar-foreground);
  --color-sidebar-primary:
    var(--sidebar-primary);
  --color-sidebar-primary-foreground:
    var(--sidebar-primary-foreground);
  --color-sidebar-accent:
    var(--sidebar-accent);
  --color-sidebar-accent-foreground:
    var(--sidebar-accent-foreground);
  --color-sidebar-border:
    var(--sidebar-border);
  --color-sidebar-ring:
    var(--sidebar-ring);
}

.dark {
  --background:
    oklch(0.145 0 0);
  --foreground:
    oklch(0.985 0 0);
  --card:
    oklch(0.205 0 0);
  --card-foreground:
    oklch(0.985 0 0);
  --popover:
    oklch(0.205 0 0);
  --popover-foreground:
    oklch(0.985 0 0);
  --primary:
    oklch(0.922 0 0);
  --primary-foreground:
    oklch(0.205 0 0);
  --secondary:
    oklch(0.269 0 0);
  --secondary-foreground:
    oklch(0.985 0 0);
  --muted:
    oklch(0.269 0 0);
  --muted-foreground:
    oklch(0.708 0 0);
  --accent:
    oklch(0.269 0 0);
  --accent-foreground:
    oklch(0.985 0 0);
  --destructive:
    oklch(0.704 0.191 22.216);
  --border:
    oklch(1 0 0 / 10%);
  --input:
    oklch(1 0 0 / 15%);
  --ring:
    oklch(0.556 0 0);
  --chart-1:
    oklch(0.488 0.243 264.376);
  --chart-2:
    oklch(0.696 0.17 162.48);
  --chart-3:
    oklch(0.769 0.188 70.08);
  --chart-4:
    oklch(0.627 0.265 303.9);
  --chart-5:
    oklch(0.645 0.246 16.439);
  --sidebar:
    oklch(0.205 0 0);
  --sidebar-foreground:
    oklch(0.985 0 0);
  --sidebar-primary:
    oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground:
    oklch(0.985 0 0);
  --sidebar-accent:
    oklch(0.269 0 0);
  --sidebar-accent-foreground:
    oklch(0.985 0 0);
  --sidebar-border:
    oklch(1 0 0 / 10%);
  --sidebar-ring:
    oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-mabry);
  }
}

@layer utilities {
  .blur-sm {
    filter: blur(8px);
  }
  .font-heading {
    font-family: var(--font-heading);
  }
  
  .font-body {
    font-family: var(--font-body);
  }
}

/* Hide Osano's default floating widget since we'll use footer link */
.osano-cm-widget,
.osano-cm-dialog__badge,
.osano-cm-badge {
  display: none !important;
}

/* Reset button styling in footer to match links exactly */
footer button {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  font-family: var(--font-body) !important;
  font-size: 0.625rem !important; /* 10px - same as text-tiny */
  font-weight: 400 !important;
  line-height: 1 !important; /* leading-none */
  letter-spacing: -0.025em !important; /* tracking-tight */
  color: var(--color-button-blue) !important; /* text-button-blue */
  text-decoration: none !important;
  text-align: left !important;
  cursor: pointer !important;
  outline: none !important;
  box-shadow: none !important;
  transition: all 0.3s ease-in-out !important;
}

footer button:hover {
  color: rgba(24, 144, 255, 0.8) !important; /* text-button-blue/80 */
  transform: translateX(0.125rem) !important; /* hover:translate-x-0.5 */
}