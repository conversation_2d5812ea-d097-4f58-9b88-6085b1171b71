/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

import { Route as rootRouteImport } from './routes/__root'
import { Route as PublicRouteImport } from './routes/_public'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated'
import { Route as AuthenticatedIndexRouteImport } from './routes/_authenticated/index'
import { Route as AuthenticatedPropertiesRouteRouteImport } from './routes/_authenticated/properties/route'
import { Route as AuthenticatedPropertiesIndexRouteImport } from './routes/_authenticated/properties/index'
import { Route as AuthenticatedPropertiesIdRouteRouteImport } from './routes/_authenticated/properties/$id/route'
import { Route as AuthenticatedPropertiesIdIndexRouteImport } from './routes/_authenticated/properties/$id/index'
import { Route as AuthenticatedPropertiesIdProFormaIndexRouteImport } from './routes/_authenticated/properties/$id/proForma/index'
import { Route as AuthenticatedPropertiesIdImagesIndexRouteImport } from './routes/_authenticated/properties/$id/images/index'
import { Route as AuthenticatedPropertiesIdHomeInfoIndexRouteImport } from './routes/_authenticated/properties/$id/home-info/index'
import { Route as AuthenticatedPropertiesIdDocumentsIndexRouteImport } from './routes/_authenticated/properties/$id/documents/index'
import { Route as AuthenticatedPropertiesIdDemographicsIndexRouteImport } from './routes/_authenticated/properties/$id/demographics/index'
import { Route as AuthenticatedPropertiesIdCompsIndexRouteImport } from './routes/_authenticated/properties/$id/comps/index'
import { Route as AuthenticatedPropertiesIdAreaInsightsIndexRouteImport } from './routes/_authenticated/properties/$id/area-insights/index'

const PublicLoginLazyRouteImport = createFileRoute('/_public/login')()

const PublicRoute = PublicRouteImport.update({
  id: '/_public',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedIndexRoute = AuthenticatedIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const PublicLoginLazyRoute = PublicLoginLazyRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => PublicRoute,
} as any).lazy(() => import('./routes/_public/login.lazy').then((d) => d.Route))
const AuthenticatedPropertiesRouteRoute =
  AuthenticatedPropertiesRouteRouteImport.update({
    id: '/properties',
    path: '/properties',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedPropertiesIndexRoute =
  AuthenticatedPropertiesIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedPropertiesRouteRoute,
  } as any)
const AuthenticatedPropertiesIdRouteRoute =
  AuthenticatedPropertiesIdRouteRouteImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AuthenticatedPropertiesRouteRoute,
  } as any)
const AuthenticatedPropertiesIdIndexRoute =
  AuthenticatedPropertiesIdIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedPropertiesIdRouteRoute,
  } as any)
const AuthenticatedPropertiesIdProFormaIndexRoute =
  AuthenticatedPropertiesIdProFormaIndexRouteImport.update({
    id: '/proForma/',
    path: '/proForma/',
    getParentRoute: () => AuthenticatedPropertiesIdRouteRoute,
  } as any)
const AuthenticatedPropertiesIdImagesIndexRoute =
  AuthenticatedPropertiesIdImagesIndexRouteImport.update({
    id: '/images/',
    path: '/images/',
    getParentRoute: () => AuthenticatedPropertiesIdRouteRoute,
  } as any)
const AuthenticatedPropertiesIdHomeInfoIndexRoute =
  AuthenticatedPropertiesIdHomeInfoIndexRouteImport.update({
    id: '/home-info/',
    path: '/home-info/',
    getParentRoute: () => AuthenticatedPropertiesIdRouteRoute,
  } as any)
const AuthenticatedPropertiesIdDocumentsIndexRoute =
  AuthenticatedPropertiesIdDocumentsIndexRouteImport.update({
    id: '/documents/',
    path: '/documents/',
    getParentRoute: () => AuthenticatedPropertiesIdRouteRoute,
  } as any)
const AuthenticatedPropertiesIdDemographicsIndexRoute =
  AuthenticatedPropertiesIdDemographicsIndexRouteImport.update({
    id: '/demographics/',
    path: '/demographics/',
    getParentRoute: () => AuthenticatedPropertiesIdRouteRoute,
  } as any)
const AuthenticatedPropertiesIdCompsIndexRoute =
  AuthenticatedPropertiesIdCompsIndexRouteImport.update({
    id: '/comps/',
    path: '/comps/',
    getParentRoute: () => AuthenticatedPropertiesIdRouteRoute,
  } as any)
const AuthenticatedPropertiesIdAreaInsightsIndexRoute =
  AuthenticatedPropertiesIdAreaInsightsIndexRouteImport.update({
    id: '/area-insights/',
    path: '/area-insights/',
    getParentRoute: () => AuthenticatedPropertiesIdRouteRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/properties': typeof AuthenticatedPropertiesRouteRouteWithChildren
  '/login': typeof PublicLoginLazyRoute
  '/': typeof AuthenticatedIndexRoute
  '/properties/$id': typeof AuthenticatedPropertiesIdRouteRouteWithChildren
  '/properties/': typeof AuthenticatedPropertiesIndexRoute
  '/properties/$id/': typeof AuthenticatedPropertiesIdIndexRoute
  '/properties/$id/area-insights': typeof AuthenticatedPropertiesIdAreaInsightsIndexRoute
  '/properties/$id/comps': typeof AuthenticatedPropertiesIdCompsIndexRoute
  '/properties/$id/demographics': typeof AuthenticatedPropertiesIdDemographicsIndexRoute
  '/properties/$id/documents': typeof AuthenticatedPropertiesIdDocumentsIndexRoute
  '/properties/$id/home-info': typeof AuthenticatedPropertiesIdHomeInfoIndexRoute
  '/properties/$id/images': typeof AuthenticatedPropertiesIdImagesIndexRoute
  '/properties/$id/proForma': typeof AuthenticatedPropertiesIdProFormaIndexRoute
}
export interface FileRoutesByTo {
  '/login': typeof PublicLoginLazyRoute
  '/': typeof AuthenticatedIndexRoute
  '/properties': typeof AuthenticatedPropertiesIndexRoute
  '/properties/$id': typeof AuthenticatedPropertiesIdIndexRoute
  '/properties/$id/area-insights': typeof AuthenticatedPropertiesIdAreaInsightsIndexRoute
  '/properties/$id/comps': typeof AuthenticatedPropertiesIdCompsIndexRoute
  '/properties/$id/demographics': typeof AuthenticatedPropertiesIdDemographicsIndexRoute
  '/properties/$id/documents': typeof AuthenticatedPropertiesIdDocumentsIndexRoute
  '/properties/$id/home-info': typeof AuthenticatedPropertiesIdHomeInfoIndexRoute
  '/properties/$id/images': typeof AuthenticatedPropertiesIdImagesIndexRoute
  '/properties/$id/proForma': typeof AuthenticatedPropertiesIdProFormaIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/_public': typeof PublicRouteWithChildren
  '/_authenticated/properties': typeof AuthenticatedPropertiesRouteRouteWithChildren
  '/_public/login': typeof PublicLoginLazyRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/properties/$id': typeof AuthenticatedPropertiesIdRouteRouteWithChildren
  '/_authenticated/properties/': typeof AuthenticatedPropertiesIndexRoute
  '/_authenticated/properties/$id/': typeof AuthenticatedPropertiesIdIndexRoute
  '/_authenticated/properties/$id/area-insights/': typeof AuthenticatedPropertiesIdAreaInsightsIndexRoute
  '/_authenticated/properties/$id/comps/': typeof AuthenticatedPropertiesIdCompsIndexRoute
  '/_authenticated/properties/$id/demographics/': typeof AuthenticatedPropertiesIdDemographicsIndexRoute
  '/_authenticated/properties/$id/documents/': typeof AuthenticatedPropertiesIdDocumentsIndexRoute
  '/_authenticated/properties/$id/home-info/': typeof AuthenticatedPropertiesIdHomeInfoIndexRoute
  '/_authenticated/properties/$id/images/': typeof AuthenticatedPropertiesIdImagesIndexRoute
  '/_authenticated/properties/$id/proForma/': typeof AuthenticatedPropertiesIdProFormaIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/properties'
    | '/login'
    | '/'
    | '/properties/$id'
    | '/properties/'
    | '/properties/$id/'
    | '/properties/$id/area-insights'
    | '/properties/$id/comps'
    | '/properties/$id/demographics'
    | '/properties/$id/documents'
    | '/properties/$id/home-info'
    | '/properties/$id/images'
    | '/properties/$id/proForma'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/login'
    | '/'
    | '/properties'
    | '/properties/$id'
    | '/properties/$id/area-insights'
    | '/properties/$id/comps'
    | '/properties/$id/demographics'
    | '/properties/$id/documents'
    | '/properties/$id/home-info'
    | '/properties/$id/images'
    | '/properties/$id/proForma'
  id:
    | '__root__'
    | '/_authenticated'
    | '/_public'
    | '/_authenticated/properties'
    | '/_public/login'
    | '/_authenticated/'
    | '/_authenticated/properties/$id'
    | '/_authenticated/properties/'
    | '/_authenticated/properties/$id/'
    | '/_authenticated/properties/$id/area-insights/'
    | '/_authenticated/properties/$id/comps/'
    | '/_authenticated/properties/$id/demographics/'
    | '/_authenticated/properties/$id/documents/'
    | '/_authenticated/properties/$id/home-info/'
    | '/_authenticated/properties/$id/images/'
    | '/_authenticated/properties/$id/proForma/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  PublicRoute: typeof PublicRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_public': {
      id: '/_public'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof PublicRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_public/login': {
      id: '/_public/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof PublicLoginLazyRouteImport
      parentRoute: typeof PublicRoute
    }
    '/_authenticated/properties': {
      id: '/_authenticated/properties'
      path: '/properties'
      fullPath: '/properties'
      preLoaderRoute: typeof AuthenticatedPropertiesRouteRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/properties/': {
      id: '/_authenticated/properties/'
      path: '/'
      fullPath: '/properties/'
      preLoaderRoute: typeof AuthenticatedPropertiesIndexRouteImport
      parentRoute: typeof AuthenticatedPropertiesRouteRoute
    }
    '/_authenticated/properties/$id': {
      id: '/_authenticated/properties/$id'
      path: '/$id'
      fullPath: '/properties/$id'
      preLoaderRoute: typeof AuthenticatedPropertiesIdRouteRouteImport
      parentRoute: typeof AuthenticatedPropertiesRouteRoute
    }
    '/_authenticated/properties/$id/': {
      id: '/_authenticated/properties/$id/'
      path: '/'
      fullPath: '/properties/$id/'
      preLoaderRoute: typeof AuthenticatedPropertiesIdIndexRouteImport
      parentRoute: typeof AuthenticatedPropertiesIdRouteRoute
    }
    '/_authenticated/properties/$id/proForma/': {
      id: '/_authenticated/properties/$id/proForma/'
      path: '/proForma'
      fullPath: '/properties/$id/proForma'
      preLoaderRoute: typeof AuthenticatedPropertiesIdProFormaIndexRouteImport
      parentRoute: typeof AuthenticatedPropertiesIdRouteRoute
    }
    '/_authenticated/properties/$id/images/': {
      id: '/_authenticated/properties/$id/images/'
      path: '/images'
      fullPath: '/properties/$id/images'
      preLoaderRoute: typeof AuthenticatedPropertiesIdImagesIndexRouteImport
      parentRoute: typeof AuthenticatedPropertiesIdRouteRoute
    }
    '/_authenticated/properties/$id/home-info/': {
      id: '/_authenticated/properties/$id/home-info/'
      path: '/home-info'
      fullPath: '/properties/$id/home-info'
      preLoaderRoute: typeof AuthenticatedPropertiesIdHomeInfoIndexRouteImport
      parentRoute: typeof AuthenticatedPropertiesIdRouteRoute
    }
    '/_authenticated/properties/$id/documents/': {
      id: '/_authenticated/properties/$id/documents/'
      path: '/documents'
      fullPath: '/properties/$id/documents'
      preLoaderRoute: typeof AuthenticatedPropertiesIdDocumentsIndexRouteImport
      parentRoute: typeof AuthenticatedPropertiesIdRouteRoute
    }
    '/_authenticated/properties/$id/demographics/': {
      id: '/_authenticated/properties/$id/demographics/'
      path: '/demographics'
      fullPath: '/properties/$id/demographics'
      preLoaderRoute: typeof AuthenticatedPropertiesIdDemographicsIndexRouteImport
      parentRoute: typeof AuthenticatedPropertiesIdRouteRoute
    }
    '/_authenticated/properties/$id/comps/': {
      id: '/_authenticated/properties/$id/comps/'
      path: '/comps'
      fullPath: '/properties/$id/comps'
      preLoaderRoute: typeof AuthenticatedPropertiesIdCompsIndexRouteImport
      parentRoute: typeof AuthenticatedPropertiesIdRouteRoute
    }
    '/_authenticated/properties/$id/area-insights/': {
      id: '/_authenticated/properties/$id/area-insights/'
      path: '/area-insights'
      fullPath: '/properties/$id/area-insights'
      preLoaderRoute: typeof AuthenticatedPropertiesIdAreaInsightsIndexRouteImport
      parentRoute: typeof AuthenticatedPropertiesIdRouteRoute
    }
  }
}

interface AuthenticatedPropertiesIdRouteRouteChildren {
  AuthenticatedPropertiesIdIndexRoute: typeof AuthenticatedPropertiesIdIndexRoute
  AuthenticatedPropertiesIdAreaInsightsIndexRoute: typeof AuthenticatedPropertiesIdAreaInsightsIndexRoute
  AuthenticatedPropertiesIdCompsIndexRoute: typeof AuthenticatedPropertiesIdCompsIndexRoute
  AuthenticatedPropertiesIdDemographicsIndexRoute: typeof AuthenticatedPropertiesIdDemographicsIndexRoute
  AuthenticatedPropertiesIdDocumentsIndexRoute: typeof AuthenticatedPropertiesIdDocumentsIndexRoute
  AuthenticatedPropertiesIdHomeInfoIndexRoute: typeof AuthenticatedPropertiesIdHomeInfoIndexRoute
  AuthenticatedPropertiesIdImagesIndexRoute: typeof AuthenticatedPropertiesIdImagesIndexRoute
  AuthenticatedPropertiesIdProFormaIndexRoute: typeof AuthenticatedPropertiesIdProFormaIndexRoute
}

const AuthenticatedPropertiesIdRouteRouteChildren: AuthenticatedPropertiesIdRouteRouteChildren =
  {
    AuthenticatedPropertiesIdIndexRoute: AuthenticatedPropertiesIdIndexRoute,
    AuthenticatedPropertiesIdAreaInsightsIndexRoute:
      AuthenticatedPropertiesIdAreaInsightsIndexRoute,
    AuthenticatedPropertiesIdCompsIndexRoute:
      AuthenticatedPropertiesIdCompsIndexRoute,
    AuthenticatedPropertiesIdDemographicsIndexRoute:
      AuthenticatedPropertiesIdDemographicsIndexRoute,
    AuthenticatedPropertiesIdDocumentsIndexRoute:
      AuthenticatedPropertiesIdDocumentsIndexRoute,
    AuthenticatedPropertiesIdHomeInfoIndexRoute:
      AuthenticatedPropertiesIdHomeInfoIndexRoute,
    AuthenticatedPropertiesIdImagesIndexRoute:
      AuthenticatedPropertiesIdImagesIndexRoute,
    AuthenticatedPropertiesIdProFormaIndexRoute:
      AuthenticatedPropertiesIdProFormaIndexRoute,
  }

const AuthenticatedPropertiesIdRouteRouteWithChildren =
  AuthenticatedPropertiesIdRouteRoute._addFileChildren(
    AuthenticatedPropertiesIdRouteRouteChildren,
  )

interface AuthenticatedPropertiesRouteRouteChildren {
  AuthenticatedPropertiesIdRouteRoute: typeof AuthenticatedPropertiesIdRouteRouteWithChildren
  AuthenticatedPropertiesIndexRoute: typeof AuthenticatedPropertiesIndexRoute
}

const AuthenticatedPropertiesRouteRouteChildren: AuthenticatedPropertiesRouteRouteChildren =
  {
    AuthenticatedPropertiesIdRouteRoute:
      AuthenticatedPropertiesIdRouteRouteWithChildren,
    AuthenticatedPropertiesIndexRoute: AuthenticatedPropertiesIndexRoute,
  }

const AuthenticatedPropertiesRouteRouteWithChildren =
  AuthenticatedPropertiesRouteRoute._addFileChildren(
    AuthenticatedPropertiesRouteRouteChildren,
  )

interface AuthenticatedRouteChildren {
  AuthenticatedPropertiesRouteRoute: typeof AuthenticatedPropertiesRouteRouteWithChildren
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedPropertiesRouteRoute:
    AuthenticatedPropertiesRouteRouteWithChildren,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

interface PublicRouteChildren {
  PublicLoginLazyRoute: typeof PublicLoginLazyRoute
}

const PublicRouteChildren: PublicRouteChildren = {
  PublicLoginLazyRoute: PublicLoginLazyRoute,
}

const PublicRouteWithChildren =
  PublicRoute._addFileChildren(PublicRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  PublicRoute: PublicRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
