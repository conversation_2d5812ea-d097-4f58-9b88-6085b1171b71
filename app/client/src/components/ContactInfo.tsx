import React from "react";
import { Phone } from "lucide-react";
import { formatPhoneNumber } from "@/lib/utils/formatUtils";

interface ContactInfoProps {
  phone?: string | null;
}

const ContactInfo: React.FC<ContactInfoProps> = ({ phone }) => {
  const formattedPhone = formatPhoneNumber(phone);
  return (
    <a
      href={`tel:${formattedPhone.rawPhone}`}
      className="flex items-center justify-center rounded-lg py-1.5 px-2 text-sm font-semibold bg-green-primary text-white hover:bg-green-primary/80 transition-colors"
      aria-label="Call agent"
    >
      <Phone className="w-3 h-3 mr-1" /> {formattedPhone.displayPhone}
    </a>
  );
};

export default ContactInfo;
