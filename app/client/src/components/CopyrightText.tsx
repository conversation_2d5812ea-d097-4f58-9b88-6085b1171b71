import React from 'react';

interface CopyrightTextProps {
  className?: string;
}

const CopyrightText: React.FC<CopyrightTextProps> = ({ className = "" }) => {
  const currentYear = new Date().getFullYear();

  return (
    <p className="text-tiny tracking-tight leading-none text-dark-gray">
      © {currentYear} Lennar Corporation. Lennar and the Lennar logo are
      U.S. registered service marks or service marks of Lennar Corporation
      and/or its subsidiaries. Seller's Broker: in expanded disclaimer. Construction
      License(s): in expanded disclaimer.
    </p>
  );
};

export default CopyrightText;