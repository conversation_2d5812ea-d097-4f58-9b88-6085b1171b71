import { usePropertiesFilterState } from '@/hooks/usePropertiesFilter';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import Header from './Header';
import Footer from './Footer';
import LeftPanel from './LeftPanel';
import MobileHeader from './MobileHeader';
import MapToggleButton from './MapToggleButton';

interface PropertiesContentLayoutProps {
  isLeftPanelOpen: boolean;
  toggleLeftPanel: () => void;
  containerRef: React.RefObject<HTMLDivElement | null>;
  children: React.ReactNode;
}

const PropertiesContentLayout = ({
  isLeftPanelOpen,
  toggleLeftPanel,
  containerRef,
  children,
}: PropertiesContentLayoutProps) => {
  const { filters, minCapRate, handleMinCapRateChange } = usePropertiesFilterState();
  const { isMobile } = useBreakpoint();

  return (
    <>
      <div className="relative flex flex-col w-full h-[100dvh] bg-white">
        {isMobile ? (
          <MobileHeader />
        ) : (
          <Header
            isOpen={isLeftPanelOpen}
            onToggle={toggleLeftPanel}
            filters={filters}
            minCapRate={minCapRate}
            handleMinCapRateChange={handleMinCapRateChange}
          />
        )}
        <div className="relative flex flex-1 w-full h-full overflow-hidden">
          {/* Left Panel */}
          <div
            className={`
            ${isLeftPanelOpen && isMobile ? 'w-full opacity-100' : ''}
            ${isLeftPanelOpen && !isMobile ? 'w-1/2 opacity-100' : ''}
            ${!isLeftPanelOpen ? 'w-0 opacity-0' : ''}
            flex transition-all duration-300 ease-in-out overflow-hidden`}
          >
            <LeftPanel />
          </div>
          {/* Right Panel */}
          <div
            ref={containerRef}
            className={`${isLeftPanelOpen && isMobile ? 'hidden' : ''}
                        ${isLeftPanelOpen && !isMobile ? 'w-1/2' : ''}
                        ${!isLeftPanelOpen ? 'w-full' : ''}
              flex flex-col h-full`}
          >
            {children}
          </div>
        </div>
        {isMobile && (
          <MapToggleButton isOpen={isLeftPanelOpen} onToggle={toggleLeftPanel} />
        )}
        {!isMobile && (
          <Footer />
        )}
      </div>
    </>
  );
};

export default PropertiesContentLayout;