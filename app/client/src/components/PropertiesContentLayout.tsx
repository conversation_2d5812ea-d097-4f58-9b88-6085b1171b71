import Header from './Header';
import Footer from './Footer';
import LeftPanel from './LeftPanel';
import { usePropertiesFilterState } from '@/hooks/usePropertiesFilter';

interface PropertiesContentLayoutProps {
  isLeftPanelOpen: boolean;
  toggleLeftPanel: () => void;
  containerRef: React.RefObject<HTMLDivElement | null>;
  children: React.ReactNode;
}

const PropertiesContentLayout = ({
  isLeftPanelOpen,
  toggleLeftPanel,
  containerRef,
  children,
}: PropertiesContentLayoutProps) => {
  const { filters, minCapRate, handleMinCapRateChange } = usePropertiesFilterState();

  return (
    <>
      <Header
        isOpen={isLeftPanelOpen}
        onToggle={toggleLeftPanel}
        filters={filters}
        minCapRate={minCapRate}
        handleMinCapRateChange={handleMinCapRateChange}
      />
      <div className="relative flex flex-1 w-full h-full overflow-hidden">
        {/* Left Panel */}
        <div
          className={`${isLeftPanelOpen ? 'w-1/2 opacity-100' : 'w-0 opacity-0'} flex border-r border-[var(--color-light-gray)] transition-all duration-300 ease-in-out overflow-hidden`}
        >
          <LeftPanel />
        </div>
        {/* Right Panel */}
        <div
          ref={containerRef}
          className={`${isLeftPanelOpen ? 'w-1/2' : 'w-full'} flex flex-col h-full`}
        >
          {children}
        </div>
      </div>
      <Footer />
    </>
  );
};

export default PropertiesContentLayout;