import dayjs from 'dayjs';
import { showSFROperatorsFullNameOnly } from './SFRBrandConvertFunction';
import { formatter } from '@/lib/utils/money';
import { MAP_LAYER_NAME_BASE } from '@/constants';
import { FaSearch } from '@react-icons/all-files/fa/FaSearch';
import { FaMinus } from '@react-icons/all-files/fa/FaMinus';
import { searchingModeType } from '@/lib/utils/types';

const dateFormat = 'YYYY-MM-DD';

interface PropertyDetails {
  unitid?: string;
  base_id?: string;
  mlsid?: string;
  uid?: string;
  type?: string;
  Address?: string[];
  address?: string;
  Status?: string;
  Owner?: string;
  Rent?: string;
  Sales?: string;
  Type?: string;
  'Yr. Built'?: string | number;
  Beds?: string | number;
  Baths?: string | number;
  Sqft?: string | number;
  'Exp.'?: string;
  PSF?: string;
  'Avail.'?: string;
  Closed?: string;
  CDOM?: string | number;
  Property?: string;
  Total?: string | number;
  '2BR'?: string | number;
  '2BR Avail.'?: string | number;
  '2BR Rent'?: string;
  '2BR PSF'?: string;
  '3BR'?: string | number;
  '3BR Avail.'?: string | number;
  '3BR Rent'?: string;
  '3BR PSF'?: string;
  Subdivision?: string;
  builder?: string;
  price?: number;
  first_seen?: string;
  status?: string;
}

interface HoverPropertyDetails {
  type: string;
  MLSPopupImageSrc?: string;
  unitid?: string;
  propertystreet1?: string;
  propertycity?: string;
  propertystate?: string;
  propertyzip?: string;
  status?: string;
  owners?: string;
  totalrent?: string | number;
  propertytype?: string;
  yearbuilt?: string | number;
  bdba?: string;
  sqft?: string | number;
  leaseexpirationdate?: string;
  base_id?: string;
  address?: string;
  standard_city?: string;
  standard_state?: string;
  exists?: boolean;
  brand?: string;
  rent?: string | number;
  bed_rooms?: string | number;
  bath_rooms?: string | number;
  square_feet?: string | number;
  available_date?: string;
  close_date?: string;
  mlsid?: string;
  fulladdress?: string;
  city?: string;
  stateorprovince?: string;
  zipcode?: string;
  latestPrice?: string | number;
  propertysubtype?: string;
  bed?: string | number;
  bath?: string | number;
  size?: string | number;
  cdom?: string | number;
  uid?: string;
  property_name?: string;
  total_relevant_units?: string | number;
  two_br_units?: string | number;
  two_br_avail?: string | number;
  two_br_rent?: string | number;
  two_br_rent_sf?: string | number;
  three_br_units?: string | number;
  three_br_avail?: string | number;
  three_br_rent?: string | number;
  three_br_rent_sf?: string | number;
  street_number?: string;
  street_prefix?: string;
  street_name?: string;
  street_suffix?: string;
  state?: string;
  zip_code?: string;
  sales?: string | number;
  year_built?: string | number;
  beds_count?: string | number;
  baths?: string | number;
  total_area_sq_ft?: string | number;
  subdivision?: string;
  builder?: string;
  price?: number;
  first_seen?: string;
  [key: string]: unknown;
}

interface PropertyDetailPopupProps {
  hoverPropertyDetails: HoverPropertyDetails;
}

let propertyType: string;
let MLSPopupImageSrc: string | undefined;
let searchingMode: searchingModeType;

function PropertyDetailPopup({ hoverPropertyDetails }: PropertyDetailPopupProps) {
  const propertyDetails = convertProperties(hoverPropertyDetails);

  let popupContent: React.ReactNode;
  propertyType = hoverPropertyDetails.type;
  MLSPopupImageSrc = hoverPropertyDetails.MLSPopupImageSrc;
  searchingMode = hoverPropertyDetails.searchingMode as searchingModeType;

  switch (propertyType) {
    case MAP_LAYER_NAME_BASE.BTOwned:
      popupContent = createBTOwnedPopup(propertyDetails);
      break;
    case MAP_LAYER_NAME_BASE.hotPads:
    case MAP_LAYER_NAME_BASE.nationalOperator:
      popupContent = createNSFRPopup(propertyDetails);
      break;
    case MAP_LAYER_NAME_BASE.mls:
      popupContent = createMLSPopup(propertyDetails);
      break;
    case MAP_LAYER_NAME_BASE.multiFamily:
      popupContent = createMultiFamilyPopup(propertyDetails);
      break;
    case MAP_LAYER_NAME_BASE.parcel:
      popupContent = createParcelPopup(propertyDetails);
      break;
    case MAP_LAYER_NAME_BASE.newbuilds:
      popupContent = createNewBuildsPopup(propertyDetails);
      break;
  }

  return <div>{popupContent}</div>;
}

export default PropertyDetailPopup;

const createBasePropertyContent = (propertyDetails: PropertyDetails) => {
  return (
    <>
      <div className="flex flex-col">
        <div>
          <p>
            <span className="text-xs font-medium">{propertyDetails.Rent}</span>{' '}
            {searchingMode === 'Lease' && propertyType !== 'parcel'
              ? 'rent'
              : propertyDetails.Status === 'Closed' && propertyType !== 'parcel'
              ? 'sold'
              : propertyType !== 'parcel'
              ? 'sales'
              : 'rent'}{' '}
            -{' '}
            {propertyType != 'parcel' ? (
              propertyDetails.Status
            ) : (
              <>
                <span className="text-xs font-medium">
                  {propertyDetails.Sales}
                </span>{' '}
                sales
              </>
            )}
          </p>
        </div>
        <div>
          <p>
            <span className="text-xs font-medium">
              {propertyDetails.Beds || '-'}
            </span>{' '}
            Bds ·{' '}
            <span className="text-xs font-medium">
              {propertyDetails.Baths || '-'}
            </span>{' '}
            Ba ·{' '}
            <span className="text-xs font-medium">
              {propertyDetails.Sqft || '-'}
            </span>{' '}
            Sqft
          </p>
        </div>
      </div>
      <div className="flex flex-col">
        <div>
          <p className="text-xs font-medium">{propertyDetails.Address?.[0]}</p>
        </div>
        <div>
          <p>{propertyDetails.Address?.[1]}</p>
        </div>
      </div>
    </>
  );
};

const createBTOwnedPopup = (propertyDetails: PropertyDetails) => {
  return (
    <div className="flex flex-col gap-1 p-2.5 text-[11px]">
      {createBasePropertyContent(propertyDetails)}
      <div className="flex flex-col" style={{ lineHeight: '18px' }}>
        <span className="text-xs font-medium">{propertyDetails.Owner}</span>
        <span>Type: {propertyDetails.Type}</span>
        <span>Yr. Built: {propertyDetails['Yr. Built']}</span>
        <span>Expiration: {propertyDetails['Exp.']}</span>
      </div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          gap: '10px',
        }}
      >
        <button
          id="locatePropertyButton"
          data-property-id={propertyDetails.unitid}
          data-property-type={propertyDetails.type}
          className="w-[26px] h-[26px] rounded-full border-none bg-transparent bg-[rgb(76,187,23)] cursor-pointer relative hover:bg-[rgb(76,187,23,0.8)]"
        >
          <FaSearch className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white pointer-events-none" />
        </button>
        {/* <button
          id="removePropertyButton"
          data-property-id={propertyDetails.unitid}
          data-property-type={propertyDetails.type}
          className="w-[26px] h-[26px] rounded-full border-none bg-transparent bg-red-500 cursor-pointer relative hover:bg-red-500/80"
        >
          <FaMinus className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white pointer-events-none" />
        </button> */}
      </div>
    </div>
  );
};

const createNSFRPopup = (propertyDetails: PropertyDetails) => {
  return (
    <div className="flex flex-col gap-1 p-2.5 text-[11px]">
      {createBasePropertyContent(propertyDetails)}
      <div className="flex flex-col" style={{ lineHeight: '18px' }}>
        <span className="text-xs font-medium">{propertyDetails.Owner}</span>
        <span>PSF: {propertyDetails.PSF}</span>
        <span>Available: {propertyDetails['Avail.']}</span>
        <span>Closed: {propertyDetails.Closed}</span>
      </div>
      {/* <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          gap: '10px',
        }}
      >
        <button
          id="locatePropertyButton"
          data-property-id={propertyDetails.base_id}
          data-property-type={propertyDetails.type}
          className="w-[26px] h-[26px] rounded-full border-none bg-transparent bg-[rgb(76,187,23)] cursor-pointer relative hover:bg-[rgb(76,187,23,0.8)]"
        >
          <FaSearch className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white pointer-events-none" />
        </button>
        <button
          id="removePropertyButton"
          data-property-id={propertyDetails.base_id}
          data-property-type={propertyDetails.type}
          className="w-[26px] h-[26px] rounded-full border-none bg-transparent bg-red-500 cursor-pointer relative hover:bg-red-500/80"
        >
          <FaMinus className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white pointer-events-none" />
        </button>
      </div> */}
    </div>
  );
};

const createMLSPopup = (propertyDetails: PropertyDetails) => {
  console.log('propertyDetails', propertyDetails);
  return (
    <>
      <div className="max-w-[240px] flex flex-col">
        {MLSPopupImageSrc && MLSPopupImageSrc != '' && (
          <>
            <div style={{ maxHeight: '150px', overflow: 'hidden' }}>
              <img
                className="w-full h-full object-cover"
                src={MLSPopupImageSrc}
                alt=""
              />
            </div>
            {/* <p
              style={{
                textAlign: 'center',
                margin: 0,
                padding: 0,
                lineHeight: '12px',
                marginTop: '5px',
              }}
            >
              Click to see images
            </p> */}
          </>
        )}
        <div className="flex flex-col gap-1 p-2.5 text-[11px]">
          {createBasePropertyContent(propertyDetails)}
          <div className="flex flex-row justify-around gap-0.5">
            <div className="flex flex-col items-center">
              <span className="text-xs font-medium">
                {propertyDetails['Yr. Built']}
              </span>
              <span>Yr. Built</span>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-xs font-medium">{propertyDetails.CDOM}</span>
              <span>CDOM</span>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-xs font-medium">{propertyDetails.Type}</span>
              <span>Type</span>
            </div>
          </div>
          {/* <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '10px',
            }}
          >
            <button
              id="locatePropertyButton"
              data-property-id={propertyDetails.mlsid}
              data-property-type={propertyDetails.type}
              className="bg-transparent cursor-pointer relative hover:bg-[rgb(76,187,23,0.8)]"
            >
              Show in table
            </button>
            <button
              id="removePropertyButton"
              data-property-id={propertyDetails.mlsid}
              data-property-type={propertyDetails.type}
              className="bg-transparent cursor-pointer relative hover:bg-red-500/80"
            >
              Remove from comps
            </button>
          </div> */}
        </div>
      </div>
    </>
  );
};

const createMultiFamilyPopup = (propertyDetails: PropertyDetails) => {
  return (
    <div className="flex flex-col gap-1 p-2.5 text-[11px]">
      <div className="flex flex-col">
        <p>
          <span className="text-xs font-medium">{propertyDetails.Property}</span>
        </p>
        <p>
          Total{' '}
          <span className="text-xs font-medium">{propertyDetails.Total}</span>
        </p>
      </div>
      <div
        className="flex flex-row justify-center"
        style={{ gap: '5px' }}
      >
        <div className="flex flex-col items-center">
          <span className="text-xs font-medium">
            {propertyDetails['2BR'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>2BR</span>
        </div>
        <div className="flex flex-col justify-center">
          <span className="text-xs font-medium">·</span>
        </div>
        <div className="flex flex-col items-center">
          <span className="text-xs font-medium">
            {propertyDetails['2BR Avail.'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>2BR Avail.</span>
        </div>
        <div className="flex flex-col justify-center">
          <span className="text-xs font-medium">·</span>
        </div>
        <div className="flex flex-col items-center">
          <span className="text-xs font-medium">
            {propertyDetails['2BR Rent'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>2BR Rent</span>
        </div>
        <div className="flex flex-col justify-center">
          <span className="text-xs font-medium">·</span>
        </div>
        <div className="flex flex-col items-center">
          <span className="text-xs font-medium">
            {propertyDetails['2BR PSF'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>2BR PSF</span>
        </div>
      </div>
      <div
        className="flex flex-row justify-center"
        style={{ gap: '5px' }}
      >
        <div className="flex flex-col items-center">
          <span className="text-xs font-medium">
            {propertyDetails['3BR'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>3BR</span>
        </div>
        <div className="flex flex-col justify-center">
          <span className="text-xs font-medium">·</span>
        </div>
        <div className="flex flex-col items-center">
          <span className="text-xs font-medium">
            {propertyDetails['3BR Avail.'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>3BR Avail.</span>
        </div>
        <div className="flex flex-col justify-center">
          <span className="text-xs font-medium">·</span>
        </div>
        <div className="flex flex-col items-center">
          <span className="text-xs font-medium">
            {propertyDetails['3BR Rent'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>3BR Rent</span>
        </div>
        <div className="flex flex-col justify-center">
          <span className="text-xs font-medium">·</span>
        </div>
        <div className="flex flex-col items-center">
          <span className="text-xs font-medium">
            {propertyDetails['3BR PSF'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>3BR PSF</span>
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          gap: '10px',
        }}
      >
        <button
          id="locatePropertyButton"
          data-property-id={propertyDetails.uid}
          data-property-type={propertyDetails.type}
          className="w-[26px] h-[26px] rounded-full border-none bg-transparent bg-[rgb(76,187,23)] cursor-pointer relative hover:bg-[rgb(76,187,23,0.8)]"
        >
          <FaSearch className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white pointer-events-none" />
        </button>
      </div>
    </div>
  );
};

const createParcelPopup = (propertyDetails: PropertyDetails) => {
  return (
    <div className="flex flex-col gap-1 p-2.5 text-[11px]">
      {createBasePropertyContent(propertyDetails)}

      <div className="flex flex-col">
        <span className="text-xs font-medium">
          {propertyDetails['Yr. Built']}
        </span>
        <span>Yr. Built</span>
      </div>
      <div className="flex flex-col">
        <span className="text-xs font-medium">{propertyDetails.Subdivision}</span>
        <span>Subdivision</span>
      </div>
    </div>
  );
};

const createNewBuildsPopup = (propertyDetails: PropertyDetails) => {
  return (
    <div style={{ padding: '10px', fontWeight: '500' }}>
      {propertyDetails.address && (
        <h4 style={{ fontSize: '14px', fontWeight: '600', margin: 0 }}>
          {propertyDetails.address}
        </h4>
      )}
      {propertyDetails.builder && propertyDetails.builder.length > 0 && (
        <p style={{ margin: 0 }}>Builder: {propertyDetails.builder}</p>
      )}
      {propertyDetails.first_seen && (
        <p style={{ margin: 0 }}>First Seen: {propertyDetails.first_seen}</p>
      )}
      <p style={{ margin: 0 }}>
        Status:{' '}
        {propertyDetails.status ? propertyDetails.status : 'Not Specified'}
      </p>
      {propertyDetails.price && propertyDetails.price > 0 && (
        <p style={{ margin: 0 }}>Price: ${formatter(propertyDetails.price)}</p>
      )}
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          gap: '10px',
        }}
      >
        <button
          id="locatePropertyButton"
          data-property-id={propertyDetails.base_id}
          data-property-type={propertyDetails.type}
          className="w-[26px] h-[26px] rounded-full border-none bg-transparent bg-[rgb(76,187,23)] cursor-pointer relative hover:bg-[rgb(76,187,23,0.8)]"
        >
          <FaSearch className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white pointer-events-none" />
        </button>
        <button
          id="removePropertyButton"
          data-property-id={propertyDetails.base_id}
          data-property-type={propertyDetails.type}
          className="w-[26px] h-[26px] rounded-full border-none bg-transparent bg-red-500 cursor-pointer relative hover:bg-red-500/80"
        >
          <FaMinus className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white pointer-events-none" />
        </button>
      </div>
    </div>
  );
};

const convertProperties = (record: HoverPropertyDetails): PropertyDetails => {
  const propertyDetails: PropertyDetails = {};
  switch (record.type) {
    case MAP_LAYER_NAME_BASE.BTOwned:
      propertyDetails.unitid = record.unitid;
      propertyDetails.type = record.type;
      propertyDetails.Address = [
        record.propertystreet1 || '',
        (record.propertycity || '') +
          ', ' +
          (record.propertystate || '') +
          ' ' +
          (record.propertyzip || ''),
      ];
      // propertyDetails.Address2 = record.standard_city + ', ' + record.standard_state;
      propertyDetails.Status = record.status;
      propertyDetails.Owner = record.owners;
      propertyDetails.Rent =
        '$' + formatter(parseFloat(String(record.totalrent || 0)).toFixed());
      propertyDetails['Type'] = record.propertytype;
      propertyDetails['Yr. Built'] = record.yearbuilt;
      propertyDetails.Beds =
        String(record.bdba || '').split('/')[0] === '--' ? '-' : String(record.bdba || '').split('/')[0];
      propertyDetails.Baths =
        String(record.bdba || '').split('/')[1] === '--'
          ? '-'
          : (+String(record.bdba || '').split('/')[1]).toFixed(1);
      propertyDetails.Sqft = record.sqft;
      propertyDetails['Exp.'] = record.leaseexpirationdate
        ? dayjs(record.leaseexpirationdate).format(dateFormat)
        : '-';
      break;
    case MAP_LAYER_NAME_BASE.hotPads:
    case MAP_LAYER_NAME_BASE.nationalOperator:
      propertyDetails.base_id = record.base_id;
      propertyDetails.type = record.type;
      propertyDetails.Address = [
        String(record.address || '').includes(',')
          ? String(record.address || '').split(',')[0]
          : String(record.address || ''),
        (record.standard_city || '') + ', ' + (record.standard_state || ''),
      ];
      // propertyDetails.Address2 = record.standard_city + ', ' + record.standard_state;
      propertyDetails.Status = record.exists ? 'Avail.' : 'Closed';
      // propertyDetails.Owner = showSFROperatorsFullNameOnly(record.brand);
      propertyDetails.Owner = showSFROperatorsFullNameOnly(record.brand || '');
      propertyDetails.Rent = '$' + formatter(parseFloat(String(record.rent || 0)).toFixed());
      propertyDetails.Beds = record.bed_rooms;
      propertyDetails.Baths = record.bath_rooms;
      propertyDetails.Sqft = record.square_feet;
      propertyDetails['PSF'] =
        record.rent && record.square_feet
          ? '$' + (Number(record.rent) / Number(record.square_feet)).toFixed(2)
          : '-';
      propertyDetails['Avail.'] = record.available_date
        ? dayjs(record.available_date).format(dateFormat)
        : '-';
      propertyDetails['Closed'] = record.close_date
        ? dayjs(record.close_date).format(dateFormat)
        : '-';
      break;
    case MAP_LAYER_NAME_BASE.mls:
      propertyDetails.mlsid = record.mlsid;
      propertyDetails.type = record.type;
      propertyDetails.Address = [
        String(record.fulladdress || '').replace(/\s{2}/g, ' '),
        (record.city || '') + ', ' + (record.stateorprovince || '') + ' ' + (record.zipcode || ''),
      ];
      propertyDetails.Status = record.status;
      propertyDetails.Rent =
        '$' + formatter(parseFloat(String(record.latestPrice || 0)).toFixed());
      propertyDetails['Type'] = [
        'Single Family Residence',
        'Single Family Detached',
      ].includes(record.propertysubtype as string)
        ? 'SFR'
        : record.propertysubtype as string;
      propertyDetails['Yr. Built'] = record.yearbuilt;
      propertyDetails.Beds = record.bed;
      propertyDetails.Baths = record.bath;
      propertyDetails.Sqft = record.size ? formatter(Number(record.size)) : '-';
      propertyDetails['CDOM'] = record.cdom;
      break;
    case MAP_LAYER_NAME_BASE.multiFamily:
      propertyDetails.uid = record.uid;
      propertyDetails.type = record.type;
      // propertyDetails['Property'] = [record.property_name, <br />];
      propertyDetails['Property'] = record.property_name;
      propertyDetails['Total'] = record.total_relevant_units;
      propertyDetails['2BR'] = record.two_br_units;
      propertyDetails['2BR Avail.'] = record.two_br_avail;
      propertyDetails['2BR Rent'] = record.two_br_rent
        ? '$' + formatter(Number(record.two_br_rent))
        : '-';
      propertyDetails['2BR PSF'] = record.two_br_rent_sf
        ? '$' + formatter(Number(record.two_br_rent_sf))
        : '-';
      propertyDetails['3BR'] = record.three_br_units;
      propertyDetails['3BR Avail.'] = record.three_br_avail;
      propertyDetails['3BR Rent'] = record.three_br_rent
        ? '$' + formatter(Number(record.three_br_rent))
        : '-';
      propertyDetails['3BR PSF'] = record.three_br_rent_sf
        ? '$' + formatter(Number(record.three_br_rent_sf))
        : '-';
      break;
    case MAP_LAYER_NAME_BASE.parcel:
      propertyDetails.Address = [
        (record.street_number || '') +
          (record.street_prefix ? ' ' + record.street_prefix : '') +
          ' ' +
          (record.street_name || '') +
          (record.street_suffix ? ' ' + record.street_suffix : ''),
        (record.city || '') + ', ' + (record.state || '') + ' ' + (record.zip_code || ''),
      ];
      propertyDetails.Rent = '$' + formatter(parseFloat(String(record.rent || 0)).toFixed());
      propertyDetails.Sales =
        '$' + formatter(parseFloat(String(record.sales || 0)).toFixed());
      propertyDetails['Yr. Built'] = record.year_built;
      propertyDetails.Beds = record.beds_count;
      propertyDetails.Baths = record.baths;
      propertyDetails.Sqft = record.total_area_sq_ft
        ? formatter(+String(record.total_area_sq_ft))
        : '-'; // ensure size is of type number
      propertyDetails.Subdivision = record.subdivision;
      break;
    case MAP_LAYER_NAME_BASE.newbuilds:
      propertyDetails.base_id = record.base_id;
      propertyDetails.type = record.type;
      propertyDetails.address = record.address;
      propertyDetails.builder = record.builder;
      propertyDetails.price = record.price;
      propertyDetails.first_seen = record.first_seen;
      propertyDetails.status = record.status;
      break;
  }
  return propertyDetails;
};
