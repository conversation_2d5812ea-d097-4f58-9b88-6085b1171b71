import React from 'react';

export const showSFROperatorsFullName = (brand: string): React.JSX.Element => {
  brand = brand.replace(/ /g, ''); // remove whitespace
  switch (brand) {
    case 'AH4R':
      return (
        <span className="text-[var(--color-AH4R)]">
          American Homes 4 Rent
        </span>
      );
    case 'Invitatio':
      return (
        <span className="text-[var(--color-Invitatio)]">
          Invitation Homes
        </span>
      );
    case 'HRG':
      return (
        <span className="text-[var(--color-HRG)]">
          HomeRiver Group
        </span>
      );
    case 'PR':
      return (
        <span className="text-[var(--color-PR)]">
          Progress Residential
        </span>
      );
    case 'CPM':
      return (
        <span className="text-[var(--color-CPM)]">
          Conrex Property Management
        </span>
      );
    case 'TR':
      return (
        <span className="text-[var(--color-TR)]">
          Tricon Residential
        </span>
      );
    case 'MYND':
      return (
        <span className="text-[var(--color-MYND)]">MYND</span>
      );
    case 'KP':
      return (
        <span className="text-[var(--color-KP)]">
          Kinloch Partners
        </span>
      );
    case 'RW':
      return (
        <span className="text-[var(--color-RW)]">
          Renters Warehouse
        </span>
      );
    case 'VH':
      return (
        <span className="text-[var(--color-VH)]">
          Vinebrook Homes
        </span>
      );
    case 'Amherst':
      return (
        <span className="text-[var(--color-Amherst)]">
          Amherst
        </span>
      );
    case 'ARG':
      return (
        <span className="text-[var(--color-ARG)]">ARG</span>
      );
    case 'Brandywine':
      return (
        <span className="text-[var(--color-Brandywine)]">
          Brandywine
        </span>
      );
    case 'BridgeHome':
      return (
        <span className="text-[var(--color-BridgeHome)]">
          Bridge Home
        </span>
      );
    case 'Camillo':
      return (
        <span className="text-[var(--color-Camillo)]">
          Camillo
        </span>
      );
    case 'Copperbay':
      return (
        <span className="text-[var(--color-Copperbay)]">
          Copperbay
        </span>
      );
    case 'Divvy':
      return (
        <span className="text-[var(--color-Divvy)]">
          Divvy
        </span>
      );
    case 'FirstKey':
      return (
        <span className="text-[var(--color-FirstKey)]">
          FirstKey
        </span>
      );
    case 'Hudson':
      return (
        <span className="text-[var(--color-Hudson)]">
          Hudson
        </span>
      );
    case 'Imagine':
      return (
        <span className="text-[var(--color-Imagine)]">
          Imagine
        </span>
      );
    case 'KairosLiving':
      return (
        <span className="text-[var(--color-KairosLiving)]">
          Kairos Living
        </span>
      );
    case 'KrchRealty':
      return (
        <span className="text-[var(--color-KrchRealty)]">
          Krch Realty
        </span>
      );
    case 'LiveReszi':
      return (
        <span className="text-[var(--color-LiveReszi)]">
          Live Reszi
        </span>
      );
    case 'OpenHouse':
      return (
        <span className="text-[var(--color-OpenHouse)]">
          Open House
        </span>
      );
    case 'Pathway':
      return (
        <span className="text-[var(--color-Pathway)]">
          Pathway Homes
        </span>
      );
    case 'Peak':
      return (
        <span className="text-[var(--color-Peak)]">Peak</span>
      );
    case 'PPMG':
      return (
        <span className="text-[var(--color-PPMG)]">PPMG</span>
      );
    case 'Propify':
      return (
        <span className="text-[var(--color-Propify)]">
          National Home Rental
        </span>
      );
    case 'RENU':
      return (
        <span className="text-[var(--color-RENU)]">RENU</span>
      );
    case 'ResiHome':
      return (
        <span className="text-[var(--color-ResiHome)]">
          ResiHome
        </span>
      );
    case 'SPA':
      return (
        <span className="text-[var(--color-SPA)]">
          Sparrow
        </span>
      );
    case 'Streetlane':
      return (
        <span className="text-[var(--color-Streetlane)]">
          Streetlane
        </span>
      );
    case 'SYLV':
      return (
        <span className="text-[var(--color-SYLV)]">
          Sylvan
        </span>
      );
    default:
      return <span>{brand}</span>;
  }
};

export const showSFROperatorsFullNameOnly = (brand: string): string => {
  brand = brand.replace(/ /g, ''); // remove whitespace
  switch (brand) {
    case 'AH4R':
      return 'American Homes 4 Rent';
    case 'Invitatio':
      return 'Invitation Homes';
    case 'HRG':
      return 'HomeRiver Group';
    case 'PR':
      return 'Progress Residential';
    case 'CPM':
      return 'Conrex Property Management';
    case 'TR':
      return 'Tricon Residential';
    case 'MYND':
      return 'MYND';
    case 'KP':
      return 'Kinloch Partners';
    case 'RW':
      return 'Renters Warehouse';
    case 'VH':
      return 'Vinebrook Homes';
    case 'Amherst':
      return 'Amherst';
    case 'ARG':
      return 'ARG';
    case 'Brandywine':
      return 'Brandywine';
    case 'BridgeHome':
      return 'Bridge Home';
    case 'Camillo':
      return 'Camillo';
    case 'Copperbay':
      return 'Copperbay';
    case 'Divvy':
      return 'Divvy';
    case 'FirstKey':
      return 'FirstKey';
    case 'Hudson':
      return 'Hudson';
    case 'Imagine':
      return 'Imagine';
    case 'KairosLiving':
      return 'Kairos';
    case 'KrchRealty':
      return 'Krch Realty';
    case 'LiveReszi':
      return 'Live Reszi';
    case 'OpenHouse':
      return 'Open House';
    case 'Pathway':
      return 'Pathway Homes';
    case 'Peak':
      return 'Peak';
    case 'PPMG':
      return 'PPMG';
    case 'Propify':
      return 'National Home Rental';
    case 'RENU':
      return 'RENU';
    case 'ResiHome':
      return 'ResiHome';
    case 'SPA':
      return 'Sparrow';
    case 'Streetlane':
      return 'Streetlane';
    case 'SYLV':
      return 'Sylvan';
    default:
      return brand;
  }
};
