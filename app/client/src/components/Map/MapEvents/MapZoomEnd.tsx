import { useEffect, useRef } from 'react';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';

const zoomLevelToShowPriceMarkers = 12;

function MapZoomEnd(): null {
  const {
    map,
    showPriceMarkers,
    setShowPriceMarkers,
  } = useMarketplaceMapContext();

  const showPriceMarkersRef = useRef(showPriceMarkers);
  showPriceMarkersRef.current = showPriceMarkers;

  useEffect(() => {
    if (!map) return;

    // show/hide price markers when zoom ends at certain zoom level
    const zoomEnd = () => {
      const currentZoomLevel = map.getZoom();

      if (
        currentZoomLevel >= zoomLevelToShowPriceMarkers &&
        !showPriceMarkersRef.current
      ) {
        setShowPriceMarkers(true);
      } else if (
        zoomLevelToShowPriceMarkers > currentZoomLevel &&
        showPriceMarkersRef.current
      ) {
        setShowPriceMarkers(false);
      }
    };

    map.on('zoomend', zoomEnd);
    return () => {
      map.off('zoomend', zoomEnd);
    };
  }, [map, setShowPriceMarkers]);

  return null;
}

export default MapZoomEnd;
