import { useEffect, useRef, useCallback } from 'react';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { getMapBounds, filterPropertiesByBounds } from '@/lib/utils/filterPropertiesByBounds';
import { useSearch } from '@tanstack/react-router';
import { debounce } from 'lodash';

function MapViewportChange(): null {
  const {
    map,
    allProperties,
    allPropertiesSubmitted,
    allPropertiesBookmarked,
    isViewportFilteringEnabled,
    setViewportFilteredProperties,
    setViewportFilteredPropertiesSubmitted,
    setViewportFilteredPropertiesBookmarked,
    setMapBounds,
  } = useMarketplaceMapContext();

  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;

  const isViewportFilteringEnabledRef = useRef(isViewportFilteringEnabled);
  isViewportFilteringEnabledRef.current = isViewportFilteringEnabled;

  // Debounced function to update viewport-filtered properties
  const updateViewportFilteredProperties = useCallback(
    debounce(() => {
      if (!map || !isViewportFilteringEnabledRef.current) {
        // If viewport filtering is disabled, set to null to indicate use regular filtering
        setViewportFilteredProperties(null);
        setViewportFilteredPropertiesSubmitted(null);
        setViewportFilteredPropertiesBookmarked(null);
        setMapBounds(null);
        return;
      }

      const bounds = getMapBounds(map);
      if (!bounds) return;

      // Update map bounds in context
      setMapBounds([[bounds.west, bounds.south], [bounds.east, bounds.north]]);

      // Apply viewport filtering to the already filtered properties
      const viewportFiltered = filterPropertiesByBounds(allProperties, bounds);
      const viewportFilteredSubmitted = filterPropertiesByBounds(allPropertiesSubmitted, bounds);
      const viewportFilteredBookmarked = filterPropertiesByBounds(allPropertiesBookmarked, bounds);

      setViewportFilteredProperties(viewportFiltered);
      setViewportFilteredPropertiesSubmitted(viewportFilteredSubmitted);
      setViewportFilteredPropertiesBookmarked(viewportFilteredBookmarked);
    }, 300), // 300ms debounce to avoid too frequent updates
    [
      map,
      allProperties,
      allPropertiesSubmitted,
      listType,
      setViewportFilteredProperties,
      setViewportFilteredPropertiesSubmitted,
      setViewportFilteredPropertiesBookmarked,
      setMapBounds
    ]
  );

  useEffect(() => {
    if (!map) return;

    // Handle map move events (pan and zoom)
    const handleMoveEnd = () => {
      updateViewportFilteredProperties();
    };

    // Initial update when map loads
    const handleLoad = () => {
      updateViewportFilteredProperties();
    };

    // Attach event listeners
    map.on('moveend', handleMoveEnd);
    map.on('load', handleLoad);

    // If map is already loaded, trigger initial update
    if (map.loaded()) {
      updateViewportFilteredProperties();
    }

    // Cleanup function
    return () => {
      map.off('moveend', handleMoveEnd);
      map.off('load', handleLoad);
      updateViewportFilteredProperties.cancel(); // Cancel any pending debounced calls
    };
  }, [map, updateViewportFilteredProperties]);

  // Update when properties or filters change
  useEffect(() => {
    updateViewportFilteredProperties();
  }, [
    allProperties,
    allPropertiesSubmitted,
    allPropertiesBookmarked,
    listType,
    isViewportFilteringEnabled
  ]);

  return null;
}

export default MapViewportChange; 