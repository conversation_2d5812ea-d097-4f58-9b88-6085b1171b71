import { useEffect, useRef, useCallback } from 'react';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { usePropertiesFilterState } from '@/hooks/usePropertiesFilter';
import { getMapBounds, filterPropertiesByBounds } from '@/lib/utils/filterPropertiesByBounds';
import { filterProperties } from '@/lib/utils/filterProperties';
import { useSearch } from '@tanstack/react-router';
import { debounce } from 'lodash';

function MapViewportChange(): null {
  const {
    map,
    allProperties,
    allPropertiesSubmitted,
    isViewportFilteringEnabled,
    setViewportFilteredProperties,
    setViewportFilteredPropertiesSubmitted,
    setMapBounds,
  } = useMarketplaceMapContext();

  const { selectedMarket, selectedPrice, selectedBeds, selectedBaths, minCapRate } = usePropertiesFilterState();
  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;

  const isViewportFilteringEnabledRef = useRef(isViewportFilteringEnabled);
  isViewportFilteringEnabledRef.current = isViewportFilteringEnabled;

  // Debounced function to update viewport-filtered properties
  const updateViewportFilteredProperties = useCallback(
    debounce(() => {
      if (!map || !isViewportFilteringEnabledRef.current) {
        // If viewport filtering is disabled, set to null to indicate use regular filtering
        setViewportFilteredProperties(null);
        setViewportFilteredPropertiesSubmitted(null);
        setMapBounds(null);
        return;
      }

      const bounds = getMapBounds(map);
      if (!bounds) return;

      // Update map bounds in context
      setMapBounds([[bounds.west, bounds.south], [bounds.east, bounds.north]]);

      // First apply regular filters, then apply viewport filtering
      const regularlyFilteredProperties = filterProperties({
        properties: listType === 'listings' ? allProperties : null,
        selectedMarket,
        selectedPrice,
        selectedBeds,
        selectedBaths,
        minCapRate
      });

      const regularlyFilteredPropertiesSubmitted = filterProperties({
        properties: listType === 'submitted' ? allPropertiesSubmitted : null,
        selectedMarket,
        selectedPrice,
        selectedBeds,
        selectedBaths,
        minCapRate
      });

      // Apply viewport filtering to the already filtered properties
      const viewportFiltered = filterPropertiesByBounds(regularlyFilteredProperties, bounds);
      const viewportFilteredSubmitted = filterPropertiesByBounds(regularlyFilteredPropertiesSubmitted, bounds);

      setViewportFilteredProperties(viewportFiltered);
      setViewportFilteredPropertiesSubmitted(viewportFilteredSubmitted);
    }, 300), // 300ms debounce to avoid too frequent updates
    [
      map,
      allProperties,
      allPropertiesSubmitted,
      selectedMarket,
      selectedPrice,
      selectedBeds,
      selectedBaths,
      minCapRate,
      listType,
      setViewportFilteredProperties,
      setViewportFilteredPropertiesSubmitted,
      setMapBounds
    ]
  );

  useEffect(() => {
    if (!map) return;

    // Handle map move events (pan and zoom)
    const handleMoveEnd = () => {
      updateViewportFilteredProperties();
    };

    // Initial update when map loads
    const handleLoad = () => {
      updateViewportFilteredProperties();
    };

    // Attach event listeners
    map.on('moveend', handleMoveEnd);
    map.on('load', handleLoad);

    // If map is already loaded, trigger initial update
    if (map.loaded()) {
      updateViewportFilteredProperties();
    }

    // Cleanup function
    return () => {
      map.off('moveend', handleMoveEnd);
      map.off('load', handleLoad);
      updateViewportFilteredProperties.cancel(); // Cancel any pending debounced calls
    };
  }, [map, updateViewportFilteredProperties]);

  // Update when properties or filters change
  useEffect(() => {
    updateViewportFilteredProperties();
  }, [
    allProperties,
    allPropertiesSubmitted,
    selectedMarket,
    selectedPrice,
    selectedBeds,
    selectedBaths,
    minCapRate,
    listType,
    isViewportFilteringEnabled
  ]);

  return null;
}

export default MapViewportChange; 