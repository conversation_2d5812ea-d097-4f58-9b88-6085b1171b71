import { Check } from "lucide-react";
import { mapMenuConfig } from "../../constants/mapSidebarConfig";
import { useMapLayerSelector } from "@/hooks/useMapLayerSelector";

const MapSideBar = () => {
  const {
    activeSubmenu,
    selectedItems,
    handleClick,
    handleSubItemClick,
    isItemSelected
  } = useMapLayerSelector();

  return (
    <div className="flex absolute top-0 z-10">
      {/* Main sidebar container with fixed width */}
      <div className="flex flex-col justify-center items-center rounded-br-[12px] border-r border-b border-medium-gray-20 py-4 px-3 bg-white space-y-4 w-16">
        {mapMenuConfig.map((item) => (
          <div
            key={item.label}
            className="relative w-full flex flex-col items-center"
            onClick={() => handleClick(item.label)}
          >
            <div className="flex flex-col items-center space-y-1 cursor-pointer h-[52px]">
              <div className="flex justify-center items-center h-7 w-7">{item.icon}</div>
              <p className="text-center text-xs text-dark-gray">{item.label}</p>

              {/* Badge positioned absolutely to avoid affecting layout */}
              <div className="absolute top-0 right-1">
                {selectedItems[item.label]?.length > 0 && (
                  <div className="bg-red rounded-full w-3 h-3"></div>
                )}
              </div>
            </div>

            {/* Submenu with fixed position from sidebar right border */}
            <div
              className={`absolute left-full -top-4 ml-2 bg-white border border-medium-gray-20 rounded shadow-lg transition-opacity duration-200 z-50 ${activeSubmenu === item.label
                ? "opacity-100 pointer-events-auto"
                : "opacity-0 pointer-events-none"
                }`}
              onClick={(e) => e.stopPropagation()} // Prevent submenu clicks from closing the menu
            >
              <ul className="py-2 min-w-[210px]">
                {item.submenu.map((sub) => (
                  <li
                    key={sub.label}
                    className={`px-4 py-1 text-sm hover:bg-light-gray cursor-pointer whitespace-nowrap flex items-center justify-between `}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSubItemClick(item.label, sub.label, sub.key);
                    }}
                  >
                    <div className="flex items-center">
                      <span className="mr-2">{sub.icon}</span>
                      <span className="text-dark-gray">{sub.label}</span>
                    </div>
                    {isItemSelected(item.label, sub.label) && (
                      <span className="text-dark-gray ml-2">
                        <Check className="w-4 h-4" />
                      </span>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MapSideBar;
