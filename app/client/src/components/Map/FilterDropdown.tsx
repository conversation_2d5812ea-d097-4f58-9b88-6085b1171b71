import React, { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import { ChevronDown, Check } from "lucide-react";
import { FilterValue, Option } from "../../types/PropertiesFilterTypes";
import { isEqual } from "lodash";

interface FilterDropdownProps {
  label: string;
  name: string;
  options: Option[];
  value: number | number[] | string[];
  onChange: ({
    changedField,
    changedValue,
  }: {
    changedField: string;
    changedValue: string | number | string[];
  }) => void;
  openDropdown?: string | null;
  setOpenDropdown?: Dispatch<SetStateAction<string | null>>;
  multiSelect?: boolean;
}

const blue = "bg-blue-20 text-dark-gray font-bold border border-transparent hover:bg-blue-20/80";

const FilterDropdown: React.FC<FilterDropdownProps> = ({
  label,
  name,
  options,
  value,
  onChange,
  openDropdown,
  setOpenDropdown,
  multiSelect = false
}) => {
  // console.log('FilterDropdown - name', name);
  // console.log('FilterDropdown - value', value);
  const [inputValue, setInputValue] = useState<number | number[] | string[] | null>(null);

  useEffect(() => {
    setInputValue(value);
    // console.log('FilterDropdown - value updated from props', value);
  }, [value]);

  const isOpen = openDropdown === label;
  const dropdownRef = useRef<HTMLDivElement>(null);

  // console.log('name', name);
  // console.log('value', value);

  // Helper function to check if an option is selected
  const isOptionSelected = (optionValue: string | number, inputValue: number | number[] | string[] | null): boolean => {
    if (name === 'price') {
      console.log('inputValue', inputValue);
      console.log('optionValue', optionValue);
      // console.log('isOptionSelected', inputValue === optionValue || (Array.isArray(inputValue) && isEqual(inputValue, optionValue)));
    }
    if (multiSelect && Array.isArray(inputValue)) {
      // for multi select, check if the optionValue is in the inputValue array
      if (name === 'selectedMarkets') {
        return inputValue.includes(optionValue);
      }
      if (name === 'price') {
        console.log('isAnyOptionSelected - mulltiple select- price should NOT be called here');
      }
    } else {
      // for single select
      if (inputValue && Array.isArray(inputValue) && optionValue && Array.isArray(optionValue)) {
        if (name === 'price') {
          console.log('isAnyOptionSelected - mulltiple select- price should be called here');
        }
        // if inputValue is an array, sort the inputValue and optionValue arrays for comparison
        const inputValueClone = [...inputValue];
        const optionValueClone = [...optionValue];
        const inputValueSorted = inputValueClone.sort();
        const optionValueSorted = optionValueClone.sort();
        if (name === 'price') {
          console.log('isAnyOptionSelected - single select- price- result', (Array.isArray(inputValueSorted) && isEqual(inputValueSorted, optionValueSorted)));
        }
        return (Array.isArray(inputValueSorted) && isEqual(inputValueSorted, optionValueSorted)); // array is for price range
      } else {
        if (name === 'price') {
          console.log('isAnyOptionSelected - single select non array - price should NOT be called here');
        }
        return inputValue === optionValue;
      }
    } 
  };

  const isAnyOptionSelected = (options: Option[]): boolean => {
    return options.some((option) => isOptionSelected(option.value, inputValue));
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) && isOpen) {
        if (setOpenDropdown) {
          setOpenDropdown(null);
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, setOpenDropdown]);

  return (
    <div className="relative inline-block text-left" ref={dropdownRef}>
      <div
        className={`relative flex items-center gap-1 px-2 py-1 rounded text-sm cursor-pointer ${blue}`}
        onClick={() => {
          if (setOpenDropdown) {
            setOpenDropdown(isOpen ? null : label);
          }
        }}
      >
        {label}
        <ChevronDown size={18} />
        {/* don't show red dot if price range is no min to no max */}
        {isAnyOptionSelected(options) && !(name === 'price' && inputValue?.[0] === 0 && inputValue?.[1] === 9999999) && (
          <span className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-red" />
        )}
      </div>

      {isOpen && (
        <ul className="absolute z-300 min-w-[160px] h-fit max-h-[85vh] bg-white border text-sm overflow-auto">
          {options.map((option) => (
            <li
              key={option.value}
              className="flex items-center px-3 py-2 cursor-pointer hover:bg-gray-100 w-full"
              onClick={(e) => {
                e.stopPropagation();

                if (['price', 'minBeds', 'minBaths'].includes(name)) {
                  if (isOptionSelected(option.value, inputValue)) {
                    setInputValue(null);
                  } else {
                    setInputValue(option.value as number | number[]);
                  }
                } else if (['selectedMarkets'].includes(name)) {
                  console.log('onChange - inputValue', inputValue);
                  if (isOptionSelected(option.value, inputValue) && inputValue && Array.isArray(inputValue)) {
                    setInputValue(inputValue.filter((v) => v !== option.value));
                  } else {
                    if (inputValue && Array.isArray(inputValue)) {
                      setInputValue([...inputValue, option.value]);
                    } else {
                      setInputValue([option.value as string]);
                    }
                  }
                }

                if (multiSelect) {

                  if (Array.isArray(value)) {
                    const optionValueStr = option.value.toString();
                    if (value.includes(optionValueStr)) {
                      onChange({
                        changedField: name,
                        changedValue: value.filter(v => v !== optionValueStr),
                      });
                    } else {
                      onChange({
                        changedField: name,
                        changedValue: [...value, optionValueStr],
                      });
                    }
                  } else {
                    onChange([option.value.toString()]);
                  }

                  if (label === "Market" && setOpenDropdown) {
                    setOpenDropdown(null);
                  }
                } else {
                  // Original single-select logic
                  if (isOptionSelected(option.value, inputValue)) {
                    onChange({
                      changedField: name,
                      changedValue: "",
                    }); // Unselect
                  } else {
                    onChange({
                      changedField: name,
                      changedValue: option.value,
                    }); // Select
                  }
                  if (setOpenDropdown) {
                    setOpenDropdown(null); // Close after selection
                  }
                }
              }}
            >
              <span className="flex-1 whitespace-nowrap">{option.label}</span>
              {isOptionSelected(option.value, inputValue) && (
                <Check size={16} className="text-dark-gray ml-auto" />
              )}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default FilterDropdown;