import React, { Dispatch, SetStateAction } from "react";
import { ChevronDown, Check } from "lucide-react";
import { FilterValue, Option } from "../../types/PropertiesFilterTypes";

interface FilterDropdownProps {
  label: string;
  options: Option[];
  value: FilterValue;
  onChange: Dispatch<SetStateAction<FilterValue>>;
  openDropdown?: string | null;
  setOpenDropdown?: Dispatch<SetStateAction<string | null>>;
}


const blue = "bg-blue-20 text-dark-gray font-bold border border-transparent hover:bg-blue-20/80";


const FilterDropdown: React.FC<FilterDropdownProps> = ({
  label,
  options,
  value,
  onChange,
  openDropdown,
  setOpenDropdown
}) => {

  const isOpen = openDropdown === label;

  return (
    <div className="relative inline-block text-left">
      <div
        className={`relative flex items-center gap-1 px-2 py-1 rounded text-sm cursor-pointer ${blue}`}
        onClick={() => {
          if (setOpenDropdown) {
            setOpenDropdown(isOpen ? null : label);
          }
        }}
      >
        {label}
        <ChevronDown size={18} />
        {value !== "" && (
          <span className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-red" />
        )}
      </div>

      {isOpen && (
        <ul className="absolute z-300 min-w-[160px] bg-white border text-sm">
          {options.map((option) => (
            <li
              key={option.value}
              className="flex items-center px-3 py-2 cursor-pointer hover:bg-gray-100 w-full"
              onClick={() => {
                if (value === option.value) {
                  onChange(""); // Unselect
                } else {
                  onChange(option.value as FilterValue); // Select
                }
                if (setOpenDropdown) {
                  setOpenDropdown(null); // Close after selection
                }
              }}
            >
              <span className="flex-1 whitespace-nowrap">{option.label}</span>
              {value === option.value && (
                <Check size={16} className="text-gray-500 ml-auto" />
              )}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default FilterDropdown;
