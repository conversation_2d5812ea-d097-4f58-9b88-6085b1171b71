import { useEffect, useRef } from 'react';
import {
  Map,
  useMap,
  ChainStoresLayerBoundary,
  ChainStoresLayerRadius,
  ChainStoresProvider,
} from '@spatiallaser/map';
import {
  initPopup,
  getMapInitializaitonPropertiesByUserGroup,
} from './MapUtility/general';
import { MAPBOX_TOKEN } from '@/constants';
import './mapboxStyle.css';
import SinglePortfolioLayer from './MapLayers/SinglePortfolioLayer';
import NationalOperatorLayer from './MapLayers/NationalOperatorLayer.tsx';
import HotPadsLayer from './MapLayers/HotPadsLayer.tsx';
import MLSLayer from './MapLayers/MLSLayer.tsx';
import MapZoomEnd from './MapEvents/MapZoomEnd';
import MapViewportChange from './MapEvents/MapViewportChange';
import SelectRadius from './MapControls/SelectRadius/SelectRadius';
import { isEmpty } from 'lodash';
import BuyerListingsLayer from './MapLayers/BuyerListingsLayer';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import RealtorDotComLayer from './MapLayers/RealtorDotComLayer.tsx';

const DEFAULT_ZOOM = 10.39787299330436;

export let propertyDetailPopup: any;
export let locatePropertyHandler: any;

function MapCMA() {
  const { setMap: setProviderMap } = useMap();
  const {
    map,
    setMap,
    selectedBuyersViewRecord,
    propertyModalTabKey,
    currentMapThemeOption,
    setCurrentMapThemeOption,
    setMapLocateProperty,
    selectedUserGroup,
    propertyModalOpened,
    mapExpandedView,
    eventCoordinates,
    mapPropertiesDoneFetching,
    setCurrentRadiusMile,
    currentMapLayerOptions,
    sadChainStoreDistance,
    searchingMode,
  } = useMarketplaceMapContext();

  const mapContainer = useRef<HTMLDivElement>(null);

  const currentMapThemeOptionRef = useRef(currentMapThemeOption);
  currentMapThemeOptionRef.current = currentMapThemeOption;

  useEffect(() => {
    if (!map) return;

    propertyDetailPopup = initPopup();

    map.on('map.themeOption', (e: any) => {
      const theme = e.payload.currentMapThemeOption;
      setCurrentMapThemeOption(theme);
    });
    // ONCE on mount set radius
    map.once('selectRadius.radius', (e: any) => {
      const radius = e.payload.currentRadiusMile;
      console.log("radius: ", radius);
      setCurrentRadiusMile(radius);
    });

    map.once('load', () => {
      if (map) {
        map.fire('cma.leaseMode', {
          payload: { leaseMode: searchingMode === 'Lease' },
        });
      }
    });

    locatePropertyHandler = (type: string, id: string) => {
      // console.log('locatePropertyHandler: ', { type: type, id: id });
      setMapLocateProperty({ type: type, id: id });
    };
  }, [map]);

  useEffect(() => {
    if (!map) return;
    map.fire('cma.leaseMode', {
      payload: { leaseMode: searchingMode === 'Lease' },
    });
  }, [searchingMode]);

  useEffect(() => {
    if (!map) return;
    map.resize();
  }, [mapExpandedView]);

  useEffect(() => {
    if (!map) return;

    const mapDrawControl =
      mapContainer.current?.querySelector('#mapDrawControl');
    if (!mapDrawControl) return;

    if (propertyModalOpened) {
      (mapDrawControl as HTMLElement).style.display = 'unset';
    } else {
      (mapDrawControl as HTMLElement).style.display = 'none';
    }
  }, [propertyModalOpened]);

  useEffect(() => {
    if (!map) return;

    const mapDrawControl =
      mapContainer.current?.querySelector('#mapDrawControl');
    if (!mapDrawControl) return;

    if (mapPropertiesDoneFetching) {
      mapDrawControl.querySelectorAll('button').forEach((button) => {
        (button as HTMLButtonElement).disabled = false;
        (button as HTMLButtonElement).style.opacity = '1';
      });
    } else {
      mapDrawControl.querySelectorAll('button').forEach((button) => {
        (button as HTMLButtonElement).disabled = true;
        (button as HTMLButtonElement).style.opacity = '0.5';
      });
    }
  }, [mapPropertiesDoneFetching]);


  // only show radius selector bar when CMA tab is active
  useEffect(() => {
    if (!map) return;

    // show radius selector bar if comps tab is active
    // hide radius selector bar for all other tabs
    const selectRadiusDiv = document.querySelector('#radiusSelectWrapper');
    if (propertyModalTabKey === 'comps') {
      if (selectRadiusDiv) {
        (selectRadiusDiv as HTMLElement).style.display = 'flex';
      }
    } else {
      if (selectRadiusDiv) {
        (selectRadiusDiv as HTMLElement).style.display = 'none';
      }
    }
  }, [propertyModalTabKey, map]);

  // TODO: Implement getBuyerListingsGeoJSON function
  // This was previously handled by Redux state
  // const getBuyerListingsGeoJSON = () => {
  //   // switch (buyerViewActiveTabKey) {
  //   //   case 'new-listings':
  //   //     return buyerNewListingsGeoJSON;
  //   //   case 'batch-listings':
  //   //     return buyerBatchListingsGeoJSON;
  //   //   case 'expired-listings':
  //   //     return buyerExpiredListingsGeoJSON;
  //   //   case 'past-listings':
  //   //     return buyerPastListingsGeoJSON;
  //   //   default:
  //   //     return geojsonTemplate;
  //   // }
  //   return generateBuyerListingsGeoJSON({
  //     dataSource: allProperties,
  //     dataType: 'offmarket',
  //   });
  // };

  return (
    <div ref={mapContainer} className="relative h-full w-full transition-[width] duration-250 ease-in-out rounded-lg">
      <Map
        getMap={(m: any) => {
          setProviderMap(m);
          setMap(m);
        }}
        token={MAPBOX_TOKEN}
        initProperties={{
          zoom: DEFAULT_ZOOM,
          ...getMapInitializaitonPropertiesByUserGroup(selectedUserGroup || ''),
          // theme: selectedUserGroup?.includes('Nhimble')
          //   ? 'Satellite'
          //   : 'Automatic',
          theme: 'Street',
        }}
        configure={{
          marketplaceOnly: true,
          showParcelBoundaryLayer: true,
          sentinelHub: false,
          mapExpander: {
            enabled: false,
            init: {
              mapExpandedView: false,
            },
          },
          mapToImageDownload: true,
          mapDraw: false,
          mapRuler: true,
          mapNavigation: { enabled: false },
          mapControlsMapLayers: false,
          selectRadius: {
            enabled: true,
            init: {
              showClearButton: true,
              defaultRadius: 2,
            },
          },
          streetview: {
            enabled: true,
          },
        }}
        // serverType={serverType}
        serverType='prod'
        user={{
          userGroup: ['Lennar'],
        }}
      >
        {/* Chain Store */}
        <ChainStoresProvider>
          {propertyModalTabKey === 'demographics' && (
            <>
              {/* <ChainStoresMenu
                onClose={() => {
                  if (!map) return;

                  const payload = {
                    currentMapLayerOptions: currentMapLayerOptions.filter(
                      (l) => l !== 'chain stores',
                    ),
                  };
                  map.fire('mapLayers.currentMapLayerOptions', {
                    payload: payload,
                  });

                  setCurrentMapLayerOptions(payload.currentMapLayerOptions);
                }}
              /> */}
              <ChainStoresLayerBoundary map={map} />
              <ChainStoresLayerRadius
                map={map}
                lat={eventCoordinates[1] || null}
                lng={eventCoordinates[0] || null}
                radius={sadChainStoreDistance * 1609.34}
                viewLayers={
                  currentMapLayerOptions.includes('chain stores')
                    ? false
                    : {
                      unFavorable: false,
                    }
                }
              />
            </>
          )}
        </ChainStoresProvider>
      </Map>
      <SinglePortfolioLayer />
      <BuyerListingsLayer />
      {
        !isEmpty(selectedBuyersViewRecord) &&
        propertyModalTabKey === 'comps' &&
        <>
          <MLSLayer />
          <NationalOperatorLayer />
          <HotPadsLayer />
          <RealtorDotComLayer />
          {/* <NewBuildsLayer /> */}
        </>
      }
      <MapZoomEnd />
      <MapViewportChange />
      <SelectRadius />
    </div>
  );
}

export default MapCMA;
