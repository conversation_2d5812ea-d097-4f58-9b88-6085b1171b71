import React from 'react';
import { GeoJSONFeature, Map, MapMouseEvent } from 'mapbox-gl';

export const PopupHoverHandler = (props: {
  map?: Map;
  layers: string[];
  delay?: number;
  afterMouseEnter?: (feature: GeoJSONFeature[], e: MapMouseEvent) => void;
  afterMouseLeave?: () => void;
  children?: React.ReactNode;
}) => {
  const { map, delay = 500 } = props;
  const mouseOnPoint = React.useRef<boolean>(false);
  const mouseOnPopup = React.useRef<boolean>(false);

  React.useEffect(() => {
    if (!map) return;

    const size = 10;
    // prettier-ignore
    const onMouseEnter = (e: MapMouseEvent) => {
      const features = map.queryRenderedFeatures(
        [[e.point.x - size / 2, e.point.y - size / 2], [e.point.x + size / 2, e.point.y + size / 2]],
        { layers: props.layers }
      );
      props?.afterMouseEnter?.(features, e)
    };

    // prettier-ignore
    const onMouseMove = (e: MapMouseEvent) => {
      const features = map.queryRenderedFeatures(
        [[e.point.x - size / 2, e.point.y - size / 2], [e.point.x + size / 2, e.point.y + size / 2]],
        { layers: props.layers }
      );

      if (features.length > 0) {
        mouseOnPoint.current = true;
      } else {
        mouseOnPoint.current = false;
      }
    };

    const onMouseLeave = () => {
      setTimeout(() => {
        if (mouseOnPoint.current || mouseOnPopup.current) return;
        props?.afterMouseLeave?.();
      }, delay);
    };

    map.on('mouseenter', props.layers, onMouseEnter);
    map.on('mousemove', onMouseMove);
    map.on('mouseleave', props.layers, onMouseLeave);
    return () => {
      map.off('mouseenter', props.layers, onMouseEnter);
      map.off('mousemove', onMouseMove);
      map.off('mouseleave', props.layers, onMouseLeave);
    };
  }, [map, props.layers, delay]);

  if (!map) return;
  return (
    <div
      onMouseMove={() => {
        if (mouseOnPopup.current) return;
        mouseOnPopup.current = true;
      }}
      onMouseLeave={() => {
        mouseOnPopup.current = false;
        setTimeout(() => {
          if (mouseOnPoint.current || mouseOnPopup.current) return;
          props?.afterMouseLeave?.();
        }, delay);
      }}
    >
      {props.children}
    </div>
  );
};
