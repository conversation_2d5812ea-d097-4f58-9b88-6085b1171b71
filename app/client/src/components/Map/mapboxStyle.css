@import '../globalVars.css';

:root {
  --color-MLS: #0d1738;
  --color-MultiFamily: #14828c;
  --color-Invitatio: #206f06;
  --color-AH4R: #af2730;
  --color-HRG: #a41618;
  --color-PR: #045d67;
  --color-CPM: #3a4737;
  --color-TR: #00b2e2;
  --color-MYND: #485c6c;
  --color-KP: #063f41;
  --color-RW: #ee2375;
  --color-VH: #008265;
  --color-Amherst: #4375c7;
  --color-HotPads: #ed6041;
  --color-PadSplit: #16457e;
  /* --color-HotPads-hover: #007A70;  */
  --color-ARG: #495e8e;
  --color-Brandywine: #1c3565;
  --color-BridgeHome: #28d500;
  --color-Camillo: #c51a2e;
  --color-Copperbay: #ba7350;
  --color-Divvy: #1b202a;
  --color-FirstKey: #5a2a96;
  --color-Hudson: #20253c;
  --color-Imagine: #26b9e6;
  --color-KairosLiving: #364e9e;
  --color-KrchRealty: #76b939;
  --color-LiveReszi: #404b54;
  --color-OpenHouse: #a0d1ee;
  --color-Pathway: #289ddd;
  --color-Peak: #1c9b45;
  --color-PPMG: #72a2bb;
  --color-Propify: #2a4f77;
  --color-RENU: #113377;
  --color-ResiHome: #fa1858;
  --color-SPA: #2266ea;
  --color-Streetlane: #2394d2;
  --color-SYLV: #fb6410;
  --color-NewBuild: #28d500;
}

.listingMarker {
  font-size: 11px;
  font-weight: 600;
  /* color: #000; */
  background: #fff;
  padding: 0px 10px;
  border-radius: 999px;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

.listingMarker:hover {
  color: #fff;
  /* background: #34C759; */
  /* background: #1a9641; */
}

.listingMarkerSelected {
  font-size: 14px;
  font-weight: 600;
  color: #fff;
  /* background: #1a9641; */
  padding: 4px 12px;
  border-radius: 999px;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
  z-index: 100;
}

.marker_MLS {
  color: var(--color-MLS);
  border: 1px solid var(--color-MLS);
  z-index: 12;
}

.marker_MLS:hover {
  background-color: var(--color-MLS);
  cursor: pointer;
}
.marker_PR {
  color: #39a39c;
  border: 1px solid #39a39c;
  z-index: 12;
}

.marker_PR:hover {
  background-color: #39a39c;
  cursor: pointer;
}

.marker_AH {
  color: #1777FF;
  border: 1px solid #1777FF;
  z-index: 12;
}

.marker_AH:hover {
  background-color: #1777FF;
  cursor: pointer;
}
.marker_LC {
  color: #40c5c7;
  border: 1px solid #40c5c7;
  z-index: 12;
}

.marker_LC:hover {
  background-color: #40c5c7;
  cursor: pointer;
}

.marker_LMF {
  color: #41a3e8;
  border: 1px solid #41a3e8;
  z-index: 12;
}

.marker_LMF:hover {
  background-color: #41a3e8;
  cursor: pointer;
}

.marker_LSF {
  color: #000;
  border: 1px solid #a1f542;
  z-index: 12;
}

.marker_LSF:hover {
  background-color: #a1f542;
  cursor: pointer;
}




.markerSelected_MLS {
  background-color: var(--color-MLS);
}

.marker_HotPads {
  color: var(--color-HotPads);
  border: 1px solid var(--color-HotPads);
  z-index: 11;
}

.marker_HotPads:hover {
  background-color: var(--color-HotPads);
}

.marker_PadSplit {
  color: var(--color-PadSplit);
  border: 1px solid var(--color-PadSplit);
  z-index: 11;
}

.marker_PadSplit:hover {
  background-color: var(--color-PadSplit);
}

.markerSelected_HotPads {
  background-color: var(--color-HotPads);
}

.markerSelected_PadSplit {
  background-color: var(--color-PadSplit);
}

.marker_nationalOperators_AH4R {
  color: var(--color-AH4R);
  border: 1px solid var(--color-AH4R);
  z-index: 11;
}

.marker_nationalOperators_AH4R:hover {
  background-color: var(--color-AH4R);
}

.markerSelected_AH4R {
  background-color: var(--color-AH4R);
}

.marker_nationalOperators_Invitatio {
  color: var(--color-Invitatio);
  border: 1px solid var(--color-Invitatio);
  z-index: 11;
}

.marker_nationalOperators_Invitatio:hover {
  background-color: var(--color-Invitatio);
}

.markerSelected_Invitatio {
  background-color: var(--color-Invitatio);
}

.marker_nationalOperators_HRG {
  color: var(--color-HRG);
  border: 1px solid var(--color-HRG);
  z-index: 11;
}

.marker_nationalOperators_HRG:hover {
  background-color: var(--color-HRG);
}

.markerSelected_HRG {
  background-color: var(--color-HRG);
}

.marker_nationalOperators_PR {
  color: var(--color-PR);
  border: 1px solid var(--color-PR);
  z-index: 11;
}

.marker_nationalOperators_PR:hover {
  background-color: var(--color-PR);
}

.markerSelected_PR {
  background-color: var(--color-PR);
}

.marker_nationalOperators_CPM {
  color: var(--color-CPM);
  border: 1px solid var(--color-CPM);
  z-index: 11;
}

.marker_nationalOperators_CPM:hover {
  background-color: var(--color-CPM);
}

.markerSelected_CPM {
  background-color: var(--color-CPM);
}

.marker_nationalOperators_TR {
  color: var(--color-TR);
  border: 1px solid var(--color-TR);
  z-index: 11;
}

.marker_nationalOperators_TR:hover {
  background-color: var(--color-TR);
}

.markerSelected_TR {
  background-color: var(--color-TR);
}

.marker_nationalOperators_MYND {
  color: var(--color-MYND);
  border: 1px solid var(--color-MYND);
  z-index: 11;
}

.marker_nationalOperators_MYND:hover {
  background-color: var(--color-MYND);
}

.markerSelected_MYND {
  background-color: var(--color-MYND);
}

.marker_nationalOperators_KP {
  color: var(--color-KP);
  border: 1px solid var(--color-KP);
  z-index: 11;
}

.marker_nationalOperators_KP:hover {
  background-color: var(--color-KP);
}

.markerSelected_KP {
  background-color: var(--color-KP);
}

.marker_nationalOperators_RW {
  color: var(--color-RW);
  border: 1px solid var(--color-RW);
  z-index: 11;
}

.marker_nationalOperators_RW:hover {
  background-color: var(--color-RW);
}

.markerSelected_RW {
  background-color: var(--color-RW);
}

.marker_nationalOperators_VH:hover {
  background-color: var(--color-VH);
}

.markerSelected_VH {
  background-color: var(--color-VH);
}

.marker_nationalOperators_Amherst:hover {
  background-color: var(--color-Amherst);
}

.markerSelected_Amherst {
  background-color: var(--color-Amherst);
}
/*  */
.marker_nationalOperators_ARG {
  color: var(--color-ARG);
  border: 1px solid var(--color-ARG);
  z-index: 11;
}
.marker_nationalOperators_ARG:hover {
  background-color: var(--color-ARG);
}
.markerSelected_ARG {
  background-color: var(--color-ARG);
}

.marker_nationalOperators_Brandywine {
  color: var(--color-Brandywine);
  border: 1px solid var(--color-Brandywine);
  z-index: 11;
}
.marker_nationalOperators_Brandywine:hover {
  background-color: var(--color-Brandywine);
}
.markerSelected_Brandywine {
  background-color: var(--color-Brandywine);
}

.marker_nationalOperators_BridgeHome {
  color: var(--color-BridgeHome);
  border: 1px solid var(--color-BridgeHome);
  z-index: 11;
}
.marker_nationalOperators_BridgeHome:hover {
  background-color: var(--color-BridgeHome);
}
.markerSelected_BridgeHome {
  background-color: var(--color-BridgeHome);
}

.marker_nationalOperators_Camillo {
  color: var(--color-Camillo);
  border: 1px solid var(--color-Camillo);
  z-index: 11;
}
.marker_nationalOperators_Camillo:hover {
  background-color: var(--color-Camillo);
}
.markerSelected_Camillo {
  background-color: var(--color-Camillo);
}

.marker_nationalOperators_Copperbay {
  color: var(--color-Copperbay);
  border: 1px solid var(--color-Copperbay);
  z-index: 11;
}
.marker_nationalOperators_Copperbay:hover {
  background-color: var(--color-Copperbay);
}
.markerSelected_Copperbay {
  background-color: var(--color-Copperbay);
}

.marker_nationalOperators_Divvy {
  color: var(--color-Divvy);
  border: 1px solid var(--color-Divvy);
  z-index: 11;
}
.marker_nationalOperators_Divvy:hover {
  background-color: var(--color-Divvy);
}
.markerSelected_Divvy {
  background-color: var(--color-Divvy);
}

.marker_nationalOperators_FirstKey {
  color: var(--color-FirstKey);
  border: 1px solid var(--color-FirstKey);
  z-index: 11;
}
.marker_nationalOperators_FirstKey:hover {
  background-color: var(--color-FirstKey);
}
.markerSelected_FirstKey {
  background-color: var(--color-FirstKey);
}

.marker_nationalOperators_Hudson {
  color: var(--color-Hudson);
  border: 1px solid var(--color-Hudson);
  z-index: 11;
}
.marker_nationalOperators_Hudson:hover {
  background-color: var(--color-Hudson);
}
.markerSelected_Hudson {
  background-color: var(--color-Hudson);
}

.marker_nationalOperators_Imagine {
  color: var(--color-Imagine);
  border: 1px solid var(--color-Imagine);
  z-index: 11;
}
.marker_nationalOperators_Imagine:hover {
  background-color: var(--color-Imagine);
}
.markerSelected_Imagine {
  background-color: var(--color-Imagine);
}

.marker_nationalOperators_KairosLiving {
  color: var(--color-KairosLiving);
  border: 1px solid var(--color-KairosLiving);
  z-index: 11;
}
.marker_nationalOperators_KairosLiving:hover {
  background-color: var(--color-KairosLiving);
}
.markerSelected_KairosLiving {
  background-color: var(--color-KairosLiving);
}

.marker_nationalOperators_KrchRealty {
  color: var(--color-KrchRealty);
  border: 1px solid var(--color-KrchRealty);
  z-index: 11;
}
.marker_nationalOperators_KrchRealty:hover {
  background-color: var(--color-KrchRealty);
}
.markerSelected_KrchRealty {
  background-color: var(--color-KrchRealty);
}

.marker_nationalOperators_LiveReszi {
  color: var(--color-LiveReszi);
  border: 1px solid var(--color-LiveReszi);
  z-index: 11;
}
.marker_nationalOperators_LiveReszi:hover {
  background-color: var(--color-LiveReszi);
}
.markerSelected_LiveReszi {
  background-color: var(--color-LiveReszi);
}

.marker_nationalOperators_OpenHouse {
  color: var(--color-OpenHouse);
  border: 1px solid var(--color-OpenHouse);
  z-index: 11;
}
.marker_nationalOperators_OpenHouse:hover {
  background-color: var(--color-OpenHouse);
}
.markerSelected_OpenHouse {
  background-color: var(--color-OpenHouse);
}

.marker_nationalOperators_Pathway {
  color: var(--color-Pathway);
  border: 1px solid var(--color-Pathway);
  z-index: 11;
}
.marker_nationalOperators_Pathway:hover {
  background-color: var(--color-Pathway);
}
.markerSelected_Pathway {
  background-color: var(--color-Pathway);
}

.marker_nationalOperators_Peak {
  color: var(--color-Peak);
  border: 1px solid var(--color-Peak);
  z-index: 11;
}
.marker_nationalOperators_Peak:hover {
  background-color: var(--color-Peak);
}
.markerSelected_Peak {
  background-color: var(--color-Peak);
}

.marker_nationalOperators_PPMG {
  color: var(--color-PPMG);
  border: 1px solid var(--color-PPMG);
  z-index: 11;
}
.marker_nationalOperators_PPMG:hover {
  background-color: var(--color-PPMG);
}
.markerSelected_PPMG {
  background-color: var(--color-PPMG);
}

.marker_nationalOperators_Propify {
  color: var(--color-Propify);
  border: 1px solid var(--color-Propify);
  z-index: 11;
}
.marker_nationalOperators_Propify:hover {
  background-color: var(--color-Propify);
}
.markerSelected_Propify {
  background-color: var(--color-Propify);
}

.marker_nationalOperators_RENU {
  color: var(--color-RENU);
  border: 1px solid var(--color-RENU);
  z-index: 11;
}
.marker_nationalOperators_RENU:hover {
  background-color: var(--color-RENU);
}
.markerSelected_RENU {
  background-color: var(--color-RENU);
}

.marker_nationalOperators_ResiHome {
  color: var(--color-ResiHome);
  border: 1px solid var(--color-ResiHome);
  z-index: 11;
}
.marker_nationalOperators_ResiHome:hover {
  background-color: var(--color-ResiHome);
}
.markerSelected_ResiHome {
  background-color: var(--color-ResiHome);
}

.marker_nationalOperators_SPA {
  color: var(--color-SPA);
  border: 1px solid var(--color-SPA);
  z-index: 11;
}
.marker_nationalOperators_SPA:hover {
  background-color: var(--color-SPA);
}
.markerSelected_SPA {
  background-color: var(--color-SPA);
}

.marker_nationalOperators_Streetlane {
  color: var(--color-Streetlane);
  border: 1px solid var(--color-Streetlane);
  z-index: 11;
}
.marker_nationalOperators_Streetlane:hover {
  background-color: var(--color-Streetlane);
}
.markerSelected_Streetlane {
  background-color: var(--color-Streetlane);
}

.marker_nationalOperators_SYLV {
  color: var(--color-SYLV);
  border: 1px solid var(--color-SYLV);
  z-index: 11;
}
.marker_nationalOperators_SYLV:hover {
  background-color: var(--color-SYLV);
}
.markerSelected_SYLV {
  background-color: var(--color-SYLV);
}

/*  */
.marker_newBuild {
  color: var(--color-NewBuild);
  border: 1px solid var(--color-NewBuild);
  z-index: 9;
}
.marker_newBuild:hover {
  background-color: var(--color-NewBuild);
}
.markerSelected_newBuild {
  background-color: var(--color-NewBuild);
}

.marker_multiFamily {
  color: var(--color-MultiFamily);
  border: 1px solid var(--color-MultiFamily);
  z-index: 9;
}

.marker_multiFamily:hover {
  background-color: var(--color-MultiFamily);
}

.markerSelected_multiFamily {
  background-color: var(--color-MultiFamily);
}

.marker_BTOwned {
  color: var(--color-BT-blue);
  border: 1px solid var(--color-BT-blue);
  z-index: 9;
}

.marker_BTOwned:hover {
  background-color: var(--color-BT-blue);
}

.markerSelected_BTOwned {
  background-color: var(--color-BT-blue);
}

.subjectPropertyMarker {
  z-index: 101;
  fill: #e200ff;
  stroke: #fff;
  stroke-width: 12px;
  filter: drop-shadow(0px 0px 2px rgb(0 0 0 / 0.4));
}

.subjectPropertyMarker:hover {
  fill: orange;
}

/* override MapLibre logo installed by AWS-amplify */
.mapboxgl-ctrl-logo {
  background-image: url('/images/mapbox/mapboxgl-ctrl-logo.svg') !important;
}

#property-detail-popup .mapboxgl-popup {
  z-index: 1000;
  pointer-events: auto !important;
}

#property-detail-popup .mapboxgl-popup-content {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19) !important;
  border-radius: 10px !important;
  padding: 0 !important;
  overflow: hidden !important;
}

#property-detail-popup .mapboxgl-popup-tip {
  display: block !important;
  margin-bottom: 10px;
}
