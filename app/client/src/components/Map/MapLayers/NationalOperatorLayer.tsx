import { useEffect, useRef, useCallback } from 'react';
import { renderToString } from 'react-dom/server';
import { Source, Layer } from '@spatiallaser/map';
import { MAP_LAYER_NAME_BASE } from '@/constants';
import {
  setPriceMarkers,
  removePriceMarkers,
} from '../MapUtility/general';
import { isEmpty } from 'lodash';
import { propertyDetailPopup } from '../MapCMA.tsx';
import PropertyDetailPopup from '../MapAdditionalComponents/PropertyDetailPopup';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';

const sourceId = MAP_LAYER_NAME_BASE.nationalOperator;

const zoomLevelToShowPriceMarkers = 12;

const circleStyle = {
  id: `${sourceId}Layer`,
  type: 'circle',
  paint: {
    'circle-radius': 8,
    'circle-color': [
      'match',
      ['get', 'brand'],
      'AH4R',
      '#af2730',
      'Invitatio',
      '#206f06',
      'HRG',
      '#a41618',
      'PR',
      '#045d67',
      'CPM',
      '#3a4737',
      'TR',
      '#00B2E2',
      'MYND',
      '#485c6c',
      'KP',
      '#063f41',
      'RW',
      '#ee2375',
      // other
      '#333',
    ],
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};

let priceMarkersNationalOperators = {};

export const removeNationalOperatorsPriceMarkers = () => {
  if (!isEmpty(priceMarkersNationalOperators)) {
    priceMarkersNationalOperators = removePriceMarkers(
      priceMarkersNationalOperators,
    );
  }
};

export const generateNationalOperatorsPriceMarkers = (
  map,
  currentNationalOperatorsGeoJSON,
) => {
  if (
    currentNationalOperatorsGeoJSON &&
    currentNationalOperatorsGeoJSON.features.length > 0
  ) {
    if (!isEmpty(priceMarkersNationalOperators)) {
      priceMarkersNationalOperators = removePriceMarkers(
        priceMarkersNationalOperators,
      );
    }
    priceMarkersNationalOperators = setPriceMarkers(
      map,
      currentNationalOperatorsGeoJSON.features,
      'base_id',
      'rent',
      sourceId,
    );
  }
};

function NationalOperatorLayer() {
  const {
    map,
    currentNationalOperatorsGeoJSON,
    showPriceMarkers,
    selectedBuyersViewRecord,
    propertyModalTabKey,
    searchingMode,
    selectedRowKeysNationalOperators,
  } = useMarketplaceMapContext();

  const mouseOnTopRef = useRef(false);
  const mouseOnTopOfPopupRef = useRef(false);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (!isEmpty(priceMarkersNationalOperators)) {
        priceMarkersNationalOperators = removePriceMarkers(priceMarkersNationalOperators);
      }
    };
  }, []);

  const showHidePriceMarkers = useCallback(() => {
    if (
      map &&
      showPriceMarkers &&
      currentNationalOperatorsGeoJSON &&
      currentNationalOperatorsGeoJSON.features.length > 0 &&
      !isEmpty(selectedBuyersViewRecord) &&
      ['comps'].includes(propertyModalTabKey)
    ) {
      // Check current zoom level
      const currentZoom = map.getZoom();

      if (currentZoom >= zoomLevelToShowPriceMarkers) {
        // Clean up existing markers first
        if (!isEmpty(priceMarkersNationalOperators)) {
          priceMarkersNationalOperators = removePriceMarkers(
            priceMarkersNationalOperators,
          );
        }
        // Create new markers
        priceMarkersNationalOperators = setPriceMarkers(
          map,
          currentNationalOperatorsGeoJSON.features,
          'base_id',
          'rent',
          sourceId,
        );
      } else {
        priceMarkersNationalOperators = removePriceMarkers(priceMarkersNationalOperators);
      }
    } else {
      priceMarkersNationalOperators = removePriceMarkers(priceMarkersNationalOperators);
    }
  }, [map]);

  useEffect(() => {
    if (!map) return;

    const mouseEnter = (e) => {
      // refer to https://docs.mapbox.com/mapbox-gl-js/example/popup-on-hover/
      const coordinates = e.features[0].geometry.coordinates.slice();

      while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
        coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
      }

      const nationalOperatorProperty = e.features[0].properties;
      mouseOnTopRef.current = true;
      mouseOnTopOfPopupRef.current = true;

      const htmlText = renderToString(
        <PropertyDetailPopup
          hoverPropertyDetails={{
            ...nationalOperatorProperty,
            type: 'nationalOperators',
            searchingMode,
          }}
        />,
      );

      if (mouseOnTopRef.current) {
        propertyDetailPopup
          .setLngLat(coordinates)
          .setHTML(htmlText)
          .addTo(map);

        const popup = propertyDetailPopup.getElement();

        popup.addEventListener('mouseenter', () => {
          mouseOnTopOfPopupRef.current = true;
        });
        popup.addEventListener('mousemove', () => {
          mouseOnTopOfPopupRef.current = true;
        });
        popup.addEventListener('mouseleave', () => {
          mouseOnTopOfPopupRef.current = false;
          if (propertyDetailPopup.getElement() && !mouseOnTopRef.current) {
            propertyDetailPopup.remove();
          }
        });
      }
    };

    const mouseMove = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}LayerSymbol`],
      });
      if (features.length > 0) {
        mouseOnTopRef.current = true;
      } else {
        mouseOnTopRef.current = false;
      }
    };

    const mouseLeave = (e) => {
      mouseOnTopRef.current = false;

      if (
        propertyDetailPopup.getElement() &&
        mouseOnTopOfPopupRef.current === false
      ) {
        if (!e.originalEvent || !e.originalEvent.relatedTarget) {
          propertyDetailPopup.remove();
        }
        if (
          e.originalEvent &&
          e.originalEvent.relatedTarget &&
          !e.originalEvent.relatedTarget.classList.contains('mapboxgl-popup')
        ) {
          propertyDetailPopup.remove();
        }
      }
    };

    const styleLoad = () => {
      showHidePriceMarkers();
    };

    map.on('mouseenter', `${sourceId}LayerSymbol`, mouseEnter);
    map.on('mousemove', `${sourceId}LayerSymbol`, mouseMove);
    map.on('mouseleave', `${sourceId}LayerSymbol`, mouseLeave);
    return () => {
      map.off('mouseenter', `${sourceId}LayerSymbol`, mouseEnter);
      map.off('mousemove', `${sourceId}LayerSymbol`, mouseMove);
      map.off('mouseleave', `${sourceId}LayerSymbol`, mouseLeave);
      map.off('style.load', styleLoad);
    };
  }, [map]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [currentNationalOperatorsGeoJSON, selectedBuyersViewRecord, propertyModalTabKey, showPriceMarkers, selectedRowKeysNationalOperators]);

  return (
    <Source id={sourceId} type="geojson" data={currentNationalOperatorsGeoJSON}>
      <Layer {...circleStyle} />
    </Source>
  );
}

export default NationalOperatorLayer;
