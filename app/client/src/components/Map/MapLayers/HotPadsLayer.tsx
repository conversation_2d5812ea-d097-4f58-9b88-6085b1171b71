import { useEffect, useRef, useCallback } from 'react';
import { renderToString } from 'react-dom/server';
import { Source, Layer } from '@spatiallaser/map';
import { MAP_LAYER_NAME_BASE } from '@/constants';
import {
  setPriceMarkers,
  removePriceMarkers,
} from '../MapUtility/general';
import { isEmpty } from 'lodash';
import { propertyDetailPopup } from '../MapCMA.tsx';
import PropertyDetailPopup from '../MapAdditionalComponents/PropertyDetailPopup';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';

export const sourceId = MAP_LAYER_NAME_BASE.hotPads;

const zoomLevelToShowPriceMarkers = 12;

const circleStyle = {
  id: `${sourceId}Layer`,
  type: 'circle',
  paint: {
    'circle-radius': 8,
    'circle-color': '#ED6041',
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};

let priceMarkersHotPads = {};

export const removeHotPadsPriceMarkers = () => {
  if (!isEmpty(priceMarkersHotPads)) {
    priceMarkersHotPads = removePriceMarkers(priceMarkersHotPads);
  }
};

export const generateHotPadsPriceMarkers = (map, currentHotPadsGeoJSON) => {
  if (currentHotPadsGeoJSON && currentHotPadsGeoJSON.features.length > 0) {
    if (!isEmpty(priceMarkersHotPads)) {
      priceMarkersHotPads = removePriceMarkers(priceMarkersHotPads);
    }
    priceMarkersHotPads = setPriceMarkers(
      map,
      currentHotPadsGeoJSON.features,
      'base_id',
      'rent',
      sourceId,
    );
  }
};

function HotPadsLayer() {
  const {
    map,
    currentHotPadsGeoJSON,
    showPriceMarkers,
    selectedBuyersViewRecord,
    propertyModalTabKey,
    searchingMode,
    selectedRowKeysHotPads,
  } = useMarketplaceMapContext();

  const mouseOnTopRef = useRef(false);
  const mouseOnTopOfPopupRef = useRef(false);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (!isEmpty(priceMarkersHotPads)) {
        priceMarkersHotPads = removePriceMarkers(priceMarkersHotPads);
      }
    };
  }, []);

  const showHidePriceMarkers = useCallback(() => {
    if (
      map &&
      showPriceMarkers &&
      currentHotPadsGeoJSON &&
      currentHotPadsGeoJSON.features.length > 0 &&
      !isEmpty(selectedBuyersViewRecord) &&
      ['comps'].includes(propertyModalTabKey)
    ) {
      // Check current zoom level
      const currentZoom = map.getZoom();

      if (currentZoom >= zoomLevelToShowPriceMarkers) {
        // Clean up existing markers first
        if (!isEmpty(priceMarkersHotPads)) {
          priceMarkersHotPads = removePriceMarkers(priceMarkersHotPads);
        }
        // Create new markers
        priceMarkersHotPads = setPriceMarkers(
          map,
          currentHotPadsGeoJSON.features,
          'base_id',
          'rent',
          sourceId,
        );
      } else {
        priceMarkersHotPads = removePriceMarkers(priceMarkersHotPads);
      }
    } else {
      priceMarkersHotPads = removePriceMarkers(priceMarkersHotPads);
    }
  }, [map]);

  useEffect(() => {
    if (!map) return;

    const mouseEnter = (e) => {
      // refer to https://docs.mapbox.com/mapbox-gl-js/example/popup-on-hover/
      const coordinates = e.features[0].geometry.coordinates.slice();

      while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
        coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
      }

      const hotPadsProperty = e.features[0].properties;
      mouseOnTopRef.current = true;
      mouseOnTopOfPopupRef.current = true;

      const htmlText = renderToString(
        <PropertyDetailPopup
          hoverPropertyDetails={{
            ...hotPadsProperty,
            type: 'HotPads',
            searchingMode,
          }}
        />,
      );

      if (mouseOnTopRef.current) {
        propertyDetailPopup
          .setLngLat(coordinates)
          .setHTML(htmlText)
          .addTo(map);

        const popup = propertyDetailPopup.getElement();

        popup.addEventListener('mouseenter', () => {
          mouseOnTopOfPopupRef.current = true;
        });
        popup.addEventListener('mousemove', () => {
          mouseOnTopOfPopupRef.current = true;
        });
        popup.addEventListener('mouseleave', () => {
          mouseOnTopOfPopupRef.current = false;
          if (propertyDetailPopup.getElement() && !mouseOnTopRef.current) {
            propertyDetailPopup.remove();
          }
        });
      }
    };

    const mouseMove = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}Layer`],
      });
      if (features.length > 0) {
        mouseOnTopRef.current = true;
      } else {
        mouseOnTopRef.current = false;
      }
    };

    const mouseLeave = (e) => {
      mouseOnTopRef.current = false;

      if (
        propertyDetailPopup.getElement() &&
        mouseOnTopOfPopupRef.current === false
      ) {
        if (!e.originalEvent || !e.originalEvent.relatedTarget) {
          propertyDetailPopup.remove();
        }
        if (
          e.originalEvent &&
          e.originalEvent.relatedTarget &&
          !e.originalEvent.relatedTarget.classList.contains('mapboxgl-popup')
        ) {
          propertyDetailPopup.remove();
        }
      }
    };

    const styleLoad = () => {
      showHidePriceMarkers();
    };

    map.on('mouseenter', `${sourceId}Layer`, mouseEnter);
    map.on('mousemove', `${sourceId}Layer`, mouseMove);
    map.on('mouseleave', `${sourceId}Layer`, mouseLeave);
    return () => {
      map.off('mouseenter', `${sourceId}Layer`, mouseEnter);
      map.off('mousemove', `${sourceId}Layer`, mouseMove);
      map.off('mouseleave', `${sourceId}Layer`, mouseLeave);
      map.off('style.load', styleLoad);
    };
  }, [map]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [currentHotPadsGeoJSON, selectedBuyersViewRecord, propertyModalTabKey, showPriceMarkers, selectedRowKeysHotPads]);

  return (
    <Source id={sourceId} type="geojson" data={currentHotPadsGeoJSON}>
      <Layer {...circleStyle} />
    </Source>
  );
}

export default HotPadsLayer;
