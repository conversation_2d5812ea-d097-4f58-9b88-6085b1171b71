import { Layer, Source } from '@spatiallaser/map';
import isEmpty from 'lodash.isempty';
import mapboxgl from 'mapbox-gl';
import React, { useCallback, useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import { renderToString } from 'react-dom/server';
import { sourceId as sourceIdHotPadsLayer } from './HotPadsLayer';
import {
  removePriceMarkers,
  setPriceMarkers,
} from '../MapUtility/general';
import { formatPricePerSqftArce } from '@/components/PropertyDetails/TabComps/utils/realtorDotComCompsFunctions';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { realtorDotComCompData } from '@/components/PropertyDetails/TabComps/utils/types';

const sourceId = 'land-sf';
let priceMarker = {};
const zoomLevelToShowParcelAVM = 17;
const zoomLevelToShowPriceMarkers = 12;

interface GeoJSONFeature {
  type: 'Feature';
  geometry: {
    type: 'Point';
    coordinates: [number, number];
  };
  properties: Record<string, unknown>;
}

interface GeoJSONData {
  type: 'FeatureCollection';
  features: GeoJSONFeature[];
}

export const removePRPriceMarkers = () => {
  if (!isEmpty(priceMarker)) {
    priceMarker = removePriceMarkers(priceMarker);
  }
};

export const generatePRPriceMarkers = (map: mapboxgl.Map, GeoJSON: GeoJSONData | null) => {
  if (GeoJSON && GeoJSON.features && GeoJSON.features.length > 0) {
    if (!isEmpty(priceMarker)) {
      priceMarker = removePriceMarkers(priceMarker);
    }
    priceMarker = setPriceMarkers(
      map,
      GeoJSON.features,
      'id',
      'currentprice',
      sourceId,
    );
  }
};

interface TooltipProps {
  feature: {
    properties: realtorDotComCompData;
    geometry: {
      coordinates: number[];
    };
  };
}

function RealtorDotComLayer() {
  const {
    map,
    showPriceMarkers,
    currentRealtorDotComGeoJSON,
    propertyModalTabKey,
    searchingMode,
    realtorSingleFamilyHover,
  } = useMarketplaceMapContext();

  function Tooltip({ feature }: TooltipProps) {
    const properties = feature.properties;
    const pps =
      Number(properties.currentprice) / Number(properties.square_feet);

    return (
      <div
        style={{
          backgroundColor: 'white',
          padding: '10px',
          fontSize: '14px',
          zIndex: '999',
        }}
      >
        <div>
          <strong>Address:</strong> {properties.address}, {properties.city},{' '}
          {properties.state}, {properties.postal_code}
        </div>
        <div>
          <strong>Size:</strong>{' '}
          {!properties.square_feet
            ? 'N/A'
            : new Intl.NumberFormat('en-US').format(
                Number(properties.square_feet),
              ) + ' sqft'}
        </div>
        <div>
          <strong>Bd:</strong> {properties.beds} | <strong>Baths:</strong>{' '}
          {properties.baths}
        </div>
        <div>
          <strong>Price:</strong> {properties.currentprice}
        </div>
        <div>
          <strong>Price per Sqft:</strong> {formatPricePerSqftArce(pps)}
        </div>
        <div>
          <strong>Status:</strong> {properties.status}
        </div>
        <div>
          <strong>Property Type:</strong>{' '}
          {properties.propertytype ? properties.propertytype : 'N/A'}
        </div>
      </div>
    );
  }

  // Create refs for values that need to be accessed in callbacks/effects
  const showPriceMarkersRef = useRef(showPriceMarkers);
  const searchingModeRef = useRef(searchingMode);
  const geoJsonRef = useRef(currentRealtorDotComGeoJSON);
  const cmaTabKeyRef = useRef(propertyModalTabKey);
  const tooltipRef = useRef(new mapboxgl.Popup({ offset: 15 }));

  // Update refs when values change
  showPriceMarkersRef.current = showPriceMarkers;
  searchingModeRef.current = searchingMode;
  geoJsonRef.current = currentRealtorDotComGeoJSON;
  cmaTabKeyRef.current = propertyModalTabKey;

  const showHidePriceMarkers = useCallback(() => {
    if (
      map &&
      cmaTabKeyRef.current === '1' &&
      searchingModeRef.current === 'Lease' &&
      showPriceMarkersRef.current &&
      geoJsonRef.current &&
      (geoJsonRef.current as GeoJSONData)?.features &&
      (geoJsonRef.current as GeoJSONData).features.length > 0
    ) {
      const currentZoom = map.getZoom();
      if (currentZoom >= zoomLevelToShowPriceMarkers) {
        if (!isEmpty(priceMarker)) {
          priceMarker = removePriceMarkers(priceMarker);
        }
        priceMarker = setPriceMarkers(
          map,
          (geoJsonRef.current as GeoJSONData).features,
          'id',
          'currentprice',
          sourceId,
        );
      } else {
        priceMarker = removePriceMarkers(priceMarker);
      }
    } else {
      priceMarker = removePriceMarkers(priceMarker);
    }
  }, [map]);

  useEffect(() => {
    if (!map) return;

    const zoomEnd = () => {
      showHidePriceMarkers();
    };

    map.on('zoomend', zoomEnd);

    return () => {
      map.off('zoomend', zoomEnd);
      // Cleanup markers when component unmounts
      priceMarker = removePriceMarkers(priceMarker);
    };
  }, [map, showHidePriceMarkers]);

  // Effect to update markers when relevant state changes
  useEffect(() => {
    showHidePriceMarkers();
  }, [showPriceMarkers, currentRealtorDotComGeoJSON, propertyModalTabKey, searchingMode]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      priceMarker = removePriceMarkers(priceMarker);
      tooltipRef.current.remove();
    };
  }, []);

  const showTooltip = useCallback(
    (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
      if (!e.features || e.features.length === 0) return;
      
      const feature = e.features[0];
      const coordinates = feature.geometry.coordinates.slice() as [number, number];
      const placeholder = document.createElement('div');
      
      // Use createRoot instead of ReactDOM.render for React 18+
      const root = ReactDOM.createRoot(placeholder);
      root.render(<Tooltip feature={feature} />);
      
      tooltipRef.current
        .setLngLat(coordinates)
        .setDOMContent(placeholder.firstChild as Node)
        .addTo(map);
    },
    [map],
  );

  const hideTooltip = useCallback(() => {
    tooltipRef.current.remove();
  }, []);

  useEffect(() => {
    if (!map) return;

    map.on('mouseenter', `${sourceId}LayerCircle`, showTooltip);
    map.on('mouseleave', `${sourceId}LayerCircle`, hideTooltip);

    return () => {
      map?.off('mouseenter', `${sourceId}LayerCircle`, showTooltip);
      map?.off('mouseleave', `${sourceId}LayerCircle`, hideTooltip);
      tooltipRef.current.remove();
    };
  }, [map, showTooltip, hideTooltip]);

  useEffect(() => {
    if (!map || !realtorSingleFamilyHover) {
      tooltipRef.current.remove();
      return;
    }

    const coordinates = [
      realtorSingleFamilyHover.longitude as number,
      realtorSingleFamilyHover.latitude as number,
    ] as [number, number];
    const feature = {
      properties: realtorSingleFamilyHover,
      geometry: realtorSingleFamilyHover.geom as { coordinates: number[] },
    };
    const htmlString = renderToString(<Tooltip feature={feature} />);
    const placeholder = document.createElement('div');
    placeholder.innerHTML = htmlString;
    tooltipRef.current
      .setLngLat(coordinates)
      .setDOMContent(placeholder)
      .addTo(map);
  }, [realtorSingleFamilyHover, map]);

  const circleStyle = {
    id: `${sourceId}LayerCircle`,
    type: 'circle' as const,
    filter: ['!', ['has', 'point_count']],
    paint: {
      'circle-radius': 8,
      'circle-color': '#a1f542',
      'circle-stroke-color': '#FFF',
      'circle-stroke-width': 1,
    },
  };

  console.log('RealtorDotComLayer currentRealtorDotComGeoJSON', currentRealtorDotComGeoJSON);
  console.log('RealtorDotComLayer propertyModalTabKey', propertyModalTabKey);
  console.log('RealtorDotComLayer searchingMode', searchingMode);

  return propertyModalTabKey === 'comps' && searchingMode === 'Lease' ? (
    <Source id={sourceId} type="geojson" data={currentRealtorDotComGeoJSON}>
      <Layer {...circleStyle} beforeId={`${sourceIdHotPadsLayer}Layer`} />
    </Source>
  ) : null;
}

export default RealtorDotComLayer;
