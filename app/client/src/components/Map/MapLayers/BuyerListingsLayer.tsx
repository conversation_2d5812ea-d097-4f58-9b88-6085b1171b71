import { useState, useEffect, useRef, useMemo } from 'react';
import mapboxgl from 'mapbox-gl';
import { isEmpty } from 'lodash';
import { convertPriceToNumber } from '@/lib/utils/stringMethods';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { generateBuyerListingsGeoJSON } from '@/lib/utils/generateBuyerListingsGeoJSON';
import { usePropertiesFilterState } from '@/hooks/usePropertiesFilter';
import { filterProperties } from '@/lib/utils/filterProperties';
import { useSearch, useRouter } from '@tanstack/react-router';

const zoomLevelToShowClusters = 9;

// Define types for better type safety
interface PropertyGeometry {
  coordinates: number[]; // Keep as number[] to match the actual GeoJSON structure
}

interface PropertyPayload {
  subjectProperty?: {
    placekey?: string;
    address?: string;
    beds?: number;
    baths?: number;
    sqft?: number;
    city?: string;
    state?: string;
    zipCode?: string;
    lat?: number;
    lng?: number;
    yearbuilt?: number;
    meta?: {
      base_price?: string;
    };
  };
  proforma?: {
    buyAndHold?: {
      "Market Value"?: number;
      "Projected Monthly Rent"?: number;
      "Projected Yield on Bid Price"?: number;
    };
  };
}

interface PropertyProperties {
  payload?: PropertyPayload;
  property_id?: string | number;
  decision?: 'reject' | 'accept';
}

interface PropertyFeature {
  geometry: PropertyGeometry;
  properties?: PropertyProperties;
}

const BuyerListingsLayer = () => {
  const {
    map,
    allProperties,
    allPropertiesSubmitted,
    selectedBuyersViewRecord,
    setSelectedPortfolioMarker
  } = useMarketplaceMapContext();

  // get listType and other search params from search
  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;
  const router = useRouter();

  const { selectedMarket, selectedPrice, selectedBeds, selectedBaths, minCapRate } = usePropertiesFilterState();
  const filteredProperties = useMemo(() => {
    return filterProperties({
      properties: listType === 'listings' ? allProperties : allPropertiesSubmitted,
      selectedMarket,
      selectedPrice,
      selectedBeds,
      selectedBaths,  
      minCapRate
    });
  }, [allProperties, allPropertiesSubmitted, selectedMarket, selectedPrice, selectedBeds, selectedBaths, minCapRate, listType]);

  const markers = useRef<Record<string, mapboxgl.Marker>>({});

  const [hoveredMarkerProperty, setHoveredMarkerProperty] = useState<{
    element: HTMLElement;
    properties: Record<string, unknown>;
  } | null>(null);

  const createMarker = (property: PropertyFeature) => {
    if (!map) return;

    const subjectProperty = property.properties?.payload?.subjectProperty;
    if (!subjectProperty) return;

    const proFormaAllValues = property.properties?.payload?.proforma?.buyAndHold;

    const {
      placekey,
      address,
      beds,
      baths,
      sqft,
      city,
      state,
      zipCode,
      lat,
      lng,
      yearbuilt,
    } = subjectProperty;

    const { coordinates } = property.geometry;
    // for Lennar
    const netSalesPrice = convertPriceToNumber(subjectProperty?.meta?.base_price as string);
    const listingPriceInUse = netSalesPrice;
    const salesInUse = proFormaAllValues?.["Market Value"] || 0;
    const rentInUse = proFormaAllValues?.["Projected Monthly Rent"] || 0;
    const capRate = proFormaAllValues?.["Projected Yield on Bid Price"] || 0;

    const el = document.createElement('div');
    el.style.cssText = 'display: flex; flex-direction: column; gap: 4px';

    const markerBaseClass = property.properties?.decision === 'reject' 
      ? 'bg-red-600 hover:bg-[rgb(255,100,100)] font-bold px-1 py-[2px] rounded-[10px] transition-colors duration-200' 
      : 'bg-green-600 hover:bg-[rgb(18,212,18)] font-bold px-1 py-[2px] rounded-[10px] transition-colors duration-200';
    const markerTriangleColor = property.properties?.decision === 'reject' ? 'red' : 'green';

    el.innerHTML = `
      <div class='price-marker-container' style='display: inline-flex; flex-direction: column; align-items: center; cursor: pointer;'>
        <div class='${markerBaseClass}'>
          <span style='color: white;'>$${Math.round(
            (listingPriceInUse || 0) / 1000,
          ).toLocaleString()}K</span>
        </div>
        <svg width="10" height="5" viewBox="0 0 10 5" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M0 0L5 5L10 0H0Z" fill="${markerTriangleColor}"></path>
        </svg>
      </div>
    `;

     const popupContent = `
      <div style='min-width: 275px;'>
        <div style='padding: 4px;'>
          <div>
            <strong>${beds || 'N/A'}</strong> bds | <strong>${baths || 'N/A'}</strong> ba | <strong>${sqft ? (sqft as number).toLocaleString(): 'N/A'}</strong> sqft | cap rate: <strong>${typeof capRate === 'number' ? (Math.round(capRate * 10000)/100).toFixed(1) : 'N/A'}%</strong>
          </div>
          <div>${address || 'N/A'}</div>
          <div>${city || 'N/A'}, ${state || 'N/A'} ${zipCode || 'N/A'}</div>
          <div>Value: $${Number(salesInUse).toLocaleString('en-US', { maximumFractionDigits: 0 })} | Rent: $${Number(rentInUse).toLocaleString('en-US', { maximumFractionDigits: 0 })}</div>
        </div>
      </div>
    `;

     const popup = new mapboxgl.Popup({ offset: 25 })
      .setHTML(popupContent);

    // Add click handler that navigates to property details page (same as PropertyCard)
    el.querySelector('.price-marker-container')?.addEventListener(
      'click',
      () => {
        // Set selected portfolio marker for map state
        if (placekey) {
          setSelectedPortfolioMarker(placekey);
        }
        
        // Navigate to property details page using the same pattern as PropertyCard
        const propertyId = property.properties?.property_id;
        if (propertyId) {
          // Find the original property data from allProperties or allPropertiesSubmitted
          const currentProperties = listType === 'listings' ? allProperties : allPropertiesSubmitted;
          
          // Add null check for currentProperties
          if (currentProperties) {
            const originalProperty = currentProperties.find(prop => 
              String(prop.property_id) === String(propertyId)
            );

            if (originalProperty) {
              router.navigate({
                to: '/properties/$id/proForma',
                params: { id: String(propertyId) },
                search: {
                  ...search,
                  placekey: originalProperty.placekey,
                  lat: originalProperty?.payload?.subjectProperty?.lat,
                  lng: originalProperty?.payload?.subjectProperty?.lng,
                  streetnum: originalProperty?.full_address?.split(' ')[0],
                  address: originalProperty?.full_address,
                  city: originalProperty?.city,
                  state: originalProperty?.state,
                  zip_code: originalProperty?.postal_code,
                  latitude: originalProperty?.payload?.subjectProperty?.lat?.toString(),
                  longitude: originalProperty?.payload?.subjectProperty?.lng?.toString(),
                  beds: originalProperty?.payload?.subjectProperty?.beds?.toString(),
                  baths: originalProperty?.payload?.subjectProperty?.baths?.toString(),
                  sqft: originalProperty?.payload?.subjectProperty?.sqft?.toString(),
                  yearbuilt: originalProperty?.payload?.subjectProperty?.yearbuilt?.toString(),
                },
              });
            }
          }
        }
      },
    );

    el.addEventListener('mouseenter', () => {
      if (placekey && markers.current && markers.current[placekey] && map) {
        const popup = markers.current[placekey].getPopup();
        if (popup) {
          popup.addTo(map);
        }
      }
      setHoveredMarkerProperty({
        element: el,
        properties: property.properties as Record<string, unknown>
      });
    });
    el.addEventListener('mouseleave', () => {
      if (placekey && markers.current && markers.current[placekey] && map) {
        const popup = markers.current[placekey].getPopup();
        if (popup) {
          popup.remove();
        }
      }
      setHoveredMarkerProperty(null);
    });

    if (placekey && coordinates.length >= 2) {
      // Ensure coordinates have at least 2 elements for [lng, lat]
      const [lng, lat] = coordinates;
      markers.current[placekey] = new mapboxgl.Marker({
        element: el,
        offset: [0, -20],
      })
        .setLngLat([lng, lat])
        .setPopup(popup)
        .addTo(map);
    }
  };

  useEffect(() => {
    if (!map) return;

    const buyerListingsGeoJSON = generateBuyerListingsGeoJSON({
      dataSource: filteredProperties,
      dataType: 'offmarket',
    });

    // if there are no properties, remove all markers
    if (!buyerListingsGeoJSON || buyerListingsGeoJSON.features.length === 0) {
      if (markers.current && !isEmpty(markers.current)) {
        for (const markerKey in markers.current) {
          markers.current[markerKey].remove();
        }
      }
      markers.current = {};
      return;
    };

    if (
      buyerListingsGeoJSON &&
      !isEmpty(buyerListingsGeoJSON) &&
      buyerListingsGeoJSON?.features &&
      Array.isArray(buyerListingsGeoJSON.features) &&
      buyerListingsGeoJSON.features.length > 0
    ) {
      // remove all previous markers
      if (markers.current && !isEmpty(markers.current)) {
        for (const markerKey in markers.current) {
          markers.current[markerKey].remove();
        }
      }

      markers.current = {};

      for (let i = 0; i < buyerListingsGeoJSON.features.length; i++) {
        const property = buyerListingsGeoJSON.features[i];
        // Type assertion with proper coordinate handling
        createMarker(property as unknown as PropertyFeature);
      }
    }
  }, [map, filteredProperties, router, search, listType, allProperties, allPropertiesSubmitted]);

  // selectedBuyersViewRecord market needs to be on top
  // many new builds can have the same coordinates
  // so when there's a new selectedBuyersViewRecord
  // we remove its marker and create a new one so it's on top
  useEffect(() => {
    if (!map || !selectedBuyersViewRecord?.placekey) return;

    if (markers.current[selectedBuyersViewRecord.placekey]) {
      markers.current[selectedBuyersViewRecord.placekey].remove();
    }

    const buyerListingsGeoJSON = generateBuyerListingsGeoJSON({
      dataSource: filteredProperties,
      dataType: 'offmarket',
    });

    // find the property in buyerListingsGeoJSON
    const selectedBuyersViewRecordGeoJSON = buyerListingsGeoJSON?.features?.find(feature => 
      feature?.properties?.property_id === selectedBuyersViewRecord?.property_id
    );

    if (selectedBuyersViewRecordGeoJSON) {
      // remove all other markers
      if (markers.current && !isEmpty(markers.current)) {
        for (const markerKey in markers.current) {
          markers.current[markerKey].remove();
        }
      }
      // Type assertion with proper coordinate handling
      createMarker(selectedBuyersViewRecordGeoJSON as unknown as PropertyFeature);
    }
  }, [map, selectedBuyersViewRecord, filteredProperties, router, search, listType, allProperties, allPropertiesSubmitted]);

  return null; // This component only handles custom markers, not map layers
};

export default BuyerListingsLayer;
