// @ts-ignore
import { Layer, Source } from "@spatiallaser/map";
import { isEmpty, isEqual } from "lodash";
import { useMarketplaceMapContext } from "@/contexts/MarketplaceMapContext";
import { Link, useSearch } from "@tanstack/react-router";
import {
  generateBuyerListingsGeoJSON,
  getCoordinates,
} from "@/lib/utils/generateBuyerListingsGeoJSON";
import React from "react";
import mapboxgl, {
  LngLatBoundsLike,
  MapMouseEvent,
  PopupOptions,
} from "mapbox-gl";
import { createPortal } from "react-dom";
import { cn } from "@/lib/utils";
import { convertPriceToNumber } from "@/lib/utils/stringMethods";
import { bbox, point } from "@turf/turf";
import { ChevronLeftIcon, ChevronRightIcon, XIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Feature, GeoJsonProperties, Point } from "geojson";
import { LennarSinglePropertyDataType } from "@/lib/utils/types";

const sourceId = "mkp-underwritten-properties";

const LAYER_ID = `${sourceId}-circle-layer`;

const circleStyle = {
  id: LAYER_ID,
  type: "circle",
  paint: {
    "circle-radius": 6,
    "circle-color": "#406855",
    "circle-stroke-color": "#fff",
    "circle-stroke-width": 1,
  },
};

const compact = new Intl.NumberFormat("en-US", {
  notation: "compact",
  maximumFractionDigits: 0,
});

// If you guys ditched the old map and use react-map-gl, I wouldn't need this here.
// prettier-ignore
const Popup = ( props: PopupOptions & { longitude: number; latitude: number; className?: string; onOpen?: (e: Event) => void; onClose?: (e: Event) => void; children?: React.ReactNode; }) => { const { map } = useMarketplaceMapContext(); const container = React.useMemo(() => { return document.createElement("div"); }, []); React.useEffect(() => { if (!props.className) return; container.className = props.className; }, [container, props.className]); const popup = React.useMemo(() => { const options = { ...props }; const pp = new mapboxgl.Popup(options); pp.setLngLat([props.longitude, props.latitude]); pp.once("open", (e: any) => { props.onOpen?.(e); }); return pp; }, [props.longitude, props.latitude]); React.useEffect(() => { if (!map) return; const onClose = (e: any) => { props.onClose?.(e); }; popup.on("close", onClose); popup.setDOMContent(container).addTo(map); return () => { popup.off("close", onClose); if (popup.isOpen()) { popup.remove(); } }; }, [map, popup]); return createPortal(props.children, container);};

function BuyerListingLayer2() {
  const {
    map,
    allProperties,
    allPropertiesSubmitted,
    allPropertiesBookmarked,
    selectedBuyersViewRecord,
    propertyModalTabKey,
    hasInitialized,
    setHasInitialized,
  } = useMarketplaceMapContext();

  const [clickedFeatures, setClickedFeatures] = React.useState<
    Array<Feature<Point, GeoJsonProperties>>
  >([]);
  // for zooming out centered on the previous subject property when a user clicks `return to list`
  const [previousSubjectPropertyCoordinates, setPreviousSubjectPropertyCoordinates] = React.useState<{ lat: number, lng: number } | null>(null);
  const [prevProperties, setPrevProperties] = React.useState<LennarSinglePropertyDataType[] | null>(null);

  const search = useSearch({ strict: false });
  const listType = search.listType;

  // prettier-ignore
  const properties = listType === "listings" ? allProperties : listType === "submitted" ? allPropertiesSubmitted : allPropertiesBookmarked;

  const [buyerListingsGeoJSON, uniqueLocations] = React.useMemo(() => {
    if (!properties) return [];
    const geojson = generateBuyerListingsGeoJSON({
      dataSource: properties,
      dataType: "offmarket",
    });

    const coords = {} as Record<string, any>;

    for (const feature of geojson.features) {
      if (!coords[feature.geometry.coordinates.join(",")])
        coords[feature.geometry.coordinates.join(",")] = [];

      const subjectProperty = feature.properties?.payload?.subjectProperty;
      const netSalesPrice = convertPriceToNumber(
        subjectProperty?.meta?.spec_price as string
      );
      coords[feature.geometry.coordinates.join(",")].push(netSalesPrice);
    }

    return [geojson, coords];
  }, [properties]);

  React.useEffect(() => {
    if (!isEqual(properties, prevProperties)) {
      setHasInitialized(false);
      setPrevProperties(properties);
    }
  }, [properties]);

  React.useEffect(() => {
    if (!map) return;

    const onClick = (e: MapMouseEvent) => {
      setClickedFeatures(
        e.features as Array<Feature<Point, GeoJsonProperties>>
      );
    };

    map.on("click", [LAYER_ID], onClick);
    return () => {
      map.off("click", [LAYER_ID], onClick);
    };
  }, [map]);

  React.useEffect(() => {
    if (!map) return;
    
    if (!isEmpty(selectedBuyersViewRecord)) {
      const subjectPropertyCoordinatesRaw = getCoordinates(
        selectedBuyersViewRecord,
        "offmarket"
      );
      if (propertyModalTabKey !== "comps") {
        const propertyLocation = bbox(
          point([
            subjectPropertyCoordinatesRaw.longitude,
            subjectPropertyCoordinatesRaw.latitude,
          ])
        );
        map.fitBounds(propertyLocation as LngLatBoundsLike, {
          animate: true,
          maxZoom: 16,
        });
      }
    } else if (previousSubjectPropertyCoordinates) {
      // when a user clicks `return to list`
      // Just clear the map draw when no property is selected
      // Remove automatic zoom to prevent conflicts with route-level zoom control
      map.fire("mapDraw.clear");

      // when a user clicks `return to list`, no pan just zoom out centered on the previous subject property
      map.easeTo({
        center: [previousSubjectPropertyCoordinates.lng, previousSubjectPropertyCoordinates.lat],
        zoom: 9,
        animate: true,
      });
      setPreviousSubjectPropertyCoordinates(null);
    }

    // Mark as initialized after all properties returned by /v2/properties are loaded on the map
    // use cases include: first render; filter by market
    if (!hasInitialized) {
      // Just clear the map draw when no property is selected
      // Remove automatic zoom to prevent conflicts with route-level zoom control
      map.fire("mapDraw.clear");
      // fitBounds on all properties
      if (buyerListingsGeoJSON && buyerListingsGeoJSON.features.length > 0) {
        const GeoJSONBbox = bbox(buyerListingsGeoJSON);
        map.fitBounds(GeoJSONBbox, { padding: 32, animate: false });
        setHasInitialized(true);
      }
    }
  }, [
    map,
    selectedBuyersViewRecord,
    properties,
    propertyModalTabKey,
    hasInitialized,
    previousSubjectPropertyCoordinates,
    buyerListingsGeoJSON,
  ]);

  // for zooming out centered on the previous subject property when a user clicks `return to list`
  React.useEffect(() => {
    if (selectedBuyersViewRecord) {
      setPreviousSubjectPropertyCoordinates({
        lat: selectedBuyersViewRecord.payload?.subjectProperty?.lat,
        lng: selectedBuyersViewRecord.payload?.subjectProperty?.lng,
      });
    }
  }, [selectedBuyersViewRecord]);

  React.useEffect(() => {
    if (!map || !buyerListingsGeoJSON || !uniqueLocations) return;

    let markersObject: Record<string, mapboxgl.Marker> = {};

    const renderMarkersWithinBounds = () => {
      const zoom = map.getZoom();
      const bounds = map.getBounds();

      Object.values(markersObject).forEach((marker) => {
        const el = marker.getElement();
        const handler = (el as any)?._markerClickHandler;
        if (handler) el.removeEventListener("click", handler);
        marker.remove();
      });
      markersObject = {};

      if (zoom <= 9) return;

      for (let i = 0; i < buyerListingsGeoJSON.features.length; i++) {
        const feature = buyerListingsGeoJSON.features[i] as Feature<
          Point,
          GeoJsonProperties
        >;
        const [lng, lat] = feature.geometry.coordinates;
        const locationKey = feature.geometry.coordinates.join(",");

        if (!bounds?.contains([lng, lat])) continue;
        if (lng === search?.lng && lat === search?.lat) continue;

        const markerColor =
          feature.properties?.decision === "reject" ? "#dc2626" : "#16a34a";

        const handleClick = () => {
          if (typeof lng !== "number" || typeof lat !== "number") return;

          const lngLat = new mapboxgl.LngLat(lng, lat);
          map.fire("click", {
            lngLat: lngLat,
            point: map.project([lng, lat]),
            // will log ERROR in console because target not defined but just ignore it mfer whoever you are
          } as MapMouseEvent);
        };

        const markerCSS =
          "display: flex; justify-content: center; align-items: center; cursor: pointer;";
        const markerInnerHtml = (
          childrenHTML: string
        ) => `<div class='circle-marker' style='
            background-color: ${markerColor}; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            padding: 4px 8px;
            color: white;
            font-size: 12px;
            font-weight: bold;
            border-radius: 4px;
            transition: transform 0.2s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
          '>${childrenHTML}</div>`;

        if (uniqueLocations?.[locationKey]?.length > 1) {
          if (markersObject[locationKey]) continue;

          const locationPrices = uniqueLocations[locationKey].sort(
            (a: number, b: number) => a - b
          );

          const el = document.createElement("div");

          el.addEventListener("click", handleClick);
          (el as any)._markerClickHandler = handleClick;

          el.style.cssText = markerCSS;
          el.innerHTML = markerInnerHtml(
            `<div>${uniqueLocations[locationKey].length} Homes</div><div>$${compact.format(locationPrices[0])} - $${compact.format(locationPrices[locationPrices.length - 1])}</div>`
          );

          const marker = new mapboxgl.Marker({ element: el })
            .setLngLat([lng, lat])
            .addTo(map);

          markersObject[locationKey] = marker;
        } else {
          const subjectProperty = feature.properties?.payload?.subjectProperty;
          const netSalesPrice = compact.format(
            convertPriceToNumber(subjectProperty?.meta?.spec_price as string)
          );

          const el = document.createElement("div");

          el.addEventListener("click", handleClick);
          (el as any)._markerClickHandler = handleClick;

          el.style.cssText = markerCSS;
          el.innerHTML = markerInnerHtml(`$${netSalesPrice}`);

          const marker = new mapboxgl.Marker({ element: el })
            .setLngLat([lng, lat])
            .addTo(map);

          markersObject[locationKey] = marker;
        }
      }
    };

    renderMarkersWithinBounds();
    map.on("moveend", renderMarkersWithinBounds);
    return () => {
      map.off("moveend", renderMarkersWithinBounds);
      Object.values(markersObject).forEach((marker) => {
        const el = marker.getElement();
        const handler = (el as any)?._markerClickHandler;
        if (handler) el.removeEventListener("click", handler);
        marker.remove();
      });
    };
  }, [map, buyerListingsGeoJSON, uniqueLocations, search]);

  const popupContent = React.useMemo(() => {
    const content = [];

    for (const feature of clickedFeatures) {
      if (!feature.properties?.payload) continue;
      const payload = JSON.parse(feature.properties.payload);
      const subjectProperty = payload?.subjectProperty;

      if (!subjectProperty) continue;
      const proFormaAllValues = payload.proforma?.buyAndHold;

      const {
        placekey,
        address,
        beds,
        baths,
        sqft,
        city,
        state,
        zipCode,
        lat,
        lng,
        yearbuilt,
      } = subjectProperty;

      // for Lennar
      const netSalesPrice = convertPriceToNumber(
        subjectProperty?.meta?.spec_price as string
      );
      const listingPriceInUse = netSalesPrice;
      const salesInUse = subjectProperty.meta?.spec_price || 0;
      const rentInUse = proFormaAllValues?.["Projected Monthly Rent"] || 0;
      const capRate = proFormaAllValues?.["Projected Yield on Bid Price"] || 0;

      const propertyImage =
        subjectProperty?.meta?.spec_image_elevation?.length > 0
          ? subjectProperty?.meta?.spec_image_elevation?.[0]
          : subjectProperty?.meta?.plan_image_elevation?.[0];
      const fallbackImage = subjectProperty?.meta?.plan_image_elevation?.[0];

      content.push({
        propertyImage,
        fallbackImage,
        placekey,
        address,
        beds,
        baths,
        sqft,
        city,
        state,
        zipCode,
        lat,
        lng,
        yearbuilt,
        netSalesPrice,
        listingPriceInUse,
        salesInUse,
        rentInUse,
        capRate,
        linkProps: {
          to: "/properties/$id/proForma",
          params: { id: feature.properties?.property_id },
          search: (prev: Record<string, any>) => ({
            ...prev,
            placekey: placekey,
            lat: subjectProperty?.lat,
            lng: subjectProperty?.lng,
            streetnum: feature.properties?.full_address?.split(" ")[0],
            address: feature.properties?.full_address,
            city: feature.properties?.city,
            state: feature.properties?.state,
            zip_code: feature.properties?.postal_code,
            latitude: lat?.toString(),
            longitude: lng?.toString(),
            beds: beds?.toString(),
            baths: baths?.toString(),
            sqft: sqft?.toString(),
            yearbuilt: yearbuilt?.toString(),
          }),
        },
      });
    }

    return content.reverse();
  }, [clickedFeatures]);

  return (
    <React.Fragment>
      {popupContent.length > 0 && (
        <MapPopupWithNav
          popupContent={popupContent}
          onClosePopup={() => setClickedFeatures([])}
        />
      )}
      <Source
        id={sourceId}
        type="geojson"
        data={
          buyerListingsGeoJSON || { type: "FeatureCollection", features: [] }
        }
      >
        <Layer {...circleStyle} />
      </Source>
    </React.Fragment>
  );
}

function MapPopupWithNav(props: {
  popupContent: any[];
  onClosePopup: () => void;
}) {
  const { popupContent, onClosePopup } = props;
  const [currentIndex, setCurrentIndex] = React.useState(0);
  const total = popupContent.length;

  const current = popupContent[currentIndex];

  React.useEffect(() => {
    setCurrentIndex(0);
    return () => {
      setCurrentIndex(0);
    };
  }, [popupContent]);

  if (total === 0 || !current) return null;
  return (
    <Popup
      longitude={current.lng}
      latitude={current.lat}
      closeButton={false}
      closeOnClick={false}
      className={cn("[&>*:nth-child(2)]:bg-background [&>*:nth-child(2)]:p-0")}
    >
      <div className="relative p-0 m-0 bg-background">
        <div className="flex flex-row justify-between items-center">
          {total > 1 && (
            <div className="flex flex-row items-center gap-0.5">
              <Button
                onClick={() =>
                  setCurrentIndex((prev) => (prev - 1 + total) % total)
                }
                variant={"ghost"}
                size={"icon"}
                className="p-1 font-semibold cursor-pointer"
              >
                <ChevronLeftIcon />
              </Button>
              <Button
                onClick={() => setCurrentIndex((prev) => (prev + 1) % total)}
                variant={"ghost"}
                size={"icon"}
                className="p-1 font-semibold cursor-pointer"
              >
                <ChevronRightIcon />
              </Button>
              <span>
                {currentIndex + 1} / {total}
              </span>
            </div>
          )}
        </div>
        <Button
          variant={"ghost"}
          size={"icon"}
          className="px-2 py-1 font-semibold cursor-pointer absolute top-0 right-0"
          onClick={() => onClosePopup()}
        >
          <XIcon />
        </Button>

        <div className="p-0 m-0">
          <div className="m-0 mb-2 p-0">
            <Link {...current.linkProps}>
              <img
                src={current.propertyImage || ""}
                alt="Property"
                className="w-full h-[120px] object-cover rounded-[4px] border-none outline-none shadow-none block m-0 p-0"
                onError={(e) =>
                  (e.currentTarget.src = current.fallbackImage || "")
                }
              />
            </Link>
          </div>
          <div className="p-1 pt-0">
            <div>
              <strong>{current.beds || "N/A"}</strong> bds |{" "}
              <strong>{current.baths || "N/A"}</strong> ba |{" "}
              <strong>
                {current.sqft
                  ? (current.sqft as number).toLocaleString()
                  : "N/A"}
              </strong>{" "}
              sqft | cap rate:{" "}
              <strong>
                {typeof current.capRate === "number"
                  ? (Math.round(current.capRate * 10000) / 100).toFixed(1)
                  : "N/A"}
                %
              </strong>
            </div>
            <div>{current.address || "N/A"}</div>
            <div>
              {current.city || "N/A"}, {current.state || "N/A"}{" "}
              {current.zipCode || "N/A"}
            </div>
            <div>
              Price: $
              {Number(current.salesInUse).toLocaleString("en-US", {
                maximumFractionDigits: 0,
              })}{" "}
              | Rent: $
              {Number(current.rentInUse).toLocaleString("en-US", {
                maximumFractionDigits: 0,
              })}
            </div>
          </div>
        </div>
      </div>
    </Popup>
  );
}

export default BuyerListingLayer2;
