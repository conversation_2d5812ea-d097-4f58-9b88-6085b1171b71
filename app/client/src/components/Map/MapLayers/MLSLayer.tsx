import { useState, useEffect, useRef, useCallback } from 'react';
import { renderToString } from 'react-dom/server';
import { Source, Layer } from '@spatiallaser/map';
import { geojsonTemplate, MAP_LAYER_NAME_BASE } from '@/constants';
import {
  setPriceMarkers,
  removePriceMarkers,
  generateGeoJSONData,
} from '../MapUtility/general';
import { isEmpty } from 'lodash';
import { cityBasedZipCode } from '@/lib/utils/geography';
import { propertyDetailPopup, locatePropertyHandler } from '../MapCMA.tsx';
import PropertyDetailPopup from '../MapAdditionalComponents/PropertyDetailPopup.tsx';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { getMLSPropertyImages } from '@/lib/query/get-mls-property-images';

const sourceId = MAP_LAYER_NAME_BASE.mls;

const zoomLevelToShowParcelAVM = 17;

// Define a zoom level threshold for showing price markers
const zoomLevelToShowPriceMarkers = 12;

const circleStyle = {
  id: `${sourceId}LayerCircle`,
  type: 'circle',
  filter: ['!', ['has', 'point_count']],
  paint: {
    'circle-radius': 8,
    'circle-color': '#17c220',
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};

const symbolStyle = {
  id: `${sourceId}LayerSymbol`,
  type: 'symbol',
  filter: ['!', ['has', 'point_count']],
  layout: {
    'text-field': ['concat', '$', ['get', 'latestPrice']],
    'text-variable-anchor': ['center'],
    'text-justify': 'center',
    'text-radial-offset': 1,
    'text-font': ['Source Sans Pro Bold', 'Open Sans Bold'],
    'text-size': [
      'interpolate',
      ['linear'],
      ['zoom'],
      zoomLevelToShowParcelAVM,
      15,
      22,
      20,
    ],
    'icon-allow-overlap': true,
    'text-allow-overlap': true,
  },
  paint: {
    'text-color': '#fff',
    'text-halo-color': '#000',
    'text-halo-width': 20,
    'text-opacity': 0,
    'icon-opacity': 0,
  },
};

const clusterStyle = {
  id: `${sourceId}LayerClusters`,
  type: 'circle',
  filter: ['has', 'point_count'],
  paint: {
    'circle-color': '#A9BBC0',
    'circle-radius': [
      'interpolate',
      ['linear'],
      ['zoom'],
      0,
      ['max', ['*', ['^', ['get', 'point_count'], 0.5], 0.1], 12],
    ],
    'circle-opacity': 0.75,
    'circle-stroke-width': 1,
    'circle-stroke-color': 'rgba(255,255,255,1)',
  },
};

const clusterSymbolStyle = {
  id: `${sourceId}LayerClustersPointCount`,
  type: 'symbol',
  filter: ['has', 'point_count'],
  layout: {
    'text-font': ['Open Sans Bold'],
    'text-field': '{point_count}',
    'text-size': 14,
    'text-justify': 'auto',
  },
  paint: {
    'text-color': 'rgba(0,0,0,1)',
  },
};

let priceMarkersMLS = {};

export const removeMLSPriceMarkers = () => {
  if (!isEmpty(priceMarkersMLS)) {
    priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
  }
};

export const generateMLSPriceMarkers = (map, currentMLSGeoJSON) => {
  if (currentMLSGeoJSON && currentMLSGeoJSON.features.length > 0) {
    if (!isEmpty(priceMarkersMLS)) {
      priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
    }
    priceMarkersMLS = setPriceMarkers(
      map,
      currentMLSGeoJSON.features,
      'mlsid',
      'latestPrice',
      sourceId,
    );
  }
};

function MLSLayer() {
  const {
    map,
    currentMLSGeoJSON,
    currentMLSPropertiesFiltered,
    showPriceMarkers,
    selectedBuyersViewRecord,
    propertyModalTabKey,
    searchingMode,
    selectedRowKeysMLSLease,
    setSelectedRowKeysMLSLease,
    selectedRowKeysMLSSale,
    setSelectedRowKeysMLSSale,
  } = useMarketplaceMapContext();

  const mouseOnTopRef = useRef(false);
  const mouseOnTopOfPopupRef = useRef(false);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (!isEmpty(priceMarkersMLS)) {
        priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
      }
    };
  }, []);

  const showHidePriceMarkers = useCallback(() => {
    if (
      map &&
      showPriceMarkers &&
      currentMLSGeoJSON &&
      currentMLSGeoJSON.features.length > 0 &&
      !isEmpty(selectedBuyersViewRecord) &&
      ['comps'].includes(propertyModalTabKey)
    ) {
      // Check current zoom level
      const currentZoom = map.getZoom();
      console.log('Current zoom level:', currentZoom);

      if (currentZoom >= zoomLevelToShowPriceMarkers) {
        console.log(
          'Zoom level sufficient and tab is valid, showing price markers',
        );
        // Clean up existing markers first
        if (!isEmpty(priceMarkersMLS)) {
          priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
        }
        // Create new markers
        priceMarkersMLS = setPriceMarkers(
          map,
          currentMLSGeoJSON.features,
          'mlsid',
          'latestPrice',
          sourceId,
        );
        console.log('priceMarkersMLS', priceMarkersMLS);
      } else {
        console.log('Zoom level too low, removing price markers');
        priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
      }
    } else {
      console.log(
        'Conditions not met, removing price markers');
      priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
    }
  }, [map]);

  useEffect(() => {
    if (!map) return;

    const mouseEnter = (e) => {
      // refer to https://docs.mapbox.com/mapbox-gl-js/example/popup-on-hover/
      const coordinates = e.features[0].geometry.coordinates.slice();

      while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
        coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
      }

      const mlsProperty = e.features[0].properties;

      let key = mlsProperty.listingkey;
      mouseOnTopRef.current = true;
      mouseOnTopOfPopupRef.current = true;

      if (
        ['sanantonio', 'san antonio'].includes(
          mlsProperty.metro.toLowerCase(),
        ) ||
        cityBasedZipCode(mlsProperty.zipcode) === 'sanantonio'
      ) {
        key = mlsProperty.mlsid;
      }
    
      getMLSPropertyImages({
        listingKey: key,
        city: cityBasedZipCode(mlsProperty.zipcode),
      }).then((imageURL) => {
        console.log(imageURL);
        const htmlText = renderToString(
          <PropertyDetailPopup
            hoverPropertyDetails={{
              ...mlsProperty,
              type: 'MLS',
              MLSPopupImageSrc: imageURL,
              searchingMode,
            }}
          />,
        );

        if (mouseOnTopRef.current) {
          propertyDetailPopup
            .setLngLat(coordinates)
            .setHTML(htmlText)
            .addTo(map);

          const popup = propertyDetailPopup.getElement();
          // const locateButton = popup.querySelector('#locatePropertyButton');
          // const removeButton = popup.querySelector('#removePropertyButton');

          popup.addEventListener('mouseenter', () => {
            mouseOnTopOfPopupRef.current = true;
          });
          popup.addEventListener('mousemove', () => {
            mouseOnTopOfPopupRef.current = true;
          });
          popup.addEventListener('mouseleave', () => {
            mouseOnTopOfPopupRef.current = false;
            if (propertyDetailPopup.getElement() && !mouseOnTopRef.current) {
              propertyDetailPopup.remove();
            }
          });

          // locateButton.addEventListener('click', () => {
          //   locatePropertyHandler(
          //     locateButton.dataset.propertyType,
          //     locateButton.dataset.propertyId,
          //   );
          // });

          // removeButton.addEventListener('click', () => {
          //   if (removeButton.dataset.propertyType != 'MLS') return;

          //   if (searchingMode === 'Lease') {
          //     setSelectedRowKeysMLSLease(selectedRowKeysMLSLease.filter(id => id !== removeButton.dataset.propertyId));
          //   } else {
          //     setSelectedRowKeysMLSSale(selectedRowKeysMLSSale.filter(id => id !== removeButton.dataset.propertyId));
          //   }
          //   propertyDetailPopup.remove();
          // });
        }
      });
    };

    const mouseMove = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}LayerSymbol`],
      });
      if (features.length > 0) {
        mouseOnTopRef.current = true;
      } else {
        mouseOnTopRef.current = false;
      }
    };

    const mouseLeave = (e) => {
      mouseOnTopRef.current = false;

      if (
        propertyDetailPopup.getElement() &&
        mouseOnTopOfPopupRef.current === false
      ) {
        // propertyDetailPopup.remove();
        if (!e.originalEvent || !e.originalEvent.relatedTarget) {
          propertyDetailPopup.remove();
        }
        if (
          e.originalEvent &&
          e.originalEvent.relatedTarget &&
          !e.originalEvent.relatedTarget.classList.contains('mapboxgl-popup')
        ) {
          propertyDetailPopup.remove();
        }
      }
    };

    const styleLoad = () => {
      showHidePriceMarkers();
    };

    // const zoomEnd = () => {
    //   // Copy over markers when map style changes
    //   if (currentMapThemeOptionRef.current === 'Automatic') {
    //     map.on('style.load', styleLoad);
    //   }
    // };

    map.on('mouseenter', `${sourceId}LayerSymbol`, mouseEnter);
    map.on('mousemove', `${sourceId}LayerSymbol`, mouseMove);
    map.on('mouseleave', `${sourceId}LayerSymbol`, mouseLeave);
    // map.on('zoomend', zoomEnd);
    return () => {
      map.off('mouseenter', `${sourceId}LayerSymbol`, mouseEnter);
      map.off('mousemove', `${sourceId}LayerSymbol`, mouseMove);
      map.off('mouseleave', `${sourceId}LayerSymbol`, mouseLeave);
      // map.off('zoomend', zoomEnd);
      map.off('style.load', styleLoad);
    };
  }, [map]);

  useEffect(() => {
    console.log('useEffect - currentMLSGeoJSON', currentMLSGeoJSON);
    showHidePriceMarkers();
  }, [currentMLSGeoJSON, selectedBuyersViewRecord, propertyModalTabKey, showPriceMarkers, selectedRowKeysMLSLease, selectedRowKeysMLSSale]);

  // console.log('MLSLayer - currentMLSGeoJSON', currentMLSGeoJSON);

  return (
    <Source id={sourceId} type="geojson" data={currentMLSGeoJSON}>
      <Layer {...circleStyle} />
      <Layer {...symbolStyle} />
      <Layer {...clusterStyle} />
      <Layer {...clusterSymbolStyle} />
    </Source>
  );
}

export default MLSLayer;
