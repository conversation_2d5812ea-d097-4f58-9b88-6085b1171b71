import { useEffect, useRef, useMemo } from 'react';
import { Source, Layer } from '@spatiallaser/map';
import { MAP_LAYER_NAME_BASE } from '@/constants';
import { default as turf_bbox } from '@turf/bbox';
import { point } from '@turf/helpers';
import { isEmpty } from 'lodash';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { generateBuyerListingsGeoJSON, getCoordinates } from '@/lib/utils/generateBuyerListingsGeoJSON';
import { usePropertiesFilterState } from '@/hooks/usePropertiesFilter';
import { filterProperties } from '@/lib/utils/filterProperties';
import { useSearch } from '@tanstack/react-router';

const sourceId = MAP_LAYER_NAME_BASE.singlePortfolio;

const zoomLevelToShowParcelAVM = 17;
const zoomLevelToShowClusters = 9;

const sourceProps = {
  cluster: true,
  clusterMaxZoom: zoomLevelToShowClusters,
  clusterRadius: 75,
};

// add layer for singlePortfolio
const circleStyle = {
  id: `${sourceId}LayerCircle`,
  type: 'circle',
  filter: ['!', ['has', 'point_count']],
  paint: {
    'circle-radius': 8,
    'circle-color': '#17c220',
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};

const symbolStyle = {
  id: `${sourceId}LayerSymbol`,
  type: 'symbol',
  filter: ['!', ['has', 'point_count']],
  layout: {
    'text-field': ['concat', '$', ['get', 'rent']],
    // ...listingSymbolLayerLayout,
    'text-variable-anchor': ['center'],
    'text-justify': 'center',
    'text-radial-offset': 1,
    // On how to use custom fonts in Mapbox, see: https://github.com/mapbox/mapbox-gl-js/issues/6666
    'text-font': [
      'Source Sans Pro Bold',
      'Open Sans Bold', // fallback font
    ],
    'text-size': [
      'interpolate',
      ['linear'],
      ['zoom'],
      zoomLevelToShowParcelAVM,
      15,
      22,
      20,
    ],
    'icon-allow-overlap': true,
    'text-allow-overlap': true,
  },
  paint: {
    'text-color': '#fff',
    'text-halo-color': '#000',
    'text-halo-width': 20, // use halo instead of an icon for text background
    'text-opacity': 0,
    'icon-opacity': 0,
  },
};

// singlePortfolio layer cluster circle
const circleClusterStyle = {
  id: `${sourceId}LayerClusters`,
  type: 'circle',
  filter: ['has', 'point_count'],
  paint: {
    'circle-color': '#A9BBC0',
    'circle-radius': [
      'interpolate',
      ['linear'],
      ['zoom'],
      0,
      ['max', ['*', ['^', ['get', 'point_count'], 0.5], 0.1], 12],
    ],
    'circle-opacity': 0.75,
    'circle-stroke-width': 1,
    'circle-stroke-color': 'rgba(255,255,255,1)',
  },
};

// singlePortfolio layer cluster point count
const symbolClusterStyle = {
  id: `${sourceId}LayerClustersPointCount`,
  type: 'symbol',
  filter: ['has', 'point_count'],
  layout: {
    'text-font': ['Open Sans Bold'],
    'text-field': '{point_count}',
    'text-size': 14,
    'text-justify': 'auto',
  },
  paint: {
    'text-color': 'rgba(0,0,0,1)',
  },
};

function SinglePortfolioLayer() {

  // get listType from search
  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;

  const { map, allProperties, allPropertiesSubmitted, currentMapThemeOption, selectedBuyersViewRecord, propertyModalTabKey } = useMarketplaceMapContext();

  const { selectedMarket, selectedPrice, selectedBeds, selectedBaths, minCapRate } = usePropertiesFilterState();
  const filteredProperties = useMemo(() => {
    return filterProperties({
      properties: listType === 'listings' ? allProperties : allPropertiesSubmitted,
      selectedMarket,
      selectedPrice,
      selectedBeds,
      selectedBaths,
      minCapRate
    });
  }, [allProperties, allPropertiesSubmitted, selectedMarket, selectedPrice, selectedBeds, selectedBaths, minCapRate, listType]);

  const currentMapThemeOptionRef = useRef(currentMapThemeOption);
  currentMapThemeOptionRef.current = currentMapThemeOption;
  const selectedBuyersViewRecordRef = useRef(selectedBuyersViewRecord);
  selectedBuyersViewRecordRef.current = selectedBuyersViewRecord;

  useEffect(() => {
    if (!map) return;

    if (!isEmpty(selectedBuyersViewRecord)) {
      const subjectPropertyCoordinatesRaw = getCoordinates(selectedBuyersViewRecord, 'offmarket');
      if (propertyModalTabKey !== 'comps') {
        const propertyLocation = turf_bbox(point([subjectPropertyCoordinatesRaw.longitude, subjectPropertyCoordinatesRaw.latitude]));
        map.fitBounds(propertyLocation, {
          // padding: 32,
          animate: true,
          maxZoom: 18,
        });
      }
    } else {
      const currentPortfolioGeoJSON = generateBuyerListingsGeoJSON({
        dataSource: filteredProperties,
        dataType: 'offmarket',
      });
      if (
        currentPortfolioGeoJSON.features &&
        currentPortfolioGeoJSON.features.length > 0
      ) {
        const GeoJSONBbox = turf_bbox(currentPortfolioGeoJSON);
        map.fitBounds(GeoJSONBbox, { padding: 32, animate: true });
      }
      // remove subject property marker
      map.fire('mapDraw.clear');
      // console.log('mapDraw.clear fired');
    }
  }, [selectedBuyersViewRecord, filteredProperties, propertyModalTabKey]);

  return (
    <Source
      id={sourceId}
      type="geojson"
      data={generateBuyerListingsGeoJSON({
        dataSource: filteredProperties,
        dataType: 'offmarket',
      })}
      {...sourceProps}
    >
      <Layer {...circleStyle} />
      <Layer {...symbolStyle} />
      <Layer {...circleClusterStyle} />
      <Layer {...symbolClusterStyle} />
    </Source>
  );
}

export default SinglePortfolioLayer;
