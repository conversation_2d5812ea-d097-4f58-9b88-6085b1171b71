import { useState, useEffect, useRef, useCallback } from 'react';
import { renderToString } from 'react-dom/server';
import { Source, Layer } from '@spatiallaser/map';
import { MAP_LAYER_NAME_BASE, geojsonTemplate } from '@/constants';
import {
  setPriceMarkers,
  removePriceMarkers,
  generateGeoJSONData,
} from '../MapUtility/general';
import { isEmpty } from 'lodash';
import { propertyDetailPopup, locatePropertyHandler } from '../MapCMA.tsx';
import PropertyDetailPopup from '../MapAdditionalComponents/PropertyDetailPopup.tsx';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';

const sourceId = MAP_LAYER_NAME_BASE.newbuilds;

const circleStyle = {
  id: `${sourceId}Layer`,
  type: 'circle',
  paint: {
    'circle-radius': 8,
    'circle-color': '#ED6041',
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};

let priceMarkers: Record<string, unknown> = {};

export const removeHotPadsPriceMarkers = () => {
  if (!isEmpty(priceMarkers)) {
    priceMarkers = removePriceMarkers(priceMarkers);
  }
};

export const generateNewBuildPriceMarkers = (map: any, geojson: any) => {
  if (geojson && geojson.features.length > 0) {
    if (!isEmpty(priceMarkers)) {
      priceMarkers = removePriceMarkers(priceMarkers);
    }
    priceMarkers = setPriceMarkers(
      map,
      geojson.features,
      'base_id',
      'price',
      sourceId,
    );
  }
};

function NewBuildsLayer() {
  const {
    map,
    selectedRowKeysNewBuilds,
    currentNewBuildsProperties,
    showPriceMarkers,
    currentMapThemeOption,
    setSelectedRowKeysNewBuilds,
  } = useMarketplaceMapContext();

  const [geojonData, setGeojsonData] = useState(geojsonTemplate);

  const currentMapThemeOptionRef = useRef(currentMapThemeOption);
  currentMapThemeOptionRef.current = currentMapThemeOption;
  const mouseOnTopRef = useRef(false);
  const mouseOnTopOfPopupRef = useRef(false);
  const selectedRowKeysNewBuildsRef = useRef(selectedRowKeysNewBuilds);
  selectedRowKeysNewBuildsRef.current = selectedRowKeysNewBuilds;

  const showHidePriceMarkers = useCallback(() => {
    if (
      map &&
      showPriceMarkers &&
      geojonData &&
      geojonData.features.length > 0
    ) {
      if (!isEmpty(priceMarkers)) {
        priceMarkers = removePriceMarkers(priceMarkers);
      }
      priceMarkers = setPriceMarkers(
        map,
        geojonData.features,
        'base_id',
        'price',
        sourceId,
      );
    } else {
      priceMarkers = removePriceMarkers(priceMarkers);
    }
  }, [map, showPriceMarkers, geojonData]);

  const showHidePriceMarkersRef = useRef(showHidePriceMarkers);
  showHidePriceMarkersRef.current = showHidePriceMarkers;

  useEffect(() => {
    if (!map) return;

    const mouseEnter = (e: any) => {
      e.originalEvent.preventDefault();
      if (propertyDetailPopup.getElement() && mouseOnTopRef.current) {
        return;
      }
      const coordinates = e.features[0].geometry.coordinates.slice();

      const htmlText = renderToString(
        <PropertyDetailPopup
          hoverPropertyDetails={{
            ...e.features[0].properties,
            type: sourceId,
          }}
        />,
      );

      mouseOnTopRef.current = true;

      propertyDetailPopup.setLngLat(coordinates).setHTML(htmlText).addTo(map);

      const popup = propertyDetailPopup.getElement();
      const locateButton = popup?.querySelector('#locatePropertyButton') as HTMLElement;
      const removeButton = popup?.querySelector('#removePropertyButton') as HTMLElement;

      popup?.addEventListener('mouseenter', () => {
        mouseOnTopOfPopupRef.current = true;
      });
      popup?.addEventListener('mousemove', () => {
        mouseOnTopOfPopupRef.current = true;
      });
      popup?.addEventListener('mouseleave', () => {
        mouseOnTopOfPopupRef.current = false;
        if (
          propertyDetailPopup.getElement() &&
          mouseOnTopRef.current === false
        ) {
          propertyDetailPopup.remove();
        }
      });

      locateButton?.addEventListener('click', () => {
        locatePropertyHandler(
          locateButton.dataset.propertyType || '',
          locateButton.dataset.propertyId || '',
        );
      });

      removeButton?.addEventListener('click', () => {
        if (removeButton.dataset.propertyType != sourceId) return;

        setSelectedRowKeysNewBuilds(
          selectedRowKeysNewBuildsRef.current.filter(
            (id) => id !== removeButton.dataset.propertyId,
          ),
        );
        propertyDetailPopup.remove();
      });
    };

    const mouseLeave = (e: any) => {
      mouseOnTopRef.current = false;
      if (
        propertyDetailPopup.getElement() &&
        mouseOnTopOfPopupRef.current === false
      ) {
        if (!e.originalEvent || !e.originalEvent.relatedTarget) {
          propertyDetailPopup.remove();
        }
        if (
          e.originalEvent &&
          e.originalEvent.relatedTarget &&
          !e.originalEvent.relatedTarget.classList.contains('mapboxgl-popup')
        ) {
          propertyDetailPopup.remove();
        }
      }
    };

    const styleLoad = () => {
      showHidePriceMarkersRef.current();
    };

    const zoomEnd = () => {
      // Copy over markers when map style changes
      if (currentMapThemeOptionRef.current === 'Automatic') {
        map.on('style.load', styleLoad);
      }
    };

    map.on('mouseenter', `${sourceId}Layer`, mouseEnter);
    map.on('mouseleave', `${sourceId}Layer`, mouseLeave);
    map.on('zoomend', zoomEnd);
    return () => {
      map.off('mouseenter', `${sourceId}Layer`, mouseEnter);
      map.off('mouseleave', `${sourceId}Layer`, mouseLeave);
      map.off('zoomend', zoomEnd);
      map.off('style.load', styleLoad);
    };
  }, [map, setSelectedRowKeysNewBuilds]);

  useEffect(() => {
    showHidePriceMarkersRef.current();
  }, [showPriceMarkers]);

  useEffect(() => {
    if (selectedRowKeysNewBuilds.length > 0) {
      const geoData = generateGeoJSONData(
        currentNewBuildsProperties,
        selectedRowKeysNewBuilds,
        'SFR',
      );
      setGeojsonData(geoData);
    } else {
      setGeojsonData(geojsonTemplate);
    }
  }, [selectedRowKeysNewBuilds, currentNewBuildsProperties]);

  useEffect(() => {
    showHidePriceMarkersRef.current();
  }, [geojonData]);

  return (
    <Source id={sourceId} type="geojson" data={geojonData}>
      <Layer {...circleStyle} />
    </Source>
  );
}

export default NewBuildsLayer;
