import { useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { dateFormat } from '@/constants';
import { default as turf_circle } from '@turf/circle';
import { default as turf_bbox } from '@turf/bbox';
import { point } from '@turf/helpers';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { geojsonTemplate } from '@/constants';

function SelectRadius() {
  const {
    map,
    eventCoordinates,
    currentRadiusMile,
    currentStatusMLS,
    searchingMode,
    currentStartMLS,
    currentEndMLS,
    expDateFilterOn,
    shouldDisableCMA,
    propertyModalTabKey,
    setCurrentRadiusMile,
    setEventCoordinates,
    setCircleBbox,
    setSubjectPropertyParcelData,
    setCurrentParcelOwnerSummary,
    setCurrentBTOwnedProperties,
    setCurrentBTOwnedGeoJSON,
    setCurrentNationalOperatorsProperties,
    setCurrentNationalOperatorsPropertiesFiltered,
    setCurrentNationalOperatorsGeoJSON,
    setCurrentHotPadsProperties,
    setCurrentHotPadsPropertiesFiltered,
    setCurrentHotPadsGeoJSON,
    setCurrentMLSProperties,
    setCurrentMLSPropertiesFiltered,
    setCurrentMLSGeoJSON,
    setCurrentMultiFamilyProperties,
    setCurrentMultiFamilyGeoJSON,
  } = useMarketplaceMapContext();

  const eventCoordinatesRef = useRef(eventCoordinates);
  eventCoordinatesRef.current = eventCoordinates;
  const currentStatusMLSRef = useRef(currentStatusMLS);
  currentStatusMLSRef.current = currentStatusMLS;
  const searchingModeRef = useRef(searchingMode);
  searchingModeRef.current = searchingMode;
  const currentStartMLSRef = useRef(currentStartMLS);
  currentStartMLSRef.current = currentStartMLS;
  const currentEndMLSRef = useRef(currentEndMLS);
  currentEndMLSRef.current = currentEndMLS;
  const expDateFilterOnRef = useRef(expDateFilterOn);
  expDateFilterOnRef.current = expDateFilterOn;

  const [prevEventCoordinates, setPrevEventCoordinates] = useState<[number, number]>([0, 0]);

  useEffect(() => {
    if (!map) return;

    if (shouldDisableCMA) return;

    map.on('selectRadius.radius', (e: { payload: { currentRadiusMile: number } }) => {
      const radius = e.payload.currentRadiusMile;
      setCurrentRadiusMile(radius);
      
      if (
        eventCoordinatesRef.current &&
        eventCoordinatesRef.current.length === 2
      ) {
        // TODO: Implement getAllPropertyData functionality
        // This was previously handled by Redux dispatch
        // getAllPropertyData({
        //   mode: 'change radius',
        //   lng: eventCoordinatesRef.current[0],
        //   lat: eventCoordinatesRef.current[1],
        //   status: currentStatusMLSRef.current,
        //   propertyType: isLeaseModeRef.current
        //     ? 'Residential Lease'
        //     : 'Residential',
        //   startDate: dayjs(currentStartMLSRef.current).format(dateFormat),
        //   endDate: dayjs(currentEndMLSRef.current).format(dateFormat),
        //   distance: radius * 1609.34,
        //   exists: currentStatusMLSRef.current,
        //   expDateFilterOn: expDateFilterOnRef.current ? 'yes' : 'no',
        // });
      }
    });

    map.on('selectRadius.clear', () => {
      // Clear all the state variables
      setCircleBbox([]);
      setEventCoordinates([]);
      setSubjectPropertyParcelData({});
      setCurrentParcelOwnerSummary({});
      setCurrentBTOwnedProperties([]);
      setCurrentBTOwnedGeoJSON(geojsonTemplate);
      setCurrentNationalOperatorsProperties([]);
      setCurrentNationalOperatorsPropertiesFiltered([]);
      setCurrentNationalOperatorsGeoJSON(geojsonTemplate);
      setCurrentHotPadsProperties([]);
      setCurrentHotPadsPropertiesFiltered([]);
      setCurrentHotPadsGeoJSON(geojsonTemplate);
      setCurrentMLSProperties([]);
      setCurrentMLSPropertiesFiltered([]);
      setCurrentMLSGeoJSON(geojsonTemplate);
      setCurrentMultiFamilyProperties([]);
      setCurrentMultiFamilyGeoJSON(geojsonTemplate);
    });
  }, [map, shouldDisableCMA]);

  useEffect(() => {
    if (!map) return;

    if (shouldDisableCMA) return;

    const setMapCoordinateAndRadius = () => {
      if (
        eventCoordinates.length > 0 &&
        eventCoordinates.every((x: number) => !isNaN(x))
      ) {
        if (eventCoordinates[0] !== prevEventCoordinates[0] || eventCoordinates[1] !== prevEventCoordinates[1]) {
          setPrevEventCoordinates(eventCoordinates);
          map.fire('selectRadius.setEventCoordinates', {
            payload: {
              eventCoordinates: eventCoordinates,
              propertyModalTabKey: propertyModalTabKey,
            },
          });
        }
        if (
          propertyModalTabKey === 'comps' 
          // ||
          // propertyModalTabKey === 'proForma' 
          // ||
          // propertyModalTabKey === 'statistics' ||
          // propertyModalTabKey === 'newbuilds' ||
          // propertyModalTabKey === 'nearby'
        ) {
          // this fix to check if style is loaded should be done in the map library
          if (map.style && map.style._loaded) {
            map.fire('selectRadius.setRadius', {
              payload: { 
                currentRadiusMile: currentRadiusMile,
                propertyModalTabKey: propertyModalTabKey,
              },
            });
          } else {
            map.once('styledata', () => {
              map.fire('selectRadius.setRadius', {
                payload: { 
                  currentRadiusMile: currentRadiusMile,
                  propertyModalTabKey: propertyModalTabKey,
                },
              });
            });
          }
          map.fire('selectRadius.showCircle');
        } else {
          map.fire('selectRadius.hideCircle');
        }
      } else {
        map.fire('selectRadius.setEventCoordinates', {
          payload: {
            eventCoordinates: [],
          },
        });
      }
    };

    if (map.style && map.style._loaded && map.isStyleLoaded()) {
      if (map.isMoving()) {
        map.once('moveend', () => {
          setMapCoordinateAndRadius();
        });
      } else {
        setMapCoordinateAndRadius();
      }
    } else {
      map.once('data', () => {
        setMapCoordinateAndRadius();
      });
    }
  }, [eventCoordinates, currentRadiusMile, propertyModalTabKey]);

  return null;
}

export default SelectRadius;
