import { useEffect } from 'react';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';

const getElements = (): (HTMLElement | null)[] => {
  return [
    document.querySelector('#MapLayers'),
    document.querySelector('#MapToImageDownload'),
    document.querySelector('#MapRuler'),
    document.querySelector('#MapTilt'),
    document.querySelector('#MapTheme'),
    document.querySelector('#MapSentinelLayerControl'),
    document.querySelector('#radiusSelectWrapper'),
    document.querySelector('#MapNavigation'),
    document.querySelector('#PropertyParcelLegend'),
    document.querySelector('#FloodZoneLegend'),
    document.querySelector('#ChainLocationLegend'),
    document.querySelector('#AttendanceZoneLegend'),
    document.querySelector('#ActivityCenterLegend'),
    document.querySelector('#MapZoomTooltip'),
  ];
};

const hideScreenElements = (): void => {
  const elements = getElements();

  if (!elements || elements.length === 0) return;

  for (let i = 0; i < elements.length; i++) {
    const element = elements[i];
    if (!element) continue;
    element.style.display = 'none';
  }
};

const showScreenElements = (): void => {
  const elements = getElements();

  if (!elements || elements.length === 0) return;

  for (let i = 0; i < elements.length; i++) {
    const element = elements[i];
    if (!element) continue;
    element.style.display = '';
  }

  const mapZoomTooltip = document.querySelector('#MapZoomTooltip') as HTMLElement;
  if (mapZoomTooltip) {
    mapZoomTooltip.style.display = 'none';
  }
};

function MapExpander(): null {
  const { map, setMapExpandedView } = useMarketplaceMapContext();

  useEffect(() => {
    if (!map) return;

    const handleMapExpandedView = (e: { payload: { mapExpandedView: boolean } }) => {
      const expanded = e.payload.mapExpandedView;

      setMapExpandedView(expanded);

      if (expanded) {
        showScreenElements();
        if (map.getZoom() > 14.5) {
          const propertyParcelLegend = document.querySelector('#PropertyParcelLegend') as HTMLElement;
          if (propertyParcelLegend) {
            propertyParcelLegend.style.display = '';
          }
        } else {
          const propertyParcelLegend = document.querySelector('#PropertyParcelLegend') as HTMLElement;
          if (propertyParcelLegend) {
            propertyParcelLegend.style.display = 'none';
          }
        }
      } else {
        hideScreenElements();
      }
    };

    map.on('mapExpandedView', handleMapExpandedView);

    return () => {
      map.off('mapExpandedView', handleMapExpandedView);
    };
  }, [map, setMapExpandedView]);

  return null;
}

export default MapExpander;
