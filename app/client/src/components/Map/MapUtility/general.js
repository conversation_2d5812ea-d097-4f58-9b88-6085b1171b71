import mapboxgl from 'mapbox-gl';
import isEmpty from 'lodash';
import { combineFormatter } from '@/lib/utils/money';
import { MAP_LAYER_NAME_BASE } from '@/constants';

export let highlightPropertyMarker = null;

export const initPopup = () => {
  return new mapboxgl.Popup({
    closeButton: false,
    closeOnClick: false,
    offset: -5,
  });
};

export const getMapInitializaitonPropertiesByUserGroup = (userGroup) => {
  switch (true) {
    case userGroup.includes('Avanta'):
      return {
        center: [-98.35, 39.5],
        zoom: 2.5,
      };
    case userGroup.includes('CommonGroundCapital'):
      return {
        center: [-97.040443, 32.89748],
        maxBounds: [
          [-98.36250660282381, 32.17494189615582],
          [-95.87561207161805, 33.838764143245825],
        ],
      };
    case userGroup.includes('demo-CMA-DFW-only'):
      return {
        center: [-97.040443, 32.89748],
        maxBounds: [
          [-98.36250660282381, 32.17494189615582],
          [-95.87561207161805, 33.838764143245825],
        ],
      };
    case userGroup.includes('demo-CMA-Charlotte-only'):
      return {
        center: [-80.776978, 35.151916],
        maxBounds: [
          [-81.430664, 34.888184],
          [-80.123978, 35.414236],
        ],
      };
    default:
      return {
        center: [-97.040443, 32.89748],
      };
  }
};

export const setPriceMarkers = (map, data, listingKey, rentKey, type) => {
  let markersObject = {};
  for (let i = 0; i < data.length; i++) {
    const property = data[i];

    if (!(property[listingKey] in markersObject)) {
      let el = document.createElement('div');
      if (['latestPrice', 'rent', 'totalrent', 'price'].includes(rentKey)) {
        el.innerHTML = '$' + combineFormatter(property.properties[rentKey]);
      } else {
        el.innerHTML = property.properties[rentKey] + ' Units';
      }

      if (type === MAP_LAYER_NAME_BASE.BTOwned) {
        el.className = 'listingMarker marker_BTOwned';
      } else if (type === MAP_LAYER_NAME_BASE.nationalOperator) {
        const brand = property.properties.brand.replace(/ /g, ''); // remove whitespace
        el.className = 'listingMarker marker_nationalOperators_' + brand;
      } else if (type === MAP_LAYER_NAME_BASE.hotPads) {
        el.className = 'listingMarker marker_HotPads';
      } else if (type === MAP_LAYER_NAME_BASE.mls) {
        el.className = 'listingMarker marker_MLS';
      } else if (type === MAP_LAYER_NAME_BASE.multiFamily) {
        el.className = 'listingMarker marker_multiFamily';
      } else if (type === MAP_LAYER_NAME_BASE.newbuilds) {
        el.className = 'listingMarker marker_newBuild';
      }

      markersObject[property.properties[listingKey]] = new mapboxgl.Marker({
        element: el,
      });
      markersObject[property.properties[listingKey]]
        .setLngLat(property.geometry.coordinates)
        .addTo(map);
    }
  }
  return markersObject;
};

export const removePriceMarkers = (markers) => {
  if (!isEmpty(markers)) {
    const markersObject = markers;
    if (markersObject && Object.keys(markersObject).length > 0) {
      for (const marker in markersObject) {
        markersObject[marker].remove();
      }
    }
    return {};
  }
  return markers;
};

export const generateGeoJSONData = (data, selectedRowKeys, type) => {
  let geojsonFeatures = [];

  for (const property of data) {
    if (type === 'MLS' && selectedRowKeys.includes(property.mlsid)) {
      let { geography, ...geojsonProperties } = property;
      geojsonFeatures.push({
        type: 'Feature',
        geometry: property.geography,
        properties: geojsonProperties,
      });
    } else if (type === 'SFR' && selectedRowKeys.includes(property.base_id)) {
      let { geom, ...geojsonProperties } = property;
      geojsonFeatures.push({
        type: 'Feature',
        geometry: property.geom,
        properties: geojsonProperties,
      });
    }
  }

  return {
    type: 'FeatureCollection',
    features: geojsonFeatures,
  };
};

export const generateHighlightMarker = (map, props) => {
  let el = document.createElement('div');
  if (props.typeHighlightMarker === 'multiFamily') {
    el.innerHTML = props.priceHighlightMarker + ' Units';
  } else {
    el.innerHTML = '$' + combineFormatter(props.priceHighlightMarker);
  }

  const brand = props.typeHighlightMarker.replace(/ /g, ''); // remove whitespace

  el.className = `listingMarkerSelected markerSelected_${brand}`;
  highlightPropertyMarker = new mapboxgl.Marker({ element: el });
  highlightPropertyMarker
    .setLngLat(props.currentHighlightCoordinates)
    .addTo(map);
};
