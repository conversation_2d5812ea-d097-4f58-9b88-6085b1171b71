import { Check } from "lucide-react";
import { mapMenuConfig } from "../../constants/mapSidebarConfig";
import { useMapLayerSelector } from "@/hooks/useMapLayerSelector";

const MapTopBar = () => {
  const {
    activeSubmenu,
    selectedItems,
    handleClick,
    handleSubItemClick,
    isItemSelected
  } = useMapLayerSelector();

  return (
    <div className="bg-white border-b border-medium-gray-20 w-full">
      {/* Horizontal menu bar */}
      <div className="w-full overflow-x-auto no-scrollbar">
        <div className="flex justify-center items-center p-2 min-w-max">
          {mapMenuConfig.map((item) => (
            <div
              key={item.label}
              className="px-3 text-center whitespace-nowrap"
              onClick={() => handleClick(item.label)}
            >
              <div className="flex items-center justify-center cursor-pointer relative">
                <p className={`text-sm text-dark-gray ${activeSubmenu === item.label ? "underline underline-offset-4" : ""}`}>
                  {item.label}
                </p>
                <div className="absolute -top-1 -right-1.5">
                  <div className={`${selectedItems[item.label]?.length > 0 ? "bg-red" : "bg-transparent"} rounded-full w-2 h-2`} />
                </div>
              </div>

              {/* Submenu */}
              {activeSubmenu === item.label && (
                <div
                  className="fixed left-0 right-0 top-[94px] bg-white border border-medium-gray-20 rounded shadow-lg z-300 min-w-[160px]"
                  onClick={(e) => e.stopPropagation()}
                >
                  <ul className="py-2">
                    {item.submenu.map((sub) => (
                      <li
                        key={sub.label}
                        className="px-4 py-2 text-sm hover:bg-light-gray cursor-pointer flex items-center justify-between"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSubItemClick(item.label, sub.label, sub.key);
                        }}
                      >
                        <span className="text-dark-gray">{sub.label}</span>
                        {isItemSelected(item.label, sub.label) && (
                          <Check className="w-4 h-4 text-dark-gray" />
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MapTopBar;