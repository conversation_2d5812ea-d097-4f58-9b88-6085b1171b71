import * as React from 'react';

import { IS_DEV } from '@/lib/utils/constants';

export const TanStackRouterDevtools = IS_DEV
  ? React.lazy(() =>
      // Lazy load in development
      import('@tanstack/router-devtools').then((res) => ({
        default: res.TanStackRouterDevtools,
        // For Embedded Mode
        // default: res.TanStackRouterDevtoolsPanel
      }))
    )
  : () => null; // Render nothing in production
