import React from "react";
import { X } from "lucide-react";
import { useRouter, useSearch } from "@tanstack/react-router";
import {
  generateUrlParamFilter,
  parseFilterInUrlSearchParams,
} from "@/lib/utils/parseFilterInUrlSearchParams";
import lennarDivisions from "@/lib/utils/lennar-divisions.json";
import { FiltersType } from "./PropertyFilters";
import MinCapRateInput from "./MinCapRateInput";
import { MarketFilter } from "./filters/market-filter";

interface MobilePropertyFilterProps {
  isOpen: boolean;
  onClose: () => void;
}

// Define price options and other filter options similar to PropertyFilters
const priceOptions = [
  { label: "No Min - No Max", value: [0, 9999999] },
  { label: "< $100K", value: [0, 100000] },
  { label: "100K - 200K", value: [100000, 200000] },
  { label: "200K - 300K", value: [200000, 300000] },
  { label: "300K - 400K", value: [300000, 400000] },
  { label: "400K+", value: [400000, 9999999] },
];

const bedsOptions = [
  { label: "1+", value: 1 },
  { label: "2+", value: 2 },
  { label: "3+", value: 3 },
  { label: "4+", value: 4 },
  { label: "5+", value: 5 },
];

const bathsOptions = [
  { label: "2+", value: 2 },
  { label: "3+", value: 3 },
  { label: "4+", value: 4 },
  { label: "5+", value: 5 },
];

const formatCapRateForDisplay = (capRate: number | undefined): string => {
  if (!capRate) return "";
  return capRate.toString();
};

const MobilePropertyFilter: React.FC<MobilePropertyFilterProps> = ({
  isOpen,
  onClose,
}) => {
  const router = useRouter();
  const search = useSearch({ from: "/_authenticated/properties" });
  const filterParam = search?.filter || "";
  const currentFilters = parseFilterInUrlSearchParams(filterParam);

  if (!isOpen) return null;

  // Use the shared filter handler from PropertyFilters
  const onChangeFilterValue = ({
    changedField,
    changedValue,
  }: {
    changedField: string;
    changedValue: string | number | string[] | number[];
  }) => {
    // get the current filters from the url search params
    const currentFilters = parseFilterInUrlSearchParams(filterParam);
    // update the filters object
    let updatedFilters: FiltersType;
    if (changedField === "price") {
      updatedFilters = {
        ...currentFilters,
        minPrice: changedValue[0],
        maxPrice: changedValue[1],
      };
    } else {
      updatedFilters = { ...currentFilters, [changedField]: changedValue };
    }
    // update the url search params
    router.navigate({
      search: { ...search, filter: generateUrlParamFilter(updatedFilters) },
      replace: true,
    });
  };

  const filters = [
    {
      label: "Market",
      name: "selectedMarkets",
      options: lennarDivisions.sort().map(division => ({ value: division, label: division })),
      value: parseFilterInUrlSearchParams(filterParam)?.selectedMarkets || [],
      onChange: onChangeFilterValue,
      multiSelect: true,
    },
    {
      label: "Price",
      name: "price",
      options: priceOptions,
      value: [
        parseFilterInUrlSearchParams(filterParam)?.minPrice || 0,
        parseFilterInUrlSearchParams(filterParam)?.maxPrice || 9999999,
      ],
      onChange: onChangeFilterValue,
    },
    {
      label: "Bed",
      name: "minBeds",
      options: bedsOptions,
      value: parseFilterInUrlSearchParams(filterParam)?.minBeds || 0,
      onChange: onChangeFilterValue,
    },
    {
      label: "Bath",
      name: "minBaths",
      options: bathsOptions,
      value: parseFilterInUrlSearchParams(filterParam)?.minBaths || 0,
      onChange: onChangeFilterValue,
    },
  ];

  // Handle toggling behavior
  const handleMarketChange = (marketValue: string) => {
    const currentMarkets = currentFilters?.selectedMarkets || [];
    let updatedMarkets: string[];

    if (currentMarkets.includes(marketValue)) {
      updatedMarkets = currentMarkets.filter((m) => m !== marketValue);
    } else {
      updatedMarkets = [...currentMarkets, marketValue];
    }

    onChangeFilterValue({
      changedField: "selectedMarkets",
      changedValue: updatedMarkets,
    });
  };

  const isOptionSelected = (name: string, optionValue: any): boolean => {
    if (name === "price") {
      const minPrice = currentFilters?.minPrice || 0;
      const maxPrice = currentFilters?.maxPrice || 9999999;

      // Check if this price range matches current filter
      return (
        Array.isArray(optionValue) &&
        optionValue[0] === minPrice &&
        optionValue[1] === maxPrice
      );
    } else if (name === "selectedMarkets") {
      const selectedMarkets = currentFilters?.selectedMarkets || [];
      return selectedMarkets.includes(optionValue.toString());
    } else if (name === "minBeds") {
      return currentFilters?.minBeds === optionValue;
    } else if (name === "minBaths") {
      return currentFilters?.minBaths === optionValue;
    }
    return false;
  };

  const handleScrollAndClose = () => {
    window.scrollTo(0, 0);
    setTimeout(() => window.scrollTo(0, 0), 100); // Extra scroll to ensure it works on all devices
    onClose();
  };

  return (
    <div className="fixed inset-0 z-310">
      <div className="absolute inset-0 bg-black/50"></div>
      <div className="absolute inset-0 bg-white flex flex-col">
        {/* Header */}
        <div className="flex justify-end items-center p-4">
          <button onClick={handleScrollAndClose} className="p-2">
            <X size={20} />
          </button>
        </div>

        {/* Filter Content - Use the filters array */}
        <div className="flex-1 overflow-auto p-4">
          <div className="space-y-6">
            <MarketFilter useMobile />

            {filters
              .filter((f) => f.name !== "selectedMarkets")
              .map((filter) => (
                <div
                  key={filter.name}
                  className="border border-medium-gray-20 rounded-xl p-4"
                >
                  <h3 className="font-medium mb-3">{filter.label}</h3>
                  <div className="pl-2">
                    {filter.name === "selectedMarkets"
                      ? // Special handling for multi-select markets with checkboxes
                        filter.options.map((option) => (
                          <div
                            key={`${filter.name}-${option.value}`}
                            className="flex items-center py-2 mb-1"
                          >
                            <input
                              type="checkbox"
                              id={`${filter.name}-${option.value}`}
                              checked={isOptionSelected(
                                filter.name,
                                option.value
                              )}
                              onChange={() =>
                                handleMarketChange(option.value.toString())
                              }
                              className="mr-2"
                            />
                            <label
                              htmlFor={`${filter.name}-${option.value}`}
                              className="text-sm"
                            >
                              {option.label}
                            </label>
                          </div>
                        ))
                      : // Standard radio button handling for other filters
                        filter.options.map((option) => (
                          <div
                            key={`${filter.name}-${option.label}`}
                            className="flex items-center py-2 mb-1"
                          >
                            <input
                              type="radio"
                              id={`${filter.name}-${option.label}`}
                              name={filter.name}
                              checked={isOptionSelected(
                                filter.name,
                                option.value
                              )}
                              onChange={() =>
                                filter.onChange({
                                  changedField: filter.name,
                                  changedValue: option.value,
                                })
                              }
                              className="mr-2"
                            />
                            <label
                              htmlFor={`${filter.name}-${option.label}`}
                              className="text-sm"
                            >
                              {option.label}
                            </label>
                          </div>
                        ))}
                  </div>
                </div>
              ))}

            {/* Min Cap Rate Filter */}
            <div className="border border-medium-gray-20 rounded-xl p-4">
              <h3 className="font-medium mb-3">Min Cap Rate</h3>
              <MinCapRateInput
                value={
                  formatCapRateForDisplay(
                    parseFilterInUrlSearchParams(filterParam)?.minCapRate
                  ) || ""
                }
                onChange={(e) =>
                  onChangeFilterValue({
                    changedField: "minCapRate",
                    changedValue: Number(e.target.value) / 100,
                  })
                }
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobilePropertyFilter;
