import React from "react";
import MapSideBar from "./Map/MapSideBar";
import MapCMA from "./Map/MapCMA";
import { useBreakpoint } from "@/hooks/useBreakpoint";
import MapTopBar from "./Map/MapTopBar";


const LeftPanel: React.FC = () => {
  const { isMobile } = useBreakpoint();
  return (
    <div className="flex flex-col border-r border-[var(--color-light-gray)] min-h-0 flex-1">
      {isMobile ? <MapTopBar /> : <MapSideBar />}

      <MapCMA />

    </div>
  );
}

export default LeftPanel;
