import { useRef, ReactNode } from 'react';
import { usePanelState } from '@/hooks/usePanelState';

import { PropertiesFilterProvider } from '@/contexts/PropertiesFilterContext';
import { useLoaderData, useSearch } from '@tanstack/react-router';
import PropertiesContentLayout from './PropertiesContentLayout';

interface AuthenticatedWrapperProps {
  children: ReactNode;
}

const PropertiesWrapper = ({ children }: AuthenticatedWrapperProps) => {

  const { isLeftPanelOpen, toggleLeftPanel } = usePanelState();
  const containerRef = useRef<HTMLDivElement>(null);
  const { properties, propertiesSubmitted } = useLoaderData({ from: '/_authenticated/properties' });

  const search = useSearch({ from: '/_authenticated/properties' });
  const listType = search.listType;

  const currentProperties = listType === 'listings' ? properties : propertiesSubmitted;

  return (
    <PropertiesFilterProvider properties={currentProperties}>
      <PropertiesContentLayout
        isLeftPanelOpen={isLeftPanelOpen}
        toggleLeftPanel={toggleLeftPanel}
        containerRef={containerRef}
      >
        {children}
      </PropertiesContentLayout>
    </PropertiesFilterProvider>
  );
};




export default PropertiesWrapper;