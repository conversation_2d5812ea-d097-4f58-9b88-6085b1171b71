import { useRef, ReactNode } from 'react';
import { usePanelState } from '@/hooks/usePanelState';

import { PropertiesFilterProvider } from '@/contexts/PropertiesFilterContext';
import { useLoaderData, useSearch } from '@tanstack/react-router';
import PropertiesContentLayout from './PropertiesContentLayout';
import SkeletonFullView from '@/components/Common/SkeletonFullView';

interface AuthenticatedWrapperProps {
  children: ReactNode;
}

const PropertiesWrapper = ({ children }: AuthenticatedWrapperProps) => {

  const { isLeftPanelOpen, toggleLeftPanel } = usePanelState();
  const containerRef = useRef<HTMLDivElement>(null);
  const search = useSearch({ from: '/_authenticated/properties' });
  const data = useLoaderData({ from: '/_authenticated/properties' });

  if (!data?.properties || !data?.propertiesSubmitted || !data?.propertiesBookmarked) {
    return <SkeletonFullView />;
  }

  const { properties, propertiesSubmitted, propertiesBookmarked } = data;
  const listType = search.listType;

  const currentProperties = listType === 'listings' ? properties : listType === 'submitted' ? propertiesSubmitted : propertiesBookmarked;

  return (
    <PropertiesFilterProvider properties={currentProperties}>
      <PropertiesContentLayout
        isLeftPanelOpen={isLeftPanelOpen}
        toggleLeftPanel={toggleLeftPanel}
        containerRef={containerRef}
      >
        {children}
      </PropertiesContentLayout>
    </PropertiesFilterProvider>
  );
};




export default PropertiesWrapper;