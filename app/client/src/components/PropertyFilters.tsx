import FilterDropdown from "./Map/FilterDropdown";
import MinCapRateInput from "./MinCapRateInput";
import { useState } from "react";
import { useRouter, useSearch } from "@tanstack/react-router";
import {
  generateUrlParamFilter,
  parseFilterInUrlSearchParams,
} from "@/lib/utils/parseFilterInUrlSearchParams";
import lennarDivisions from "@/lib/utils/lennar-divisions.json";
import { MarketFilter } from "./filters/market-filter";

interface PropertyFiltersProps {
  // filters: FilterConfig[];
  minCapRate: string;
  handleMinCapRateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export interface FiltersType {
  selectedMarkets?: string[];
  minBeds?: number;
  minBaths?: number;
  minPrice?: number;
  maxPrice?: number;
  minCapRate?: number;
}

const priceOptions = [
  { label: "No Min - No Max", value: [0, 9999999] },
  { label: "< $100K", value: [0, 100000] },
  { label: "100K - 200K", value: [100000, 200000] },
  { label: "200K - 300K", value: [200000, 300000] },
  { label: "300K - 400K", value: [300000, 400000] },
  { label: "400K+", value: [400000, 9999999] },
];

const bedsOptions = [
  { label: "1+", value: 1 },
  { label: "2+", value: 2 },
  { label: "3+", value: 3 },
  { label: "4+", value: 4 },
  { label: "5+", value: 5 },
];

const bathsOptions = [
  { label: "2+", value: 2 },
  { label: "3+", value: 3 },
  { label: "4+", value: 4 },
  { label: "5+", value: 5 },
];

const PropertyFilters = ({
  minCapRate,
  handleMinCapRateChange,
}: PropertyFiltersProps) => {
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  const router = useRouter();
  const search = useSearch({ from: "/_authenticated/properties" });
  const filterParam = search?.filter || "";

  //prettier-ignore
  const onChangeFilterValue = ({
    changedField,
    changedValue,
  }: {
    changedField: string;
    changedValue: string | number | string[];
  }) => {
    // get the current filters from the url search params
    const currentFilters = parseFilterInUrlSearchParams(filterParam);
    // update the filters object
    let updatedFilters: FiltersType;
    if (changedField === 'price' && Array.isArray(changedValue)) {
      updatedFilters = { ...currentFilters, minPrice: changedValue[0] as number, maxPrice: changedValue[1] as number };
    } else {
      updatedFilters = { ...currentFilters, [changedField]: changedValue as any };
    }

    // Generate the complete filter string from the updated filters
    const filterString = generateUrlParamFilter(updatedFilters);

    router.navigate({
      search: { ...search, filter: filterString },
      replace: true,
    });
  }

  //prettier-ignore
  const filters = [
    {
      label: 'Market',
      name: 'selectedMarkets',
      options: lennarDivisions.sort().map(division => ({ value: division, label: division })),
      value: parseFilterInUrlSearchParams(filterParam)?.selectedMarkets || [],
      onChange: onChangeFilterValue,
      openDropdown,
      setOpenDropdown,
      multiSelect: true,
    },
    {
      label: 'Price',
      name: 'price',
      options: priceOptions,
      value: [
        parseFilterInUrlSearchParams(filterParam)?.minPrice || 0,
        parseFilterInUrlSearchParams(filterParam)?.maxPrice || 9999999,
      ],
      onChange: onChangeFilterValue,
      openDropdown,
      setOpenDropdown,
    },
    {
      label: 'Bed',
      name: 'minBeds',
      options: bedsOptions,
      value: parseFilterInUrlSearchParams(filterParam)?.minBeds || 0,
      onChange: onChangeFilterValue,
      openDropdown,
      setOpenDropdown,
    },
    {
      label: 'Bath',
      name: 'minBaths',
      options: bathsOptions,
      value: parseFilterInUrlSearchParams(filterParam)?.minBaths || 0,
      onChange: onChangeFilterValue,
      openDropdown,
      setOpenDropdown,
    },
  ]

  return (
    <div className="flex">
      <div className="flex items-center gap-2">
        <MarketFilter />

        {filters
          .filter((f) => f.name !== "selectedMarkets")
          .map((filter) => (
            <FilterDropdown
              key={filter.name}
              name={filter.name}
              label={filter.label}
              options={filter.options}
              value={filter.value}
              onChange={filter.onChange}
              openDropdown={openDropdown}
              setOpenDropdown={setOpenDropdown}
              multiSelect={"multiSelect" in filter ? filter.multiSelect : false}
            />
          ))}

        <MinCapRateInput
          value={
            parseFilterInUrlSearchParams(filterParam)?.minCapRate?.toString() ||
            ""
          }
          onChange={(e) =>
            onChangeFilterValue({
              changedField: "minCapRate",
              changedValue: Number(e.target.value) / 100,
            })
          }
        />
      </div>
    </div>
  );
};

export default PropertyFilters;