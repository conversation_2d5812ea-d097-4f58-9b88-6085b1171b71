import { FilterConfig, FilterValue } from "@/types/PropertiesFilterTypes";
import FilterDropdown from "./Map/FilterDropdown";
import MinCapRateInput from "./MinCapRateInput";
import { Dispatch, SetStateAction, useState } from "react";

interface PropertyFiltersProps {
  filters: FilterConfig[];
  minCapRate: string;
  handleMinCapRateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const PropertyFilters = ({ filters, minCapRate, handleMinCapRateChange }: PropertyFiltersProps) => {
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  return (
    <div className="flex">
      <div className="flex items-center gap-2">
        {filters.map((filter) => (
          <FilterDropdown
            key={filter.label}
            label={filter.label}
            options={filter.options}
            value={filter.value}
            onChange={filter.onChange as Dispatch<SetStateAction<FilterValue>>}
            openDropdown={openDropdown}
            setOpenDropdown={setOpenDropdown}
          />
        ))}

        <MinCapRateInput value={minCapRate} onChange={handleMinCapRateChange} />
      </div>
    </div>
  );
};

export default PropertyFilters;