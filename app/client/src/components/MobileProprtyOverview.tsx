import React from 'react'
import { PhoneCall } from 'lucide-react';
import { formatPhoneNumber } from "@/lib/utils/formatUtils";
import PromotionalBanner from './PromotionalBanner';

interface MobilePropertyOverviewProps {
  formattedPrice: string;
  address?: string;
  onStartOfferClick: () => void;
  phone?: string | null;
  isLoading?: boolean;
}

const MobilePropertyOverview: React.FC<MobilePropertyOverviewProps> = ({
  formattedPrice,
  address,
  onStartOfferClick,
  phone,
  isLoading = false
}) => {
  const formattedPhone = formatPhoneNumber(phone);
  return (
    <div className="flex flex-col gap-4 text-base text-dark-gray p-2 font-medium">
      <PromotionalBanner isMobile={true} />
      <p>{address}</p>
      <div className="flex items-center justify-between gap-4 ">
        <button onClick={onStartOfferClick} disabled={isLoading} className="flex-1 h-8 px-3 bg-dark-gray text-white rounded-lg text-base">
          {isLoading ? "Loading..." : (
            <>
              Purchase <span className="font-thin">{formattedPrice}</span>
            </>
          )}
        </button>
        <a
          href={`tel:${formattedPhone.rawPhone}`}
          className="flex items-center justify-center rounded-full h-8 w-8 text-sm font-semibold bg-dark-gray text-white hover:bg-dark-gray/80 transition-colors"
          aria-label="Call agent"
        >
          <PhoneCall className="w-4 h-4" />
        </a>
      </div>
    </div>
  )
}

export default MobilePropertyOverview