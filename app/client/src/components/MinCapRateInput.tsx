import { defaultMinCapRate } from "@/contexts/PropertiesFilterContext";
import { useBreakpoint } from "@/hooks/useBreakpoint";
import React, { useEffect, useState } from "react";

interface MinCapRateInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const MinCapRateInput: React.FC<MinCapRateInputProps> = ({
  value,
  onChange,
}) => {
  const [minCapRate, setMinCapRate] = useState(defaultMinCapRate);
  const { isMobile } = useBreakpoint();

  useEffect(() => {
    setMinCapRate(value ? (Number(value) * 100)?.toString() : defaultMinCapRate);
  }, [value]);

  return (
    <div className="flex flex-row justify-start items-center gap-2">
      <div className="text-sm text-dark-gray">
        <span>Min Cap Rate</span>
      </div>
      <div className="rounded-none flex justify-center items-center">
        <input
          type="number"
          className={`w-11 h-8 border border-medium-gray-20 text-center rounded-tl-md rounded-bl-md cursor-pointer focus:outline-none text-dark-gray ${isMobile ? "text-base" : "text-sm"}`}
          value={minCapRate}
          onChange={(e) => {
            setMinCapRate(e.target.value);
            onChange(e);
          }}
          placeholder={defaultMinCapRate}
          step="0.1"
          min="0"
          max="999"
        />
        <div className="flex items-center justify-center text-center w-7 h-8 rounded-tr-md rounded-br-md border-t border-r border-b border-medium-gray-20 bg-light-gray text-dark-gray text-sm">
          %
        </div>
      </div>
    </div>
  );
};

export default MinCapRateInput;
