import { defaultMinCapRate } from "@/contexts/PropertiesFilterContext";
import React from "react";

interface MinCapRateInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const MinCapRateInput: React.FC<MinCapRateInputProps> = ({
  value,
  onChange,
}) => (
  <>
    <div className="text-sm text-dark-gray">
      <span>Min Cap Rate</span>
    </div>
    <div className="rounded-none flex justify-center items-center">
      <input
        type="number"
        className="w-11 h-8 border border-medium-gray-20 text-center rounded-tl-md rounded-bl-md cursor-pointer focus:outline-none text-sm text-dark-gray"
        value={value}
        onChange={onChange}
        placeholder={defaultMinCapRate}
        step="0.1"
        min="0"
        max="999"
        defaultValue={defaultMinCapRate}
      />
      <div className="flex items-center justify-center text-center w-7 h-8 rounded-tr-md rounded-br-md border-t border-r border-b border-medium-gray-20 bg-light-gray text-dark-gray text-sm">
        %
      </div>
    </div>
  </>
);

export default MinCapRateInput;
