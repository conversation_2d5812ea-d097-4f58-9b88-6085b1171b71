import React from 'react';

interface FinancingDisclosureProps {
  className?: string;
}

const FinancingDisclosure: React.FC<FinancingDisclosureProps> = ({ className = "" }) => {
  return (
    <p className={className}>
      Financing is available through seller's affiliate Lennar Mortgage, LLC, but use of Lennar Mortgage, LLC is not required to purchase a home (See Affiliated Business Arrangement Disclosure - <a href="https://mcv0gwmvppxkjtntxgnb0-0xn53y.pub.sfmc-content.com/2wss1jvor1f" target="_blank" rel="noopener noreferrer" className="text-button-blue">https://mcv0gwmvppxkjtntxgnb0-0xn53y.pub.sfmc-content.com/2wss1jvor1f</a>). Lennar Mortgage, LLC – NMLS # 1058. For complete licensing information: <a href="https://www.lennarmortgage.com/Licensing" target="_blank" rel="noopener noreferrer" className="text-button-blue">https://www.lennarmortgage.com/Licensing</a>.
    </p>
  );
};

export default FinancingDisclosure;