import React, { ReactNode } from 'react';

interface ActionButtonProps {
  text: string;
  onClick?: () => void;
  className?: string;
  icon?: ReactNode;
  disabled?: boolean;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  text,
  className = "py-2 px-4",
  onClick,
  icon,
  disabled = false
}) => {
  const defaultStyle = `border border-medium-gray-20 rounded text-xs transition-colors flex items-center gap-2 
  ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-medium-gray-20 cursor-pointer'}`;
  return (
    <button
      className={`${defaultStyle} ${className}`}
      onClick={onClick}
      disabled={disabled}
    >
      {icon && icon}
      {text}
    </button>
  );
};

export default ActionButton;