import React, { ChangeEvent } from 'react';


interface DropdownOption {
  value: string | number;
  label: string;
}

interface DropdownProps {
  label: string;
  options: DropdownOption[];
  value: string | number;
  onChange: (value: string | number) => void;
  className?: string;
  valueType?: 'string' | 'number';
}

const Selection: React.FC<DropdownProps> = ({
  label,
  options,
  value,
  onChange,
  className = "",
  valueType = 'string',
}) => {
  const handleChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const newValue = e.target.value;
    if (valueType === 'number' || typeof value === 'number') {
      onChange(Number(newValue));
    } else {
      onChange(newValue);
    }
  };

  return (
    <div className="flex items-center space-x-2">
      {label && (
        <label className="text-[13px] text-dark-gray whitespace-nowrap">
          {label}
        </label>
      )}
      <select
        value={value}
        onChange={handleChange}
        className={`
          min-w-24 h-6 px-2
          pr-6 rounded border border-medium-gray-20 text-xs   cursor-pointer
          ${className}`}
      >
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
};

export default Selection;