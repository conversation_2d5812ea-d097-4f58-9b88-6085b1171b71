import React, { ChangeEvent } from 'react';

interface CustomCheckboxProps {
  checked: boolean;
  onChange: (event: ChangeEvent<HTMLInputElement>) => void;
  indeterminate?: boolean;
  className?: string;
}

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
  checked,
  onChange,
  indeterminate = false,
  className = ''
}) => {
  return (
    <input
      type="checkbox"
      checked={checked}
      onChange={onChange}
      className={`
        appearance-none w-4 h-4 border border-[var(--color-light-gray)] rounded 
        bg-[var(--color-super-light-gray)] cursor-pointer relative
        checked:bg-[var(--color-button-blue)] checked:border-[var(--color-button-blue)]
        after:content-[''] after:absolute after:hidden checked:after:block
        after:left-[5px] after:top-[2px] after:w-1 after:h-2
        after:border-r-2 after:border-b-2 after:border-white after:rotate-45
        ${indeterminate ?
          'bg-[var(--color-light-gray)] border-[var(--color-super-light-gray)] after:block after:left-[3px] after:top-[5px] after:w-2.5 after:h-0.5 after:bg-[var(--color-button-blue)] after:rotate-0 after:border-none'
          : ''
        }
        ${className}
      `}
    />
  );
};

export default CustomCheckbox;