import React from 'react';

interface WebsiteLinkProps {
  url: string | undefined;
  className?: string;
}

export const WebsiteLink: React.FC<WebsiteLinkProps> = ({ url, className }) => {
  if (!url) return null;

  return (
    <a
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      className={className || "text-xs text-button-blue break-all hover:text-button-blue/50 transition-colors duration-200"}
    >
      {url.split('/')[2]}
    </a>
  );
};