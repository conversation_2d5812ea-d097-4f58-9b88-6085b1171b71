import React, { useState } from "react";

interface ToggleSwitchProps {
  label: string;
  initial?: boolean;
  onToggle?: (on: boolean) => void;
}

const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
  label,
  initial = false,
}) => {
  const [on, setOn] = useState(initial);

  return (
    <div
      onClick={() => setOn(!on)}
      className={`flex px-3 py-1 items-center justify-center text-xs rounded-lg border border-[var(--color-light-gray)] cursor-pointer transition-colors duration-200
        ${on ? "bg-[var(--color-lennar-blue-light)] text-[var(--color-button-blue)]" : "bg-white text-[var(--color-button-blue)] hover:bg-[var(--color-super-light-gray)]"}
      `}
    >
      {label}
    </div>
  );
};

export default ToggleSwitch;
