import React from 'react';

interface PhoneLinkProps {
  phone: string | { rawPhone?: string; displayPhone?: string } | undefined;
  className?: string;
}

export const PhoneLink: React.FC<PhoneLinkProps> = ({ phone, className }) => {
  if (!phone) return null;

  const rawPhone = typeof phone === 'string' ? phone : phone.rawPhone;
  const displayPhone = typeof phone === 'string' ? phone : (phone.displayPhone || phone.rawPhone);

  if (!rawPhone) return null;

  return (
    <a
      href={`tel:${rawPhone}`}
      className={className || "text-xs text-button-blue hover:text-button-blue/50 transition-colors duration-200"}
    >
      {displayPhone}
    </a>
  );
};