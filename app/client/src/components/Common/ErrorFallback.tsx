import React from 'react';

interface ErrorFallbackProps {
  error: Error;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error }) => {

  console.log('Error occurred:', error);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-white p-4">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-dark-gray mb-4">
          The platform is experiencing an issue and will be restored momentarily.
        </h2>
        <p className="text-dark-gray text-lg mb-6">
          Thank you for your patience.
        </p>
        <div className="flex gap-4 justify-center">
          <a
            href="/"
            className="px-6 py-2 bg-green-primary text-white rounded-4xl hover:bg-green-primary/80 transition-colors"
          >
            Go Home
          </a>
        </div>
      </div>
    </div>
  );
};

export default ErrorFallback;