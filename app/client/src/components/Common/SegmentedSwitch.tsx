import React from "react";

interface SegmentedSwitchProps {
  options: string[];
  onClick?: (option: string) => void;
  selectedOption?: string;
  className?: string;
}

const SegmentedSwitch: React.FC<SegmentedSwitchProps> = ({
  options,
  onClick,
  selectedOption,
  className = ""
}) => {
  // Handle click on an option
  const handleClick = (option: string) => {
    if (onClick) {
      onClick(option);
    }
  };

  return (
    <div className={`inline-flex border border-medium-gray-20 rounded-lg overflow-hidden ${className}`}>
      {options.map((option) => (
        <div
          key={option}
          // Fixed: Now properly calls handleClick with the option value when clicked
          onClick={() => handleClick(option)}
          className={`flex px-2 py-1 items-center justify-center text-xs border-r border-light-gray first:border-l-0 last:border-r-0
            transition-colors cursor-pointer duration-200
            ${selectedOption === option
              ? "bg-[var(--color-lennar-blue-light)] text-button-blue"
              : "text-button-blue bg-white hover:text-button-blue/80"
            }
          `}
        >
          {option}
        </div>
      ))}
    </div>
  );
};

export default SegmentedSwitch;