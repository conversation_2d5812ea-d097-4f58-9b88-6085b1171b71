import React from 'react';

interface EmailLinkProps {
  email: string | undefined;
  className?: string;
}

export const EmailLink: React.FC<EmailLinkProps> = ({ email, className }) => {
  if (!email) return null;

  return (
    <a
      href={`mailto:${email}`}
      className={className || "text-xs text-button-blue hover:text-button-blue/50 transition-colors duration-200"}
    >
      {email}
    </a>
  );
};