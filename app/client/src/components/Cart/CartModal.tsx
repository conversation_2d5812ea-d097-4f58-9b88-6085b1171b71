import React, { useState } from 'react';
import { X, ShoppingCart, AlertCircle } from 'lucide-react';
import { useCart } from '../../contexts/CartContext';
import CartItemRow from './CartItemRow';
import { formatPrice } from '../../lib/utils/formatUtils';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  AlertDialogOverlay,
  AlertDialogPortal,
} from '../ui/alert-dialog';

const CartModal: React.FC = () => {
  const {
    cart,
    isCartModalOpen,
    setIsCartModalOpen,
    clearCart,
    getConfirmedItemCount,
    isLoading,
    setIsOfferFlowOpen,
    selectAllItems,
    deselectAllItems
  } = useCart();

  if (!isCartModalOpen) return null;

  const confirmedCount = getConfirmedItemCount();
  const totalSelectedValue = cart?.items
    .filter(item => item.isConfirmed)
    .reduce((sum, item) => {
      const price = parseFloat(String(item.offerPrice)) || 0;
      return sum + price;
    }, 0) || 0;

  const handleClose = () => {
    setIsCartModalOpen(false);
  };

  const handleClearCart = async () => {
    await clearCart();
  };

  const handleSubmitOffers = () => {
    if (confirmedCount === 0) {
      alert('Please select at least one property to submit offers.');
      return;
    }

    // Validate that all confirmed items have offer prices
    const confirmedItems = cart?.items.filter(item => item.isConfirmed) || [];
    const itemsWithoutPrices = confirmedItems.filter(item => !item.offerPrice || item.offerPrice <= 0);

    if (itemsWithoutPrices.length > 0) {
      alert('Please enter valid offer prices for all selected properties.');
      return;
    }

    // Close cart modal first, then open offer flow
    setIsCartModalOpen(false);
    setTimeout(() => {
      setIsOfferFlowOpen(true);
    }, 100);
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[1000]">
      <div className="bg-white rounded-xl shadow-lg w-[90%] max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <ShoppingCart size={24} className="text-green-primary" />
            <h2 className="text-xl font-semibold text-dark-gray">Shopping Cart</h2>
            {cart && cart.totalItems > 0 && (
              <span className="bg-green-primary text-white text-sm px-2 py-1 rounded-full">
                {cart.totalItems} {cart.totalItems === 1 ? 'home' : 'homes'}
              </span>
            )}
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X size={20} className="text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Cart Items */}
          <div className="flex-1 overflow-y-auto p-6">
            {!cart || cart.totalItems === 0 ? (
              <div className="text-center py-12">
                <ShoppingCart size={48} className="text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-500 mb-2">Your cart is empty</h3>
                <p className="text-gray-400">Add properties to your cart to submit bulk offers</p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Select All Checkbox */}
                <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg bg-gray-50">
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={cart.items.length > 0 && cart.items.every(item => item.isConfirmed)}
                      ref={(input) => {
                        if (input) {
                          const someChecked = cart.items.some(item => item.isConfirmed);
                          const allChecked = cart.items.every(item => item.isConfirmed);
                          input.indeterminate = someChecked && !allChecked;
                        }
                      }}
                      onChange={(e) => {
                        if (e.target.checked) {
                          selectAllItems();
                        } else {
                          deselectAllItems();
                        }
                      }}
                      className="w-4 h-4 text-green-primary border-gray-300 rounded focus:ring-green-primary"
                    />
                    <span className="text-sm font-medium text-gray-700">
                      Select All ({cart.items.length} homes)
                    </span>
                  </div>
                  <span className="text-sm text-gray-500">
                    {confirmedCount} selected
                  </span>
                </div>

                {/* Cart Items */}
                {cart.items.map((item) => (
                  <CartItemRow key={item.propertyId} item={item} />
                ))}
              </div>
            )}
          </div>

          {/* Footer Actions */}
          {cart && cart.totalItems > 0 && (
            <div className="border-t border-gray-200 p-6">
              <div className="flex justify-between items-center mb-4">
                <div className="text-sm text-gray-600">
                  {confirmedCount > 0 && (
                    <div className="flex items-center gap-4">
                      <span>
                        {confirmedCount} of {cart.totalItems} homes selected
                      </span>
                      {confirmedCount > 0 && (
                        <div className="text-right">
                          <div className="text-lg font-semibold text-gray-900">
                            Total: {formatPrice(totalSelectedValue)}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex gap-3 justify-end">
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <button
                      disabled={isLoading}
                      className="px-4 py-2 text-sm text-red-600 border border-red-300 rounded-lg hover:bg-red-50 transition-colors disabled:opacity-50"
                    >
                      Clear Cart
                    </button>
                  </AlertDialogTrigger>
                  <AlertDialogPortal>
                    <AlertDialogOverlay className="z-[1100]" />
                    <AlertDialogContent className="z-[1100] !fixed !left-[50%] !top-[50%] !translate-x-[-50%] !translate-y-[-50%]">
                      <AlertDialogHeader>
                        <AlertDialogTitle className="flex items-center gap-2">
                          <AlertCircle className="w-5 h-5 text-red-500" />
                          Clear Shopping Cart
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to clear your cart? This will remove all {cart?.totalItems || 0} properties and cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter className="flex flex-row justify-center items-center">
                        <AlertDialogCancel className="m-0">Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleClearCart}
                          className="bg-red-600 hover:bg-red-700 focus:ring-red-600 ml-4"
                        >
                          Clear Cart
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialogPortal>
                </AlertDialog>

                <button
                  onClick={handleSubmitOffers}
                  disabled={confirmedCount === 0 || isLoading}
                  className="px-6 py-2 bg-green-primary text-white rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Processing...' : 'Start Purchase'}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="absolute inset-0 bg-white/80 flex items-center justify-center rounded-xl">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-primary mx-auto mb-2"></div>
              <p className="text-gray-600">Processing cart...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CartModal; 