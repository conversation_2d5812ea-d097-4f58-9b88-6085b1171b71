import React from 'react';
import { Plus, Check, Trash2 } from 'lucide-react';
import { useCart } from '../../contexts/CartContext';
import { useMarketplaceMapContext } from '../../contexts/MarketplaceMapContext';
import { ShoppingCart } from 'lucide-react';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface AddToCartButtonProps {
  className?: string;
}

const AddToCartButton: React.FC<AddToCartButtonProps> = ({ className = '' }) => {
  const { addToCart, removeFromCart, isLoading, cart } = useCart();
  const { selectedBuyersViewRecord, proFormaAllValues } = useMarketplaceMapContext();
  const { isMobile } = useBreakpoint();

  if (!selectedBuyersViewRecord?.property_id) {
    return null;
  }

  const propertyId = selectedBuyersViewRecord.property_id;
  // Direct check instead of using the callback to ensure re-renders
  const isInCart = cart?.items.some(item => item.propertyId === propertyId) || false;

  const handleClick = async () => {
    if (isInCart) {
      await removeFromCart(propertyId);
    } else {
      // Get purchase price from Pro Forma if available
      const purchasePrice = proFormaAllValues?.["Bid Price"];
      const originalRent = proFormaAllValues?.adjustedRent;
      const buyerFinalRent = proFormaAllValues?.['Projected Monthly Rent']
      console.log('test21 proFormaAllValues:', proFormaAllValues);
      const payload = {
        subjectProperty: selectedBuyersViewRecord?.payload?.subjectProperty,
        proforma: proFormaAllValues
      }
      await addToCart(selectedBuyersViewRecord, purchasePrice, originalRent, buyerFinalRent, payload)
    }
  };

  const getButtonContent = () => {
    if (isLoading) {
      return (
        <>
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          <span>Loading...</span>
        </>
      );
    }

    if (isInCart) {
      return (
        <>
          <Check size={16} />
          <span>In Cart</span>
        </>
      );
    }

    return (
      <>
        <Plus size={16} />
        <span>Add to Cart</span>
      </>
    );
  };

  if (isMobile) {
    return (
      <button className="flex items-center h-8 px-1 gap-1 border border-dark-gray rounded-lg" onClick={handleClick}
        disabled={isLoading}>
        {isLoading ? (
          <div className="w-[18px] h-[18px] border-2 border-medium-gray-20 border-t-transparent rounded-full animate-spin" />
        ) : (
          <div className="flex items-center justify-center">
            {isInCart ? <Check size={18} /> : <Plus size={18} />}
          </div>
        )}
        <ShoppingCart size={21} />
      </button>
    );
  }

  const getButtonStyles = () => {
    if (isInCart) {
      return `
        bg-gray-100 text-gray-700 border border-gray-300 
        hover:bg-red-50 hover:text-red-700 hover:border-red-300
        group
      `;
    }

    return `
      bg-white text-green-primary border border-green-primary 
      hover:bg-green-primary hover:text-white
    `;
  };

  const getHoverContent = () => {
    if (isInCart) {
      return (
        <>
          <Trash2 size={16} />
          <span>Remove</span>
        </>
      );
    }
    return getButtonContent();
  };

  return (
    <button
      onClick={handleClick}
      disabled={isLoading}
      className={`
        flex items-center gap-2 rounded-lg py-1.5 px-3 text-sm font-medium
        transition-all duration-200 ease-in-out
        disabled:opacity-50 disabled:cursor-not-allowed
        ${getButtonStyles()}
        ${className}
      `}
      aria-label={isInCart ? 'Remove from cart' : 'Add to cart'}
    >
      {/* Default content */}
      <div className={`flex items-center gap-2 ${isInCart ? 'group-hover:hidden' : ''}`}>
        {getButtonContent()}
      </div>

      {/* Hover content for items in cart */}
      {isInCart && (
        <div className="hidden group-hover:flex items-center gap-2">
          {getHoverContent()}
        </div>
      )}
    </button>
  );
};

export default AddToCartButton; 