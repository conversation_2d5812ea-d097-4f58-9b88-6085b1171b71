import React from 'react';
import { OfferSubmissionStep, UserRole } from '../../types/offerTypes';
import ModalWrapper from '../Modal/ModalWrapper';
import OfferSubmissionHeader from '../Modal/OfferSubmissionHeader';
import ProgressIndicator from '../Modal/ProgressIndicator';
import FormInput from '../Modal/FormInput';
import FormTextarea from '../Modal/FormTextarea';
import FinancingCheckbox from '../Modal/FinancingCheckbox';
import TermsOfUseAgreement from '../Modal/TermsOfUseAgreement';
import ModalButton from '../Modal/ModalButton';
import FormInputNumber from '../Modal/FormInpuNumber';
import { formatPrice, formatTitleWithStepCount } from '../../lib/utils/formatUtils';
import { CartItem } from '../../types/cartTypes';
import PropertyCartImage from './PropertyCartImage';
import ContactInfo from '../ContactInfo';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import PMCheckbox from '../Modal/PMCheckbox';

interface BatchPurchaseModalProps {
  email: string;
  setEmail: (value: string) => void;
  phone: string;
  setPhone: (value: string) => void;
  firstName: string;
  setFirstName: (value: string) => void;
  lastName: string;
  setLastName: (value: string) => void;
  comments?: string;
  setComments: (value: string) => void;
  financing: "yes" | "no" | "";
  setFinancing: (value: "yes" | "no" | "") => void;
  PM: "yes" | "no" | "";
  setPM: (value: "yes" | "no" | "") => void;
  compensation: string;
  setCompensation: (value: string) => void;
  userRole: UserRole;
  address?: string;
  lotNumber?: string | number;
  currentModalStep: OfferSubmissionStep;
  handleSubmitOffer: () => void;
  handleMainClose: () => void;
  openTermsModal: (e: React.MouseEvent) => void;
  confirmedItems: CartItem[];
  onUpdateItemPrice: (propertyId: number, newPrice: number) => void;
  totalValue: number;
  agentPhone?: string;
}

const BatchPurchaseModal: React.FC<BatchPurchaseModalProps> = ({
  email,
  setEmail,
  phone,
  setPhone,
  firstName,
  setFirstName,
  lastName,
  setLastName,
  comments,
  setComments,
  financing,
  setFinancing,
  PM,
  setPM,
  compensation,
  setCompensation,
  userRole,
  address,
  lotNumber,
  currentModalStep,
  handleSubmitOffer,
  handleMainClose,
  openTermsModal,
  confirmedItems,
  onUpdateItemPrice,
  totalValue,
  agentPhone,
}) => {

  const isFormValid = firstName && lastName && email && phone && financing !== '' && PM !== '' &&
    confirmedItems.every(item => item.offerPrice && item.offerPrice > 0);

  const { isMobile } = useBreakpoint();
  const formattedTitle = formatTitleWithStepCount(currentModalStep, "Bulk Purchase", isMobile);

  return (
    <ModalWrapper onClose={handleMainClose}>
      <OfferSubmissionHeader
        title={formattedTitle}
        onClose={handleMainClose}
        address=""
        lotNumber={`${confirmedItems.length} Homes`}
        isPropertyCount={true}
      />
      {!isMobile && <ProgressIndicator currentStep={currentModalStep} />}

      <p className="text-left text-dark-gray text-sm mb-4">
        Congratulations on starting the offer process. Offers are non-binding. The following screens will guide you through the buyer process. Once submitted, someone from Lennar will respond to your offer.
      </p>

      {agentPhone && (
        <div className="flex items-center justify-center gap-2 mb-4">
          <span className="text-sm text-gray-600">Contact:</span>
          <ContactInfo phone={agentPhone} />
        </div>
      )}

      <form className="flex flex-col gap-1 text-base">
        {/* Properties List Section */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-3">
            <span className="text-sm font-medium text-dark-gray">Address</span>
            <span className="text-sm font-medium text-dark-gray">Purchase Price</span>
          </div>

          <div className="space-y-3">
            {confirmedItems.map((item) => (
              <div key={item.propertyId} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {/* Property Image */}
                  <PropertyCartImage
                    propertyAddress={item.propertyAddress}
                    propertyId={item.propertyId}
                    className="w-12 h-12"
                  />
                  <div>
                    <p className="text-sm text-gray-900 font-medium">{item.propertyAddress}</p>
                    {item.propertyCity && item.propertyState && item.propertyPostalCode && (
                      <p className="text-xs text-gray-500">{item.propertyCity}, {item.propertyState} {item.propertyPostalCode}</p>
                    )}
                  </div>
                </div>
                <div className="w-24">
                  <FormInputNumber
                    id={`price-${item.propertyId}`}
                    label=""
                    type="number"
                    value={item.offerPrice || 0}
                    onChange={(value) => onUpdateItemPrice(item.propertyId, value)}
                    required
                  />
                </div>
              </div>
            ))}
          </div>

          <div className="mt-3 text-right">
            <span className="text-sm font-semibold text-gray-900">
              Total: {(() => {
                const total = confirmedItems.reduce((sum, item) => {
                  const price = parseFloat(String(item.offerPrice)) || 0;
                  return sum + price;
                }, 0);
                return total === 0 ? '$0' : formatPrice(total);
              })()}
            </span>
          </div>
        </div>

        {/* First Name and Last Name */}
        <div className="flex gap-2">
          <FormInput
            id="firstName"
            label="First Name *"
            type="text"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            width="w-1/2"
            required
          />
          <FormInput
            id="lastName"
            label="Last Name *"
            type="text"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            width="w-1/2"
            required
          />
        </div>

        {/* Email and Phone */}
        <div className="flex gap-2">
          <FormInput
            id="email"
            label="Email *"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            width="w-1/2"
            required
            disabled={true}
          />
          <FormInput
            id="phone"
            label="Phone Number *"
            type="tel"
            value={phone}
            placeholder="Enter your Phone Number"
            onChange={(e) => setPhone(e.target.value)}
            width="w-1/2"
            required
          />
        </div>

        {/* Comments */}
        <FormTextarea
          id="comments"
          label="Comments"
          value={comments || ''}
          onChange={(e) => setComments(e.target.value)}
          placeholder="Enter your Comments"
          minHeight="min-h-[70px]"
        />

        {/* Financing Checkbox */}
        <FinancingCheckbox
          value={financing}
          onChange={setFinancing}
          error={financing === "" ? "Please select an option" : ""}
          isRequired
        />

        {/* PM Checkbox */}
        <PMCheckbox
          value={PM}
          onChange={setPM}
          error={PM === "" ? "Please select an option" : ""}
          isRequired
        />

        {/* Terms of Use */}
        <TermsOfUseAgreement openTermsModal={openTermsModal} />
      </form>

      <ModalButton
        onClick={handleSubmitOffer}
        title="Start Purchase"
        disabled={!isFormValid}
      />
    </ModalWrapper>
  );
};

export default BatchPurchaseModal; 