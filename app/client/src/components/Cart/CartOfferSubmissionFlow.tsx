import React, { useState, useEffect, useMemo } from 'react';
import StartOfferModal from '../Modal/StartOfferModal';
import SubmitOfferModal from '../Modal/SubmitOfferModal';
import SubmitSuccessModal from '../Modal/SubmitSuccessModal';
import OfferTermsOfUseModal from '../Modal/OfferTermsOfUseModal';
import BatchPurchaseModal from './BatchPurchaseModal';
import { OfferSubmissionStep, UserRole } from '../../types/offerTypes';
import { useCart } from '../../contexts/CartContext';
import { getUserFirstName, getUserLastName, getUserEmail, getUserPhoneNumber, getUserRole } from '../../lib/utils/auth';

// Utility function to normalize user role values from AWS Amplify to API format
const normalizeUserRole = (amplifyRole: string): UserRole => {
  console.log('normalizeUserRole - Input:', amplifyRole, 'Type:', typeof amplifyRole);
  
  // Handle empty or undefined roles
  if (!amplifyRole || amplifyRole.trim() === '') {
    console.warn('User role is empty or undefined, defaulting to "investor"');
    return 'investor';
  }

  const trimmedRole = amplifyRole.trim();
  console.log('normalizeUserRole - Trimmed role:', trimmedRole);

  switch (trimmedRole) {
    case 'Agent':
      console.log('normalizeUserRole - Returning: agent');
      return 'agent';
    case 'Investor':
      console.log('normalizeUserRole - Returning: investor');
      return 'investor';
    case 'Investor and Agent':
      console.log('normalizeUserRole - Returning: agent_investor');
      return 'agent_investor';
    default:
      console.warn(`Unknown user role: "${amplifyRole}", defaulting to 'investor'`);
      return 'investor';
  }
};

const CartOfferSubmissionFlow: React.FC = () => {
    const { cart, submitSelectedOffers, isOfferFlowOpen, setIsOfferFlowOpen, updateOfferPrice } = useCart();
    
    const [currentModalStep, setCurrentModalStep] = useState<OfferSubmissionStep>(OfferSubmissionStep.START);
    const [isTermsOfUseOpen, setIsTermsOfUseOpen] = useState(false);
    
    // Form state for bulk submission
    const [email, setEmail] = useState('');
    const [phone, setPhone] = useState('');
    const [firstName, setFirstName] = useState('');
    const [lastName, setLastName] = useState('');
    const [comments, setComments] = useState('');
    const [financing, setFinancing] = useState<"yes" | "no" | "">('');
    const [PM, setPM] = useState<"yes" | "no" | "">('');
    const [compensation, setCompensation] = useState('');
    const [userRole, setUserRole] = useState<UserRole>('investor'); // Default for cart purchases

    const confirmedItems = cart?.items.filter(item => item.isConfirmed) || [];
    const totalValue = confirmedItems
        .filter(item => item.offerPrice)
        .reduce((sum, item) => sum + (item.offerPrice || 0), 0);

    // Pre-populate form fields with user data
    useEffect(() => {
        const prePopulateFormFields = async () => {
            try {
                // Get user attributes from AWS Amplify
                const [userFirstName, userLastName, userEmail, userPhoneNumber, role] = await Promise.all([
                    getUserFirstName(),
                    getUserLastName(),
                    getUserEmail(),
                    getUserPhoneNumber(),
                    getUserRole()
                ]);

                setUserRole(normalizeUserRole(role));
                setFirstName(userFirstName || '');
                setLastName(userLastName || '');
                setEmail(userEmail || '');
                setPhone(userPhoneNumber || '');
            } catch (error) {
                console.error('Error pre-populating form fields:', error);
            }
        };

        if (isOfferFlowOpen) {
            prePopulateFormFields();
        }
    }, [isOfferFlowOpen]);

    // Get address info for header
    const headerAddress = confirmedItems.length === 1 ? (confirmedItems[0]?.propertyAddress || '') : '';
    const lotNumber = confirmedItems.length === 1 ? '' : `${confirmedItems.length} Properties`;
    
    // Calculate total dynamically - reactive to cart changes
    const dynamicTotal = useMemo(() => {
        return confirmedItems.reduce((sum, item) => sum + (item.offerPrice || 0), 0);
    }, [confirmedItems]);

    if (!isOfferFlowOpen) {
        return null;
    }

    const handleMainClose = () => {
        if (isTermsOfUseOpen) {
            setIsTermsOfUseOpen(false);
        } else {
            setCurrentModalStep(OfferSubmissionStep.START); // Reset when closing
            setIsOfferFlowOpen(false);
        }
    };

    const handleStartOffer = () => {
        setCurrentModalStep(OfferSubmissionStep.SUBMIT);
    };

    const handleSubmitOffer = async () => {
        try {
            const userInfo = {
                firstName,
                lastName,
                email,
                phone,
                userRole,
                comments: comments,
                financingRequired: financing === 'yes',
                PMRequired: PM === 'yes'
            };

            await submitSelectedOffers(userInfo);
            setCurrentModalStep(OfferSubmissionStep.SUCCESS);
        } catch (error) {
            console.error('Failed to submit bulk offers:', error);
            // Don't proceed to success modal on error
            // The error will be shown via toast in the cart context
        }
    };

    const openTermsModal = (e: React.MouseEvent) => {
        e.preventDefault();
        setIsTermsOfUseOpen(true);
    };

    const handleUpdateItemPrice = async (propertyId: number, newPrice: number) => {
        await updateOfferPrice(propertyId, newPrice);
    };

    // Determine if we should show batch purchase modal (multiple properties)
    const shouldShowBatchModal = confirmedItems.length > 1;

    return (
        <div className="fixed inset-0 bg-[rgba(0,0,0,0.6)] flex justify-center items-center z-200">
            <div className="bg-white rounded-lg shadow-lg max-w-[600px] w-full max-h-[80vh] overflow-y-auto">
                {isTermsOfUseOpen ? (
                    <OfferTermsOfUseModal onClose={() => setIsTermsOfUseOpen(false)} />
                ) : (
                    <>
                        {currentModalStep === OfferSubmissionStep.START && (
                            <StartOfferModal
                                address={headerAddress}
                                lotNumber={lotNumber}
                                currentModalStep={currentModalStep}
                                handleStartOffer={handleStartOffer}
                                handleMainClose={handleMainClose}
                                isPropertyCount={confirmedItems.length > 1}
                                agentPhone={undefined}
                            />
                        )}
                        {currentModalStep === OfferSubmissionStep.SUBMIT && (
                            shouldShowBatchModal ? (
                                <BatchPurchaseModal
                                    email={email}
                                    setEmail={setEmail}
                                    phone={phone}
                                    setPhone={setPhone}
                                    firstName={firstName}
                                    setFirstName={setFirstName}
                                    lastName={lastName}
                                    setLastName={setLastName}
                                    comments={comments}
                                    setComments={setComments}
                                    financing={financing}
                                    setFinancing={setFinancing}
                                    PM={PM}
                                    setPM={setPM}
                                    compensation={compensation}
                                    setCompensation={setCompensation}
                                    userRole={userRole}
                                    address={headerAddress}
                                    lotNumber={lotNumber}
                                    currentModalStep={currentModalStep}
                                    handleSubmitOffer={handleSubmitOffer}
                                    handleMainClose={handleMainClose}
                                    openTermsModal={openTermsModal}
                                    confirmedItems={confirmedItems}
                                    onUpdateItemPrice={handleUpdateItemPrice}
                                    totalValue={dynamicTotal}
                                    agentPhone={undefined}
                                />
                            ) : (
                                <SubmitOfferModal
                                    email={email}
                                    setEmail={setEmail}
                                    phone={phone}
                                    setPhone={setPhone}
                                    firstName={firstName}
                                    setFirstName={setFirstName}
                                    lastName={lastName}
                                    setLastName={setLastName}
                                    purchasePrice={confirmedItems[0]?.offerPrice || 0}
                                    setPurchasePrice={(value) => handleUpdateItemPrice(confirmedItems[0]?.propertyId, value)}
                                    comments={comments}
                                    setComments={setComments}
                                    financing={financing}
                                    setFinancing={setFinancing}
                                    PM={PM}
                                    setPM={setPM}
                                    compensation={compensation}
                                    setCompensation={setCompensation}
                                    userRole={userRole}
                                    address={headerAddress}
                                    lotNumber={lotNumber}
                                    currentModalStep={currentModalStep}
                                    handleSubmitOffer={handleSubmitOffer}
                                    handleMainClose={handleMainClose}
                                    openTermsModal={openTermsModal}
                                    listPrice={confirmedItems[0]?.listedPrice || 0}
                                    agentPhone={undefined}
                                />
                            )
                        )}
                        {currentModalStep === OfferSubmissionStep.SUCCESS && (
                            <SubmitSuccessModal
                                currentModalStep={currentModalStep}
                                handleMainClose={handleMainClose}
                            />
                        )}
                    </>
                )}
            </div>
        </div>
    );
};

export default CartOfferSubmissionFlow; 