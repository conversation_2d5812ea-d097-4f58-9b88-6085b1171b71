import React from 'react';
import { ShoppingCart } from 'lucide-react';
import { useCart } from '../../contexts/CartContext';

interface CartIconProps {
  className?: string;
}

const CartIcon: React.FC<CartIconProps> = ({ className = '' }) => {
  const { getCartItemCount, setIsCartModalOpen, isLoading } = useCart();
  
  const itemCount = getCartItemCount();

  const handleClick = () => {
    setIsCartModalOpen(true);
  };

  return (
    <div className="relative">
      <button
        onClick={handleClick}
        disabled={isLoading}
        className={`
          flex items-center justify-center rounded-sm border border-medium-gray-20 
          cursor-pointer hover:bg-light-gray h-[32px] px-2 transition-colors
          ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
          ${className}
        `}
        aria-label={`Shopping cart with ${itemCount} items`}
      >
        <div className="relative">
          <ShoppingCart size={16} strokeWidth={1.5} className="text-dark-gray" />
          
          {/* Badge */}
          {itemCount > 0 && (
            <div className="absolute -top-2 -right-2 bg-green-primary text-white text-xs font-medium rounded-full w-5 h-5 flex items-center justify-center">
              {itemCount > 99 ? '99+' : itemCount}
            </div>
          )}
        </div>
        
        <span className="ml-2 text-sm text-dark-gray whitespace-nowrap">
          Cart
        </span>
      </button>
    </div>
  );
};

export default CartIcon; 