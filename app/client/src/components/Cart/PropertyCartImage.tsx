import React, { useState, useEffect } from 'react';
import { Home } from 'lucide-react';
import fallbackImage from '../../assets/images/TX Core_Watermill_Ramsey_3330_Living_4.jpg';
import { useMarketplaceMapContext } from '../../contexts/MarketplaceMapContext';
import { usePropertyImages } from '../../hooks/usePropertyImages';

interface PropertyCartImageProps {
  propertyAddress: string;
  propertyId: number;
  className?: string;
}

const PropertyCartImage: React.FC<PropertyCartImageProps> = ({ 
  propertyAddress, 
  propertyId,
  className = "w-20 h-20" 
}) => {
  const { allProperties } = useMarketplaceMapContext();
  const [imageUrl, setImageUrl] = useState<string>(fallbackImage);
  
  // Find the property data to get real images
  const propertyData = allProperties?.find(p => p.property_id === propertyId);
  const { images } = usePropertyImages(propertyData);
  
  useEffect(() => {
    // Use the first image from property images if available, otherwise fallback
    if (images && images.length > 0) {
      setImageUrl(images[0].url);
    } else {
      setImageUrl(fallbackImage);
    }
  }, [images]);

  return (
    <div className={`${className} bg-gray-200 rounded-lg overflow-hidden shadow-sm`}>
      <img
        src={imageUrl}
        alt={propertyAddress}
        className="w-full h-full object-cover"
        onError={(e) => {
          // Fallback to placeholder if image fails to load
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
          target.parentElement!.innerHTML = `
            <div class="w-full h-full bg-gray-300 rounded flex items-center justify-center">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" class="text-gray-500">
                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                <polyline points="9,22 9,12 15,12 15,22"/>
              </svg>
            </div>
          `;
        }}
      />
    </div>
  );
};

export default PropertyCartImage; 