import React, { useState, useEffect } from 'react';
import { Trash2 } from 'lucide-react';
import { useCart } from '../../contexts/CartContext';
import { CartItem } from '../../types/cartTypes';
import { formatPrice, formatNumber } from '../../lib/utils/formatUtils';
import PropertyCartImage from './PropertyCartImage';
import { AlertCircle } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  AlertDialogOverlay,
  AlertDialogPortal,
} from '../ui/alert-dialog';

interface CartItemRowProps {
  item: CartItem;
}

const CartItemRow: React.FC<CartItemRowProps> = ({ item }) => {
  const { removeFromCart, toggleItemConfirmation, updateOfferPrice } = useCart();

  // Format price without decimals for display
  const formatPriceWithoutDecimals = (price: number): string => {
    return Math.round(price).toLocaleString();
  };

  const [offerInput, setOfferInput] = useState(
    item.offerPrice ? formatPriceWithoutDecimals(item.offerPrice) : ''
  );

  // Update offerInput when item.offerPrice changes (e.g., when added from Pro Forma)
  useEffect(() => {
    if (item.offerPrice) {
      setOfferInput(formatPriceWithoutDecimals(item.offerPrice));
    }
  }, [item.offerPrice]);

  const handleOfferPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9,]/g, ''); // Allow only numbers and commas
    setOfferInput(value);

    // Convert to number and update if valid
    const numValue = parseFloat(value.replace(/,/g, ''));
    if (!isNaN(numValue) && numValue > 0) {
      updateOfferPrice(item.propertyId, Math.round(numValue)); // Round to remove decimals
    }
  };

  const handleOfferPriceBlur = () => {
    // Format the input value on blur without decimals
    const numValue = parseFloat(offerInput.replace(/,/g, ''));
    if (!isNaN(numValue) && numValue > 0) {
      setOfferInput(formatPriceWithoutDecimals(numValue));
    } else {
      setOfferInput('');
    }
  };

  const handleRemove = () => {
    removeFromCart(item.propertyId);
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4 bg-white">
      <div className="flex gap-3">
        {/* Checkbox */}
        <div className="flex items-start pt-2">
          <input
            type="checkbox"
            checked={item.isConfirmed}
            onChange={() => toggleItemConfirmation(item.propertyId)}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
        </div>

        {/* Property Image */}
        <div className="flex-shrink-0">
          <PropertyCartImage
            propertyAddress={item.propertyAddress}
            propertyId={item.propertyId}
          />
        </div>

        {/* Property Details */}
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-gray-900 text-sm mb-1">
            {item.propertyAddress}
          </h3>

          <div className="text-sm text-gray-600 mb-2">
            {item.beds || 0} Bd  {item.baths || 0} Bath  {item.sqft ? formatNumber(item.sqft) : 0} Sq Ft
          </div>

          <div className="text-sm text-gray-600 mb-2">
            <span className="font-medium">Asking:</span> {item.listedPrice ? formatPrice(item.listedPrice) : 'N/A'}
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600 font-medium">Purchase Price:</span>
            <div className="flex items-center">
              <span className="text-sm text-gray-600">$</span>
              <input
                type="text"
                value={offerInput}
                onChange={handleOfferPriceChange}
                onBlur={handleOfferPriceBlur}
                placeholder={item.listedPrice ? formatPriceWithoutDecimals(item.listedPrice) : '0'}
                className="py-1 text-sm text-blue-500 border-b border-gray-300 focus:border-blue-500 focus:outline-none bg-transparent w-18"
              />
            </div>
          </div>
        </div>

        {/* Remove Button */}
        <div className="flex items-start">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <button
                className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                title="Remove from cart"
              >
                <Trash2 size={18} />
              </button>
            </AlertDialogTrigger>
            <AlertDialogPortal>
              <AlertDialogOverlay className="z-[1100]" />
              <AlertDialogContent className="z-[1100] !fixed !left-[50%] !top-[50%] !translate-x-[-50%] !translate-y-[-50%]">
                <AlertDialogHeader>
                  <AlertDialogTitle className="flex items-center gap-2">
                    <AlertCircle className="w-5 h-5 text-red-500" />
                    Remove Home From Cart
                  </AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to remove this home from your cart?
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className="flex flex-row justify-center items-center">
                  <AlertDialogCancel className="m-0">Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleRemove}
                    className="bg-red-600 hover:bg-red-700 focus:ring-red-600 ml-4"
                  >
                    Remove Home
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialogPortal>
          </AlertDialog>
        </div>
      </div>
    </div>
  );
};

export default CartItemRow; 