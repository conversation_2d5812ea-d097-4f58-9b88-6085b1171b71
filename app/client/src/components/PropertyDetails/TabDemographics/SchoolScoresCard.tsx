import React from 'react';
import { GreatSchoolsScoresProps } from '../../../types/PropertyDetailPage.types';
import TabDemographicsTooltip from './TabDemographicsTooltip';

interface SchoolScoresCardProps {
  scores: GreatSchoolsScoresProps;
}

const SchoolScoresCard: React.FC<SchoolScoresCardProps> = ({ scores }) => (
  <div className="flex flex-col rounded-lg border border-medium-gray-20 p-2 h-full">
    <div className="flex flex-row gap-2">
      <h5 className="flex text-left text-sm text-dark-gray">Great Schools Scores</h5>
      <TabDemographicsTooltip title="Great Schools Scores" />
    </div>
    <table className="w-full">
      <tbody>
        <tr>
          <td className="text-left text-sm text-dark-gray">Elementary:</td>
          <td className="text-right text-sm text-dark-gray font-bold">{scores.elementary}</td>
        </tr>
        <tr>
          <td className="text-left text-sm text-dark-gray">Middle:</td>
          <td className="text-right text-dark-gray text-sm font-bold">{scores.middle}</td>
        </tr>
        <tr>
          <td className="text-left text-sm text-dark-gray">High:</td>
          <td className="text-right text-dark-gray text-sm font-bold">{scores.high}</td>
        </tr>
      </tbody>
    </table>
  </div>
);

export default SchoolScoresCard;