import React from 'react';
import { GreatSchoolsScoresProps } from '../../../types/PropertyDetailPage.types';

interface SchoolScoresCardProps {
  scores: GreatSchoolsScoresProps;
}

const SchoolScoresCard: React.FC<SchoolScoresCardProps> = ({ scores }) => (
  <div className="flex flex-col justify-between rounded-lg border border-medium-gray-20 p-2 h-full bg-light-blue">
    <h5 className="flex text-left text-sm text-[var(--color-text-black)] h-9">Great Schools Scores</h5>
    <table className="w-full">
      <tbody>
        <tr>
          <td className="text-left text-sm text-[var(--color-text-black)] ">Elementary:</td>
          <td className="text-right text-sm text-[var(--color-text-black)] font-bold">{scores.elementary}</td>
        </tr>
        <tr>
          <td className="text-left text-sm text-[var(--color-text-black)]">Middle:</td>
          <td className="text-right text-[var(--color-text-black)] text-sm font-bold">{scores.middle}</td>
        </tr>
        <tr>
          <td className="text-left text-sm text-[var(--color-text-black)]">High:</td>
          <td className="text-right text-[var(--color-text-black)] text-sm font-bold">{scores.high}</td>
        </tr>
      </tbody>
    </table>
  </div>
);

export default SchoolScoresCard;