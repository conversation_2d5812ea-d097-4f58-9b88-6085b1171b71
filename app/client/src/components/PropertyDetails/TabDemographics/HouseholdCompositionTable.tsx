import ErrorMessage from "@/components/Common/ErrorMessage";
import LoadingSpinner from "@/components/Common/LoadingSpinner";
import { HouseholdCompositionProps } from "@/types/PropertyDetailPage.types";
import NoDataDisplay from "./NoDataDisplay";
import downloadLink from "@/constants/downloadLink";
import { formatSegmentName } from "@/lib/utils/formatUtils";

interface HouseholdCompositionTableProps {
  householdComposition: HouseholdCompositionProps[];
  isLoading?: boolean;
  error?: Error | null;
}

const HouseholdCompositionTable: React.FC<HouseholdCompositionTableProps> = ({ householdComposition, isLoading, error }) => {
  if (isLoading) {
    return (
      <div className="flex w-full min-h-[182px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex w-full min-h-[182px]">
        <ErrorMessage
          message="We couldn't retrieve the data at this time. Please try adjusting your search parameters or try again later."
        />
      </div>
    );
  }

  if (!householdComposition || householdComposition.length === 0) {
    return (
      <NoDataDisplay entityName="household composition data" isAreaContext={true} />
    );
  }


  return (
    <div className="w-full min-h-[182px] mt-1">
      {/* Header Row */}
      <div className="grid grid-cols-[2fr_1fr_1fr] gap-3 border-b border-[var(--color-light-gray)]">
        <div className="text-left font-bold text-xs text-dark-gray">Segment</div>
        <div className="text-center font-bold text-xs text-dark-gray">Percentage</div>
        <div className="text-center font-bold text-xs text-dark-gray">Link</div>
      </div>

      {/* Data Rows */}
      {householdComposition.map((item, index) => {

        return (
          <div
            key={index}
            className="grid grid-cols-[2fr_1fr_1fr] gap-3 border-b border-medium-gray-20"
          >
            <div className="flex items-center justify-start h-8 text-xs text-dark-gray">
              {formatSegmentName(item.column_name)}
            </div>
            <div className="flex items-center justify-center h-8 text-xs text-dark-gray">
              {item.percentage}%
            </div>
            <div className="flex items-center justify-center h-8 text-xs text-dark-gray">
              <a
                href={downloadLink[item.column_name.split('_')[0].toUpperCase()]}
                target="_blank"
                rel="noopener noreferrer"
                className="text-button-blue underline"
              >
                Download
              </a>
            </div>
          </div>
        )
      })}
    </div>
  );
};

export default HouseholdCompositionTable;