import React from 'react';
import { DemographicsDataProps } from '../../../types/PropertyDetailPage.types';
import { DistanceDriveModeParams } from '@/lib/utils/csvExportUtils';
import TabDemographicsSectionWrapper from './TabDemographicsSectionWrapper';
import TabDemographicsSectionHeader from './TabDemographicsSectionHeader';
import DemographicsDetails from './DemographicsDetails';
import ActionButton from '../../Common/ActionButton';


interface DemographicsSectionProps {
  demographics: DemographicsDataProps;
  driveTime: number;
  setDriveTime: React.Dispatch<React.SetStateAction<number>>;
  distance: number;
  setDistance: React.Dispatch<React.SetStateAction<number>>;
  isDistanceMode: boolean;
  setIsDistanceMode: React.Dispatch<React.SetStateAction<boolean>>;
  isLoading?: boolean;
  error?: Error | null;
  exportCSV?: (params: DistanceDriveModeParams) => Promise<void>;
  isExportLoading?: boolean;
}

const DemographicsSection: React.FC<DemographicsSectionProps> = ({
  demographics,
  driveTime,
  setDriveTime,
  distance,
  setDistance,
  isDistanceMode,
  setIsDistanceMode,
  isLoading,
  error,
  exportCSV,
  isExportLoading = false,

}) => {

  const onClickCSVButton = async () => {
    if (!exportCSV) {
      console.error('Export function not provided');
      return;
    }

    const mode = isDistanceMode ? 'distance' : 'drive';
    const params: DistanceDriveModeParams = {
      distance: distance,
      mode: mode,
      time: driveTime,
    };

    try {
      await exportCSV(params);
    } catch (error) {
      console.error('Error exporting CSV:', error);
    }
  };

  return (
    <TabDemographicsSectionWrapper className="min-h-[270px]">
      <TabDemographicsSectionHeader
        title="Demographics"
        rightContent={<ActionButton text="Export CSV" onClick={onClickCSVButton} disabled={isExportLoading} />}
      />
      <DemographicsDetails
        demographics={demographics}
        driveTime={driveTime}
        setDriveTime={setDriveTime}
        distance={distance}
        setDistance={setDistance}
        isDistanceMode={isDistanceMode}
        setIsDistanceMode={setIsDistanceMode}
        isLoading={isLoading}
        error={error}
      />
    </TabDemographicsSectionWrapper>
  );
};

export default DemographicsSection;