import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface PaginationProps {
  currentPage: number;
  handleNextPage: () => void;
  handlePreviousPage: () => void;
  isNextDisabled: boolean;
  isPreviousDisabled: boolean;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  handleNextPage,
  handlePreviousPage,
  isNextDisabled,
  isPreviousDisabled
}) => {
  return (
    <div className="flex justify-end items-center gap-1">
      <button
        onClick={handlePreviousPage}
        disabled={isPreviousDisabled}
        className="rounded p-1 flex items-center justify-center text-dark-gray disabled:opacity-30 disabled:cursor-not-allowed"
      >
        <ChevronLeft size={16} />
      </button>

      <span className="border border-medium-gray-20 text-xs px-2 py-1 rounded text-dark-gray">
        {currentPage}
      </span>

      <button
        onClick={handleNextPage}
        disabled={isNextDisabled}
        className="p-1 flex items-center justify-center text-dark-gray disabled:opacity-30 disabled:cursor-not-allowed"
      >
        <ChevronRight size={16} />
      </button>
    </div>
  );
};

export default Pagination;