import React, { useState } from 'react';
import { useNearbyChainStore } from '@spatiallaser/map';
import { useMarketplaceMapContext } from '../../../contexts/MarketplaceMapContext';
import { useDemograpchics } from '../../../hooks/useDemographics';
import { useDemographicsData } from '../../../hooks/useDemographicsData';
import { useMajorEmployersData } from '../../../hooks/useMajorEmployersData';
import { useHouseholdData } from '../../../hooks/useHouseholdData';
import { usePagination } from '../../../hooks/usePagination';
import { DistanceDriveModeParams, downloadDemographicsCSV, downloadMajorEmployersToCSV, handleCSVExport } from '../../../lib/utils/csvExportUtils';

import SummaryCards from './SummaryCards';
import DemographicsSection from './DemographicsSection';
import MajorEmployersSection from './MajorEmployersSection';
import HouseholdCompositionSection from './HouseholdCompositionSection';
import FavorableBrandsNearbySection from './FavorableBrandsNearbySection';


export interface SummaryCardsProps {
  greatSchoolsScores: {
    elementary: number;
    middle: number;
    high: number;
  }
  populationGrowth5Yr: number;
  incomeGrowth5Yr: number;
  floodZone: string;
  bachelorsOrAbove: number;
  crimeScore: number;
  medianHHIncome: string;
}

interface TabDemographicsComponentProps {
  summaryCardsData: SummaryCardsProps;
}

const TabDemographics: React.FC<TabDemographicsComponentProps> = ({ summaryCardsData }) => {
  const [demographicsCSVLoading, setDemographicsCSVLoading] = useState<boolean>(false);
  const [majorEmployersCSVLoading, setMajorEmployersCSVLoading] = useState<boolean>(false);

  const ITEMS_PER_PAGE = 5;

  const { greatSchoolsScores, floodZone, bachelorsOrAbove, crimeScore, populationGrowth5Yr, incomeGrowth5Yr, medianHHIncome } = summaryCardsData;

  const {
    eventCoordinates,
    sadChainStoreDistance,
    setSadChainStoreDistance
  } = useMarketplaceMapContext();

  const lat = eventCoordinates?.[1] ?? null;
  const lng = eventCoordinates?.[0] ?? null;

  const {
    demographicsDistance,
    setDemographicsDistance,
    demographicsDriveTime,
    setDemographicsDriveTime,
    isDemographicsDistanceMode,
    setDemographicsIsDistanceMode,

    majorEmployersDistance,
    setMajorEmployersDistance,
    majorEmployersDriveTime,
    setMajorEmployersDriveTime,
    isMajorEmployersDistanceMode,
    setMajorEmployersIsDistanceMode,

    householdDriveTime,
    setHouseholdDriveTime,
    householdDistance,
    setHouseholdDistance,
    isHouseholdDistanceMode,
    setHouseholdIsDistanceMode,
  } = useDemograpchics();

  const {
    data: areaDemographicData,
    isLoading: areaDemographicLoading,
    error: areaDemographicError,
  } = useDemographicsData(
    lat, lng, demographicsDistance, demographicsDriveTime, isDemographicsDistanceMode
  );

  const {
    data: majorEmployerData,
    isLoading: majorEmployersLoading,
    error: majorEmployersError
  } = useMajorEmployersData(
    lat, lng, majorEmployersDistance, majorEmployersDriveTime, isMajorEmployersDistanceMode
  );

  const {
    paginatedData: paginatedEmployerData,
    currentPage: employerCurrentPage,
    handleNextPage: handleEmployerNextPage,
    handlePreviousPage: handleEmployerPreviousPage,
    totalItems: totalEmployerItems
  } = usePagination({
    data: majorEmployerData || [],
    itemsPerPage: ITEMS_PER_PAGE
  });

  const {
    data: householdData,
    isLoading: householdLoading,
    error: householdError
  } = useHouseholdData(
    lat, lng, householdDistance, householdDriveTime, isHouseholdDistanceMode
  );

  const {
    paginatedData: paginatedHouseholdData,
    currentPage: householdCurrentPage,
    handleNextPage: handleHouseholdNextPage,
    handlePreviousPage: handleHouseholdPreviousPage,
    totalItems: totalHouseholdItems
  } = usePagination({
    data: householdData || [],
    itemsPerPage: ITEMS_PER_PAGE
  });


  const handleDemographicsExport = async (
    params: DistanceDriveModeParams,
    coordinates: { lat: number | null; lng: number | null }
  ) => {

    return handleCSVExport(
      params,
      coordinates,
      downloadDemographicsCSV,
      setDemographicsCSVLoading
    );
  };

  const handleMajorEmployersExport = async (
    params: DistanceDriveModeParams,
    coordinates: { lat: number | null; lng: number | null }
  ) => {

    return handleCSVExport(
      params,
      coordinates,
      downloadMajorEmployersToCSV,
      setMajorEmployersCSVLoading
    );
  };

  const {
    data: chainData,
    isLoading: chainLoading,
    isError: chainError,
  } = useNearbyChainStore();

  const sortedFavorableBrands = React.useMemo(() => {
    if (!chainData?.favorable) return [];

    return [...chainData.favorable].sort((a, b) => b.distance - a.distance);
  }, [chainData?.favorable]);

  const {
    paginatedData: paginatedBrandData,
    currentPage: brandCurrentPage,
    handleNextPage: handleBrandNextPage,
    handlePreviousPage: handleBrandPreviousPage,
    totalItems: totalBrandItems
  } = usePagination({
    data: sortedFavorableBrands,
    itemsPerPage: ITEMS_PER_PAGE
  });

  return (
    <div className="flex flex-col justify-center items-center gap-4 w-full ">
      <SummaryCards
        greatSchoolsScores={greatSchoolsScores}
        populationGrowth5Yr={populationGrowth5Yr}
        incomeGrowth5Yr={incomeGrowth5Yr}
        floodZone={floodZone}
        bachelorsOrAbove={bachelorsOrAbove}
        crimeScore={crimeScore}
        medianHHIncome={medianHHIncome}
      />

      <DemographicsSection
        demographics={areaDemographicData}
        driveTime={demographicsDriveTime}
        setDriveTime={setDemographicsDriveTime}
        distance={demographicsDistance}
        setDistance={setDemographicsDistance}
        isDistanceMode={isDemographicsDistanceMode}
        setIsDistanceMode={setDemographicsIsDistanceMode}
        isLoading={areaDemographicLoading}
        error={areaDemographicError}
        exportCSV={(params: DistanceDriveModeParams) => handleDemographicsExport(params, { lat, lng })}
        isExportLoading={demographicsCSVLoading}
      />

      <MajorEmployersSection
        employers={paginatedEmployerData}
        driveTime={majorEmployersDriveTime}
        setDriveTime={setMajorEmployersDriveTime}
        distance={majorEmployersDistance}
        setDistance={setMajorEmployersDistance}
        isDistanceMode={isMajorEmployersDistanceMode}
        setIsDistanceMode={setMajorEmployersIsDistanceMode}
        currentPage={employerCurrentPage}
        handleNextPage={handleEmployerNextPage}
        handlePreviousPage={handleEmployerPreviousPage}
        totalItems={totalEmployerItems}
        itemsPerPage={ITEMS_PER_PAGE}
        isLoading={majorEmployersLoading}
        error={majorEmployersError}
        exportCSV={(params: DistanceDriveModeParams) => handleMajorEmployersExport(params, { lat, lng })}
        isExportLoading={majorEmployersCSVLoading}
      />

      <FavorableBrandsNearbySection
        favorableBrands={paginatedBrandData}
        distance={sadChainStoreDistance}
        setDistance={setSadChainStoreDistance}
        currentPage={brandCurrentPage}
        handleNextPage={handleBrandNextPage}
        handlePreviousPage={handleBrandPreviousPage}
        totalItems={totalBrandItems}
        itemsPerPage={ITEMS_PER_PAGE}
        isLoading={chainLoading}
        error={chainError}
      />

      <HouseholdCompositionSection
        householdComposition={paginatedHouseholdData}
        driveTime={householdDriveTime}
        setDriveTime={setHouseholdDriveTime}
        distance={householdDistance}
        setDistance={setHouseholdDistance}
        isDistanceMode={isHouseholdDistanceMode}
        setIsDistanceMode={setHouseholdIsDistanceMode}
        currentPage={householdCurrentPage}
        handleNextPage={handleHouseholdNextPage}
        handlePreviousPage={handleHouseholdPreviousPage}
        totalItems={totalHouseholdItems}
        itemsPerPage={ITEMS_PER_PAGE}
        isLoading={householdLoading}
        error={householdError}
      />
    </div>
  );
};


export default TabDemographics;
