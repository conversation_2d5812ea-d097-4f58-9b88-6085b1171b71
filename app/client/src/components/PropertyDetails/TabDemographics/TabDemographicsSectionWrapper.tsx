import React, { ReactNode } from 'react';

interface TabDemographicsSectionWrapperProps {
  className?: string;
  children: ReactNode;
}

const TabDemographicsSectionWrapper: React.FC<TabDemographicsSectionWrapperProps> = ({
  className = "",
  children
}) => {
  return (
    <section className={`flex flex-col w-full px-8 py-6 gap-4 border border-medium-gray-20 rounded-md ${className}`}>
      {children}
    </section>
  );
};

export default TabDemographicsSectionWrapper;