import React, { ReactNode } from 'react';
import { useBreakpoint } from "../../../hooks/useBreakpoint"

interface TabDemographicsSectionWrapperProps {
  className?: string;
  children: ReactNode;
}

const TabDemographicsSectionWrapper: React.FC<TabDemographicsSectionWrapperProps> = ({
  className = "",
  children
}) => {
  const { isMobile } = useBreakpoint();
  return (
    <section className={`flex flex-col w-full ${isMobile ? "p-2" : "px-8 py-6"}  gap-4 border border-medium-gray-20 rounded-md ${className}`}>
      {children}
    </section>
  );
};

export default TabDemographicsSectionWrapper;