import { MobileTooltip } from '@/components/ui/tooltip';
import { demographicsTooltips } from '@/constants/demographicsTooltips';
import { Info } from 'lucide-react';
import React from 'react';

interface TabDemographicsTooltipProps {
  title: string;
  className?: string;
}

const TabDemographicsTooltip: React.FC<TabDemographicsTooltipProps> = ({
  title,
  className = ""
}) => {
  return (
    <MobileTooltip
      content={
        <div className="bg-white text-dark-gray rounded-md py-2 px-1 z-300 border border-medium-gray-20 text-xs">
          {demographicsTooltips[title]}
        </div>
      }
    >
      <div className={`flex items-center justify-center self-start h-5 w-5 ${className}`}>
        <Info className="cursor-pointer text-dark-gray hover:text-primary transition-colors" />
      </div>
    </MobileTooltip>
  );
};

export default TabDemographicsTooltip;