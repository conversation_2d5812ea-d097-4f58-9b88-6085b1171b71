import React from 'react';

interface StatCardProps {
  value: string | number;
  title: React.ReactNode;
  className?: string;
}

const StatCard: React.FC<StatCardProps> = ({ value, title, className }) => (
  <div className={`flex flex-col justify-end rounded-lg border border-medium-gray-20 h-full min-h-29 p-2 ${className}`}>
    <h4 className="text-base font-bold text-[var(--color-text-black)] text-left">{value}</h4>
    <p className="h-9 text-left text-sm text-[var(--color-text-black)]">{title}</p>
  </div>
);

export default StatCard;