import React from 'react';
import TabDemographicsTooltip from './TabDemographicsTooltip';

interface StatCardProps {
  value: string | number;
  title: string;
  className?: string;
}

const StatCard: React.FC<StatCardProps> = ({ value, title, className }) => {
  return (
    <div className={`flex flex-col justify-end rounded-lg border border-medium-gray-20 h-full p-2 ${className}`}>
      <h4 className="text-base font-bold text-dark-gray text-left">{value}</h4>
      <div className="flex flex-row gap-2">
        <p className="text-left text-sm text-dark-gray">{title}</p>
        <TabDemographicsTooltip title={title} />
      </div>
    </div>
  );
};

export default StatCard;