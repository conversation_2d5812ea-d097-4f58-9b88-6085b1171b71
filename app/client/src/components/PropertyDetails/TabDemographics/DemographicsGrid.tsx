import React from 'react';
import { DemographicsDataProps } from '../../../types/PropertyDetailPage.types';
import { formatNumber, formatPercentage2, formatPrice } from '../../../lib/utils/formatUtils';
import LoadingSpinner from '@/components/Common/LoadingSpinner';
import ErrorMessage from '@/components/Common/ErrorMessage';

interface DemographicsGridProps {
  demographics: DemographicsDataProps;
  isLoading?: boolean;
  error?: Error | null;
}

const DemographicsGrid: React.FC<DemographicsGridProps> = ({ demographics, isLoading, error }) => {
  if (isLoading) {
    return (
      <div className="flex w-full min-h-[128px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex w-full min-h-[128px]">
        <ErrorMessage
          message="We couldn't retrieve the demographic information at this time. Please try adjusting your search parameters or try again later."
        />
      </div>
    );
  }

  if (!demographics) {
    return (
      <div className="flex flex-col justify-center items-center text-center w-full min-h-[128px] ">
        <p className="text-[13px] text-dark-gray">No data found for this area</p>
      </div>
    );
  }
  return (
    <div className="grid grid-cols-[auto_1fr] gap-y-3 items-center">
      <div className="text-left text-xs text-dark-gray">Total Population:</div>
      <div className="text-right text-xs text-dark-gray">{formatNumber(demographics.total_population)}</div>

      <div className="text-left text-xs text-dark-gray">Total Household:</div>
      <div className="text-right text-xs text-dark-gray">{formatNumber(demographics.total_households)}</div>

      <div className="text-left text-xs text-dark-gray">5 Years Population Growth:</div>
      <div className="text-right text-xs text-dark-gray">{formatPercentage2(demographics.five_year_pop_growth)}</div>

      <div className="text-left text-xs text-dark-gray">5 Years Household Income Growth:</div>
      <div className="text-right text-xs text-dark-gray">{formatPercentage2(demographics.five_year_hh_income_growth)}</div>

      <div className="text-left text-xs text-dark-gray">Median Household Income:</div>
      <div className="text-right text-xs text-dark-gray">{formatPrice(demographics.median_hh_income)}</div>
    </div>
  );
};

export default DemographicsGrid;