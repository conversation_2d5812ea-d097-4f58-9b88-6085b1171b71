import React from 'react';
import { HouseholdCompositionProps } from '../../../types/PropertyDetailPage.types';
import DriveTimeDistanceToggle from './DriveTimeDistanceToggle';
import CompositionTable from './HouseholdCompositionTable';
import Pagination from './Pagination';


interface HouseholdCompositionContentProps {
  householdComposition: HouseholdCompositionProps[];
  driveTime: number;
  setDriveTime: React.Dispatch<React.SetStateAction<number>>;
  distance: number;
  setDistance: React.Dispatch<React.SetStateAction<number>>;
  isDistanceMode: boolean;
  setIsDistanceMode: React.Dispatch<React.SetStateAction<boolean>>;
  currentPage: number;
  handleNextPage: () => void;
  handlePreviousPage: () => void;
  totalItems: number;
  itemsPerPage: number;
  isLoading: boolean;
  error: Error | null;
}

const HouseholdCompositionContent: React.FC<HouseholdCompositionContentProps> = ({
  householdComposition,
  driveTime,
  setDriveTime,
  distance,
  setDistance,
  isDistanceMode,
  setIsDistanceMode,
  currentPage,
  handleNextPage,
  handlePreviousPage,
  totalItems,
  itemsPerPage,
  isLoading,
  error
}) => {

  return (
    <>
      <DriveTimeDistanceToggle
        driveTime={driveTime}
        setDriveTime={setDriveTime}
        distance={distance}
        setDistance={setDistance}
        isDistanceMode={isDistanceMode}
        setIsDistanceMode={setIsDistanceMode}
      />

      <CompositionTable householdComposition={householdComposition} isLoading={isLoading} error={error} />

      <Pagination
        currentPage={currentPage}
        handleNextPage={handleNextPage}
        handlePreviousPage={handlePreviousPage}
        isNextDisabled={currentPage * itemsPerPage >= totalItems}
        isPreviousDisabled={currentPage === 1}
      />
    </>
  );
};

export default HouseholdCompositionContent;