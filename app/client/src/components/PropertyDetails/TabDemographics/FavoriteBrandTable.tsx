import React from 'react';
import { FavorableBrandProps } from '../../../types/PropertyDetailPage.types';
import { formatMiles } from '../../../lib/utils/formatUtils';
import LoadingSpinner from '@/components/Common/LoadingSpinner';
import ErrorMessage from '@/components/Common/ErrorMessage';
import NoDataDisplay from './NoDataDisplay';

interface FavoriteBrandTableProps {
  brands: FavorableBrandProps[];
  isLoading?: boolean;
  error?: Error | null;
}

const FavoriteBrandTable: React.FC<FavoriteBrandTableProps> = ({ brands, isLoading, error }) => {
  if (isLoading) {
    return (
      <div className="flex w-full min-h-[182px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex w-full min-h-[182px]">
        <ErrorMessage
          message="We couldn't retrieve the data at this time. Please try adjusting your search parameters or try again later."
        />
      </div>
    );
  }

  if (!brands || brands.length === 0) {
    return (
      <NoDataDisplay entityName="Favorable Brand data" isAreaContext={true} />
    );
  }

  return (
    <div className="w-full min-h-[182px] mt-1">
      {/* Table Header */}
      <div className="border-b border-medium-gray-20">
        <div className="grid grid-cols-2 gap-3">
          <div className="text-left font-semibold text-xs text-dark-gray">Chain name</div>
          <div className="text-center font-semibold text-xs text-dark-gray">Distance</div>
        </div>
      </div>

      {/* Table Body */}
      <div className="bg-white">
        {brands.map((brand, index) => (
          <div
            key={index}
            className={`grid grid-cols-2 space-x-3 border-b border-medium-gray-20`}
          >
            <div className="flex items-center justify-start h-8 text-xs text-dark-gray">{brand.chain_name}</div>
            <div className="flex items-center justify-center h-8 text-xs text-dark-gray">{formatMiles(brand.distance)}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FavoriteBrandTable;