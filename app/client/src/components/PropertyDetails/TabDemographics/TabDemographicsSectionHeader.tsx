import React, { ReactNode } from 'react';

interface TabDemographicsSectionHeaderProps {
  title: string;
  rightContent?: ReactNode;
  className?: string;
}

const TabDemographicsSectionHeader: React.FC<TabDemographicsSectionHeaderProps> = ({
  title,
  rightContent,
  className = ""
}) => {
  return (
    <div className={`flex justify-between items-center ${className}`}>
      <h3 className="text-base font-bold text-dark-gray">{title}</h3>
      {rightContent && <div>{rightContent}</div>}
    </div>
  );
};

export default TabDemographicsSectionHeader;