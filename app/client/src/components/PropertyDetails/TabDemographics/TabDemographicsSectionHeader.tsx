import React, { ReactNode } from 'react';
import TabDemographicsTooltip from './TabDemographicsTooltip';

interface TabDemographicsSectionHeaderProps {
  title: string;
  rightContent?: ReactNode;
  className?: string;
}

const TabDemographicsSectionHeader: React.FC<TabDemographicsSectionHeaderProps> = ({
  title,
  rightContent,
  className = ""
}) => {
  return (
    <div className={`flex justify-between items-center ${className}`}>
      <div className="flex gap-2"><h3 className="text-base font-bold text-dark-gray">{title}</h3><TabDemographicsTooltip title={title} /></div>
      {rightContent && <div>{rightContent}</div>}
    </div>
  );
};

export default TabDemographicsSectionHeader;