import React from 'react';
import SchoolScoresCard from './SchoolScoresCard';
import StatCard from './StatCard';
import CardContainer from './CardContainer';
import { formatPercentage2, formatPrice } from '@/lib/utils/formatUtils';

interface SummaryCardsProps {
  greatSchoolsScores: {
    elementary: number;
    middle: number;
    high: number;
  }
  populationGrowth5Yr: number;
  incomeGrowth5Yr: number;
  floodZone: string;
  bachelorsOrAbove: number;
  crimeScore: number;
  medianHHIncome: string;
}

const SummaryCards: React.FC<SummaryCardsProps> = ({
  greatSchoolsScores,
  populationGrowth5Yr,
  incomeGrowth5Yr,
  floodZone,
  bachelorsOrAbove,
  crimeScore,
  medianHHIncome
}) => {
  return (
    <CardContainer>
      {greatSchoolsScores && <SchoolScoresCard scores={greatSchoolsScores} />}
      <StatCard value={floodZone === "N" ? "No" : "Yes"} title="Flood Zone" className="bg-light-blue" />
      <StatCard value={formatPercentage2(populationGrowth5Yr)} title="Total 5 Year Population Growth" />
      <StatCard value={formatPercentage2(incomeGrowth5Yr)} title="Total 5 Year Income Growth" />
      <StatCard value={formatPrice(medianHHIncome)} title="Median HH Income" />
      <StatCard value={`${crimeScore}/10`} title={<>Crime Score <br />(1 is lowest)</>} />
      <StatCard value={formatPercentage2(bachelorsOrAbove)} title="Bachelor's & Above" />
    </CardContainer>
  );
};

export default SummaryCards;