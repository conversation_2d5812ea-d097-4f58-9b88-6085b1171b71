import React from 'react';
import { MajorEmployerDataProps } from '../../../types/PropertyDetailPage.types';
import { formatDistanceToMiles, formatNumber } from '../../../lib/utils/formatUtils';
import LoadingSpinner from '@/components/Common/LoadingSpinner';
import ErrorMessage from '@/components/Common/ErrorMessage';
import NoDataDisplay from './NoDataDisplay';

interface EmployersTableProps {
  employers: MajorEmployerDataProps[];
  isLoading?: boolean;
  error?: Error | null;
}

const MajorEmployersTable: React.FC<EmployersTableProps> = ({ employers, isLoading, error }) => {
  if (isLoading) {
    return (
      <div className="flex w-full min-h-[182px] mt-1">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex w-full min-h-[182px] mt-1">
        <ErrorMessage
          message="We couldn't retrieve the demographic information at this time. Please try adjusting your search parameters or try again later."
        />
      </div>
    );
  }

  if (!employers || employers.length === 0) {
    return (
      <NoDataDisplay entityName="Major Employers data" isAreaContext={true} />
    );
  }
  return (
    <div className="w-full min-h-[182px] mt-1">
      {/* Header Row */}
      <div className="grid grid-cols-[2fr_1fr_1fr_2fr] gap-3 border-b border-medium-gray-20 ">
        <div className="text-left font-semibold text-xs text-dark-gray">Company Name</div>
        <div className="text-center font-semibold text-xs text-dark-gray">Distance</div>
        <div className="text-center font-semibold text-xs text-dark-gray">Employees</div>
        <div className="text-left font-semibold text-xs text-dark-gray">Category</div>
      </div>

      {/* Data Rows */}
      {employers.map((employer, index) => (
        <div
          key={index}
          className="grid grid-cols-[2fr_1fr_1fr_2fr] gap-3 border-b order-medium-gray-20 "
        >
          <div className="flex items-center justify-start h-8 text-xs text-dark-gray">
            {employer.companyName}
          </div>
          <div className="flex items-center justify-center h-8 text-xs text-dark-gray">
            {formatDistanceToMiles(employer.distance)}
          </div>
          <div className="flex items-center justify-center h-8 text-xs text-dark-gray">
            {formatNumber(employer.locationEmployeeSizeActual)}
          </div>
          <div className="flex items-center justify-start h-8 text-xs text-dark-gray">
            {employer.subcategory}
          </div>
        </div>
      ))}
    </div>
  );
};

export default MajorEmployersTable;