import React from 'react';
import { DemographicsDataProps } from '../../../types/PropertyDetailPage.types';
import DriveTimeDistanceToggle from './DriveTimeDistanceToggle';
import DemographicsGrid from './DemographicsGrid';


interface DemographicsDetailsProps {
  demographics: DemographicsDataProps;
  driveTime: number;
  setDriveTime: React.Dispatch<React.SetStateAction<number>>;
  distance: number;
  setDistance: React.Dispatch<React.SetStateAction<number>>;
  isDistanceMode: boolean;
  setIsDistanceMode: React.Dispatch<React.SetStateAction<boolean>>;
  isLoading?: boolean;
  error?: Error | null;
}

const DemographicsDetails: React.FC<DemographicsDetailsProps> = ({
  demographics,
  driveTime,
  setDriveTime,
  distance,
  setDistance,
  isDistanceMode,
  setIsDistanceMode,
  isLoading,
  error,
}) => {
  return (
    <>
      <DriveTimeDistanceToggle
        driveTime={driveTime}
        setDriveTime={setDriveTime}
        distance={distance}
        setDistance={setDistance}
        isDistanceMode={isDistanceMode}
        setIsDistanceMode={setIsDistanceMode}
      />
      <DemographicsGrid demographics={demographics} isLoading={isLoading} error={error} />
    </>
  );
};

export default DemographicsDetails;