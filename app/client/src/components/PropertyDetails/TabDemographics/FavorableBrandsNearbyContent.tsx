import React from 'react';
import { FavorableBrandProps } from '../../../types/PropertyDetailPage.types';
import Selection from '../../Common/Selection';
import FavoriteBrandTable from './FavoriteBrandTable';
import Pagination from './Pagination';

interface FavorableBrandsNearbyContentProps {
  favorableBrands: FavorableBrandProps[];
  distance: number;
  setDistance: (distance: number) => void;
  currentPage: number;
  handleNextPage: () => void;
  handlePreviousPage: () => void;
  totalItems: number;
  itemsPerPage: number;
  isLoading?: boolean;
  error?: Error | null;
}

const FavorableBrandsNearbyContent: React.FC<FavorableBrandsNearbyContentProps> = ({
  favorableBrands,
  distance,
  setDistance,
  currentPage,
  handleNextPage,
  handlePreviousPage,
  totalItems,
  itemsPerPage,
  isLoading = false,
  error = null,
}) => {
  const distanceOptions = [
    { value: 1, label: '1 mile' },
    { value: 2, label: '2 miles' },
    { value: 3, label: '3 miles' },
    { value: 4, label: '4 miles' },
    { value: 5, label: '5 miles' },
  ];

  return (
    <div className="flex flex-col gap-3">
      <div className="flex items-center">
        <Selection
          label="Distance:"
          value={distance}
          options={distanceOptions}
          onChange={(value) => setDistance(Number(value))}
          valueType="number"
        />
      </div>

      <FavoriteBrandTable
        brands={favorableBrands}
        isLoading={isLoading}
        error={error}
      />

      <Pagination
        currentPage={currentPage}
        handleNextPage={handleNextPage}
        handlePreviousPage={handlePreviousPage}
        isNextDisabled={currentPage * itemsPerPage >= totalItems}
        isPreviousDisabled={currentPage === 1}
      />
    </div>
  );
};

export default FavorableBrandsNearbyContent;