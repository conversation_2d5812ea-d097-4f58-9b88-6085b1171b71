import React, { useEffect, useState } from 'react';
import { FavorableBrandProps } from '../../../types/PropertyDetailPage.types';
import Selection from '../../Common/Selection';
import FavoriteBrandTable from './FavoriteBrandTable';
import Pagination from './Pagination';
import { Map as MapboxMap } from 'mapbox-gl';

interface FavorableBrandsNearbyContentProps {
  favorableBrands: FavorableBrandProps[];
  distance: number;
  setDistance: (distance: number) => void;
  currentPage: number;
  handleNextPage: () => void;
  handlePreviousPage: () => void;
  totalItems: number;
  itemsPerPage: number;
  isLoading?: boolean;
  error?: Error | null;
  map: MapboxMap | null;
  eventCoordinates: [number, number];
}

const FavorableBrandsNearbyContent: React.FC<FavorableBrandsNearbyContentProps> = ({
  favorableBrands,
  distance,
  setDistance,
  currentPage,
  handleNextPage,
  handlePreviousPage,
  totalItems,
  itemsPerPage,
  isLoading = false,
  error = null,
  map,
  eventCoordinates,
}) => {
  const [currentHoverSection, setCurrentHoverSection] = useState('area-chain-stores');

  const clearMapLayers = () => {
    if (!map) return;
    if (map && map.getLayer('chain-store-circle')) {
      map.removeLayer('chain-store-circle');
    }
    if (map && map.getLayer('chain-store-outline')) {
      map.removeLayer('chain-store-outline');
    }
    if (map && map.getSource('chain-store-area')) {
      map.removeSource('chain-store-area');
    }
  };

  const updateMapCircle = (center: [number, number], radiusMiles: number) => {
    if (!map) return;

    clearMapLayers();

    // Create a circular polygon
    const points = 64;
    const radiusMeters = radiusMiles * 1609.34;
    const coords = [];

    for (let i = 0; i < points; i++) {
      const angle = (i * 360) / points;
      const lat =
        center[1] + (radiusMeters / 111320) * Math.cos((angle * Math.PI) / 180);
      const lng =
        center[0] +
        (radiusMeters / (111320 * Math.cos((center[1] * Math.PI) / 180))) *
          Math.sin((angle * Math.PI) / 180);
      coords.push([lng, lat]);
    }
    coords.push(coords[0]); // Close the circle

    map.addSource('chain-store-area', {
      type: 'geojson',
      data: {
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'Polygon',
          coordinates: [coords],
        },
      },
    });

    map.addLayer({
      id: 'chain-store-circle',
      type: 'fill',
      source: 'chain-store-area',
      layout: {
        visibility:
          currentHoverSection === 'area-chain-stores' ? 'visible' : 'none',
      },
      paint: {
        'fill-color': '#22c55e',
        'fill-opacity': 0.2,
      },
    });

    map.addLayer({
      id: 'chain-store-outline',
      type: 'line',
      source: 'chain-store-area',
      layout: {
        visibility:
          currentHoverSection === 'area-chain-stores' ? 'visible' : 'none',
      },
      paint: {
        'line-color': '#22c55e',
        'line-width': 2,
      },
    });
  };

  // Add visibility effect
  useEffect(() => {
    if (!map) return;

    const circleLayerExists = map.getLayer('chain-store-circle');

    if (circleLayerExists) {
      map.setLayoutProperty(
        'chain-store-circle',
        'visibility',
        currentHoverSection === 'area-chain-stores' ? 'visible' : 'none',
      );
      map.setLayoutProperty(
        'chain-store-outline',
        'visibility',
        currentHoverSection === 'area-chain-stores' ? 'visible' : 'none',
      );
    }
  }, [currentHoverSection, map]);

  // Update circle when distance or location changes
  useEffect(() => {
    if (
      map &&
      eventCoordinates?.[0] &&
      eventCoordinates?.[1]
    ) {
      updateMapCircle(
        eventCoordinates,
        Number(distance),
      );
    } else {
      clearMapLayers();
    }
  }, [eventCoordinates, distance, map]);

  
  const distanceOptions = [
    { value: 1, label: '1 mile' },
    { value: 2, label: '2 miles' },
    { value: 3, label: '3 miles' },
    { value: 4, label: '4 miles' },
    { value: 5, label: '5 miles' },
  ];

  return (
    <div className="flex flex-col gap-3">
      <div className="flex items-center">
        <Selection
          label="Distance:"
          value={distance}
          options={distanceOptions}
          onChange={(value) => setDistance(Number(value))}
          valueType="number"
        />
      </div>

      <FavoriteBrandTable
        brands={favorableBrands}
        isLoading={isLoading}
        error={error}
        setCurrentHoverSection={setCurrentHoverSection}
      />

      <Pagination
        currentPage={currentPage}
        handleNextPage={handleNextPage}
        handlePreviousPage={handlePreviousPage}
        isNextDisabled={currentPage * itemsPerPage >= totalItems}
        isPreviousDisabled={currentPage === 1}
      />
    </div>
  );
};

export default FavorableBrandsNearbyContent;