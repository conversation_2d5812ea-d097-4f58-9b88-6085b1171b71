import React from 'react';

interface NoDataDisplayProps {
  entityName?: string;
  message?: string;
  actionMessage?: string;
  isAreaContext?: boolean;
}

const NoDataDisplay: React.FC<NoDataDisplayProps> = ({
  entityName = "data",
  message,
  actionMessage,
  isAreaContext = false,
}) => {
  const defaultMessage = isAreaContext ? `No ${entityName} found within the selected area.` : `No ${entityName} found.`
  const defaultActionMessage = isAreaContext ? "Try increasing the area to see more results." : "";

  return (
    <div className="flex flex-col justify-center items-center text-center w-full min-h-[182px]">
      <p className="text-[13px] text-[var(--color-dark-gray)]">
        {message || defaultMessage}
      </p>
      <p className="text-[13px] text-[var(--color-dark-gray)]">
        {actionMessage || defaultActionMessage}
      </p>
    </div>
  );
};

export default NoDataDisplay;