import React from 'react';
import { MajorEmployerDataProps } from '../../../types/PropertyDetailPage.types';
import DriveTimeDistanceToggle from './DriveTimeDistanceToggle';
import MajorEmployersTable from './MajorEmployersTable';
import Pagination from './Pagination';

interface MajorEmployersContentProps {
  employers: MajorEmployerDataProps[] | [];
  driveTime: number;
  setDriveTime: React.Dispatch<React.SetStateAction<number>>;
  distance: number;
  setDistance: React.Dispatch<React.SetStateAction<number>>;
  isDistanceMode: boolean;
  setIsDistanceMode: React.Dispatch<React.SetStateAction<boolean>>;
  currentPage: number;
  handleNextPage: () => void;
  handlePreviousPage: () => void;
  handleInputBlur?: () => void;
  totalItems: number;
  itemsPerPage: number;
  isLoading?: boolean;
  error?: Error | null;
}

const MajorEmployersContent: React.FC<MajorEmployersContentProps> = ({
  employers,
  driveTime,
  setDriveTime,
  distance,
  setDistance,
  isDistanceMode,
  setIsDistanceMode,
  currentPage,
  handleNextPage,
  handlePreviousPage,
  totalItems,
  itemsPerPage,
  isLoading,
  error,
}) => {

  return (
    <>
      <DriveTimeDistanceToggle
        driveTime={driveTime}
        setDriveTime={setDriveTime}
        distance={distance}
        setDistance={setDistance}
        isDistanceMode={isDistanceMode}
        setIsDistanceMode={setIsDistanceMode}
      />

      <MajorEmployersTable
        employers={employers}
        isLoading={isLoading}
        error={error}
      />

      <Pagination
        currentPage={currentPage}
        handleNextPage={handleNextPage}
        handlePreviousPage={handlePreviousPage}
        isNextDisabled={currentPage * itemsPerPage >= totalItems}
        isPreviousDisabled={currentPage === 1}
      />
    </>
  );
};

export default MajorEmployersContent;