import React from 'react';
import { MajorEmployerDataProps } from '../../../types/PropertyDetailPage.types';
import TabDemographicsSectionWrapper from './TabDemographicsSectionWrapper';
import TabDemographicsSectionHeader from './TabDemographicsSectionHeader';
import MajorEmployersContent from './MajorEmployersContent';
import ActionButton from '@/components/Common/ActionButton';
import { DistanceDriveModeParams } from '@/lib/utils/csvExportUtils';

interface MajorEmployersSectionProps {
  employers: MajorEmployerDataProps[] | [];
  driveTime: number;
  setDriveTime: React.Dispatch<React.SetStateAction<number>>;
  distance: number;
  setDistance: React.Dispatch<React.SetStateAction<number>>;
  isDistanceMode: boolean;
  setIsDistanceMode: React.Dispatch<React.SetStateAction<boolean>>;
  currentPage: number;
  handleNextPage: () => void;
  handlePreviousPage: () => void;
  totalItems: number;
  itemsPerPage: number;
  isLoading?: boolean;
  error?: Error | null;
  exportCSV?: (params: DistanceDriveModeParams) => Promise<void>;
  isExportLoading?: boolean;
}

const MajorEmployersSection: React.FC<MajorEmployersSectionProps> = ({
  employers,
  driveTime,
  distance,
  setDistance,
  setDriveTime,
  isDistanceMode,
  setIsDistanceMode,
  currentPage,
  handleNextPage,
  handlePreviousPage,
  totalItems,
  itemsPerPage,
  isLoading,
  error,
  exportCSV,
  isExportLoading = false,
}) => {

  const onClickCSVButton = async () => {
    if (!exportCSV) {
      console.error('Export function not provided');
      return;
    }

    const mode = isDistanceMode ? 'distance' : 'drive';
    const params: DistanceDriveModeParams = {
      distance: distance,
      mode: mode,
      time: driveTime,
    };

    try {
      await exportCSV(params);
    } catch (error) {
      console.error('Error exporting CSV:', error);
    }
  };


  return (
    <TabDemographicsSectionWrapper>
      <TabDemographicsSectionHeader
        title="Major Employers in"
        rightContent={<ActionButton text="Export CSV" onClick={onClickCSVButton} disabled={isExportLoading}
        />}
      />
      <MajorEmployersContent
        employers={employers}
        driveTime={driveTime}
        setDriveTime={setDriveTime}
        distance={distance}
        setDistance={setDistance}
        isDistanceMode={isDistanceMode}
        setIsDistanceMode={setIsDistanceMode}
        currentPage={currentPage}
        handleNextPage={handleNextPage}
        handlePreviousPage={handlePreviousPage}
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        error={error}
      />
    </TabDemographicsSectionWrapper>
  );
};

export default MajorEmployersSection;