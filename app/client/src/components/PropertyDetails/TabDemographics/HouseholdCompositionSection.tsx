import React from 'react';
import { HouseholdCompositionProps } from '../../../types/PropertyDetailPage.types';
import HouseholdCompositionContent from './HouseholdCompositionContent';
import TabDemographicsSectionWrapper from './TabDemographicsSectionWrapper';
import TabDemographicsSectionHeader from './TabDemographicsSectionHeader';

interface HouseholdCompositionSectionProps {
  householdComposition: HouseholdCompositionProps[] | [];
  driveTime: number;
  setDriveTime: React.Dispatch<React.SetStateAction<number>>;
  distance: number;
  setDistance: React.Dispatch<React.SetStateAction<number>>;
  isDistanceMode: boolean;
  setIsDistanceMode: React.Dispatch<React.SetStateAction<boolean>>;
  currentPage: number;
  handleNextPage: () => void;
  handlePreviousPage: () => void;
  totalItems: number;
  itemsPerPage: number;
  isLoading: boolean;
  error: Error | null;
}

const HouseholdCompositionSection: React.FC<HouseholdCompositionSectionProps> = ({
  householdComposition,
  driveTime,
  setDriveTime,
  distance,
  setDistance,
  isDistanceMode,
  setIsDistanceMode,
  currentPage,
  handleNextPage,
  handlePreviousPage,
  totalItems,
  itemsPerPage,
  isLoading,
  error,
}) => {

  return (
    <TabDemographicsSectionWrapper>
      <TabDemographicsSectionHeader
        title="Household Composition (Powered by Spatial.ai)"
      />
      <HouseholdCompositionContent
        householdComposition={householdComposition}
        driveTime={driveTime}
        setDriveTime={setDriveTime}
        distance={distance}
        setDistance={setDistance}
        isDistanceMode={isDistanceMode}
        setIsDistanceMode={setIsDistanceMode}
        currentPage={currentPage}
        handleNextPage={handleNextPage}
        handlePreviousPage={handlePreviousPage}
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        error={error}
      />
    </TabDemographicsSectionWrapper>
  );
};

export default HouseholdCompositionSection;