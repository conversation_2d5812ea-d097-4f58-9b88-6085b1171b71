import React from 'react';


interface DriveTimeDistanceToggleProps {
  driveTime: number;
  setDriveTime: React.Dispatch<React.SetStateAction<number>>;
  distance: number;
  setDistance: React.Dispatch<React.SetStateAction<number>>;
  isDistanceMode: boolean;
  setIsDistanceMode: React.Dispatch<React.SetStateAction<boolean>>;
}


const DriveTimeDistanceToggle: React.FC<DriveTimeDistanceToggleProps> = ({
  driveTime,
  setDriveTime,
  distance,
  setDistance,
  isDistanceMode,
  setIsDistanceMode }) => {

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (isDistanceMode) {
      setDistance(value);
    } else {
      setDriveTime(value);
    }
  };

  const handleToggleClick = (isDistanceMode: boolean) => {
    setIsDistanceMode(!isDistanceMode);
  };

  return (
    <div className="flex items-center gap-5">
      <div className="flex bg-medium-gray-20 p-0.5 rounded-md">
        <button
          className={`w-[85px] py-[8px] px-[10px] border-none rounded text-xs transition-all duration-200 ${!isDistanceMode
            ? "bg-white text-black font-semibold cursor-pointer"
            : "bg-transparent text-dark-gray hover:bg-medium-gray hover:text-black cursor-pointer"
            }`}
          onClick={() => handleToggleClick(isDistanceMode)}

        >
          Drive Time
        </button>
        <button
          className={`w-[85px] py-[8px] px-[10px] border-none rounded cursor-pointer text-xs transition-all duration-200 ${isDistanceMode
            ? "bg-white text-black font-semibold"
            : "bg-transparent text-dark-gray hover:bg-medium-gray hover:text-black"
            }`}
          onClick={() => handleToggleClick(isDistanceMode)}

        >
          Distance
        </button>
      </div>
      <div className="flex items-center">
        <input
          type="number"
          value={isDistanceMode ? distance : driveTime}
          min={1}
          max={isDistanceMode ? 50 : 60}
          className="w-14 p-2 border border-medium-gray-20 rounded-l-md text-xs bg-white text-dark-gray text-center appearance-none"
          onChange={handleInputChange}
        />
        <span className="w-14 p-2 text-xs rounded-r-md border-t border-r border-b border-medium-gray-20 text-dark-gray">
          {isDistanceMode ? 'Miles' : 'Mins'}
        </span>
      </div>
    </div>
  );
};

export default DriveTimeDistanceToggle;