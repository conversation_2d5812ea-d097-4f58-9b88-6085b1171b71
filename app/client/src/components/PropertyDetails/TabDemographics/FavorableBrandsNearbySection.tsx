import React from 'react';
import { FavorableBrandProps } from '../../../types/PropertyDetailPage.types';
import TabDemographicsSectionWrapper from './TabDemographicsSectionWrapper';
import TabDemographicsSectionHeader from './TabDemographicsSectionHeader';
import FavorableBrandsNearbyContent from './FavorableBrandsNearbyContent';
import { Map as MapboxMap } from 'mapbox-gl';

interface FavorableBrandsNearbyProps {
  favorableBrands: FavorableBrandProps[];
  distance: number;
  setDistance: (distance: number) => void;
  currentPage: number;
  handleNextPage: () => void;
  handlePreviousPage: () => void;
  totalItems: number;
  itemsPerPage: number;
  isLoading?: boolean;
  error?: Error | null;
  map: MapboxMap | null;
  eventCoordinates: [number, number];
}

const FavorableBrandsNearbySection: React.FC<FavorableBrandsNearbyProps> = ({
  favorableBrands,
  distance,
  setDistance,
  currentPage,
  handleNextPage,
  handlePreviousPage,
  totalItems,
  itemsPerPage,
  isLoading = false,
  error = null,
  map,
  eventCoordinates,
}) => {
  return (
    <TabDemographicsSectionWrapper>
      <TabDemographicsSectionHeader
        title="Favorable Brands Nearby"
      />
      <FavorableBrandsNearbyContent
        favorableBrands={favorableBrands}
        distance={distance}
        setDistance={setDistance}
        currentPage={currentPage}
        handleNextPage={handleNextPage}
        handlePreviousPage={handlePreviousPage}
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        error={error}
        map={map}
        eventCoordinates={eventCoordinates}
      />
    </TabDemographicsSectionWrapper>
  );
};

export default FavorableBrandsNearbySection;