import React from 'react';
import { Link, useSearch } from "@tanstack/react-router";
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';

interface PropertyDetailHeaderProps {
  onStartOfferClick: () => void;
}

const PropertyDetailHeader: React.FC<PropertyDetailHeaderProps> = ({
  onStartOfferClick
}) => {

  const { setSelectedBuyersViewRecord, setEventCoordinates, setPropertyModalTabKey } = useMarketplaceMapContext();

  const handleReturnToList = () => {
    setSelectedBuyersViewRecord(null);
    setEventCoordinates([]);
    setPropertyModalTabKey(null);
  };

  const search = useSearch({ from: '/_authenticated/properties' });

  return (
    <div className="flex justify-between items-center">
      <Link
        className="flex items-center gap-2 text-medium-gray hover:text-[var(--color-button-blue)] transition-colors"
        to="/properties"
        search={{
          source: search.source,
          listType: search.listType,
          viewStyle: search.viewStyle,
        }}
        onClick={handleReturnToList}
      >
        <span className="text-lg ">←</span> Return to list
      </Link>

      <button
        className="flex rounded-lg py-1.5 px-2 text-base font-bold bg-green-primary text-white hover:bg-[green-primary/80 transition-colors"
        onClick={onStartOfferClick}
        aria-label="Start Purchase"
      >
        Start Purchase
      </button>
    </div>
  );
};

export default PropertyDetailHeader;