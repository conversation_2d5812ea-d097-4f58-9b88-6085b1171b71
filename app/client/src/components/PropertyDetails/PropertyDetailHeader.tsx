import React from 'react';
import AddToCartButton from '../Cart/AddToCartButton';
import ContactInfo from '../ContactInfo';
import ReturnToList from './ReturnToList';

interface PropertyDetailHeaderProps {
  onStartOfferClick: () => void;
  phone?: string | null;
  isLoading?: boolean;
  handleReturnToList: () => void;
}

const PropertyDetailHeader: React.FC<PropertyDetailHeaderProps> = ({
  phone = "",
  onStartOfferClick,
  isLoading = false,
  handleReturnToList
}) => {
  return (
    <div className="flex justify-between items-center">
      <ReturnToList handleReturnToList={handleReturnToList} />

      <div className="flex items-center gap-2">
        {phone && <ContactInfo phone={phone} />}
        <AddToCartButton />
        <button
          className="flex rounded-lg py-1.5 px-2 text-sm font-bold bg-green-primary text-white hover:bg-green-primary/80 transition-colors disabled:opacity-50"
          onClick={onStartOfferClick}
          disabled={isLoading}
          aria-label="Add to Cart"
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              Adding...
            </>
          ) : (
            'Start Purchase'
          )}
        </button>
      </div>
    </div>
  );
};

export default PropertyDetailHeader;