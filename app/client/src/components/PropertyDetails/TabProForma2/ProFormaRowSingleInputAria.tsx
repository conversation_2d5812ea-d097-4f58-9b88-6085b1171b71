import { Label, Input, NumberField as NumberFieldReact<PERSON>ria } from "react-aria-components";
import { Controller } from "react-hook-form";
import type { Control } from "react-hook-form";
import { formatPrice, formatPercentage } from '@/lib/utils/formatUtils';
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { Info } from "lucide-react";

const getFormatOptions = ({
  inputValueType,
  maximumFractionDigitsCurrency = 0,
  maximumFractionDigitsPercent = 2,
  maximumFractionDigitsNumber = 0,
}: {
  inputValueType: 'currency' | 'percent' | 'number' | undefined;
  maximumFractionDigitsCurrency?: number;
  maximumFractionDigitsPercent?: number;
  maximumFractionDigitsNumber?: number;
}) => {
  if (!inputValueType || inputValueType === 'currency') {
    return {
      style: 'currency' as const,
      currency: 'USD' as const,
      currencyDisplay: 'symbol' as const,
      currencySign: 'accounting' as const,
      minimumFractionDigits: 0,
      maximumFractionDigits: maximumFractionDigitsCurrency,
    };
  } else if (inputValueType === 'percent') {
    return {
      style: 'percent' as const,
      minimumFractionDigits: 0,
      maximumFractionDigits: maximumFractionDigitsPercent,
    };
  } else if (inputValueType === 'number') {
    return {
      minimumFractionDigits: 0,
      maximumFractionDigits: maximumFractionDigitsNumber,
    };
  }
};

const ProFormaRowSingleInputAria = ({
  control,
  name,
  label,
  value,
  onChange,
  minValue,
  // maxValue,
  inputValueType,
  className,
  valueCoeff,
  valueCoeffType,
  maximumFractionDigitsCurrency,
  maximumFractionDigitsPercent,
  maximumFractionDigitsNumber,
  tooltip,
}: {
  control: Control<any>;
  name: string;
  label: string;
  value: number;
  onChange: (value: number) => void;
  minValue?: number;
  // maxValue?: number;
  inputValueType?: 'currency' | 'percent' | 'number' | undefined;
  className?: string;
  valueCoeff?: number;
  valueCoeffType?: 'currency' | 'percent';
  maximumFractionDigitsCurrency?: number;
  maximumFractionDigitsPercent?: number;
  maximumFractionDigitsNumber?: number;
  tooltip?: string;
}) => {
  return (
    <Controller
      control={control}
      name={name}
      render={({
        field: {
          // onChange: onChangeField,
          // onBlur, 
          // value: valueField, 
          ref,
          // name 
        },
        // fieldState: {
        //   isDirty,
        // }
      }) => (
        <NumberFieldReactAria
          ref={ref}
          name={name}
          value={value}
          onChange={onChange}
          minValue={minValue || 0}
          // maxValue={maxValue || 0}
          formatOptions={getFormatOptions({ inputValueType, maximumFractionDigitsCurrency, maximumFractionDigitsPercent, maximumFractionDigitsNumber })}
          className={`flex justify-between items-center text-xs pl-2 ${className || ""} `}
        >
          <div className="flex items-center gap-1">
            <Label className="font-extralight mr-auto text-dark-gray">{label}</Label>
            {tooltip && (
              <Tooltip>
                <TooltipTrigger aria-label={`Information about ${label}`}>
                  <div className="flex items-center justify-center h-4 w-4">
                    <Info className="h-3 w-3 cursor-help text-dark-gray hover:text-primary transition-colors" />
                  </div>
                </TooltipTrigger>
                <TooltipContent className="w-[200px] max-h-[50vh] overflow-y-auto overflow-x-auto bg-white text-dark-gray rounded-md py-5 px-4 z-300 border border-medium-gray-20 text-xs">
                  {tooltip}
                </TooltipContent>
              </Tooltip>
            )}
          </div>
          <div className="flex">
            {valueCoeff && (
              <div className="flex justify-end items-center font-normal mr-4 min-w-[50px] text-right text-dark-gray">
                {valueCoeffType === 'currency' ? formatPrice(valueCoeff) : formatPercentage(valueCoeff, 2)}
              </div>
            )}
            <Input className="border border-medium-gray-20 rounded w-[72px] text-right text-button-blue pr-2 py-1" />
          </div>
        </NumberFieldReactAria>
      )}
    />
  );
};

export default ProFormaRowSingleInputAria;