import { Label, Input, NumberField as NumberFieldReact<PERSON>ria } from "react-aria-components";
import { Controller } from "react-hook-form";
import type { Control } from "react-hook-form";
import { formatPrice, formatPercentage } from '@/lib/utils/formatUtils';
import { MobileTooltip } from "@/components/ui/tooltip";
import { Info } from "lucide-react";
import { useBreakpoint } from "../../../hooks/useBreakpoint";


const getFormatOptions = ({
  inputValueType,
  maximumFractionDigitsCurrency = 0,
  maximumFractionDigitsPercent = 2,
  maximumFractionDigitsNumber = 0,
}: {
  inputValueType: 'currency' | 'percent' | 'number' | undefined;
  maximumFractionDigitsCurrency?: number;
  maximumFractionDigitsPercent?: number;
  maximumFractionDigitsNumber?: number;
}) => {
  if (!inputValueType || inputValueType === 'currency') {
    return {
      style: 'currency' as const,
      currency: 'USD' as const,
      currencyDisplay: 'symbol' as const,
      currencySign: 'accounting' as const,
      minimumFractionDigits: 0,
      maximumFractionDigits: maximumFractionDigitsCurrency,
    };
  } else if (inputValueType === 'percent') {
    return {
      style: 'percent' as const,
      minimumFractionDigits: 0,
      maximumFractionDigits: maximumFractionDigitsPercent,
    };
  } else if (inputValueType === 'number') {
    return {
      minimumFractionDigits: 0,
      maximumFractionDigits: maximumFractionDigitsNumber,
    };
  }
};

const ProFormaRowSingleInputAria = ({
  control,
  name,
  label,
  value,
  onChange,
  minValue,
  // maxValue,
  inputValueType,
  className,
  valueCoeff,
  valueCoeffType,
  maximumFractionDigitsCurrency,
  maximumFractionDigitsPercent,
  maximumFractionDigitsNumber,
  tooltip,
}: {
  control: Control<any>;
  name: string;
  label: string;
  value: number;
  onChange: (value: number) => void;
  minValue?: number;
  // maxValue?: number;
  inputValueType?: 'currency' | 'percent' | 'number' | undefined;
  className?: string;
  valueCoeff?: number;
  valueCoeffType?: 'currency' | 'percent';
  maximumFractionDigitsCurrency?: number;
  maximumFractionDigitsPercent?: number;
  maximumFractionDigitsNumber?: number;
  tooltip?: string;


}) => {
  const { isMobile } = useBreakpoint();
  const handleInputBlur = () => {
    if (isMobile) {
      setTimeout(() => {
        window.scrollTo(0, 0);
      }, 150);
    }
  };

  const getLabelWidth = (isMobile: boolean, label: string) => {
    if (!isMobile) return "";
    if (label === "Repair & Maintenance") return "w-[96px]";
    if (label === "Est. Home Price Appreciation (Hist 5 Yr.)") return "w-[208px]";
    return "w-1/2";
  }
  return (
    <div
      className={`flex justify-between items-center text-xs pl-2  ${className || ""} `}
    >
      <div className={`flex items-center ${getLabelWidth(isMobile, label)}`}>
        <Label className={`font-extralight text-dark-gray ${isMobile ? 'text-base' : 'text-xs'}`}>{label}</Label>
        {tooltip && (
          <MobileTooltip
            content={
              <div className="w-[200px] max-h-[50vh] overflow-y-auto overflow-x-auto bg-white text-dark-gray rounded-md py-5 px-4 z-300 border border-medium-gray-20 text-xs">
                {tooltip}
              </div>
            }
          >
            <div className={`flex items-center justify-center self-start ${isMobile ? "h-5 w-5" : "h-4 w-4"} ml-2`}>
              <Info className={` cursor-pointer text-dark-gray hover:text-primary transition-colors`} />
            </div>
          </MobileTooltip>
        )}
      </div>
      <div
        className={`flex items-center justify-end`}
      >
        <Controller
          control={control}
          name={name}
          render={({
            field: {
              // onChange: onChangeField,
              // onBlur, 
              // value: valueField, 
              ref,
              // name 
            },
            // fieldState: {
            //   isDirty,
            // }
          }) => (
            <NumberFieldReactAria
              aria-label={label}
              ref={ref}
              name={name}
              value={value}
              onChange={onChange}
              minValue={minValue || 0}
              // maxValue={maxValue || 0}
              formatOptions={getFormatOptions({ inputValueType, maximumFractionDigitsCurrency, maximumFractionDigitsPercent, maximumFractionDigitsNumber })}
              className={`flex justify-between items-center text-xs pl-2 ${className || ""} `}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault(); // Prevent form submission
                }
              }}
            >

              <div className="flex">
                {valueCoeff && (
                  <div className={`flex justify-end items-center font-normal  min-w-[50px] text-right text-dark-gray}  ${isMobile ? 'text-base mr-2' : 'text-xs mr-4'}`}>
                    {valueCoeffType === 'currency' ? formatPrice(valueCoeff) : formatPercentage(valueCoeff, 2)}
                  </div>
                )}
                <Input
                  className={`border border-medium-gray-20 rounded  text-right text-button-blue pr-2 py-1 ${isMobile ? 'text-base w-[110px]' : 'text-xs w-[72px]'}`}
                  onBlur={handleInputBlur} />
              </div>
            </NumberFieldReactAria>
          )}
        />
      </div>
    </div>
  );
};

export default ProFormaRowSingleInputAria;