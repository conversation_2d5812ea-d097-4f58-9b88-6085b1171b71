import React from 'react';
import Display<PERSON>ield from './DisplayField';
// import EditableProFormaField from './EditableField';
import ProFormaRowSingleInputAria from './ProFormaRowSingleInputAria';
// import ProFormaRowDoubleInputsAria from './ProFormaRowDoubleInputsAria';
import ProFormaSection from './ProFormaSection';
import { formatPrice, formatPercentage } from '@/lib/utils/formatUtils';
import { Control } from 'react-hook-form';
import { ProFormaValuesLennar } from '@/lib/utils/types';

interface PropertyRentalIncomeSectionProps {
  allValues: ProFormaValuesLennar;
  control: Control<any>;
  // setUserInputProjectedRent: (value: number | null) => void;
  setUserInputOtherIncome: (value: number | null) => void;
  saveUserInputValues: (changedValues: {
    userInputBidPrice?: number | null;
    userInputClosingCosts?: number | null;
    userInputProjectedRent?: number | null;
    userInputOtherIncome?: number | null;
    userInputPropertyTax?: number | null;
    userInputInsurance?: number | null;
    userInputHoa?: number | null;
    userInputRAndM?: number | null;
    userInputPmFeesCoeff?: number | null;
    userInputPmFees?: number | null;
    userInputVacancyLossCoeff?: number | null;
    userInputVacancyLoss?: number | null;
    userInputAnnualCommunityDevelopmentFee?: number | null;
    userInputLoanToValue?: number | null;
    userInputMortgageRate?: number | null;
    userInputMortgageTerm?: number | null;
    userInputHPA5Yr?: number | null;
  }) => void;
}

const PropertyRentalIncomeSection: React.FC<PropertyRentalIncomeSectionProps> = ({
  allValues,
  control,
  // setUserInputProjectedRent,
  setUserInputOtherIncome,
  saveUserInputValues,
}) => {
  return (
    <ProFormaSection title="Est. Rental Income" width="w-full" className="mt-2">
      {/* <ProFormaRowSingleInputAria
        control={control}
        name="projectedRent"
        label="Projected Monthly Rent"
        value={allValues["Projected Monthly Rent"]}
        onChange={(value) => {
          setUserInputProjectedRent(value);
          saveUserInputValues({ userInputProjectedRent: value });
        }}
      /> */}

      <ProFormaRowSingleInputAria
        control={control}
        name="otherIncome"
        label="Other Monthly Income"
        value={allValues["Other Monthly Income"]}
        onChange={(value) => {
          setUserInputOtherIncome(value);
          saveUserInputValues({ userInputOtherIncome: value });
        }}
      />

      <DisplayField
        label="Total Annual Rental Income"
        value={formatPrice(allValues["Total Annual Rental Income"])}
      />
    </ProFormaSection>
  );
};

export default PropertyRentalIncomeSection;