import React, { ReactNode } from "react";

interface ProFormaSummaryProps {
  label: ReactNode;
  value: ReactNode;
  width?: string;
  height?: string;
  className?: string;
}

const ProFormaSummary: React.FC<ProFormaSummaryProps> = ({
  label,
  value,
  width = "w-full",
  height = "h-[28px]",
  className = "",
}) => {
  return (
    <div
      className={`flex justify-center ${width} ${height} items-center px-2 rounded bg-[var(--color-lennar-blue-light)] text-xs ${className}`}
    >
      <span className="text-sm whitespace-nowrap mr-0 p-0">{label}</span>
      <span className="font-bold text-sm whitespace-nowrap ml-1.5">
        {value}
      </span>
    </div>
  );
};

export default ProFormaSummary;
