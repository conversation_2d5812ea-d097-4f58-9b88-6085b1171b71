import React from 'react';
import ProFormaSection from './ProFormaSection';
import Display<PERSON>ield from './DisplayField';
import ProFormaRowSingleInputAria from './ProFormaRowSingleInputAria';
import { formatPrice, formatPercentage } from '@/lib/utils/formatUtils';
import { Control } from 'react-hook-form';
import { ProFormaValuesLennar } from '@/lib/utils/types';

interface PropertyDebtSectionProps {
  allValues: ProFormaValuesLennar;
  // control: Control<any>;
  // setUserInputLoanToValue: (value: number | null) => void;
  // setUserInputMortgageRate: (value: number | null) => void;
  // setUserInputMortgageTerm: (value: number | null) => void;
  // saveUserInputValues: (changedValues: {
  //   userInputBidPrice?: number | null;
  //   userInputClosingCosts?: number | null;
  //   userInputProjectedRent?: number | null;
  //   userInputOtherIncome?: number | null;
  //   userInputPropertyTax?: number | null;
  //   userInputInsurance?: number | null;
  //   userInputHoa?: number | null;
  //   userInputRAndM?: number | null;
  //   userInputPmFeesCoeff?: number | null;
  //   userInputPmFees?: number | null;
  //   userInputVacancyLossCoeff?: number | null;
  //   userInputVacancyLoss?: number | null;
  //   userInputAnnualCommunityDevelopmentFee?: number | null;
  //   userInputLoanToValue?: number | null;
  //   userInputMortgageRate?: number | null;
  //   userInputMortgageTerm?: number | null;
  //   userInputHPA5Yr?: number | null;
  // }) => void;
}

const PropertyDebtSection: React.FC<PropertyDebtSectionProps> = ({
  allValues,
  // control,
  // setUserInputLoanToValue,
  // setUserInputMortgageRate,
  // setUserInputMortgageTerm,
  // saveUserInputValues,
}) => {
  return (
    <ProFormaSection title="Debt" width="w-full" className="mt-2">
      {/* <ProFormaRowSingleInputAria
        control={control}
        name="loanToValue"
        label="Loan to Value"
        value={allValues["Loan to Value"]}
        onChange={(value) => {
          setUserInputLoanToValue(value);
          saveUserInputValues({ userInputLoanToValue: value });
        }}
        inputValueType="percent"
      /> */}

      <DisplayField
        label="Mortgage Amount"
        value={formatPrice(allValues["Mortgage Amount"])}
      />

      {/* <ProFormaRowSingleInputAria
        control={control}
        name="mortgageRate"
        label="Interest Rate"
        value={allValues["Mortgage Rate"]}
        onChange={(value) => {
          setUserInputMortgageRate(value);
          saveUserInputValues({ userInputMortgageRate: value });
        }}
        inputValueType="percent"
      /> */}

      

      <DisplayField
        label="Monthly Mortgage Payment"
        value={formatPrice(allValues["Monthly Mortgage Payment"])}
      />
    </ProFormaSection>
  );
};

export default PropertyDebtSection;