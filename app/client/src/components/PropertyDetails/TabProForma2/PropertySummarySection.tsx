import React from 'react';
import <PERSON>sp<PERSON><PERSON><PERSON> from './DisplayField';
import ProFormaRowSingleInputAria from './ProFormaRowSingleInputAria';
import { formatPrice, formatPercentage } from '@/lib/utils/formatUtils';
import { Control, Controller } from 'react-hook-form';
import { ProFormaValuesLennar } from '@/lib/utils/types';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Button } from '@/components/ui/button';
import { getProFormaTooltip } from '../../../constants/proFormaTooltips';
import { useBreakpoint } from '@/hooks/useBreakpoint';


interface PropertySummarySectionProps {
  allValues: ProFormaValuesLennar;
  control: Control<any>;
  setUserInputLoanToValue: (value: number | null) => void;
  setUserInputProjectedRent: (value: number | null) => void;
  setUserInputHPA5Yr: (value: number | null) => void;
  setUserInputYearsHolding: (value: number | null) => void;
  setUserInputMortgageRate: (value: number | null) => void;
  setShowFullProForma: (value: boolean) => void;
  showFullProForma: boolean;
}

const PropertySummarySection: React.FC<PropertySummarySectionProps> = ({
  allValues,
  control,
  setUserInputLoanToValue,
  setUserInputProjectedRent,
  setUserInputHPA5Yr,
  setUserInputYearsHolding,
  setUserInputMortgageRate,
  // saveUserInputValues,
  setShowFullProForma,
  showFullProForma,
}) => {
  const { isMobile } = useBreakpoint();
  return (
    <div className="@container mx-auto">
      <div className="grid grid-cols-1 @2xl:grid-cols-2 gap-8">
        <div className="flex flex-col gap-3">
          <div className={`text-dark-gray font-bold ml-2 mt-3 mb-1 ${isMobile ? 'text-base' : 'text-xs'}`}>
            Investment Assumptions
          </div>
          <DisplayField
            label="Purchase Price"
            value={formatPrice(allValues["Bid Price"])}
          />
          <DisplayField
            label="Initial Investment"
            value={formatPrice(allValues["Total Initial Investment"])}
            tooltip={getProFormaTooltip("Total Initial Investment")}
          />
          <ProFormaRowSingleInputAria
            control={control}
            name="loanToValue"
            label="Down Payment"
            value={1 - allValues["Loan to Value"]}
            inputValueType="percent"
            onChange={(value) => {
              setUserInputLoanToValue(1 - value);
              // saveUserInputValues({ userInputLoanToValue: 1 - value });
            }}
          />
          <ProFormaRowSingleInputAria
            control={control}
            name="projectedRent"
            label="Est. Monthly Rent"
            value={allValues["Projected Monthly Rent"]}
            inputValueType="currency"
            onChange={(value) => {
              setUserInputProjectedRent(value);
              // saveUserInputValues({ userInputProjectedRent: value });
            }}
            tooltip={getProFormaTooltip("Projected Monthly Rent")}
          />
          <ProFormaRowSingleInputAria
            control={control}
            name="hpa5Yr"
            label="Est. Home Price Appreciation (Hist 5 Yr.)"
            value={allValues["HPA 5Yr"]}
            inputValueType="percent"
            onChange={(value) => {
              setUserInputHPA5Yr(value);
              // saveUserInputValues({ userInputHPA5Yr: value });
            }}
            minValue={-100}
            tooltip={getProFormaTooltip("HPA 5Yr")}
          />
          <ProFormaRowSingleInputAria
            control={control}
            name="mortgageRate"
            label="Mortgage Rate"
            value={allValues["Mortgage Rate"]}
            onChange={(value) => {
              setUserInputMortgageRate(value);
              // saveUserInputValues({ userInputMortgageRate: value });
            }}
            inputValueType="percent"
            tooltip={getProFormaTooltip("Mortgage Rate")}
          />
        </div>
        <div className="flex flex-col gap-3">
          <div className={`text-dark-gray font-bold ml-2 mt-3 mb-1 ${isMobile ? 'text-base' : 'text-xs'}`}>
            Return Metrics
          </div>
          <DisplayField
            label="Est. Annual Cash Flow"
            value={formatPrice(allValues["Levered Cash Flow Yearly"])}
            tooltip={getProFormaTooltip("Levered Cash Flow Yearly")}
          />
          <DisplayField
            label="Est. Annual Cash on Cash Return"
            value={formatPercentage(allValues["Cash-on-Cash Return"])}
            tooltip={getProFormaTooltip("Cash-on-Cash Return")}
          />
          <DisplayField
            label="Est. Cap Rate"
            value={allValues["Projected Monthly Rent"] === 0 ? "N/A" : formatPercentage(allValues["Projected Yield on Bid Price"])}
            tooltip={getProFormaTooltip("Projected Yield on Bid Price")}
          />
          <Controller
            control={control}
            name="yearsHolding"
            render={({ field: { ref } }) => (
              <ToggleGroup
                type="single"
                ref={ref}
                value={allValues["Years Holding"].toString()}
                onValueChange={(value) => {
                  setUserInputYearsHolding(+value);
                  // saveUserInputValues({ userInputYearsHolding: +value });
                }}
                className='w-full h-[26px] flex justify-between items-center bg-[var(--color-super-light-gray)] rounded-full p-0.5'
              >
                <ToggleGroupItem value='5' aria-label='5 year holding' className='w-1/3 h-[22px] text-xs text-dark-gray rounded-full cursor-pointer data-[state=off]:!bg-transparent data-[state=off]:!font-normal data-[state=on]:!bg-blue-20 data-[state=on]:!font-bold'>
                  5 Yrs
                </ToggleGroupItem>
                <ToggleGroupItem value='7' aria-label='7 year holding' className='w-1/3 h-[22px] text-xs text-dark-gray rounded-full cursor-pointer data-[state=off]:!bg-transparent data-[state=off]:!font-normal data-[state=on]:!bg-blue-20 data-[state=on]:!font-bold'>
                  7 Yrs
                </ToggleGroupItem>
                <ToggleGroupItem value='10' aria-label='10 year holding' className='w-1/3 h-[22px] text-xs text-dark-gray rounded-full cursor-pointer data-[state=off]:!bg-transparent data-[state=off]:!font-normal data-[state=on]:!bg-blue-20 data-[state=on]:!font-bold'>
                  10 Yrs
                </ToggleGroupItem>
              </ToggleGroup>
            )}
          />
          <DisplayField
            label="Est. Total Return"
            value={formatPrice(allValues["Total Return"])}
            tooltip={getProFormaTooltip("Total Return")}
          />
          <DisplayField
            label="Est. Annualized Return"
            value={formatPercentage(allValues["Annualized Return"])}
            tooltip={getProFormaTooltip("Annualized Return")}
          />
        </div>
      </div>
      <div className='w-full flex justify-center items-center mt-2'>
        <Button
          type='button' // prevent this button from being clicked and triggering form submission whenever Enter is pressed
          onClick={() => setShowFullProForma(!showFullProForma)}
          className={`w-[164px] flex justify-center items-center text-dark-gray bg-transparent mt-2 underline underline-offset-8 cursor-pointer ${isMobile ? 'text-base' : 'text-xs'}`}
        >
          {showFullProForma ? 'Show Summary Only' : 'See Detailed Pro Forma'}
        </Button>
      </div>
    </div>
  );
};

export default PropertySummarySection;