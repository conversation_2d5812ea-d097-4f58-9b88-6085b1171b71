import { ReactNode } from "react";
import { useBreakpoint } from "@/hooks/useBreakpoint";

interface ProFormaSectionProps {
  title: string;
  width?: string;
  className?: string;
  children: ReactNode;
}

const ProFormaSection = ({
  title,
  width = "w-[45%]",
  className,
  children,
}: ProFormaSectionProps) => {
  const { isMobile } = useBreakpoint();

  return (
    <div className={`flex flex-col ${width} gap-2 ${className}`}>
      <h3 className={`text-dark-gray mt-0 font-bold pl-2 py-1 ${isMobile ? 'text-base' : 'text-xs'}`}>
        {title}
      </h3>
      {children}
    </div>
  );
}

export default ProFormaSection;
