import { ReactNode } from "react";

interface ProFormaSectionProps {
  title: string;
  width?: string;
  className?: string;
  children: ReactNode;
}

const ProFormaSection = ({
  title,
  width = "w-[45%]",
  className,
  children,
}: ProFormaSectionProps) => (
  <div className={`flex flex-col ${width} gap-2 ${className}`}>
    <h3 className="text-dark-gray mt-0 font-bold text-xs pl-2 py-1">
      {title}
    </h3>
    {children}
  </div>
);

export default ProFormaSection;
