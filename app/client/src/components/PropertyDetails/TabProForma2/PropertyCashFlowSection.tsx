import React from 'react';
import ProFormaSection from './ProFormaSection';
import DisplayField from './DisplayField';
import { formatPrice, formatPercentage } from '@/lib/utils/formatUtils';

interface PropertyCashFlowSectionProps {
  allValues: any;
}

const PropertyCashFlowSection: React.FC<PropertyCashFlowSectionProps> = ({
  allValues,
}) => {
  return (
    <ProFormaSection title="Cash Flow" width="w-[55%]">
      <DisplayField
        label="Cash Flow"
        value={formatPrice(allValues["Levered Cash Flow Yearly"])}
      />
      <DisplayField
        label="Cash-on-Cash Return"
        value={formatPercentage(allValues["Cash-on-Cash Return"])}
      />
    </ProFormaSection>
  );
};

export default PropertyCashFlowSection;