import { useState, useEffect } from "react";
import {
  getAllValues,
  standardizeProFormaBuyAndHoldLennarInput,
} from '@/lib/utils/ProFormaBuyAndHoldLennarFormula2';
import {
  ParcelDataType,
  LennarSinglePropertyDataType,
  DemographicsDataType,
  IntelligentCompingResponseDataType,
  ProFormaValuesLennar,
} from '@/lib/utils/types';
import { useForm, SubmitHandler } from "react-hook-form";
import { isEqual } from 'lodash';
import { formatPrice, formatPercentage } from "@/lib/utils/formatUtils";
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import ProFormaSummary from "./ProFormaSummary";
import PropertyPricingSection from "./PropertyPricingSection";
import PropertyDebtSection from "./PropertyDebtSection";
import PropertyExpensesSection from "./PropertyExpensesSection";
import PropertyCashFlowSection from "./PropertyCashFlowSection";
import PropertyRentalIncomeSection from "./PropertyRentalIncomeSection";
import { getSavedSingleProForma, saveSingleProForma } from "@/lib/query/save-pro-forma-off-market";
import DisplayField from "./DisplayField";
import PropertySummarySection from "./PropertySummarySection";

interface FormInput {
  bidPrice: number;
  closingCosts: number;
  projectedRent: number;
  otherIncome: number;
  propertyTax: number;
  insurance: number;
  hoa: number;
  rAndM: number;
  pmFeesCoeff: number;
  pmFees: number;
  vacancyLossCoeff: number;
  vacancyLoss: number;
  annualCommunityDevelopmentFee: number;
  loanToValue: number;
  mortgageRate: number;
  mortgageTerm: number;
  hpa5Yr: number;
  yearsHolding: number;
}

// if a userInput state is null, that means user has not manually changed the value
// if a userInput state is not null, that means user has manually changed the value
// non-null userInput states override the calculated values

// for double inputs such as property management fees and vacancy loss,
// when a user changes the percentage, the currency value will be set to null
// so that the currency value will be recalculated based on the percentage
// same for changing the currency value, the percentage value will be set to null
// so that the percentage value will be recalculated based on the currency value

// we only save the userInput values to the database, not the calculated values
// we save all userInput values when userInput values change
// and we fetch and load saved userInput values when the subject property changes

const ProFormaLennar = ({
  parcelData,
  subjectPropertyData,
  demographicsData,
  intelligentCompsData,
  hpa5Yr,
}: {
  parcelData: ParcelDataType;
  subjectPropertyData: LennarSinglePropertyDataType;
  demographicsData: DemographicsDataType;
  intelligentCompsData: IntelligentCompingResponseDataType;
  hpa5Yr: number;
}) => {
  const [showFullProForma, setShowFullProForma] = useState(false);

  const { setProFormaAllValues } = useMarketplaceMapContext();
  
  const [userInputBidPrice, setUserInputBidPrice] = useState<number | null>(null);
  const [userInputClosingCosts, setUserInputClosingCosts] = useState<number | null>(null);
  const [userInputProjectedRent, setUserInputProjectedRent] = useState<number | null>(null);
  const [userInputOtherIncome, setUserInputOtherIncome] = useState<number | null>(null);
  const [userInputPropertyTax, setUserInputPropertyTax] = useState<number | null>(null);
  const [userInputInsurance, setUserInputInsurance] = useState<number | null>(null);
  const [userInputHoa, setUserInputHoa] = useState<number | null>(null);
  const [userInputRAndM, setUserInputRAndM] = useState<number | null>(null);
  const [userInputPmFeesCoeff, setUserInputPmFeesCoeff] = useState<number | null>(null);
  const [userInputPmFees, setUserInputPmFees] = useState<number | null>(null);
  const [userInputVacancyLossCoeff, setUserInputVacancyLossCoeff] = useState<number | null>(null);
  const [userInputVacancyLoss, setUserInputVacancyLoss] = useState<number | null>(null);
  const [userInputAnnualCommunityDevelopmentFee, setUserInputAnnualCommunityDevelopmentFee] = useState<number | null>(null);
  const [userInputLoanToValue, setUserInputLoanToValue] = useState<number | null>(null);
  const [userInputMortgageRate, setUserInputMortgageRate] = useState<number | null>(null);
  const [userInputMortgageTerm, setUserInputMortgageTerm] = useState<number | null>(null);
  const [userInputHPA5Yr, setUserInputHPA5Yr] = useState<number | null>(null);
  const [userInputYearsHolding, setUserInputYearsHolding] = useState<number | null>(null);

  const [prevSubjectPropertyData, setPrevSubjectPropertyData] = useState<LennarSinglePropertyDataType | null>(null);
  const [prevAllValues, setPrevAllValues] = useState<ProFormaValuesLennar | null>(null);

  // Listen for projected rent updates from other components (like Comps tab)
  useEffect(() => {
    const handleProjectedRentUpdate = (event: CustomEvent) => {
      const { value, propertyId } = event.detail;
      // Only update if it's for the same property
      if (propertyId === subjectPropertyData?.property_id) {
        setUserInputProjectedRent(value);
      }
    };

    window.addEventListener('projectedRentUpdated', handleProjectedRentUpdate as EventListener);
    return () => {
      window.removeEventListener('projectedRentUpdated', handleProjectedRentUpdate as EventListener);
    };
  }, [subjectPropertyData?.property_id]);

  // after subject property has changed, reset the pro forma
  if (!isEqual(subjectPropertyData, prevSubjectPropertyData)) {
    setPrevSubjectPropertyData(subjectPropertyData);
    // fetch saved pro forma user input values
    getSavedSingleProForma(subjectPropertyData?.property_id.toString()).then((savedInputValues) => {
      console.log('savedInputValues', savedInputValues);
      setUserInputBidPrice(savedInputValues.userInputBidPrice || null);
      setUserInputProjectedRent(savedInputValues.userInputProjectedRent || null);
      setUserInputOtherIncome(savedInputValues.userInputOtherIncome || null);
      setUserInputPropertyTax(savedInputValues.userInputPropertyTax || null);
      setUserInputInsurance(savedInputValues.userInputInsurance || null);
      setUserInputHoa(savedInputValues.userInputHoa || null);
      setUserInputRAndM(savedInputValues.userInputRAndM || null);
      setUserInputPmFeesCoeff(savedInputValues.userInputPmFeesCoeff || null);
      setUserInputPmFees(savedInputValues.userInputPmFees || null);
      setUserInputVacancyLossCoeff(savedInputValues.userInputVacancyLossCoeff || null);
      setUserInputVacancyLoss(savedInputValues.userInputVacancyLoss || null);
      setUserInputAnnualCommunityDevelopmentFee(savedInputValues.userInputAnnualCommunityDevelopmentFee || null);
      setUserInputClosingCosts(savedInputValues.userInputClosingCosts || null);
      setUserInputLoanToValue(savedInputValues.userInputLoanToValue || null);
      setUserInputMortgageRate(savedInputValues.userInputMortgageRate || null);
      setUserInputMortgageTerm(savedInputValues.userInputMortgageTerm || null);
      setUserInputHPA5Yr(savedInputValues.userInputHPA5Yr || null);
      setUserInputYearsHolding(savedInputValues.userInputYearsHolding || null);
    });
  }

  const proFormaInputs = standardizeProFormaBuyAndHoldLennarInput({
    parcelData,
    subjectPropertyData: subjectPropertyData?.payload?.subjectProperty,
    demographicsData,
    adjustedRent: intelligentCompsData?.data?.results?.adjusted_rent?.adjusted_value,
    adjustedSales: intelligentCompsData?.data?.results?.adjusted_sales?.adjusted_value,
    hpa5Yr,
    userInputBidPrice,
    userInputClosingCosts,
    userInputProjectedRent,
    userInputOtherIncome,
    userInputPropertyTax,
    userInputInsurance,
    userInputHoa,
    userInputRAndM,
    userInputPmFeesCoeff,
    userInputPmFees,
    userInputVacancyLossCoeff,
    userInputVacancyLoss,
    userInputAnnualCommunityDevelopmentFee,
    userInputLoanToValue,
    userInputMortgageRate,
    userInputMortgageTerm,
    userInputHPA5Yr,
    userInputYearsHolding,
  });
  // console.log('proFormaInputs', proFormaInputs);
  
  const allValues = getAllValues(proFormaInputs);
  console.log('allValues', allValues);

  // Update context with pro forma values whenever they change
  if (!isEqual(allValues, prevAllValues)) {
    setProFormaAllValues(allValues);
    setPrevAllValues(allValues);
  }

  // save pro forma values when userInput values change
  const saveUserInputValues = (changedValues: {
    userInputBidPrice?: number | null;
    userInputClosingCosts?: number | null;
    userInputProjectedRent?: number | null;
    userInputOtherIncome?: number | null;
    userInputPropertyTax?: number | null;
    userInputInsurance?: number | null;
    userInputHoa?: number | null;
    userInputRAndM?: number | null;
    userInputPmFeesCoeff?: number | null;
    userInputPmFees?: number | null;
    userInputVacancyLossCoeff?: number | null;
    userInputVacancyLoss?: number | null;
    userInputAnnualCommunityDevelopmentFee?: number | null;
    userInputLoanToValue?: number | null;
    userInputMortgageRate?: number | null;
    userInputMortgageTerm?: number | null;
    userInputHPA5Yr?: number | null;
    userInputYearsHolding?: number | null;
  }) => {
    saveSingleProForma({
      propertyId: subjectPropertyData?.property_id,
      source: 'offmarket',
      proFormaValues: {
        userInputBidPrice,
        userInputClosingCosts,
        userInputProjectedRent,
        userInputOtherIncome,
        userInputPropertyTax,
        userInputInsurance,
        userInputHoa,
        userInputRAndM,
        userInputPmFeesCoeff,
        userInputPmFees,
        userInputVacancyLossCoeff,
        userInputVacancyLoss,
        userInputAnnualCommunityDevelopmentFee,
        userInputLoanToValue,
        userInputMortgageRate,
        userInputMortgageTerm,
        userInputHPA5Yr,
        userInputYearsHolding,
        ...changedValues,
      },
    });
  };

  const { handleSubmit, control } = useForm<FormInput>({
    defaultValues: {},
  });

  const onSubmit: SubmitHandler<FormInput> = (data) => {
    console.log(data);
  };

  return (
    <>
      {/* <div className="flex gap-4">
        <ProFormaSummary
          label="Projected NOI"
          value={formatPrice(allValues["Projected NOI"])}
          width={"w-[45%]"}
        />
        <ProFormaSummary
          label="Projected Cap Rate at Bid Price:"
          value={formatPercentage(allValues["Projected Yield on Bid Price"])}
          width={"w-[55%]"}
        />
      </div> */}
      <form onSubmit={handleSubmit(onSubmit)}>
        {/* summary */}
        <div className="w-full px-2 py-2">
          <PropertySummarySection
            allValues={allValues}
            control={control}
            setUserInputLoanToValue={setUserInputLoanToValue}
            setUserInputProjectedRent={setUserInputProjectedRent}
            setUserInputHPA5Yr={setUserInputHPA5Yr}
            setUserInputMortgageRate={setUserInputMortgageRate}
            setUserInputYearsHolding={setUserInputYearsHolding}
            saveUserInputValues={saveUserInputValues}
            setShowFullProForma={setShowFullProForma}
            showFullProForma={showFullProForma}
          />
        </div>
        {showFullProForma && (
          <div>
            {/* border line */}
            {/* <div className="border-b border-[var(--color-super-light-gray)] mt-8"></div> */}
            {/* Top */}
            <div className="flex gap-12 py-4 mt-4">
              {/*  Left: Pricing, Rental Income*/}
              <div className="block w-1/2 pl-2">
                <PropertyPricingSection
                  allValues={allValues}
                  control={control}
                  setUserInputBidPrice={setUserInputBidPrice}
                  setUserInputClosingCosts={setUserInputClosingCosts}
                  saveUserInputValues={saveUserInputValues}
                />
                <PropertyRentalIncomeSection
                  allValues={allValues}
                  control={control}
                  // setUserInputProjectedRent={setUserInputProjectedRent}
                  setUserInputOtherIncome={setUserInputOtherIncome}
                  saveUserInputValues={saveUserInputValues}
                />
                <PropertyDebtSection
                  allValues={allValues}
                  // control={control}
                  // setUserInputLoanToValue={setUserInputLoanToValue}
                  // setUserInputMortgageRate={setUserInputMortgageRate}
                  // setUserInputMortgageTerm={setUserInputMortgageTerm}
                  // saveUserInputValues={saveUserInputValues}
                />
              </div>
              {/* Right: Annual Operating Expenses, and Net Operating Income */}
              <div className="block w-1/2 pr-2">
                <PropertyExpensesSection
                  allValues={allValues}
                  control={control}
                  setUserInputPropertyTax={setUserInputPropertyTax}
                  setUserInputInsurance={setUserInputInsurance}
                  setUserInputHoa={setUserInputHoa}
                  setUserInputRAndM={setUserInputRAndM}
                  setUserInputPmFeesCoeff={setUserInputPmFeesCoeff}
                  setUserInputPmFees={setUserInputPmFees}
                  setUserInputVacancyLossCoeff={setUserInputVacancyLossCoeff}
                  setUserInputVacancyLoss={setUserInputVacancyLoss}
                  setUserInputAnnualCommunityDevelopmentFee={setUserInputAnnualCommunityDevelopmentFee}
                  saveUserInputValues={saveUserInputValues}
                />
              </div>
            </div>

            {/* border line */}
            {/* <div className="border-b border-[var(--color-super-light-gray)]"></div> */}

            {/* Bottom: Debt and Cash Flow */}
            {/* <div className="flex gap-4 py-4">
              <PropertyDebtSection
                allValues={allValues}
                control={control}
                setUserInputLoanToValue={setUserInputLoanToValue}
                setUserInputMortgageRate={setUserInputMortgageRate}
                setUserInputMortgageTerm={setUserInputMortgageTerm}
                saveUserInputValues={saveUserInputValues}
              />

              <PropertyCashFlowSection
                allValues={allValues}
              />
            </div> */}
          </div>
        )}
      </form>
    </>
  );
};

export default ProFormaLennar;