import { useState, useEffect } from "react";
import { LennarSinglePropertyDataType } from '@/lib/utils/types';
import { useForm, SubmitHandler, FormProvider } from "react-hook-form";
import { useRouter } from '@tanstack/react-router';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { useFormSubmission } from '@/contexts/FormSubmissionContext';
import { saveToBookmarkData } from "@/lib/query/save-to-bookmark";
import { useProFormaLennar } from "@/lib/hooks/useProFormaLennar";
import { bookmarkManager } from "@/lib/utils/bookmarkManager";
import PropertyPricingSection from "./PropertyPricingSection";
import PropertyDebtSection from "./PropertyDebtSection";
import PropertyExpensesSection from "./PropertyExpensesSection";
import PropertyRentalIncomeSection from "./PropertyRentalIncomeSection";
import PropertySummarySection from "./PropertySummarySection";

interface FormInput {
  bidPrice: number;
  closingCosts: number;
  projectedRent: number;
  otherIncome: number;
  propertyTax: number;
  insurance: number;
  hoa: number;
  rAndM: number;
  pmFeesCoeff: number;
  pmFees: number;
  vacancyLossCoeff: number;
  vacancyLoss: number;
  annualCommunityDevelopmentFee: number;
  loanToValue: number;
  mortgageRate: number;
  mortgageTerm: number;
  hpa5Yr: number;
  yearsHolding: number;
}

// if a userInput state is null, that means user has not manually changed the value
// if a userInput state is not null, that means user has manually changed the value
// non-null userInput states override the calculated values

// for double inputs such as property management fees and vacancy loss,
// when a user changes the percentage, the currency value will be set to null
// so that the currency value will be recalculated based on the percentage
// same for changing the currency value, the percentage value will be set to null
// so that the percentage value will be recalculated based on the currency value

// we only save the userInput values to the database, not the calculated values
// we save all userInput values when userInput values change
// and we fetch and load saved userInput values when the subject property changes

const ProFormaLennar = ({
  subjectPropertyData,
}: {
  subjectPropertyData: LennarSinglePropertyDataType;
}) => {
  const [showFullProForma, setShowFullProForma] = useState(false);

  const {
    proFormaAllValues,
    proFormaInputsLennar,
    setProFormaInputsLennar,
    isBookmarked,
  } = useMarketplaceMapContext();

  const { registerSubmitCallback, setIsSubmitting } = useFormSubmission();
  const router = useRouter();

  const methods = useForm<FormInput>({
    defaultValues: {},
  });

  const onSubmit: SubmitHandler<FormInput> = async (data) => {
    setIsSubmitting(true);
    
    // Get the current bookmark state from local storage (most up-to-date)
    const propertyId = subjectPropertyData?.property_id;
    const currentBookmarkState = propertyId ? bookmarkManager.isBookmarked(propertyId) : false;
    
    try {
      await saveToBookmarkData({
        source: 'offmarket',
        propertyId: subjectPropertyData?.property_id?.toString() || '',
        body: {
          decision: 'approve with condition',
          notes: '',
          payload: {
            proforma: {
              buyAndHold: currentBookmarkState ? proFormaAllValues : null,
              userInputs: proFormaInputsLennar?.proFormaValues || {},
            },
            subjectProperty: subjectPropertyData?.payload?.subjectProperty,
          }
        }
      });
      
      // Mark bookmark as synced with backend
      bookmarkManager.syncWithBackend([]);
      
      // Optional: Refresh data in background for eventual consistency
      setTimeout(() => router.invalidate(), 1000);
    } catch (error) {
      console.error('Error saving to bookmark:', error);
      // Revert optimistic update on error
      if (subjectPropertyData?.property_id) {
        bookmarkManager.toggleBookmark(subjectPropertyData.property_id);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Register the form submission callback with the context
  useEffect(() => {
    registerSubmitCallback(() => {
      methods.handleSubmit(onSubmit)();
    });
  }, [methods.handleSubmit]);

  // prevent form submission when enter key is pressed
  const preventEnterKeySubmission = (e: React.KeyboardEvent<HTMLFormElement>) => {
    const target = e.target;
    if (e.key === "Enter" && target instanceof HTMLInputElement) {
      e.preventDefault();
    }
  };

  const { allValues, resetProForma } = useProFormaLennar();

  console.log('allValues', allValues);

  console.log('underwriting id', subjectPropertyData?.underwriting_id);

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} onKeyDown={preventEnterKeySubmission}>
        <div className={`flex justify-end gap-8 mr-4`}>
          {/* submit button */}
          {subjectPropertyData?.decision === 'approve with condition' && (
            <button type="submit" className="text-[var(--color-dark-green)] text-sm lg:text-base cursor-pointer hover:text-[var(--color-lennar-blue-dark)]">
              Update Saved Pro Forma
            </button>
          )}
          <button type="button" className="text-[var(--color-dark-green)] text-sm lg:text-base cursor-pointer hover:text-[var(--color-lennar-blue-dark)]" onClick={resetProForma}>
            Reset Pro Forma
          </button>
        </div>

        {/* summary */}
        <div className="w-full px-2 py-2">
          <PropertySummarySection
            allValues={allValues}
            control={methods.control}
            setUserInputLoanToValue={(value: number | null) => {
              setProFormaInputsLennar({
                ...proFormaInputsLennar,
                proFormaValues: {
                  ...proFormaInputsLennar?.proFormaValues,
                  userInputLoanToValue: value,
                },
              });
            }}
            setUserInputProjectedRent={(value: number | null) => {
              setProFormaInputsLennar({
                ...proFormaInputsLennar,
                proFormaValues: {
                  ...proFormaInputsLennar?.proFormaValues,
                  userInputProjectedRent: value,
                },
              });
            }}
            setUserInputHPA5Yr={(value: number | null) => {
              setProFormaInputsLennar({
                ...proFormaInputsLennar,
                proFormaValues: {
                  ...proFormaInputsLennar?.proFormaValues,
                  userInputHPA5Yr: value,
                },
              });
            }}
            setUserInputMortgageRate={(value: number | null) => {
              setProFormaInputsLennar({
                ...proFormaInputsLennar,
                proFormaValues: {
                  ...proFormaInputsLennar?.proFormaValues,
                  userInputMortgageRate: value,
                },
              });
            }}
            setUserInputYearsHolding={(value: number | null) => {
              setProFormaInputsLennar({
                ...proFormaInputsLennar,
                proFormaValues: {
                  ...proFormaInputsLennar?.proFormaValues,
                  userInputYearsHolding: value,
                },
              });
            }}
            // saveUserInputValues={saveUserInputValues}
            setShowFullProForma={setShowFullProForma}
            showFullProForma={showFullProForma}
          />
        </div>
        {showFullProForma && (
          <div className="@container mx-auto px-2 py-2">
            <div className="grid grid-cols-1 @2xl:grid-cols-2 gap-8 mt-4">
              {/*  Left: Pricing, Rental Income*/}
              <div className="block">
                <PropertyPricingSection
                  allValues={allValues}
                  control={methods.control}
                  setUserInputBidPrice={(value: number | null) => {
                    setProFormaInputsLennar({
                      ...proFormaInputsLennar,
                      proFormaValues: {
                        ...proFormaInputsLennar?.proFormaValues,
                        userInputBidPrice: value,
                      },
                    });
                  }}
                  setUserInputClosingCosts={(value: number | null) => {
                    setProFormaInputsLennar({
                      ...proFormaInputsLennar,
                      proFormaValues: {
                        ...proFormaInputsLennar?.proFormaValues,
                        userInputClosingCosts: value,
                      },
                    });
                  }}
                // saveUserInputValues={saveUserInputValues}
                />
                <PropertyRentalIncomeSection
                  allValues={allValues}
                  control={methods.control}
                  // setUserInputProjectedRent={setUserInputProjectedRent}
                  setUserInputOtherIncome={(value: number | null) => {
                    setProFormaInputsLennar({
                      ...proFormaInputsLennar,
                      proFormaValues: {
                        ...proFormaInputsLennar?.proFormaValues,
                        userInputOtherIncome: value,
                      },
                    });
                  }}
                // saveUserInputValues={saveUserInputValues}
                />
                <PropertyDebtSection
                  allValues={allValues}
                />
              </div>
              {/* Right: Annual Operating Expenses, and Net Operating Income */}
              <div className="block">
                <PropertyExpensesSection
                  allValues={allValues}
                  control={methods.control}
                  setUserInputPropertyTax={(value: number | null) => {
                    setProFormaInputsLennar({
                      ...proFormaInputsLennar,
                      proFormaValues: {
                        ...proFormaInputsLennar?.proFormaValues,
                        userInputPropertyTax: value,
                      },
                    });
                  }}
                  setUserInputInsurance={(value: number | null) => {
                    setProFormaInputsLennar({
                      ...proFormaInputsLennar,
                      proFormaValues: {
                        ...proFormaInputsLennar?.proFormaValues,
                        userInputInsurance: value,
                      },
                    });
                  }}
                  setUserInputHoa={(value: number | null) => {
                    setProFormaInputsLennar({
                      ...proFormaInputsLennar,
                      proFormaValues: {
                        ...proFormaInputsLennar?.proFormaValues,
                        userInputHoa: value,
                      },
                    });
                  }}
                  setUserInputRAndM={(value: number | null) => {
                    setProFormaInputsLennar({
                      ...proFormaInputsLennar,
                      proFormaValues: {
                        ...proFormaInputsLennar?.proFormaValues,
                        userInputRAndM: value,
                      },
                    });
                  }}
                  setUserInputVacancyLossCoeff={(value: number | null) => {
                    setProFormaInputsLennar({
                      ...proFormaInputsLennar,
                      proFormaValues: {
                        ...proFormaInputsLennar?.proFormaValues,
                        userInputVacancyLossCoeff: value,
                        userInputVacancyLoss: null,
                      },
                    });
                  }}
                  setUserInputVacancyLoss={(value: number | null) => {
                    setProFormaInputsLennar({
                      ...proFormaInputsLennar,
                      proFormaValues: {
                        ...proFormaInputsLennar?.proFormaValues,
                        userInputVacancyLoss: value,
                        userInputVacancyLossCoeff: null,
                      },
                    });
                  }}
                  setUserInputPmFeesCoeff={(value: number | null) => {
                    setProFormaInputsLennar({
                      ...proFormaInputsLennar,
                      proFormaValues: {
                        ...proFormaInputsLennar?.proFormaValues,
                        userInputPmFeesCoeff: value,
                        userInputPmFees: null,
                      },
                    });
                  }}
                  setUserInputPmFees={(value: number | null) => {
                    setProFormaInputsLennar({
                      ...proFormaInputsLennar,
                      proFormaValues: {
                        ...proFormaInputsLennar?.proFormaValues,
                        userInputPmFees: value,
                        userInputPmFeesCoeff: null,
                      },
                    });
                  }}
                  setUserInputAnnualCommunityDevelopmentFee={(value: number | null) => {
                    setProFormaInputsLennar({
                      ...proFormaInputsLennar,
                      proFormaValues: {
                        ...proFormaInputsLennar?.proFormaValues,
                        userInputAnnualCommunityDevelopmentFee: value,
                      },
                    });
                  }}
                // saveUserInputValues={saveUserInputValues}
                />
              </div>
            </div>
          </div>
        )}
      </form>
    </FormProvider>
  );
};

export default ProFormaLennar;