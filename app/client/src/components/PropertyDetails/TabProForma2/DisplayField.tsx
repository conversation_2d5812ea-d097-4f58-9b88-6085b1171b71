import React, { ReactNode } from "react";
import { Info } from 'lucide-react';
import { MobileTooltip, Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { useBreakpoint } from "@/hooks/useBreakpoint";

interface DisplayFieldProps {
  label: string;
  value: ReactNode;
  className?: string;
  height?: string;
  boldLabel?: boolean;
  tooltip?: string;
}

const DisplayField: React.FC<DisplayFieldProps> = ({
  label,
  value,
  className = "",
  height = "h-[26px]",
  boldLabel = false,
  tooltip = "",
}) => {
  const { isMobile } = useBreakpoint();
  const getLabelWidth = (isMobile: boolean, label: string) =>
    isMobile && label === "Est. Annual Cash on Cash Return" ? "w-[185px]" : "";
  return (
    <div
      className={`flex justify-between items-center ${isMobile ? 'text-base' : 'text-xs'} pl-2 ${className} ${height}`}
    >
      <div className="flex flex-1 items-center gap-1">
        <span
          className={`
            ${boldLabel
              ? "font-semibold text-dark-gray"
              : "font-extralight text-dark-gray"}
           ${getLabelWidth(isMobile, label)}`}
        >
          {label}
        </span>
        {tooltip && (
          <MobileTooltip
            content={
              <div className="w-[200px] max-h-[50vh] overflow-y-auto overflow-x-auto bg-white text-dark-gray rounded-md py-5 px-4 z-300 border border-medium-gray-20 text-xs">
                {tooltip}
              </div>
            }
          >
            <div className={`flex items-center justify-center self-start ${isMobile ? "h-5 w-5" : "h-4 w-4"} cursor-pointer  ml-2`}>
              <Info className={`text-dark-gray hover:text-primary transition-colors`} />
            </div>
          </MobileTooltip>
        )}

      </div>
      <span className={`inline-flex justify-end py-1 pr-2 text-dark-gray ${tooltip ? 'w-1/3' : ''}`}>{value}</span>
    </div>
  );
};

export default DisplayField;
