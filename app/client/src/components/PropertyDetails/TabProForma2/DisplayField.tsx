import React, { ReactNode } from "react";
import { Info } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

interface DisplayFieldProps {
  label: string;
  value: ReactNode;
  className?: string;
  height?: string;
  boldLabel?: boolean;
  tooltip?: string;
}

const DisplayField: React.FC<DisplayFieldProps> = ({
  label,
  value,
  className = "",
  height = "h-[26px]",
  boldLabel = false,
  tooltip = "",
}) => {
  return (
    <div
      className={`flex justify-between items-center text-xs pl-2 ${className} ${height}`}
    >
      <div className="flex items-center gap-1">
        <span
          className={
            boldLabel
              ? "font-semibold text-dark-gray"
              : "font-extralight text-dark-gray"
          }
        >
          {label}
        </span>
        {tooltip && (
          <Tooltip>
            <TooltipTrigger aria-label={`Information about ${label}`}>
              <div className="flex items-center justify-center h-4 w-4">
                <Info className="h-3 w-3 cursor-help text-dark-gray hover:text-primary transition-colors" />
              </div>
            </TooltipTrigger>
            <TooltipContent className="w-[200px] max-h-[50vh] overflow-y-auto overflow-x-auto bg-white text-dark-gray rounded-md py-5 px-4 z-300 border border-medium-gray-20 text-xs">
              {tooltip}
            </TooltipContent>
          </Tooltip>
        )}

      </div>
      <span className="py-1 pr-2 text-dark-gray">{value}</span>
    </div>
  );
};

export default DisplayField;
