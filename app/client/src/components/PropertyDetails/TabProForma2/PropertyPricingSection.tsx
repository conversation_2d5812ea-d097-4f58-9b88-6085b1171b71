import React from 'react';
import DisplayField from './DisplayField';
// import EditableProFormaField from './EditableField';
import ProFormaRowSingleInputAria from './ProFormaRowSingleInputAria';
// import ProFormaRowDoubleInputsAria from './ProFormaRowDoubleInputsAria';
import ProFormaSection from './ProFormaSection';
import { formatPrice, formatPercentage } from '@/lib/utils/formatUtils';
import { Control } from 'react-hook-form';
import { ProFormaValuesLennar } from '@/lib/utils/types';
import { getProFormaTooltip } from '@/constants/proFormaTooltips';

interface PropertyPricingSectionProps {
  allValues: ProFormaValuesLennar;
  control: Control<any>;
  setUserInputBidPrice: (value: number | null) => void;
  setUserInputClosingCosts: (value: number | null) => void;
  // saveUserInputValues: (changedValues: {
  //   userInputBidPrice?: number | null;
  //   userInputClosingCosts?: number | null;
  //   userInputProjectedRent?: number | null;
  //   userInputOtherIncome?: number | null;
  //   userInputPropertyTax?: number | null;
  //   userInputInsurance?: number | null;
  //   userInputHoa?: number | null;
  //   userInputRAndM?: number | null;
  //   userInputPmFeesCoeff?: number | null;
  //   userInputPmFees?: number | null;
  //   userInputVacancyLossCoeff?: number | null;
  //   userInputVacancyLoss?: number | null;
  //   userInputAnnualCommunityDevelopmentFee?: number | null;
  //   userInputLoanToValue?: number | null;
  //   userInputMortgageRate?: number | null;
  //   userInputMortgageTerm?: number | null;
  //   userInputHPA5Yr?: number | null;
  // }) => void;
}

const PropertyPricingSection: React.FC<PropertyPricingSectionProps> = ({
  allValues,
  control,
  setUserInputBidPrice,
  setUserInputClosingCosts,
  // saveUserInputValues,
}) => {
  return (
    <ProFormaSection title="Pricing" width="w-full">
      <DisplayField
        label="List Price"
        value={formatPrice(allValues["Asking Price"])}
      />
      <ProFormaRowSingleInputAria
        control={control}
        name="bidPrice"
        label="Purchase Price"
        value={allValues["Bid Price"]}
        onChange={(value) => {
          setUserInputBidPrice(value);
          // saveUserInputValues({ userInputBidPrice: value });
        }}
      />
      <DisplayField
        label="Purchase to List Ratio"
        value={
          allValues["Bid Price"] && allValues["Bid Price"] > 0
            ? formatPercentage(
              (allValues["Bid Price"] || 0) / allValues["Asking Price"]
            )
            : "N/A"
        }
      />
      <ProFormaRowSingleInputAria
        control={control}
        name="closingCosts"
        label="Closing Costs"
        value={allValues["Closing Costs"]}
        onChange={(value) => {
          setUserInputClosingCosts(value);
          // saveUserInputValues({ userInputClosingCosts: value });
        }}
        tooltip={getProFormaTooltip("Closing Costs")}
      />
      <DisplayField
        label="Total Acquisition Cost"
        value={formatPrice(allValues["Total Acquisition Cost"])}
      />
    </ProFormaSection>
  );
};

export default PropertyPricingSection;