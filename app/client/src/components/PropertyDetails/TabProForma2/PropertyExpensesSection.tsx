import React from 'react';
import <PERSON>FormaSection from './ProFormaSection';
import <PERSON>sp<PERSON><PERSON><PERSON> from './DisplayField';
import ProFormaRowSingleInputAria from './ProFormaRowSingleInputAria';
import ProFormaRowDoubleInputsAria from './ProFormaRowDoubleInputsAria';
import { formatPrice, formatPercentage } from '@/lib/utils/formatUtils';
import { Control } from 'react-hook-form';
import { ProFormaValuesLennar } from '@/lib/utils/types';


interface PropertyExpensesSectionProps {
  allValues: ProFormaValuesLennar;
  control: Control<any>;
  setUserInputPropertyTax: (value: number | null) => void;
  setUserInputInsurance: (value: number | null) => void;
  setUserInputHoa: (value: number | null) => void;
  setUserInputRAndM: (value: number | null) => void;
  setUserInputVacancyLossCoeff: (value: number | null) => void;
  setUserInputVacancyLoss: (value: number | null) => void;
  setUserInputPmFeesCoeff: (value: number | null) => void;
  setUserInputPmFees: (value: number | null) => void;
  setUserInputAnnualCommunityDevelopmentFee: (value: number | null) => void;
  saveUserInputValues: (changedValues: {
    userInputBidPrice?: number | null;
    userInputClosingCosts?: number | null;
    userInputProjectedRent?: number | null;
    userInputOtherIncome?: number | null;
    userInputPropertyTax?: number | null;
    userInputInsurance?: number | null;
    userInputHoa?: number | null;
    userInputRAndM?: number | null;
    userInputPmFeesCoeff?: number | null;
    userInputPmFees?: number | null;
    userInputVacancyLossCoeff?: number | null;
    userInputVacancyLoss?: number | null;
    userInputAnnualCommunityDevelopmentFee?: number | null;
    userInputLoanToValue?: number | null;
    userInputMortgageRate?: number | null;
    userInputMortgageTerm?: number | null;
    userInputHPA5Yr?: number | null;
  }) => void;
}

const PropertyExpensesSection: React.FC<PropertyExpensesSectionProps> = ({
  allValues,
  control,
  setUserInputPropertyTax,
  setUserInputInsurance,
  setUserInputHoa,
  setUserInputRAndM,
  setUserInputVacancyLossCoeff,
  setUserInputVacancyLoss,
  setUserInputPmFeesCoeff,
  setUserInputPmFees,
  setUserInputAnnualCommunityDevelopmentFee,
  saveUserInputValues,
}) => {
  return (
    <ProFormaSection title="Est. Annual Operating Expenses" width="w-full">
      <ProFormaRowSingleInputAria
        control={control}
        name="propertyTax"
        label="Property Tax"
        value={allValues["Property Tax"]}
        valueCoeff={allValues["Property Tax Coefficient"]}
        valueCoeffType="percent"
        onChange={(value) => {
          setUserInputPropertyTax(value);
          saveUserInputValues({ userInputPropertyTax: value });
        }}
      />

      <ProFormaRowSingleInputAria
        control={control}
        name="insurance"
        label="Insurance"
        value={allValues["Insurance"]}
        onChange={(value) => {
          setUserInputInsurance(value);
          saveUserInputValues({ userInputInsurance: value });
        }}
      />

      <ProFormaRowSingleInputAria
        control={control}
        name="hoaFees"
        label="HOA Fees"
        value={allValues["Annual HOA Fees"]}
        onChange={(value) => {
          setUserInputHoa(value);
          saveUserInputValues({ userInputHoa: value });
        }}
      />

      <ProFormaRowSingleInputAria
        control={control}
        name="rAndM"
        label="Repair & Maintenance"
        value={allValues["Repair & Maintenance"]}
        onChange={(value) => {
          setUserInputRAndM(value);
          saveUserInputValues({ userInputRAndM: value });
        }}
      />

      <ProFormaRowDoubleInputsAria
        control={control}
        namePercent="pmFeesCoeff"
        nameCurrency="pmFees"
        label="Property Management"
        valuePercent={allValues["Property Management Fees Coefficient"]}
        valueCurrency={allValues["Property Management Fees"]}
        onChangePercent={(value) => {
          setUserInputPmFeesCoeff(value);
          setUserInputPmFees(null);
          saveUserInputValues({
            userInputPmFeesCoeff: value,
            userInputPmFees: null,
          });
        }}
        onChangeCurrency={(value) => {
          setUserInputPmFees(value);
          setUserInputPmFeesCoeff(null);
          saveUserInputValues({
            userInputPmFees: value,
            userInputPmFeesCoeff: null,
          });
        }}
      />

      <ProFormaRowDoubleInputsAria
        control={control}
        namePercent="vacancyLossCoeff"
        nameCurrency="vacancyLoss"
        label="Vacancy Loss"
        valuePercent={allValues["Vacancy Loss Coefficient"]}
        valueCurrency={allValues["Vacancy Loss"]}
        onChangePercent={(value) => {
          setUserInputVacancyLossCoeff(value);
          setUserInputVacancyLoss(null);
          saveUserInputValues({
            userInputVacancyLossCoeff: value,
            userInputVacancyLoss: null,
          });
        }}
        onChangeCurrency={(value) => {
          setUserInputVacancyLoss(value);
          setUserInputVacancyLossCoeff(null);
          saveUserInputValues({
            userInputVacancyLoss: value,
            userInputVacancyLossCoeff: null,
          });
        }}
      />

      <ProFormaRowSingleInputAria
        control={control}
        name="communityDevelopmentFee"
        label="Community Development Fee"
        value={allValues["Annual Community Development Fee"]}
        onChange={(value) => {
          setUserInputAnnualCommunityDevelopmentFee(value);
          saveUserInputValues({
            userInputAnnualCommunityDevelopmentFee: value,
          });
        }}
      />

      <DisplayField
        label="Total Annual Expenses"
        value={formatPrice(allValues["Total Annual Expenses"])}
      />

      <DisplayField
        label="Est. Net Operating Income"
        value={formatPrice(allValues["Projected NOI"])}
        className="bg-blue-20"
        boldLabel
      />
    </ProFormaSection>
  );
};

export default PropertyExpensesSection;