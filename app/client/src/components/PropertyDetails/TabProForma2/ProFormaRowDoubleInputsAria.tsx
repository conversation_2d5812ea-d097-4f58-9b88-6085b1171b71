import { Label, Input, NumberField as NumberFieldReact<PERSON>ria } from "react-aria-components";
import { Controller } from "react-hook-form";
import type { Control } from "react-hook-form";

const commonPropsCurrency = {
  formatOptions: {
    style: 'currency' as const,
    currency: 'USD' as const,
    currencyDisplay: 'symbol' as const,
    currencySign: 'accounting' as const,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  },
};

const ProFormaRowDoubleInputsAria = ({
  control,
  namePercent,
  nameCurrency,
  label,
  valuePercent,
  valueCurrency,
  onChangePercent,
  onChangeCurrency,
  className
}: {
  control: Control<any>;
  namePercent: string;
  nameCurrency: string;
  label: string;
  valuePercent: number;
  valueCurrency: number;
  onChangePercent: (value: number | null) => void;
  onChangeCurrency: (value: number | null) => void;
  className?: string;
}) => {
  return (
    <div
      className={`flex justify-start items-center text-xs pl-2 ${className || ""} `}
    >
      <Label className="font-extralight mr-auto">{label}</Label>
      <Controller
        control={control}
        name={namePercent}
        render={({
          field: {
            // onChange: onChangeField,
            // onBlur, 
            // value: valueField, 
            ref,
            // name 
          },
          // fieldState: {
          //   isDirty,
          // }
        }) => (
          <NumberFieldReactAria
            ref={ref}
            name={namePercent}
            value={valuePercent}
            onChange={(value) => {
              onChangePercent(value);
            }}
            minValue={0}
            // maxValue={maxValue || 0}
            formatOptions={{
              style: 'percent' as const,
              minimumFractionDigits: 0,
              maximumFractionDigits: 2,
            }}
          >
            <Input className="rounded w-[72px] text-right text-button-blue py-1 mr-4" />
          </NumberFieldReactAria>
        )}
      />
      <Controller
        control={control}
        name={nameCurrency}
        render={({
          field: {
            // onChange: onChangeField,
            // onBlur, 
            // value: valueField, 
            ref,
            // name 
          },
          // fieldState: {
          //   isDirty,
          // }
        }) => (
          <NumberFieldReactAria
            ref={ref}
            name={nameCurrency}
            value={valueCurrency}
            onChange={(value) => {
              onChangeCurrency(value);
            }}
            minValue={0}
            // maxValue={maxValue || 0}
            {...commonPropsCurrency}
          >
            <Input className="rounded border border-medium-gray-20 w-[72px] text-right text-button-blue pr-2 py-1" />
          </NumberFieldReactAria>
        )}
      />
    </div>
  );
};

export default ProFormaRowDoubleInputsAria;