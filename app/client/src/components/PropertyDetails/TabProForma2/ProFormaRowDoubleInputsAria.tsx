import { MobileTooltip } from "@/components/ui/tooltip";
import { Info } from "lucide-react";
import { Label, Input, NumberField as NumberFieldReactAria } from "react-aria-components";
import { Controller } from "react-hook-form";
import type { Control } from "react-hook-form";
import { useBreakpoint } from "@/hooks/useBreakpoint";

const commonPropsCurrency = {
  formatOptions: {
    style: 'currency' as const,
    currency: 'USD' as const,
    currencyDisplay: 'symbol' as const,
    currencySign: 'accounting' as const,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  },
};

const ProFormaRowDoubleInputsAria = ({
  control,
  namePercent,
  nameCurrency,
  label,
  valuePercent,
  valueCurrency,
  onChangePercent,
  onChangeCurrency,
  className,
  tooltip = ""
}: {
  control: Control<any>;
  namePercent: string;
  nameCurrency: string;
  label: string;
  valuePercent: number;
  valueCurrency: number;
  onChangePercent: (value: number | null) => void;
  onChangeCurrency: (value: number | null) => void;
  className?: string;
  tooltip?: string;
}) => {
  const { isMobile } = useBreakpoint();
  const handleInputBlur = () => {
    if (isMobile) {
      setTimeout(() => {
        window.scrollTo(0, 0);
      }, 150);
    }
  };
  return (
    <div
      className={`flex justify-between items-center text-xs pl-2 ${className || ""} `}
    >
      <div className={`flex items-center ${isMobile ? 'w-1/3' : ''}`}>
        <Label className={`font-extralight ${isMobile ? 'text-base' : 'text-xs'}`}>{label}</Label>
        {tooltip && (
          <MobileTooltip
            content={
              <div className="w-[200px] max-h-[50vh] overflow-y-auto overflow-x-auto bg-white text-dark-gray rounded-md py-5 px-4 z-300 border border-medium-gray-20 text-xs">
                {tooltip}
              </div>
            }
          >
            <div className={`flex items-center justify-center self-start ${isMobile ? "h-5 w-5" : "h-4 w-4"} cursor-pointer ml-2`}>
              <Info className={` text-dark-gray hover:text-primary transition-colors`} />
            </div>
          </MobileTooltip>
        )}
      </div>
      <div className={`flex items-center justify-end ${isMobile ? 'w-2/3' : ''}`}>
        <Controller
          control={control}
          name={namePercent}
          render={({
            field: {
              // onChange: onChangeField,
              // onBlur, 
              // value: valueField, 
              ref,
              // name 
            },
            // fieldState: {
            //   isDirty,
            // }
          }) => (
            <NumberFieldReactAria
              aria-label={label + " Percent"}
              ref={ref}
              name={namePercent}
              value={valuePercent}
              onChange={(value) => {
                onChangePercent(value);
              }}
              minValue={0}
              // maxValue={maxValue || 0}
              formatOptions={{
                style: 'percent' as const,
                minimumFractionDigits: 0,
                maximumFractionDigits: 2,
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault(); // Prevent form submission
                }
              }}
            >
              <Input
                className={`rounded  text-right text-button-blue py-1 mr-4 ${isMobile ? 'text-base  w-[45px]' : 'text-xs w-[72px]'}`}
                onBlur={handleInputBlur} />
            </NumberFieldReactAria>
          )}
        />
        <Controller
          control={control}
          name={nameCurrency}
          render={({
            field: {
              // onChange: onChangeField,
              // onBlur, 
              // value: valueField, 
              ref,
              // name 
            },
            // fieldState: {
            //   isDirty,
            // }
          }) => (
            <NumberFieldReactAria
              aria-label={label + " Currency"}
              ref={ref}
              name={nameCurrency}
              value={valueCurrency}
              onChange={(value) => {
                onChangeCurrency(value);
              }}
              minValue={0}
              // maxValue={maxValue || 0}
              {...commonPropsCurrency}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault(); // Prevent form submission
                }
              }}
            >
              <Input
                className={`rounded border border-medium-gray-20 text-right text-button-blue pr-2 py-1 ${isMobile ? 'text-base w-[110px]' : 'text-xs w-[72px]'}`}
                onBlur={handleInputBlur} />
            </NumberFieldReactAria>
          )}
        />
      </div>
    </div>
  );
};

export default ProFormaRowDoubleInputsAria;