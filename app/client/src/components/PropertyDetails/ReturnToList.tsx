import { Link, useSearch } from '@tanstack/react-router'
import React from 'react'

interface ReturnToListProps {
  handleReturnToList: () => void;
}

const ReturnToList: React.FC<ReturnToListProps> = ({ handleReturnToList }) => {
  const search = useSearch({ from: '/_authenticated/properties' });
  return (
    <>
      <Link
        className="flex items-center gap-2 text-medium-gray hover:text-[var(--color-button-blue)] transition-colors"
        to="/properties"
        search={{
          ...search,
        }}
        onClick={handleReturnToList}
      >
        <span className="text-lg ">←</span> Return to list
      </Link>
    </>
  )
}

export default ReturnToList