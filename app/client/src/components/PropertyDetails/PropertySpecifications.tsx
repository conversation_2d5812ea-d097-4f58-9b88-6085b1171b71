import { formatNumber, pluralize } from "@/lib/utils/formatUtils";
import React from "react";

interface PropertySpecificationsProps {
  beds?: number;
  baths?: number;
  sqft?: number;
  stories?: string;
  garage?: string;
}

const PropertySpecifications: React.FC<PropertySpecificationsProps> = ({
  beds,
  baths,
  sqft,
  stories,
  garage,
}) => {
  const storiesLabel = pluralize(stories || "", "Story", "Stories");
  return (
    <table className="border-collapse text-center w-full">
      <tbody>
        <tr>
          <td className="font-bold py-1 w-[16%] text-xs lg:text-sm border-r border-medium-gray-20">
            {beds}
          </td>
          <td className="font-bold py-1 w-[16%] text-xs lg:text-sm border-r border-medium-gray-20">
            {baths}
          </td>
          <td className="font-bold py-1 w-[20%] text-xs lg:text-sm border-r border-medium-gray-20">
            {formatNumber(sqft)}
          </td>
          <td className="font-bold py-1 w-[20%] text-xs lg:text-sm border-r border-medium-gray-20">
            {stories}
          </td>
          <td className="font-bold py-1 w-[28%] text-xs lg:text-sm ">
            {garage}
          </td>
        </tr>
        <tr>
          <td className="py-1 text-small lg:text-sm px-3 border-r border-medium-gray-20">Bed</td>
          <td className="py-1 text-small lg:text-sm px-3 border-r border-medium-gray-20">Bath</td>
          <td className="py-1 text-small lg:text-sm whitespace-nowrap px-3 border-r border-medium-gray-20">Sq Ft</td>
          <td className="py-1 text-small lg:text-sm px-3 border-r border-medium-gray-20">{storiesLabel}</td>
          <td className="py-1 text-small lg:text-sm whitespace-nowrap px-3">Car Garage</td>
        </tr>
      </tbody>
    </table>
  );
};

export default PropertySpecifications;