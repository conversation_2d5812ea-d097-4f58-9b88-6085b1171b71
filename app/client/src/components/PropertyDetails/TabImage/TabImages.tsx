import React, { useState, useEffect } from 'react';
import { ExternalLink } from 'lucide-react';

import { ImageProps } from '../../../types/PropertyDetailPage.types';
import { PropertyImageItem } from '../../../hooks/usePropertyImages';
import fallbackImage from '../../../assets/images/TX Core_Watermill_Ramsey_3330_Living_4.jpg';
import LoadingSpinner from '../../../components/Common/LoadingSpinner';

interface TabImagesProps {
  images: PropertyImageItem[];
  virtualTourUrl?: string | null;
  isLoading?: boolean;
  error?: boolean;
}

// Helper function to get category display name
const getCategoryDisplayName = (category: string): string => {
  switch (category) {
    case 'elevation':
      return 'Elevation';
    case 'interior':
      return 'Interior';
    case 'floorplan':
      return 'Floorplan';
    case 'additional_floorplan':
      return 'Additional Floorplan';
    default:
      return 'Other';
  }
};

// Helper function to get category color
const getCategoryColor = (category: string): string => {
  // Use the same color for all categories
  return 'bg-[#406855] text-white border-[#406855]';
};

const TabImages: React.FC<TabImagesProps> = ({ images, virtualTourUrl, isLoading, error }) => {
  // Track loading state for each image individually
  const [loadedImages, setLoadedImages] = useState<Record<string, boolean>>({});

  // Debug: log all image data when component mounts or images change
  useEffect(() => {
    if (images && images.length > 0) {
      console.log('=== TabImages Debug Info ===');
      console.log('Total images:', images.length);
      console.log('Virtual tour URL:', virtualTourUrl);
      console.log('Image details:', images.map(img => `${img.imageName} (${img.category}) -> ${img.url}`));
      console.log('============================');
    }
  }, [images, virtualTourUrl]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    )
  }

  if (error || !images || images.length === 0) {
    return (
      <div className="flex flex-col rounded-lg overflow-hidden">
        <img
          src={fallbackImage}
          alt="Property image"
          className="w-full object-cover aspect-[16/9]"
          loading="lazy"
        />
      </div>
    )
  }

  const handleImageLoad = (imageName: string) => {
    setLoadedImages(prev => ({
      ...prev,
      [imageName]: true
    }));
  };

  return (
    <div>
      {/* Virtual Tour Link */}
      {virtualTourUrl && (
        <div className="mb-6">
          <a
            href={virtualTourUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 px-4 py-2 text-[#406855] hover:text-[#2d4a38] underline text-sm font-medium"
          >
            Click here for virtual tour
            <ExternalLink className="w-4 h-4" />
          </a>
        </div>
      )}
      
      <div className="grid grid-cols-1 gap-4">
        {images.map((image) => {
          const displayCategory = getCategoryDisplayName(image.category);
          const categoryColorClass = getCategoryColor(image.category);
          
          return (
            <div
              key={image.imageName}
              className="flex flex-col rounded-lg overflow-hidden relative aspect-[16/9]"
            >
              {/* Category label */}
              <div className={`absolute top-3 left-3 px-2 py-1 rounded-md text-xs font-medium border z-10 ${categoryColorClass}`}>
                {displayCategory}
              </div>
              
              {/* Loading skeleton that shows until image is loaded */}
              {!loadedImages[image.imageName] && (
                <div className="absolute inset-0 bg-[var(--color-light-gray)] animate-pulse" />
              )}
              <img
                src={image.url}
                alt={image.imageName}
                className={`w-full h-full object-cover transition-opacity duration-500 ease-in-out ${loadedImages[image.imageName] ? "opacity-100" : "opacity-0"
                  }`}
                loading="lazy"
                onLoad={() => handleImageLoad(image.imageName)}
                onError={() => handleImageLoad(image.imageName)}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default TabImages;