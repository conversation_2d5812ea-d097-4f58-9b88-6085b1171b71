import React from "react";
import TabNavigationItem from "./TabNavigationItem";
import { useLocation, useParams, useRouter } from "@tanstack/react-router";
import { singlePropertySearchParams } from "../../routes/_authenticated/properties/$id/route";
import { TAB_NAVIGATION } from "../../constants/tabNavigationConstants";


const TabNavigation: React.FC = () => {
  const { search } = useLocation();
  const router = useRouter();
  const params = useParams({ from: '/_authenticated/properties/$id' });

  const currentPath = router.state.location.pathname;
  const pathSegments = currentPath.split('/');
  const tabFromUrl = pathSegments[pathSegments.length - 1];

  const typedSearch: singlePropertySearchParams = {
    ...search,
    placekey: search.placekey || "",
    lat: Number(search.lat) || 0,
    lng: Number(search.lng) || 0,
    streetnum: search.streetnum || "",
    address: search.address || "",
    city: search.city || "",
    state: search.state || "",
    zip_code: search.zip_code || "",
    latitude: search.latitude || "",
    longitude: search.longitude || "",
    beds: search.beds || "",
    baths: search.baths || "",
    sqft: search.sqft || "",
    yearbuilt: search.yearbuilt || "",
    source: search.source || "",
  };

  return (
    <div className="flex w-full overflow-x-auto scroll-smooth rounded-xl justify-start">
      {TAB_NAVIGATION.map((tab) => (
        <TabNavigationItem
          href={tab.path}
          label={tab.label}
          isActive={tabFromUrl === tab.key}
          params={{ id: params.id }}
          search={typedSearch}
        />
      ))}
    </div>
  );
};

export default TabNavigation;