import { useBreakpoint } from "@/hooks/useBreakpoint";
import { useScroll } from "../../hooks/useScroll";
import { Link } from "@tanstack/react-router";

const TabNavigationItem = ({
  label,
  isActive,
  href,
  params,
  search
}: {
  label: string;
  isActive: boolean;
  href: string;
  params: { id: string };
  search: { placekey?: string; lat?: number; lng?: number; streetnum?: string; }
}) => {
  const { isMobile } = useBreakpoint();
  const { scrollToTop } = useScroll();

  const handleClick = () => {
    // Only prevent default if we're doing custom navigation
    if (isActive) return;
    scrollToTop();
  };

  if (isMobile) {
    return (
      <Link
        to={href}
        params={params}
        search={search}
        onClick={handleClick}
        className={`text-sm rounded-2xl py-1 px-2 whitespace-nowrap cursor-pointer border border-medium-gray-20 transition-colors duration-200 focus:outline-none mr-2 flex-shrink-0 flex-grow text-center
      ${isActive
            ? "bg-blue-20 text-dark-gray font-bold"
            : "bg-light-gray text-medium-gray hover:text-black "
          }
    `}
      >
        {label}
      </Link>
    );
  }

  return (
    <Link
      to={href}
      params={params}
      search={search}
      onClick={handleClick}
      className={`flex justify-center w-full text-sm transition-colors duration-200 focus:outline-none p-2 whitespace-nowrap cursor-pointer 
      ${isActive
          ? "bg-blue-20 text-dark-gray font-bold"
          : "border-transparent bg-light-gray text-medium-gray hover:text-black "
        }
    `}
    >
      {label}
    </Link>
  );

}



export default TabNavigationItem;