import { Link } from "@tanstack/react-router";

const TabNavigationItem = ({
  label,
  isActive,
  href,
  params,
  search
}: {
  label: string;
  isActive: boolean;
  href: string;
  params: { id: string };
  search: { placekey?: string; lat?: number; lng?: number; streetnum?: string; }
}) => (
  <Link
    to={href}
    params={params}
    search={search}
    className={`flex justify-center w-full text-sm transition-colors duration-200 focus:outline-none p-2 whitespace-nowrap cursor-pointer 
      ${isActive
        ? "bg-blue-20 text-dark-gray font-bold"
        : "border-transparent bg-light-gray text-medium-gray hover:text-black "
      }
    `}
  >
    {label}
  </Link>
);

export default TabNavigationItem;