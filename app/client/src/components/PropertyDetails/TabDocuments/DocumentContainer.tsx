import React from 'react';
import { File } from 'lucide-react';
import { DocumentProps } from '../../../types/PropertyDetailPage.types';

interface DocumentContainerProps {
  categoryKey: string;
  groupedDocuments: Record<string, DocumentProps[]>;
  categoryDisplayNames: Record<string, string>;
}

const DocumentContainer: React.FC<DocumentContainerProps> = ({
  categoryKey,
  groupedDocuments,
  categoryDisplayNames
}) => {
  const docsInCategory = groupedDocuments[categoryKey];
  const displayName = categoryDisplayNames[categoryKey] || categoryKey;

  // Only render if there are documents in the category
  if (!docsInCategory || docsInCategory.length === 0) {
    return null;
  }

  return (
    <div>
      <h3 className="mt-0 mb-2 text-xs font-light text-dark-gray">
        {displayName}
      </h3>

      {/* Document container */}
      <div className="border border-medium-gray-20 rounded-lg mt-2 mb-6">
        {/* Header row */}
        <div className="flex items-center px-4 py-3 border-b border-medium-gray-20">
          <div className="flex-grow ml-3 mr-4 text-xs ">
            Name
          </div>
          <div className="flex-shrink-0 text-small mr-[22px]">
            Link
          </div>
        </div>

        {/* Document rows */}
        {docsInCategory.map((doc) => (
          <div key={doc.id} className="flex items-center px-4 py-3 border-b border-medium-gray-20 last:border-b-0">
            <File color="dimgray" size={22} />
            <div className="flex-grow ml-3 mr-4 text-small ">
              {doc.fileName}
            </div>
            <div className="flex-shrink-0 text-small">
              <a
                href={doc.downloadLink}
                target="_blank"
                rel="noopener noreferrer"
                className="text-button-blue underline"
              >
                Download
              </a>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DocumentContainer;