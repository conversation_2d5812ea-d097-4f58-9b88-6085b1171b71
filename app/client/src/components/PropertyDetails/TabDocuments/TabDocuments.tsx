import React, { useEffect, useState } from 'react';
import { File } from "lucide-react";
import { useMarketplaceMapContext } from '../../../contexts/MarketplaceMapContext';
import { getDocuments } from '../../../lib/query/get-documents';
import { formatFileSize, getFileType } from '../../../lib/utils/formatUtils';
import { DOCUMENT_TYPES } from '../../../constants/tabDocuments';
import LoadingSpinner from '@/components/Common/LoadingSpinner';
import ErrorMessage from '@/components/Common/ErrorMessage';
import NoDataDisplay from '../TabDemographics/NoDataDisplay';

interface filesType {
  key: string;
  lastModified: string;
  size: number;
  etag: string;
  downloadUrl: string;
}

interface DocumentType {
  id: string;
  key: string;
  name: string;
  type: string;
  size: string;
  uploadDate: string;
  category: string;
  downloadUrl: string;
}

const TabDocuments: React.FC = () => {
  const { selectedBuyersViewRecord } = useMarketplaceMapContext();

  const [documents, setDocuments] = useState<DocumentType[] | null>(null);
  const [prevProject, setPrevProject] = useState<string | null>(null);
  const [prevCode, setPrevCode] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);


  useEffect(() => {
    const currentCommunity = selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.community;
    const currentPlan = selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.plan;

    const hasRequiredData = currentCommunity && currentPlan;
    const dataHasChanged = prevProject !== currentCommunity || prevCode !== currentPlan;

    if (hasRequiredData && dataHasChanged) {
      const fetchData = async () => {
        setIsLoading(true);
        try {
          const docs = await getDocuments({
            project: currentCommunity,
            code: currentPlan,
          });

          const communityName = selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.master_comm;
          const tableData: DocumentType[] = [];

          docs.files.forEach((file: filesType) => {
            const fileName = file.key.split('/').pop() || '';
            const keyLower = file.key.toLowerCase();

            let documentType = '';
            let category = 'Other';

            if (DOCUMENT_TYPES.SITE_PLAN.keywords.some(keyword => keyLower.includes(keyword))) {
              documentType = DOCUMENT_TYPES.SITE_PLAN.displayName;
              category = DOCUMENT_TYPES.SITE_PLAN.category;
            }
            else if (
              DOCUMENT_TYPES.SPEC_SHEET.keywords.some(keyword => keyLower.includes(keyword)) ||
              DOCUMENT_TYPES.SPEC_SHEET.patterns.some(pattern => pattern.test(keyLower))
            ) {
              documentType = DOCUMENT_TYPES.SPEC_SHEET.displayName;
              category = DOCUMENT_TYPES.SPEC_SHEET.category;
            }
            else {
              return; // Skip if no specific document type is matched
            }

            const displayName = `${communityName} ${documentType}`;

            tableData.push({
              id: file.key,
              key: file.key,
              name: displayName,
              type: getFileType(fileName),
              size: formatFileSize(file.size),
              uploadDate: new Date(file.lastModified).toLocaleDateString(),
              category: category,
              downloadUrl: file.downloadUrl
            });
          });

          // Add floor plan if available
          if (docs.floorPlan) {
            const planName = selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.plan_name || ""
            const displayName = `${communityName} - ${planName} - Floor Plan`;

            tableData.push({
              id: docs.floorPlan.key,
              key: docs.floorPlan.key,
              name: displayName,
              type: 'PDF',
              size: formatFileSize(docs.floorPlan.size),
              uploadDate: new Date(docs.floorPlan.lastModified).toLocaleDateString(),
              category: 'Plans',
              downloadUrl: docs.floorPlan.downloadUrl
            });
          }

          setDocuments(tableData);
          setPrevProject(selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.community);
          setPrevCode(selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.plan);
          setError(null);
        } catch (error) {
          console.error("Error fetching documents:", error);
          setDocuments([]);
          setError(error instanceof Error ? error : new Error('Failed to fetch documents'));
        } finally {
          setIsLoading(false);
        }
      };

      fetchData();
    }
  }, [selectedBuyersViewRecord]);

  if (isLoading) {
    return (
      <div className="flex w-full min-h-[182px] mt-1">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex w-full min-h-[182px] mt-1">
        <ErrorMessage
          message="We couldn't retrieve the document information at this time. Please try again later."
        />
      </div>
    );
  }

  if (!documents || documents.length === 0) {
    return (
      <NoDataDisplay entityName="Documents" />
    );
  }

  // Group documents by category
  const documentsByCategory = documents.reduce((acc, doc) => {
    if (!acc[doc.category]) {
      acc[doc.category] = [];
    }
    acc[doc.category].push(doc);
    return acc;
  }, {} as Record<string, DocumentType[]>);

  return (
    <div className="flex flex-col gap-2">
      {Object.entries(documentsByCategory).map(([category, categoryDocuments]) => (
        <div key={category}>
          <h3 className="mt-0 mb-2 text-xs font-light text-[var(--color-dark-gray)] capitalize">
            {category} Documents ({categoryDocuments.length})
          </h3>

          {/* Document container */}
          <div className="border border-medium-gray-20 rounded-lg mt-2 mb-6">
            {/* Header row */}
            <div className="flex items-center px-4 py-3 border-b border-medium-gray-20">
              <div className="flex-grow ml-3 mr-4 text-small ">
                Name
              </div>
              <div className="flex-shrink-0 text-small mr-[22px]">
                Link
              </div>
            </div>

            {/* Document rows */}
            {categoryDocuments.map((document) => (
              <div key={document.id} className={`flex items-center px-4 py-3 `}>
                <File color="var(--color-text-black)" size={18} strokeWidth={1} />
                <div className="flex-grow ml-3 mr-4 text-small ">
                  {document.name}
                </div>
                <div className="flex-shrink-0 text-small">
                  <a
                    href={document.downloadUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[var(--color-button-blue)] underline hover:text-[var(--color-button-blue)]/60  transition-colors duration-200 ease-in-out"
                  >
                    Download
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default TabDocuments;