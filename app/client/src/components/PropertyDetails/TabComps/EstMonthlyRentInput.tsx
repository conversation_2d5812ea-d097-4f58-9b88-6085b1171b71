import React from 'react';
import { useForm } from 'react-hook-form';
import ProFormaRowSingleInputAria from '../TabProForma2/ProFormaRowSingleInputAria';
import { useProjectedRentSync } from '@/hooks/useProjectedRentSync';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Info } from 'lucide-react';

interface FormInput {
  projectedRent: number;
}

const EstMonthlyRentInput: React.FC = () => {
  const { getCurrentValue, updateProjectedRent } = useProjectedRentSync();
  
  const { control } = useForm<FormInput>({
    defaultValues: {},
  });

  return (
    <div className="flex items-center">
      <div className="bg-[var(--color-super-light-gray)] border border-[var(--color-light-gray)] rounded-md px-3 py-2 min-w-[200px]">
        <div className="flex items-center gap-2">
          <div className="flex-1">
            <ProFormaRowSingleInputAria
              control={control}
              name="projectedRent"
              label="Est Monthly Rent"
              value={getCurrentValue()}
              inputValueType="currency"
              onChange={updateProjectedRent}
              className="bg-transparent [&_label]:mr-4"
            />
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="w-4 h-4 text-[var(--color-dark-gray)] cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Adjusting the Est Monthly Rent will update the Pro Forma</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    </div>
  );
};

export default EstMonthlyRentInput; 