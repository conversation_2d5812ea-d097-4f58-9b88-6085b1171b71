import React from 'react';
import { useForm } from 'react-hook-form';
import ProFormaRowSingleInputAria from '../TabProForma2/ProFormaRowSingleInputAria';
// import { useProjectedRentSync } from '@/hooks/useProjectedRentSync';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { useProFormaLennar } from '@/lib/hooks/useProFormaLennar';

interface FormInput {
  projectedRent: number;
}

const EstMonthlyRentInput: React.FC = () => {
  // const { getCurrentValue, updateProjectedRent } = useProjectedRentSync();
  const { proFormaInputsLennar, setProFormaInputsLennar } = useMarketplaceMapContext();

  const { control } = useForm<FormInput>({
    defaultValues: {},
  });

  const { allValues } = useProFormaLennar();

  return (
    <div className="flex items-center">
      <div className="bg-[var(--color-super-light-gray)] border border-[var(--color-light-gray)] rounded-md p-2 min-w-[200px]">
        <div className="flex items-center gap-2">
          <div className="flex-1">
            <ProFormaRowSingleInputAria
              control={control}
              name="projectedRent"
              label="Est. Monthly Rent"
              value={allValues?.["Projected Monthly Rent"]}
              inputValueType="currency"
              onChange={(value) => {
                console.log('EstMonthlyRentInput - value', value);
                setProFormaInputsLennar({
                  ...proFormaInputsLennar,
                  proFormaValues: {
                    ...proFormaInputsLennar?.proFormaValues,
                    userInputProjectedRent: value,
                  },
                });
              }}
              className="bg-transparent [&_label]:mr-4"
            />
          </div>
          {/* <div className="flex items-center gap-1 text-xs text-[var(--color-dark-gray)]">
            <RefreshCw className="w-3 h-3" />
            <span>Pro Forma</span>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default EstMonthlyRentInput; 