import { getHotPadsCompsData } from '@/lib/query/get-hotpads-comps-data';
import { getHotPadsCompsWithinPolygonData } from '@/lib/query/get-hotpads-comps-within-polygon-data';
import { FeatureCollection } from '@turf/helpers';
import { isEmpty, isEqual } from 'lodash';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { filterDataBySubjectPolygon } from '../utils/filter-geo';
import { filterCompData } from '../utils/filter-process';
import {
  countyPolygon,
  filters,
  sfrAndHotPadsCompData,
  HotPadsListingStatusType,
  schoolDistrictPolygon,
  ZIPCodePolygon,
  MLSListingStatusType,
} from '../utils/types';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext.tsx';
import { useCompsContext } from '@/contexts/CompsContext.tsx';
import { LennarSinglePropertyDataType } from '@/lib/utils/types';
import { getStatusHotPadsFromStatusMLS } from '@/lib/utils/getStatusSFRFromStatusMLS';

export const useHotPadsComps = () => {
  // State
  const [loading, setLoading] = useState(false);
  const [filteredComps, setFilteredComps] = useState<sfrAndHotPadsCompData[]>(
    [],
  );
  const [prevSelectedBuyerViewRecord, setPrevSelectedBuyerViewRecord] = useState<LennarSinglePropertyDataType | null>(null);
  const [prevRadiusMile, setPrevRadiusMile] = useState<number | null>(null);
  const [prevStartMLS, setPrevStartMLS] = useState<string | null>(null);
  const [prevEndMLS, setPrevEndMLS] = useState<string | null>(null);
  const [allCompsDeduped, setAllCompsDeduped] = useState<
    sfrAndHotPadsCompData[]
  >([]);
  const [prevAllCompsDeduped, setPrevAllCompsDeduped] = useState<
    sfrAndHotPadsCompData[]
  >([]);
  const [prevFilters, setPrevFilters] = useState<filters | null>(null);
  const [prevDrawnCustomPolygons, setPrevDrawnCustomPolygons] = useState<any[]>(
    [],
  );
  const [prevSchoolDistrictProperties, setPrevSchoolDistrictProperties] =
    useState<schoolDistrictPolygon[]>([]);
  const [prevCountyData, setPrevCountyData] = useState<countyPolygon[]>([]);
  const [prevZipCodeData, setPrevZipCodeData] = useState<ZIPCodePolygon[]>([]);

  const {
    currentRadiusMile,
    selectedBuyersViewRecord,
    eventCoordinates,
    drawnCustomPolygons,
    selectedRowKeysHotPads,
    searchingMode,
  } = useMarketplaceMapContext();

  const {
    currentStatusMLS,
    currentStartMLS,
    currentEndMLS,
    currentSchoolDistrictProperties,
    currentCountyData,
    currentZipCodeData,
    isSameSchoolDistrict,
    isSameCounty,
    isSameZIPCode,
    minBeds,
    maxBeds,
    minBaths,
    maxBaths,
    minSqft,
    maxSqft,
    minLotSize,
    maxLotSize,
    minYearBuilt,
    maxYearBuilt,
    minCumulativeDaysOnMarket,
    maxCumulativeDaysOnMarket,
    minCoveredParking,
    maxCoveredParking,
    minRent,
    maxRent,
    minSales,
    maxSales,
    isPoolAllowed,
  } = useCompsContext();

  const filters: filters = {
    isSameSchoolDistrict: isSameSchoolDistrict,
    isSameCounty: isSameCounty,
    isSameZIPCode: isSameZIPCode,
    minBeds: minBeds,
    maxBeds: maxBeds,
    minBaths: minBaths,
    maxBaths: maxBaths,
    minSqft: minSqft,
    maxSqft: maxSqft,
    minLotSize: minLotSize,
    maxLotSize: maxLotSize,
    minYearBuilt: minYearBuilt,
    maxYearBuilt: maxYearBuilt,
    minCumulativeDaysOnMarket: minCumulativeDaysOnMarket,
    maxCumulativeDaysOnMarket: maxCumulativeDaysOnMarket,
    minCoveredParking: minCoveredParking,
    maxCoveredParking: maxCoveredParking,
    minRent: minRent,
    maxRent: maxRent,
    minSales: minSales,
    maxSales: maxSales,
    isPoolAllowed: isPoolAllowed,
  };

  useEffect(() => {
    // only fetch comps if the property address, radius mile, start date or end date has changed
    // and only for lease mode since HotPads comps are only available for lease
    const fetchComps = async () => {
      if (
        searchingMode === 'Lease' &&
        (!isEmpty(selectedBuyersViewRecord) &&
          currentRadiusMile &&
          currentStartMLS &&
          currentEndMLS &&
          (!isEqual(selectedBuyersViewRecord, prevSelectedBuyerViewRecord) ||
            currentRadiusMile !== prevRadiusMile ||
            dayjs(currentStartMLS).isSame(dayjs(prevStartMLS)) ||
            dayjs(currentEndMLS).isSame(dayjs(prevEndMLS)))) ||
        (drawnCustomPolygons &&
          Array.isArray(drawnCustomPolygons) &&
          drawnCustomPolygons.length > 0 &&
          !isEqual(drawnCustomPolygons, prevDrawnCustomPolygons))
      ) {
        setLoading(true);
        try {
          let result;
          if (drawnCustomPolygons.length === 0) {
            result = await getHotPadsCompsData({
              exists: getStatusHotPadsFromStatusMLS(currentStatusMLS as MLSListingStatusType) as HotPadsListingStatusType,
              startDate: currentStartMLS,
              endDate: currentEndMLS,
              distance: currentRadiusMile * 1609.344,
              lng: selectedBuyersViewRecord?.payload?.subjectProperty?.lng,
              lat: selectedBuyersViewRecord?.payload?.subjectProperty?.lat,
            });
          } else {
            result = await getHotPadsCompsWithinPolygonData({
              exists: getStatusHotPadsFromStatusMLS(currentStatusMLS as MLSListingStatusType) as HotPadsListingStatusType,
              startDate: currentStartMLS,
              endDate: currentEndMLS,
              body: drawnCustomPolygons,
            });
          }

          // Dedupe results by base_id
          const dedupedResult = (
            result as unknown as sfrAndHotPadsCompData[]
          ).filter(
            (item, index, self) =>
              index === self.findIndex((t) => t.base_id === item.base_id),
          );
          setAllCompsDeduped(dedupedResult);
          setPrevSelectedBuyerViewRecord(selectedBuyersViewRecord);
          setPrevRadiusMile(currentRadiusMile);
          setPrevStartMLS(currentStartMLS);
          setPrevEndMLS(currentEndMLS);
          setPrevDrawnCustomPolygons(drawnCustomPolygons);
        } catch (error) {
          console.error('Error fetching HotPads comps:', error);
          setFilteredComps([]);
        }
        setLoading(false);
      }
    };

    fetchComps();
  }, [
    selectedBuyersViewRecord,
    currentRadiusMile,
    currentStartMLS,
    currentEndMLS,
    drawnCustomPolygons,
    searchingMode,
  ]);

  useEffect(() => {
    if (
      !isEqual(filters, prevFilters) ||
      !isEqual(allCompsDeduped, prevAllCompsDeduped) ||
      !isEqual(currentSchoolDistrictProperties, prevSchoolDistrictProperties) ||
      !isEqual(currentCountyData, prevCountyData) ||
      !isEqual(currentZipCodeData, prevZipCodeData)
    ) {
      // Apply filters
      const filteredData = filterCompData({
        compData: allCompsDeduped,
        compType: 'sfrAndHotPads',
        filters,
        leaseOrSale: searchingMode === 'Lease' ? 'lease' : 'sale',
        startDate: currentStartMLS,
        endDate: currentEndMLS,
      }) as sfrAndHotPadsCompData[];

      // Apply geo filters if needed
      let geoFilteredData = filteredData;
      if (isSameSchoolDistrict && currentSchoolDistrictProperties) {
        geoFilteredData = filterDataBySubjectPolygon({
          subjectCoordinates: eventCoordinates,
          data: filteredData,
          polygons: currentSchoolDistrictProperties,
        }) as sfrAndHotPadsCompData[];
      }
      if (isSameCounty && currentCountyData && geoFilteredData) {
        geoFilteredData = filterDataBySubjectPolygon({
          subjectCoordinates: eventCoordinates,
          data: geoFilteredData,
          polygons: currentCountyData,
        }) as sfrAndHotPadsCompData[];
      }
      if (isSameZIPCode && currentZipCodeData && geoFilteredData) {
        geoFilteredData = filterDataBySubjectPolygon({
          subjectCoordinates: eventCoordinates,
          data: geoFilteredData,
          polygons: currentZipCodeData,
        }) as sfrAndHotPadsCompData[];
      }

      setFilteredComps(geoFilteredData);
      if (!isEqual(filters, prevFilters)) {
        setPrevFilters(filters);
      }
      if (!isEqual(allCompsDeduped, prevAllCompsDeduped)) {
        setPrevAllCompsDeduped(allCompsDeduped);
      }
      if (
        !isEqual(currentSchoolDistrictProperties, prevSchoolDistrictProperties)
      ) {
        setPrevSchoolDistrictProperties(currentSchoolDistrictProperties);
      }
      if (!isEqual(currentCountyData, prevCountyData)) {
        setPrevCountyData(currentCountyData);
      }
      if (!isEqual(currentZipCodeData, prevZipCodeData)) {
        setPrevZipCodeData(currentZipCodeData);
      }
    }
  }, [
    allCompsDeduped,
    filters,
    currentSchoolDistrictProperties,
    currentCountyData,
    currentZipCodeData,
  ]);

  // Create GeoJSON from filtered comps
  const geoJSON: FeatureCollection = {
    type: 'FeatureCollection',
    features: filteredComps
      .filter((comp) => selectedRowKeysHotPads.includes(comp.base_id))
      .map((comp) => ({
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: comp.geom.coordinates,
        },
        properties: {
          ...comp,
        },
      })),
  };

  return {
    loading,
    filteredComps,
    geoJSON,
  };
};
