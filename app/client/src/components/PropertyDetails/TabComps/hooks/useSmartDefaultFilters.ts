import { useEffect, useState } from 'react';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { useCompsContext } from '@/contexts/CompsContext';
import dayjs from 'dayjs';

interface SmartFilterInfo {
  enabled: boolean;
  appliedFilters: Array<{
    name: string;
    value: string;
  }>;
}

export const useSmartDefaultFilters = () => {
  const { selectedBuyersViewRecord } = useMarketplaceMapContext();
  const {
    setCurrentStartMLS,
    setCurrentEndMLS,
    setMinBeds,
    setMaxBeds,
    setMinSqft,
    setMaxSqft,
    setMinYearBuilt,
  } = useCompsContext();

  const [smartFiltersEnabled, setSmartFiltersEnabled] = useState(true);
  const [appliedFilters, setAppliedFilters] = useState<Array<{ name: string; value: string }>>([]);

  const enableSmartFilters = () => {
    setSmartFiltersEnabled(true);
  };

  const disableSmartFilters = () => {
    setSmartFiltersEnabled(false);
    // Clear all smart filter contexts when disabled
    setCurrentStartMLS('');
    setCurrentEndMLS('');
    setMinBeds(0);
    setMaxBeds(999);
    setMinSqft(0);
    setMaxSqft(999999);
    setMinYearBuilt(1800);
    setAppliedFilters([]);
  };

  useEffect(() => {
    if (!smartFiltersEnabled || !selectedBuyersViewRecord?.payload?.subjectProperty) {
      if (!smartFiltersEnabled) {
        setAppliedFilters([]);
      }
      return;
    }

    const subjectProperty = selectedBuyersViewRecord.payload.subjectProperty;
    const filters: Array<{ name: string; value: string }> = [];

    try {
      // 1. Closing date: Within last 180 days from today
      const endDate = dayjs().format('YYYY-MM-DD');
      const startDate = dayjs().subtract(180, 'days').format('YYYY-MM-DD');
      
      setCurrentStartMLS(startDate);
      setCurrentEndMLS(endDate);
      filters.push({
        name: 'Closing Date',
        value: `Within last 180 days (${startDate} to ${endDate})`
      });

      // 2. Bedrooms: Subject property beds ± 1
      const subjectBeds = subjectProperty.beds || 3;
      const minBeds = Math.max(1, subjectBeds - 1);
      const maxBeds = subjectBeds + 1;
      
      setMinBeds(minBeds);
      setMaxBeds(maxBeds);
      filters.push({
        name: 'Bedrooms',
        value: `${minBeds}-${maxBeds} beds (${subjectBeds}±1)`
      });

      // 3. Bathrooms: Any (no restriction) - not added to visible filters since no restriction
      // 4. Lot size: Any (no restriction) - not added to visible filters since no restriction

      // 5. Square footage: Subject property sqft ± 25%
      const subjectSqft = subjectProperty.sqft || 2000;
      const minSqft = Math.round(subjectSqft * 0.75);
      const maxSqft = Math.round(subjectSqft * 1.25);
      
      setMinSqft(minSqft);
      setMaxSqft(maxSqft);
      filters.push({
        name: 'Square Footage',
        value: `${minSqft.toLocaleString()} - ${maxSqft.toLocaleString()} sqft (subject: ${subjectSqft.toLocaleString()})`
      });

      // 6. Year built: 2000 or newer
      setMinYearBuilt(2000);
      filters.push({
        name: 'Year Built',
        value: '2000 or newer'
      });

      // 7. Property type: Exclude Mobile Homes by default
      filters.push({
        name: 'Property Type',
        value: 'Exclude Mobile Homes'
      });

      setAppliedFilters(filters);

    } catch (error) {
      console.error('Error applying smart default filters:', error);
      setAppliedFilters([]);
    }
  }, [smartFiltersEnabled, selectedBuyersViewRecord?.payload?.subjectProperty]);

  const smartFilterInfo: SmartFilterInfo = {
    enabled: smartFiltersEnabled,
    appliedFilters
  };

  return {
    smartFilterInfo,
    enableSmartFilters,
    disableSmartFilters,
  };
}; 