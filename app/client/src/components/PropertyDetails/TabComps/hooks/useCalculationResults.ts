import { useEffect, useState } from 'react';
import { calculateMarketRentAndValue } from '../utils/calculate-market-rent-value';
import { realtorDotComCompData } from '../utils/types';
import { useCompsContext } from '@/contexts/CompsContext';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';

export interface CalculationResults {
  rentAverageMLS: number;
  rentAverageNationalOperators: number;
  rentAverageHotPads: number;
  rentAverageAll: number;
  MLSAverageToAVMRatio: number;
  MLSMedianToAVMRatio: number | null;
  marketRentOrValue: number | string;
  rentAdjustedFormula: string;
  rentAveragePerSqftMLS: number;
  rentAveragePerSqftNationalOperators: number;
  rentAveragePerSqftHotPads: number;
  rentAveragePerSqftRealtorDotCom: number;
  rentAveragePerSqftAll: number;
  rentMedianMLS: number;
  rentMedianSFR: number;
  rentMedianHotPads: number;
  rentMedianRealtorDotCom: number;
  rentPerSqftMedianMLS: number;
  rentPerSqftMedianSFR: number;
  rentPerSqftMedianHotPads: number;
  rentPerSqftMedianRealtorDotCom: number;
}

export const useCalculationResults = (
  filteredComps: realtorDotComCompData[],
  selectedRowKeysRealtorDotCom: string[]
) => {
  const [calculationResults, setCalculationResults] = useState<CalculationResults | null>(null);

  const {
    searchingMode,
    marketRentPreference,
  } = useCompsContext();

  const {
    subjectPropertyParcelData
  } = useMarketplaceMapContext();

  // Calculate market rent/value when filtered comps or selected rows change
  useEffect(() => {
    if (
      filteredComps.length > 0 &&
      selectedRowKeysRealtorDotCom.length > 0 &&
      subjectPropertyParcelData
    ) {
      const results = calculateMarketRentAndValue({
        mlsCompDataFiltered: [],
        sfrCompDataFiltered: [],
        hotPadsCompDataFiltered: [],
        realtorDotComCompDataFiltered: filteredComps,
        subjectPropertyParcelData,
        leaseOrSale: searchingMode === 'Lease' ? 'lease' : 'sale',
        compingMode: 'smartFilter',
        preference: marketRentPreference,
        selectedRowKeysMLSParam: [],
        selectedRowKeysNationalOperatorsParam: [],
        selectedRowKeysHotPadsParam: [],
        selectedRowKeysRealtorDotComParam: selectedRowKeysRealtorDotCom,
      });

      setCalculationResults(results);
    } else {
      setCalculationResults(null);
    }
  }, [
    filteredComps,
    selectedRowKeysRealtorDotCom,
    subjectPropertyParcelData,
    searchingMode,
    marketRentPreference,
  ]);

  return {
    calculationResults,
  };
}; 