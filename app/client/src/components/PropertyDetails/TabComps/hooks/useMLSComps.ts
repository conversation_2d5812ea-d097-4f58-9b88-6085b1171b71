import { getMLSCompsData } from '@/lib/query/get-mls-comps-data';
import { getMLSCompsWithinPolygonData } from '@/lib/query/get-mls-comps-within-polygon-data';
import { FeatureCollection } from '@turf/helpers';
import { isEmpty, isEqual } from 'lodash';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { calculateMarketRentAndValue } from '../utils/calculate-market-rent-value';
import { filterDataBySubjectPolygon } from '../utils/filter-geo';
import { filterCompData } from '../utils/filter-process';
import {
  countyPolygon,
  filters,
  mlsCompData,
  MLSListingStatusType,
  schoolDistrictPolygon,
  ZIPCodePolygon,
} from '../utils/types';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext.tsx';
import { useCompsContext } from '@/contexts/CompsContext.tsx';
import { LennarSinglePropertyDataType } from '@/lib/utils/types';

export const useMLSComps = () => {
  // State
  const [loading, setLoading] = useState(false);
  const [filteredComps, setFilteredComps] = useState<mlsCompData[]>(
    [],
  );
  // const [calculationResults, setCalculationResults] = useState<any>(null);
  const [prevSelectedBuyerViewRecord, setPrevSelectedBuyerViewRecord] = useState<LennarSinglePropertyDataType | null>(null);
  const [prevRadiusMile, setPrevRadiusMile] = useState<number | null>(null);
  const [prevStartMLS, setPrevStartMLS] = useState<string | null>(null);
  const [prevEndMLS, setPrevEndMLS] = useState<string | null>(null);
  const [allCompsDeduped, setAllCompsDeduped] = useState<
    mlsCompData[]
  >([]);
  const [prevAllCompsDeduped, setPrevAllCompsDeduped] = useState<
    mlsCompData[]
  >([]);
  const [prevFilters, setPrevFilters] = useState<filters | null>(null);
  const [prevDrawnCustomPolygons, setPrevDrawnCustomPolygons] = useState<any[]>(
    [],
  );
  const [prevSchoolDistrictProperties, setPrevSchoolDistrictProperties] =
    useState<schoolDistrictPolygon[]>([]);
  const [prevCountyData, setPrevCountyData] = useState<countyPolygon[]>([]);
  const [prevZipCodeData, setPrevZipCodeData] = useState<ZIPCodePolygon[]>([]);

  const {
    currentRadiusMile,
    selectedBuyersViewRecord,
    eventCoordinates,
    drawnCustomPolygons,
    subjectPropertyParcelData,
    selectedRowKeysMLSLease,
    selectedRowKeysMLSSale,
    searchingMode,
  } = useMarketplaceMapContext();

  const {
    currentStatusMLS,
    currentStartMLS,
    currentEndMLS,
    marketRentPreference,
    currentSchoolDistrictProperties,
    currentCountyData,
    currentZipCodeData,
    isSameSchoolDistrict,
    isSameCounty,
    isSameZIPCode,
    minBeds,
    maxBeds,
    minBaths,
    maxBaths,
    minSqft,
    maxSqft,
    minLotSize,
    maxLotSize,
    minYearBuilt,
    maxYearBuilt,
    minCumulativeDaysOnMarket,
    maxCumulativeDaysOnMarket,
    minCoveredParking,
    maxCoveredParking,
    minRent,
    maxRent,
    minSales,
    maxSales,
    isPoolAllowed,
  } = useCompsContext();

  const filters: filters = {
    isSameSchoolDistrict: isSameSchoolDistrict,
    isSameCounty: isSameCounty,
    isSameZIPCode: isSameZIPCode,
    minBeds: minBeds,
    maxBeds: maxBeds,
    minBaths: minBaths,
    maxBaths: maxBaths,
    minSqft: minSqft,
    maxSqft: maxSqft,
    minLotSize: minLotSize,
    maxLotSize: maxLotSize,
    minYearBuilt: minYearBuilt,
    maxYearBuilt: maxYearBuilt,
    minCumulativeDaysOnMarket: minCumulativeDaysOnMarket,
    maxCumulativeDaysOnMarket: maxCumulativeDaysOnMarket,
    minCoveredParking: minCoveredParking,
    maxCoveredParking: maxCoveredParking,
    minRent: minRent,
    maxRent: maxRent,
    minSales: minSales,
    maxSales: maxSales,
    isPoolAllowed: isPoolAllowed,
  };

  useEffect(() => {
    // only fetch comps if the property address, radius mile, start date or end date has changed
    const fetchComps = async () => {
      if (
        (!isEmpty(selectedBuyersViewRecord) &&
          currentRadiusMile &&
          currentStartMLS &&
          currentEndMLS &&
          (!isEqual(selectedBuyersViewRecord, prevSelectedBuyerViewRecord) ||
            currentRadiusMile !== prevRadiusMile ||
            dayjs(currentStartMLS).isSame(dayjs(prevStartMLS)) ||
            dayjs(currentEndMLS).isSame(dayjs(prevEndMLS)))) ||
        (drawnCustomPolygons &&
          Array.isArray(drawnCustomPolygons) &&
          drawnCustomPolygons.length > 0 &&
          !isEqual(drawnCustomPolygons, prevDrawnCustomPolygons))
      ) {
        setLoading(true);
        try {
          let result;
          if (drawnCustomPolygons.length === 0) {
            result = await getMLSCompsData({
              status: currentStatusMLS as MLSListingStatusType,
              propertyType: searchingMode === 'Lease' ? 'Residential Lease' : 'Residential',
              startDate: currentStartMLS,
              endDate: currentEndMLS,
              constructionType: 'ALL',
              lat: selectedBuyersViewRecord?.payload?.subjectProperty?.lat,
              lng: selectedBuyersViewRecord?.payload?.subjectProperty?.lng,
              distance: currentRadiusMile * 1609.344,
            });
          } else {
            result = await getMLSCompsWithinPolygonData({
              body: drawnCustomPolygons,
              startDate: currentStartMLS,
              endDate: currentEndMLS,
              status: currentStatusMLS as MLSListingStatusType,
              propertyType: searchingMode === 'Lease' ? 'Residential Lease' : 'Residential',
              constructionType: 'ALL'
            });
          }

          // Dedupe results by id
          const dedupedResult = (
            result as unknown as mlsCompData[]
          ).filter(
            (item, index, self) =>
              index === self.findIndex((t) => t.mlsid === item.mlsid),
          );
          setAllCompsDeduped(dedupedResult);
          setPrevSelectedBuyerViewRecord(selectedBuyersViewRecord);
          setPrevRadiusMile(currentRadiusMile);
          setPrevStartMLS(currentStartMLS);
          setPrevEndMLS(currentEndMLS);
          setPrevDrawnCustomPolygons(drawnCustomPolygons);
        } catch (error) {
          console.error('Error fetching realtor.com comps:', error);
          setFilteredComps([]);
        }
        setLoading(false);
      }
    };

    fetchComps();
  }, [
    selectedBuyersViewRecord,
    currentRadiusMile,
    currentStartMLS,
    currentEndMLS,
    drawnCustomPolygons,
  ]);

  useEffect(() => {
    if (
      !isEqual(filters, prevFilters) ||
      !isEqual(allCompsDeduped, prevAllCompsDeduped) ||
      !isEqual(currentSchoolDistrictProperties, prevSchoolDistrictProperties) ||
      !isEqual(currentCountyData, prevCountyData) ||
      !isEqual(currentZipCodeData, prevZipCodeData)
    ) {
      // Apply filters
      const filteredData = filterCompData({
        compData: allCompsDeduped,
        compType: 'mls',
        filters,
        leaseOrSale: searchingMode === 'Lease' ? 'lease' : 'sale',
      }) as mlsCompData[];

      // Apply geo filters if needed
      let geoFilteredData = filteredData;
      if (isSameSchoolDistrict && currentSchoolDistrictProperties) {
        geoFilteredData = filterDataBySubjectPolygon({
          subjectCoordinates: eventCoordinates,
          data: filteredData,
          polygons: currentSchoolDistrictProperties,
        }) as mlsCompData[];
      }
      if (isSameCounty && currentCountyData && geoFilteredData) {
        geoFilteredData = filterDataBySubjectPolygon({
          subjectCoordinates: eventCoordinates,
          data: geoFilteredData,
          polygons: currentCountyData,
        }) as mlsCompData[];
      }
      if (isSameZIPCode && currentZipCodeData && geoFilteredData) {
        geoFilteredData = filterDataBySubjectPolygon({
          subjectCoordinates: eventCoordinates,
          data: geoFilteredData,
          polygons: currentZipCodeData,
        }) as mlsCompData[];
      }

      console.log('useMLSComps geoFilteredData', geoFilteredData);

      setFilteredComps(geoFilteredData);
      if (!isEqual(filters, prevFilters)) {
        setPrevFilters(filters);
      }
      if (!isEqual(allCompsDeduped, prevAllCompsDeduped)) {
        setPrevAllCompsDeduped(allCompsDeduped);
      }
      if (
        !isEqual(currentSchoolDistrictProperties, prevSchoolDistrictProperties)
      ) {
        setPrevSchoolDistrictProperties(currentSchoolDistrictProperties);
      }
      if (!isEqual(currentCountyData, prevCountyData)) {
        setPrevCountyData(currentCountyData);
      }
      if (!isEqual(currentZipCodeData, prevZipCodeData)) {
        setPrevZipCodeData(currentZipCodeData);
      }
    }
  }, [
    allCompsDeduped,
    filters,
    currentSchoolDistrictProperties,
    currentCountyData,
    currentZipCodeData,
  ]);

  const getSelectedRowKeys = () => {
    if (searchingMode === 'Lease') {
      return selectedRowKeysMLSLease;
    } else if (searchingMode === 'Sale') {
      return selectedRowKeysMLSSale;
    } else {
      return [];
    }
  };

  console.log('useMLSComps - selectedRowKeysMLSLease', selectedRowKeysMLSLease);
  console.log('useMLSComps - selectedRowKeysMLSSale', selectedRowKeysMLSSale);

  console.log('useMLSComps - getSelectedRowKeys', getSelectedRowKeys());

  // Create GeoJSON from filtered comps
  const geoJSON: FeatureCollection = {
    type: 'FeatureCollection',
    features: filteredComps
      .filter((comp) => getSelectedRowKeys().includes(comp.mlsid))
      .map((comp) => ({
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [Number(comp.longitude), Number(comp.latitude)],
        },
        properties: {
          ...comp,
        },
      })),
  };

  console.log('useMLSComps geoJSON', geoJSON);

  return {
    loading,
    filteredComps,
    // calculationResults,
    geoJSON,
  };
};
