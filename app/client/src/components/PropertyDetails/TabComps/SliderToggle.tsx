import React from 'react';

interface SliderToggleProps {
  label: string;
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  className?: string;
}

const SliderToggle: React.FC<SliderToggleProps> = ({
  label,
  checked = false,
  onChange,
  className = "",
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e.target.checked);
    }
  };

  return (
    <div className={`flex items-center gap-2.5 ${className}`}>
      <span className="text-[13px] text-[var(--color-text-black)]">{label}</span>
      <label className="relative inline-block w-10 h-5 cursor-pointer">
        <input
          type="checkbox"
          checked={checked}
          onChange={handleChange}
          className="sr-only"
        />
        <div
          className={`
            absolute inset-0 rounded-full transition-colors duration-300 ease-in-out
            ${checked ? 'bg-[var(--color-button-blue)]' : 'bg-[var(--color-light-gray)]'}
          `}
        ></div>

        <div
          style={{ transform: checked ? 'translateX(20px)' : 'translateX(2px)' }}
          className={`
            absolute top-[2px] w-4 h-4 bg-white rounded-full shadow-md
            transition-transform duration-300 ease-in-out
          `}
        ></div>
      </label>
    </div>
  );
};

export default SliderToggle;