"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Checkbox } from "@/components/ui/checkbox"
import { formatter } from '@/lib/utils/money'
import { sfrAndHotPadsCompData } from '../utils/types'

export const columns: ColumnDef<sfrAndHotPadsCompData>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={table.getIsAllRowsSelected()}
          onCheckedChange={(value) => table.toggleAllRowsSelected(!!value)}
          aria-label="Select all"
          className="h-4 w-4"
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="h-4 w-4"
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "address",
    header: () => <div className="text-[12px] text-left pl-2">Address</div>,
    cell: ({ row }) => {
      const record = row.original
      return (
        <div className="text-[12px] text-left pl-2">
          {record.address}
        </div>
      )
    },
  },
  {
    accessorKey: "distance",
    header: () => <div className="text-[12px] text-left pl-2">Dist.</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{(record.distance / 1609.34).toFixed(2)} mi</div>
    },
  },
  {
    accessorKey: "status",
    header: () => <div className="text-[12px] text-center">Status</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.status}</div>
    },
  },
  {
    accessorKey: "owner",
    header: () => <div className="text-[12px] text-center">Owner</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.brand || '-'}</div>
    },
  },
  {
    accessorKey: "rent",
    header: () => <div className="text-[12px] text-center">Rent</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{formatter(record.rent)}</div>
    },
  },
  {
    accessorKey: "propertysubtype",
    header: () => <div className="text-[12px] text-center">Type</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.propertysubtype || '-'}</div>
    },
  },
  {
    accessorKey: "yearbuilt",
    header: () => <div className="text-[12px] text-center">Year Built</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.yearbuilt || '-'}</div>
    },
  },
  {
    accessorKey: "bed_rooms",
    header: () => <div className="text-[12px] text-center">Beds</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.bed_rooms}</div>
    },
  },
  {
    accessorKey: "bath_rooms",
    header: () => <div className="text-[12px] text-center">Baths</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.bath_rooms}</div>
    },
  },
  {
    accessorKey: "square_feet",
    header: () => <div className="text-[12px] text-center">Sq Ft</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{formatter(record.square_feet)}</div>
    },
  },
  {
    accessorKey: "cdom",
    header: () => <div className="text-[12px] text-center">CDOM</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.cdom}</div>
    },
  },
  {
    accessorKey: "closing_date",
    header: () => <div className="text-[12px] text-center">Closing</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.close_date || '-'}</div>
    },
  },
  {
    accessorKey: "price_per_sqft",
    header: () => <div className="text-[12px] text-center">$/Sqft</div>,
    cell: ({ row }) => {
      const record = row.original
      const rent = record.rent
      const sqft = record.square_feet
      const pps = sqft > 0 ? rent / sqft : 0
      return <div className="text-[12px] text-center">{pps ? `$${pps.toFixed(2)}` : 'N/A'}</div>
    },
  },
]