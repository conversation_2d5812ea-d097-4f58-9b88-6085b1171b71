import { useSFRComps } from '../hooks/useSFRComps';
import { useHotPadsComps } from '../hooks/useHotPadsComps';
import { formatter } from '@/lib/utils/money';
import { useEffect, useRef, useState } from 'react';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { sfrAndHotPadsCompData } from '../utils/types';
import { columns } from './columns';
import { DataTable } from '../components/data-table';
import { isEqual } from 'lodash';

interface TableSFRAndHotPadsProps {
  compType: 'sfr' | 'hotpads';
}

const SFRAndHotPadsTable: React.FC<TableSFRAndHotPadsProps> = ({ compType }) => {
  const {
    map,
    setCurrentNationalOperatorsGeoJSON,
    setSelectedRowKeysNationalOperators,
    setCurrentHotPadsGeoJSON,
    setSelectedRowKeysHotPads,
  } = useMarketplaceMapContext();

  const SFRContainer = useRef<HTMLDivElement>(null);

  const [sfrAndHotPadsDataForRender, setSFRAndHotPadsDataForRender] = useState<sfrAndHotPadsCompData[]>([]);
  const [prevFilteredComps, setPrevFilteredComps] = useState<sfrAndHotPadsCompData[]>([]);
  const [prevGeoJSON, setPrevGeoJSON] = useState<Record<string, unknown> | null>(null);

  // Use the appropriate hook based on compType
  const sfrComps = useSFRComps();
  const hotPadsComps = useHotPadsComps();
  
  const { loading, filteredComps, geoJSON } = compType === 'sfr' ? sfrComps : hotPadsComps;

  // Use the appropriate context setters based on compType
  const setCurrentGeoJSON = compType === 'sfr' ? setCurrentNationalOperatorsGeoJSON : setCurrentHotPadsGeoJSON;
  const setSelectedRowKeys = compType === 'sfr' ? setSelectedRowKeysNationalOperators : setSelectedRowKeysHotPads;

  useEffect(() => {
    if (!isEqual(prevGeoJSON, geoJSON)) {
      if (geoJSON) {
        setCurrentGeoJSON(geoJSON);
      }
      setPrevGeoJSON(geoJSON);
    }
  }, [geoJSON, setCurrentGeoJSON]);

  useEffect(() => {
    if (!isEqual(prevFilteredComps, filteredComps)) {
      setSFRAndHotPadsDataForRender(filteredComps);
      // select all rows
      setSelectedRowKeys(filteredComps.map(row => row.base_id));
      setPrevFilteredComps(filteredComps);
    }
  }, [filteredComps, setSelectedRowKeys]);

  const handleRowClick = (record: sfrAndHotPadsCompData) => {
    if (map) {
      map.flyTo({
        center: record.geom.coordinates as [number, number],
        zoom: 16,
        speed: 2,
        curve: 1,
        easing: (t: number) => t,
      });
    }
  };

  return (
    <div
      ref={SFRContainer}
      key={`${compType} Table`}
      className="my-8 bg-white"
    >
      {/* add table title */}
      <div className="pl-1.5 pb-2 font-bold text-left">
        {compType === 'sfr' ? 'National SFR Operators Listings' : 'Portal Listings'}
      </div>
      <DataTable
        rowKey="base_id"
        columns={columns}
        data={sfrAndHotPadsDataForRender as sfrAndHotPadsCompData[]}
        loading={loading}
        setSelectedRowKeys={setSelectedRowKeys}
        onRowClick={handleRowClick}
      />
      
      {/* Summary section */}
      {/* <div className="px-4 py-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">
            Median Rent of Selected
          </span>
          <span className="text-sm font-semibold text-gray-900">
            {sfrAndHotPadsDataForRender.length > 0 
              ? formatter(calculateMedianRent(sfrAndHotPadsDataForRender))
              : '-'
            }
          </span>
        </div>
      </div> */}
    </div>
  );
};

// Helper function to calculate median rent
const calculateMedianRent = (data: sfrAndHotPadsCompData[]): number => {
  if (data.length === 0) return 0;
  
  const rents = data.map(item => item.rent).sort((a, b) => a - b);
  const mid = Math.floor(rents.length / 2);
  
  if (rents.length % 2 === 0) {
    return (rents[mid - 1] + rents[mid]) / 2;
  } else {
    return rents[mid];
  }
};

export default SFRAndHotPadsTable;
