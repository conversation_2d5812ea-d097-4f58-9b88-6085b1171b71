import React, { ChangeEvent } from 'react';
import { CompListingProps } from '../../../types/PropertyDetailPage.types';
import { formatNumber } from '../../../lib/utils/formatUtils';
import CustomCheckbox from '../../Common/CustomCheckbox';

interface CompTableProps {
  listings: CompListingProps[];
  selectedListings: string[];
  onListingSelect: (address: string, isSelected: boolean) => void;
  formatPrice: (price: number | string | null | undefined) => string;
  styleClass?: string;
}

const CompTable: React.FC<CompTableProps> = ({
  listings,
  selectedListings,
  onListingSelect,
  formatPrice,
  styleClass = ''
}) => {
  const handleCheckboxChange = (event: ChangeEvent<HTMLInputElement>, address: string) => {
    onListingSelect(address, event.target.checked);
  };

  // Define the column layout for consistent grid - using fixed fractions without gap
  const columnLayout = "grid grid-cols-[25px_3fr_1fr_1fr_1fr_1fr_1fr_1fr_2fr] items-center";

  return (
    <div className={`w-full ${styleClass}`}>
      {/* Table Header */}
      <div className={`${columnLayout} border-b border-[var(--color-text-black)] py-2.5`}>
        <div className=""></div>
        <div className="text-[12px] text-left pl-2">Address</div>
        <div className="text-[12px] text-center">Dist.</div>
        <div className="text-[12px] text-center">Status</div>
        <div className="text-[12px] text-center">Rent</div>
        <div className="text-[12px] text-center">Built</div>
        <div className="text-[12px] text-center">Bd/Ba</div>
        <div className="text-[12px] text-center">Sq Ft</div>
        <div className="text-[12px] text-center">Closed</div>
      </div>

      {/* Table Rows */}
      {listings.map((listing, index) => (
        <div
          key={index}
          className={`${columnLayout} py-2.5 ${selectedListings.includes(listing.address) ? "bg-[var(--color-lennar-blue-light)]" : ''} hover:bg-gray-50`}
        >
          <div className="flex items-center justify-center">
            <CustomCheckbox
              checked={selectedListings.includes(listing.address)}
              onChange={(e) => handleCheckboxChange(e, listing.address)}
            />
          </div>
          <div className="text-[12px] text-left pl-2">{listing.address}</div>
          <div className="text-[12px] text-center">{listing.distance}</div>
          <div className="text-[12px] text-center">{listing.status}</div>
          <div className="text-[12px] text-center">{formatPrice(listing.rent)}</div>
          <div className="text-[12px] text-center">{listing.built}</div>
          <div className="text-[12px] text-center">{listing.bedsBaths}</div>
          <div className="text-[12px] text-center">{formatNumber(listing.sqFt)}</div>
          <div className="text-[12px] text-center whitespace-nowrap">{listing.closedDate}</div>
        </div>
      ))}
    </div>
  );
};

export default CompTable;