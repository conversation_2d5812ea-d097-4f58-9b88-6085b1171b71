import React, { ChangeEvent } from 'react';
import TableTitle from './TableTitle';
import TableSummary from './TableSummary';

interface TableHeaderProps {
  title: string;
  isAllSelected: boolean;
  isIndeterminate: boolean;
  onSelectionChange: (event: ChangeEvent<HTMLInputElement>) => void;
  medianRent: string | number;
  totalSelected: number;
  showSummary: boolean;
  formatter: (value: number | string | null | undefined) => string;
  className?: string;
}

const TableHeader: React.FC<TableHeaderProps> = ({
  title,
  isAllSelected,
  isIndeterminate,
  onSelectionChange,
  medianRent,
  totalSelected,
  showSummary,
  formatter,
  className = ''
}) => {
  return (
    <div className={`flex items-center justify-between mb-0.5 ${className}`}>
      <TableTitle
        title={title}
        isAllSelected={isAllSelected}
        isIndeterminate={isIndeterminate}
        onSelectionChange={onSelectionChange}
      />
      <TableSummary
        medianRent={medianRent}
        totalSelected={totalSelected}
        showSummary={showSummary}
        formatter={formatter}
      />
    </div>
  );
};

export default TableHeader;