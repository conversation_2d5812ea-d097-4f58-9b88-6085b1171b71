"use client"

import { useEffect, useState, useMemo, useRef } from "react"
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  SortingState,
  ColumnFiltersState,
  VisibilityState,
  RowSelectionState,
  OnChangeFn,
  Column,
  filterFns,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableFooter,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Filter, X, Zap, Settings } from "lucide-react"
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"

import { DataTablePagination } from "./data-table-pagination"
import FilterDropdown, { FilterDropdownRef } from "./FilterDropdown"
import { isEqual } from "lodash"
import { formatter } from '@/lib/utils/money'
import EstMonthlyRentInput from "../EstMonthlyRentInput"
import { useBreakpoint } from "@/hooks/useBreakpoint"

// Type helpers for accessing rent/price data from different data types
type RecordWithId = Record<string, any>

// Helper function to get unified price/rent value from different data types
const getUnifiedPrice = (record: RecordWithId): number => {
  // Handle MLS data
  if ('currentprice' in record && 'status' in record && 'closeprice' in record) {
    return record.status === 'Closed' ? record.closeprice : record.currentprice;
  }
  // Handle SFR/HotPads data
  else if ('rent' in record && typeof record.rent === 'number') {
    return record.rent;
  }
  // Handle Realtor.com data
  else if ('currentprice' in record) {
    return Number(record.currentprice) || 0;
  }
  return 0;
};

// Function to calculate median from array of numbers
const calculateMedian = (values: number[]): number => {
  if (values.length === 0) return 0;

  const sortedValues = [...values].sort((a, b) => a - b);
  const mid = Math.floor(sortedValues.length / 2);

  if (sortedValues.length % 2 === 0) {
    return (sortedValues[mid - 1] + sortedValues[mid]) / 2;
  } else {
    return sortedValues[mid];
  }
};






interface SmartFilterInfo {
  enabled: boolean;
  appliedFilters: Array<{
    name: string;
    value: string;
  }>;
}

interface DataTableProps<TData, TValue> {
  rowKey: string
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  loading?: boolean
  selectedRowKeys?: string[]
  setSelectedRowKeys?: (selectedRowKeys: string[]) => void
  onRowClick?: (row: TData) => void
  onRowMouseEnter?: (row: TData) => void
  onRowMouseLeave?: () => void
  smartFilterInfo?: SmartFilterInfo
  onToggleFilterMode?: () => void
}

export function DataTable<TData, TValue>({
  rowKey,
  columns,
  data,
  loading = false,
  selectedRowKeys,
  setSelectedRowKeys,
  onRowClick,
  onRowMouseEnter,
  onRowMouseLeave,
  smartFilterInfo,
  onToggleFilterMode,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({})
  const [prevRowSelection, setPrevRowSelection] = useState<RowSelectionState>({})
  const [prevData, setPrevData] = useState<TData[]>([])
  const [showFilterDropdown, setShowFilterDropdown] = useState(false)
  const filterDropdownRef = useRef<FilterDropdownRef>(null)
  const { isMobile } = useBreakpoint();

  // Add TanStack filter functions based on column types
  const enhancedColumns = useMemo(() => {
    return columns.map((column) => {
      // Get column identifier safely
      const columnId = ('accessorKey' in column ? column.accessorKey as string : column.id) || ''

      // Skip select/checkbox columns
      if (columnId === 'select' || columnId === 'actions') {
        return {
          ...column,
          enableColumnFilter: false,
        }
      }

      // Debug: Check if column has custom filterFn
      const hasCustomFilter = !!column.filterFn;
      console.log(`Column ${columnId}: hasCustomFilter=${hasCustomFilter}`);

      // Don't override if column already has a custom filterFn
      if (column.filterFn) {
        console.log(`Using custom filterFn for ${columnId}`);
        return {
          ...column,
          enableColumnFilter: true,
        }
      }

      // Use TanStack's built-in filter functions only if no custom filterFn exists
      if (columnId.includes('price') || columnId.includes('rent') ||
        columnId.includes('bed') || columnId.includes('bath') ||
        columnId.includes('sqft') || columnId.includes('year') ||
        columnId.includes('size') || columnId.includes('distance') ||
        columnId.includes('cdom')) {
        console.log(`Using inNumberRange for ${columnId}`);
        return {
          ...column,
          enableColumnFilter: true,
          filterFn: filterFns.inNumberRange,
        }
      } else if (columnId.includes('status') || columnId.includes('brand') ||
        columnId.includes('source') || columnId.includes('type')) {
        console.log(`Using equals for ${columnId}`);
        return {
          ...column,
          enableColumnFilter: true,
          filterFn: filterFns.equals,
        }
      } else {
        console.log(`Using includesString for ${columnId}`);
        return {
          ...column,
          enableColumnFilter: true,
          filterFn: filterFns.includesString,
        }
      }
    })
  }, [columns])

  // Get filter types and options for each column
  const getColumnFilterInfo = (columnId: string) => {
    let filterType: 'text' | 'number' | 'select' | 'date' = 'text'
    let selectOptions: string[] = []

    if (columnId.includes('price') || columnId.includes('rent') ||
      columnId.includes('bed') || columnId.includes('bath') ||
      columnId.includes('sqft') || columnId.includes('year') ||
      columnId.includes('size') || columnId.includes('distance') ||
      columnId.includes('cdom')) {
      filterType = 'number'
    } else if (columnId.includes('date') || columnId === 'closing_date' || columnId === 'close_date') {
      filterType = 'date'
    } else if (columnId.includes('status') || columnId.includes('brand') ||
      columnId.includes('source') || columnId.includes('type')) {
      filterType = 'select'

      // Special handling for brand/source column with institutional grouping
      if (columnId === 'brand') {
        const uniqueValues = Array.from(new Set(
          data.map((row) => {
            const record = row as RecordWithId
            // Use the same logic as getBrandOrSource function
            if ('brand' in record && record.brand && record.brand !== 'Hotpads') {
              // Everything with a brand (except Hotpads) that's not MLS should be "Institutional Groups"
              return 'Institutional Groups';
            } else if ('mlsid' in record) {
              return 'MLS';
            }
            return '3rd Party';
          }).filter(Boolean)
        )) as string[]

        // Ensure MLS is always included in options
        if (!uniqueValues.includes('MLS')) {
          uniqueValues.push('MLS');
        }

        // Sort with MLS first, then institutional groups, then others
        const sortedValues = [...uniqueValues].sort((a, b) => {
          if (a === 'MLS') return -1;
          if (b === 'MLS') return 1;
          if (a === 'Institutional Groups') return -1;
          if (b === 'Institutional Groups') return 1;
          return a.localeCompare(b);
        });

        selectOptions = sortedValues.slice(0, 15) // Increase limit for sources
      } else if (columnId.includes('type') || columnId === 'propertysubtype' || columnId === 'propertytype') {
        // Extract property types and exclude Mobile Homes by default
        const uniqueValues = Array.from(new Set(
          data.map((row) => {
            const record = row as RecordWithId
            let propertyType = '';
            if ('propertysubtype' in record && record.propertysubtype) {
              propertyType = record.propertysubtype;
            } else if ('propertytype' in record && record.propertytype) {
              propertyType = record.propertytype;
            }
            return propertyType;
          }).filter(Boolean)
        )) as string[]
        selectOptions = uniqueValues.slice(0, 10) // Limit options
      } else {
        // Extract unique values for other select columns
        const uniqueValues = Array.from(new Set(
          data.map((row) => {
            const record = row as RecordWithId
            return record[columnId]
          }).filter(Boolean)
        )) as string[]
        selectOptions = uniqueValues.slice(0, 10) // Limit options
      }
    }

    return { filterType, selectOptions }
  }

  const table = useReactTable({
    data,
    columns: enhancedColumns,
    getRowId: (row) => (row as RecordWithId)[rowKey],
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    enableColumnFilters: true,
    enableFilters: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  })

  // Calculate median rent for selected properties from filtered data
  const medianRent = useMemo(() => {
    // Get currently visible/filtered data
    const visibleData = table.getFilteredRowModel().rows.map(row => row.original);
    // Debug: console.log(`Filtering: ${data.length} -> ${visibleData.length}, Filters:`, columnFilters.length);

    // Find selected rows from the visible data
    const selectedData = visibleData.filter((row) => {
      const id = (row as RecordWithId)[rowKey];
      return rowSelection[id];
    });

    if (selectedData.length === 0) return 0;

    const rentValues = selectedData
      .map(row => getUnifiedPrice(row as RecordWithId))
      .filter(value => value > 0); // Filter out invalid/zero values

    return calculateMedian(rentValues);
  }, [table, rowSelection, rowKey, data.length, columnFilters]);

  // Count selected rows from currently visible data
  const selectedCount = useMemo(() => {
    const visibleData = table.getFilteredRowModel().rows.map(row => row.original);
    return visibleData.filter((row) => {
      const id = (row as RecordWithId)[rowKey];
      return rowSelection[id];
    }).length;
  }, [table, rowSelection, rowKey]);

  // select all rows by default when data changes
  useEffect(() => {
    if (!isEqual(prevData, data)) {
      const rowSelection = data.reduce((acc, row) => {
        const id = (row as RecordWithId)[rowKey];
        acc[id] = true
        return acc
      }, {} as RowSelectionState)
      setRowSelection(rowSelection);
      setPrevData(data);
    }
  }, [data, rowKey])

  // Notify parent of row selection changes
  useEffect(() => {
    if (!isEqual(prevRowSelection, rowSelection)) {
      const selectedRowKeys = Object.keys(rowSelection).filter(key => rowSelection[key])
      setSelectedRowKeys?.(selectedRowKeys as string[])
      setPrevRowSelection(rowSelection)
    }
  }, [rowSelection, setSelectedRowKeys])

  // Debug: Monitor columnFilters changes
  // useEffect(() => {
  //   console.log('Column filters changed:', columnFilters);
  // }, [columnFilters])

  return (
    <div className="space-y-4">
      {/* Filter Dropdown */}
      <div className="flex items-center justify-between">
        <div className={`flex ${isMobile ? 'flex-col mr-2' : 'flex-row'} gap-2`}>
          <FilterDropdown
            ref={filterDropdownRef}
            isOpen={showFilterDropdown}
            onOpenChange={setShowFilterDropdown}
            columns={table.getAllColumns()}
            data={data}
            getColumnFilterInfo={getColumnFilterInfo}
            onClearAllFilters={() => {
              setColumnFilters([])
              table.resetColumnFilters()
              filterDropdownRef.current?.clearFilterRules()
            }}
            activeFiltersCount={columnFilters.length}
            columnFilters={columnFilters}
            onColumnFiltersChange={setColumnFilters}
            smartFilterInfo={smartFilterInfo}
            onToggleFilterMode={onToggleFilterMode}
            trigger={
              <Button
                variant="outline"
                size="sm"
                className="gap-2"
              >
                <Filter className="h-4 w-4" />
                Filters
                {smartFilterInfo?.enabled && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Zap className="h-3 w-3 cursor-pointer" style={{ color: '#406855' }} />
                    </TooltipTrigger>
                    <TooltipContent className="w-[300px] max-h-[50vh] overflow-y-auto overflow-x-auto bg-white text-dark-gray rounded-md py-3 px-4 z-300 border border-medium-gray-20 text-xs">
                      Smart Filters automatically set the comps to display comps that are within 1 bedroom, 25% of the home square footage, built since 2010, have a reported closed rent within 6 months, and exclude Mobile Homes.
                    </TooltipContent>
                  </Tooltip>
                )}
                {!smartFilterInfo?.enabled && (
                  <Settings className="h-3 w-3 text-orange-600" />
                )}
                {columnFilters.length > 0 && (
                  <span
                    className="text-xs px-1.5 py-0.5 rounded-full ml-1"
                    style={{
                      backgroundColor: '#f0f7f4',
                      color: '#406855'
                    }}
                  >
                    {columnFilters.length}
                  </span>
                )}
              </Button>
            }
          />
          {columnFilters.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setColumnFilters([])
                table.resetColumnFilters()
                filterDropdownRef.current?.clearFilterRules()
              }}
              className="gap-2"
            >
              <X className="h-4 w-4" />
              Clear All Filters ({columnFilters.length})
            </Button>
          )}
        </div>
        <EstMonthlyRentInput />
      </div>

      <div className="relative overflow-auto max-h-[70vh] border rounded-md" style={{
        scrollbarWidth: 'thin',
        scrollbarColor: '#cbd5e1 #f1f5f9'
      }}>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="p-2">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center text-dark-gray">
                  Loading...
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  onClick={() => onRowClick?.(row.original)}
                  onMouseEnter={() => onRowMouseEnter?.(row.original)}
                  onMouseLeave={onRowMouseLeave}
                  className="cursor-pointer hover:bg-muted/50 text-dark-gray"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center text-dark-gray">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
          {selectedCount > 0 && (
            <TableFooter>
              <TableRow>
                {table.getHeaderGroups()[0]?.headers.map((header, index) => {
                  const columnId = header.column.id;
                  const isPriceColumn = columnId === 'price' || columnId === 'rent' || columnId === 'currentprice';
                  const isAddressColumn = columnId === 'address' || columnId === 'fulladdress' || index === 1;

                  return (
                    <TableCell key={header.id} className="text-center p-2 text-dark-gray">
                      {index === 0 ? (
                        <span className="text-xs text-muted-foreground">
                          {selectedCount} selected
                        </span>
                      ) : isAddressColumn ? (
                        <span className="text-sm font-medium">
                          Median Rent:
                        </span>
                      ) : isPriceColumn ? (
                        <span className="text-sm font-semibold">
                          {medianRent > 0 ? `$${formatter(medianRent)}` : 'N/A'}
                        </span>
                      ) : null}
                    </TableCell>
                  );
                })}
              </TableRow>
            </TableFooter>
          )}
        </Table>
      </div>

      <DataTablePagination table={table} />
    </div>
  )
}

