import React, { useState, useImperative<PERSON>andle, forwardRef, useEffect, useRef } from 'react';
import { X, Plus, Trash2, RotateCcw } from 'lucide-react';
import { Column, ColumnFiltersState } from "@tanstack/react-table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface FilterRule {
  id: string;
  columnId: string;
  operator: string;
  value: string | [number | undefined, number | undefined] | [string | undefined, string | undefined];
}

interface SmartFilterInfo {
  enabled: boolean;
  appliedFilters: Array<{
    name: string;
    value: string;
  }>;
}

interface FilterDropdownProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  columns: any[];
  data: any[];
  getColumnFilterInfo: (columnId: string) => { filterType: 'text' | 'number' | 'select' | 'date', selectOptions: string[] };
  onClearAllFilters: () => void;
  activeFiltersCount: number;
  trigger: React.ReactNode;
  columnFilters: ColumnFiltersState;
  onColumnFiltersChange: (filters: ColumnFiltersState) => void;
  smartFilterInfo?: SmartFilterInfo;
  onToggleFilterMode?: () => void;
}

export interface FilterDropdownRef {
  clearFilterRules: () => void;
}

// Type guard for operator objects
interface OperatorObject {
  operator: string;
  value: any;
}

const isOperatorObject = (value: any): value is OperatorObject => {
  return typeof value === 'object' && value !== null && 'operator' in value && 'value' in value;
};

const getOperatorsByType = (filterType: 'text' | 'number' | 'select' | 'date') => {
  switch (filterType) {
    case 'text':
      return [
        { value: 'contains', label: 'contains' },
        { value: 'equals', label: 'equals' },
        { value: 'startsWith', label: 'starts with' },
        { value: 'endsWith', label: 'ends with' },
      ];
    case 'number':
      return [
        { value: 'equals', label: 'equals' },
        { value: 'greaterThanOrEqual', label: 'greater than or equal (≥)' },
        { value: 'lessThanOrEqual', label: 'less than or equal (≤)' },
        { value: 'between', label: 'between' },
      ];
    case 'select':
      return [
        { value: 'equals', label: 'equals' },
        { value: 'notEquals', label: 'not equals' },
      ];
    case 'date':
      return [
        { value: 'before', label: 'before' },
        { value: 'after', label: 'after' },
        { value: 'between', label: 'between' },
        { value: 'withinLastDays', label: 'within last X days' },
      ];
    default:
      return [];
  }
};

const scrollToTop = () => {
  setTimeout(() => {
    window.scrollTo(0, 0);
  }, 150);
};

const FilterDropdown = forwardRef<FilterDropdownRef, FilterDropdownProps>(({
  isOpen,
  onOpenChange,
  columns,
  data,
  getColumnFilterInfo,
  onClearAllFilters,
  activeFiltersCount,
  trigger,
  columnFilters,
  onColumnFiltersChange,
  smartFilterInfo,
  onToggleFilterMode
}, ref) => {
  const [filterRules, setFilterRules] = useState<FilterRule[]>([]);
  const { isMobile } = useBreakpoint();
  const prevIsOpenRef = useRef(isOpen);

  useEffect(() => {
    // Only execute scrollToTop when dropdown changes from open to closed
    if (prevIsOpenRef.current && !isOpen && isMobile) {
      scrollToTop();
    }
    prevIsOpenRef.current = isOpen;
  }, [isOpen, isMobile]);


  // Convert columnFilters to filterRules format
  const convertColumnFiltersToRules = (filters: ColumnFiltersState): FilterRule[] => {
    return filters.map((filter, index) => {
      const { filterType } = getColumnFilterInfo(filter.id);
      let operator = '';
      let value: string | [number | undefined, number | undefined] | [string | undefined, string | undefined] = '';

      // Determine operator and value based on filter value type and content
      if (Array.isArray(filter.value)) {
        const [min, max] = filter.value;
        if (min !== undefined && max !== undefined) {
          operator = 'between';
          value = filterType === 'date' ? [String(min), String(max)] : [min, max];
        } else if (min !== undefined) {
          operator = 'greaterThanOrEqual';
          value = min.toString();
        } else if (max !== undefined) {
          operator = 'lessThanOrEqual';
          value = max.toString();
        }
      } else {
        if (filterType === 'number') {
          operator = 'equals';
          value = String(filter.value);
        } else if (filterType === 'select') {
          // Handle notEquals operator object
          if (isOperatorObject(filter.value) && filter.value.operator === 'notEquals') {
            operator = 'notEquals';
            value = filter.value.value;
          } else {
            operator = 'equals';
            value = String(filter.value);
          }
        } else if (filterType === 'date') {
          operator = 'equals';
          value = String(filter.value);
        } else {
          // Default to contains for text fields
          operator = 'contains';
          value = String(filter.value);
        }
      }

      return {
        id: `rule_${index}_${Date.now()}`,
        columnId: filter.id,
        operator,
        value,
      };
    });
  };

  // Populate filterRules when dropdown opens and has existing filters
  useEffect(() => {
    if (isOpen) {
      if (columnFilters.length > 0 && filterRules.length === 0) {
        const rules = convertColumnFiltersToRules(columnFilters);
        setFilterRules(rules);
      }
    } else {
      // Clear filterRules when dropdown closes to ensure fresh state on next open
      setFilterRules([]);
    }
  }, [isOpen, columnFilters.length]);

  // Expose clearFilterRules function via ref
  useImperativeHandle(ref, () => ({
    clearFilterRules: () => {
      setFilterRules([]);
    }
  }));

  // Get filterable columns - temporarily show all columns for debugging
  const filterableColumns = columns.filter(column => {
    const columnId = column.id;
    // Skip checkbox and action columns
    if (columnId === 'select' || columnId === 'actions') {
      return false;
    }

    // Debug column info if needed
    // console.log(`Column ${columnId}:`, column.columnDef);

    // Temporarily show all non-select columns for debugging
    return true;
  });

  // Debug: console.log('Filterable columns:', filterableColumns.length);

  const addFilterRule = () => {
    const newRule: FilterRule = {
      id: Math.random().toString(36).substr(2, 9),
      columnId: '',
      operator: '',
      value: '',
    };
    setFilterRules([...filterRules, newRule]);
  };

  const removeFilterRule = (ruleId: string) => {
    const rule = filterRules.find(r => r.id === ruleId);
    if (rule && rule.columnId) {
      // Remove the filter from columnFilters state
      const newFilters = columnFilters.filter(filter => filter.id !== rule.columnId);
      onColumnFiltersChange(newFilters);
    }
    setFilterRules(filterRules.filter(rule => rule.id !== ruleId));
  };

  const updateFilterRule = (ruleId: string, field: keyof FilterRule, value: any) => {
    const updatedRules = filterRules.map(rule =>
      rule.id === ruleId ? { ...rule, [field]: value } : rule
    );
    setFilterRules(updatedRules);
  };

  const applyFilter = (rule: FilterRule) => {
    if (!rule.columnId || !rule.operator) return;

    const { filterType } = getColumnFilterInfo(rule.columnId);
    let filterValue: any;

    console.log(`Applying filter: ${rule.columnId} ${rule.operator} ${rule.value} (type: ${filterType})`);

    switch (rule.operator) {
      case 'contains':
      case 'startsWith':
      case 'endsWith':
        filterValue = rule.value as string;
        break;
      case 'equals':
        if (filterType === 'number') {
          const numValue = Number(rule.value);
          // Don't apply filter if value is NaN or empty string
          if (isNaN(numValue) || rule.value === '') {
            return;
          }
          filterValue = numValue;
        } else {
          filterValue = rule.value as string;
        }
        break;
      case 'notEquals':
        // Handle not equals for select fields - fixed logic
        if (filterType === 'select') {
          filterValue = { operator: 'notEquals', value: rule.value as string };
        } else {
          filterValue = rule.value as string;
        }
        break;
      case 'greaterThanOrEqual':
        const gteValue = Number(rule.value);
        if (isNaN(gteValue) || rule.value === '') {
          return;
        }
        filterValue = [gteValue, undefined];
        break;
      case 'lessThanOrEqual':
        const lteValue = Number(rule.value);
        if (isNaN(lteValue) || rule.value === '') {
          return;
        }
        filterValue = [undefined, lteValue];
        break;
      case 'between':
        filterValue = rule.value as [number, number];
        break;
      case 'before':
      case 'after':
        // Date operators
        filterValue = { operator: rule.operator, value: rule.value as string };
        break;
      case 'withinLastDays':
        const days = Number(rule.value);
        if (isNaN(days) || rule.value === '') {
          return;
        }
        filterValue = { operator: 'withinLastDays', value: days };
        break;
      default:
        filterValue = rule.value;
    }

    // Update columnFilters state properly
    const newFilters = columnFilters.filter(filter => filter.id !== rule.columnId);
    if (filterValue !== undefined && filterValue !== '' && filterValue !== null) {
      newFilters.push({ id: rule.columnId, value: filterValue });
    }

    onColumnFiltersChange(newFilters);
  };

  const renderValueInput = (rule: FilterRule) => {
    if (!rule.columnId) return null;

    const { filterType, selectOptions } = getColumnFilterInfo(rule.columnId);

    if (rule.operator === 'between') {
      if (filterType === 'number') {
        const values = (rule.value as [number | undefined, number | undefined]) || [undefined, undefined];
        return (
          <div className="flex space-x-2">
            <Input
              type="number"
              placeholder="Min"
              value={values[0] ?? ""}
              onChange={(e) => {
                const newValue = [e.target.value ? Number(e.target.value) : undefined, values[1]] as [number | undefined, number | undefined];
                updateFilterRule(rule.id, 'value', newValue);
              }}
              onBlur={(e) => {
                const value = e.target.value;
                const newValue = [value ? Number(value) : undefined, values[1]] as [number | undefined, number | undefined];
                if (value.trim() !== '' && !isNaN(Number(value))) {
                  applyFilter({ ...rule, value: newValue });
                }
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  const value = (e.target as HTMLInputElement).value;
                  const newValue = [value ? Number(value) : undefined, values[1]] as [number | undefined, number | undefined];
                  if (value.trim() !== '' && !isNaN(Number(value))) {
                    applyFilter({ ...rule, value: newValue });
                  }
                }
              }}
              className="h-8 w-24"
            />
            <Input
              type="number"
              placeholder="Max"
              value={values[1] ?? ""}
              onChange={(e) => {
                const newValue = [values[0], e.target.value ? Number(e.target.value) : undefined] as [number | undefined, number | undefined];
                updateFilterRule(rule.id, 'value', newValue);
              }}
              onBlur={(e) => {
                const value = e.target.value;
                const newValue = [values[0], value ? Number(value) : undefined] as [number | undefined, number | undefined];
                if (value.trim() !== '' && !isNaN(Number(value))) {
                  applyFilter({ ...rule, value: newValue });
                }
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  const value = (e.target as HTMLInputElement).value;
                  const newValue = [values[0], value ? Number(value) : undefined] as [number | undefined, number | undefined];
                  if (value.trim() !== '' && !isNaN(Number(value))) {
                    applyFilter({ ...rule, value: newValue });
                  }
                }
              }}
              className="h-8 w-24"
            />
          </div>
        );
      } else if (filterType === 'date') {
        const values = (rule.value as [string | undefined, string | undefined]) || [undefined, undefined];
        return (
          <div className="flex space-x-2">
            <Input
              type="date"
              placeholder="Start Date"
              value={values[0] ?? ""}
              onChange={(e) => {
                const newValue = [e.target.value || undefined, values[1]] as [string | undefined, string | undefined];
                updateFilterRule(rule.id, 'value', newValue);
                if (e.target.value.trim() !== '') {
                  applyFilter({ ...rule, value: newValue });
                }
              }}
              className="h-8 w-32"
            />
            <Input
              type="date"
              placeholder="End Date"
              value={values[1] ?? ""}
              onChange={(e) => {
                const newValue = [values[0], e.target.value || undefined] as [string | undefined, string | undefined];
                updateFilterRule(rule.id, 'value', newValue);
                if (e.target.value.trim() !== '') {
                  applyFilter({ ...rule, value: newValue });
                }
              }}
              className="h-8 w-32"
            />
          </div>
        );
      }
    }

    if (rule.operator === 'withinLastDays') {
      return (
        <div className="flex space-x-2 items-center">
          <Input
            type="number"
            placeholder="Number of days"
            value={rule.value as string || ""}
            onChange={(e) => {
              const value = e.target.value;
              updateFilterRule(rule.id, 'value', value);
            }}
            onBlur={(e) => {
              const value = e.target.value;
              if (value.trim() !== '' && !isNaN(Number(value))) {
                applyFilter({ ...rule, value });
              }
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                const value = (e.target as HTMLInputElement).value;
                if (value.trim() !== '' && !isNaN(Number(value))) {
                  applyFilter({ ...rule, value });
                }
              }
            }}
            className="h-8 w-24"
          />
          <span className="text-xs text-gray-500">days ago</span>
        </div>
      );
    }

    if (filterType === 'select') {
      return (
        <Select
          value={rule.value as string || ""}
          onValueChange={(value) => {
            updateFilterRule(rule.id, 'value', value);
            applyFilter({ ...rule, value });
          }}
        >
          <SelectTrigger className="h-8 w-full">
            <SelectValue placeholder="Select value" />
          </SelectTrigger>
          <SelectContent>
            {selectOptions.map((option) => (
              <SelectItem key={option} value={option}>
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    }

    if (filterType === 'date') {
      return (
        <Input
          type="date"
          placeholder="Select date"
          value={rule.value as string || ""}
          onChange={(e) => {
            const value = e.target.value;
            updateFilterRule(rule.id, 'value', value);
            if (value.trim() !== '') {
              applyFilter({ ...rule, value });
            }
          }}
          className="h-8 w-full"
        />
      );
    }

    if (filterType === 'number') {
      return (
        <Input
          type="number"
          placeholder="Enter value"
          value={rule.value as string || ""}
          onChange={(e) => {
            const value = e.target.value;
            updateFilterRule(rule.id, 'value', value);
          }}
          onBlur={(e) => {
            const value = e.target.value;
            if (value.trim() !== '') {
              applyFilter({ ...rule, value });
            }
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              const value = (e.target as HTMLInputElement).value;
              if (value.trim() !== '') {
                applyFilter({ ...rule, value });
              }
            }
          }}
          className="h-8 w-full"
        />
      );
    }

    // Text input
    return (
      <Input
        placeholder="Enter value"
        value={rule.value as string || ""}
        onChange={(e) => {
          updateFilterRule(rule.id, 'value', e.target.value);
          applyFilter({ ...rule, value: e.target.value });
        }}
        className="h-8 w-full"
      />
    );
  };

  const getColumnDisplayName = (columnId: string) => {
    // Map column IDs to their exact table header display names
    const displayNameMap: Record<string, string> = {
      'address': 'Address',
      'distance': 'Distance',
      'status': 'Status',
      'brand': 'Source',
      'currentprice': 'Price/Rent',
      'propertysubtype': 'Property Type',
      'yearbuilt': 'Year Built',
      'beds': 'Bed',
      'baths': 'Bath',
      'sqft': 'Sq Ft',
      'cdom': 'CDOM',
      'closing_date': 'Closing Date',
      'price_per_sqft': '$/Sqft',
      // Add fallback for any other columns
      'rent': 'Rent',
      'propertytype': 'Property Type',
      'size': 'Sq Ft',
      'bed': 'Bed',
      'bath': 'Bath',
      'year_built': 'Year Built',
      'close_date': 'Closing Date'
    };

    return displayNameMap[columnId] || columnId.charAt(0).toUpperCase() + columnId.slice(1).replace(/([A-Z])/g, ' $1');
  };

  // Helper function to get a readable filter description
  const getFilterDescription = (filter: { id: string; value: any }) => {
    const columnDisplayName = getColumnDisplayName(filter.id);
    const { filterType } = getColumnFilterInfo(filter.id);

    if (Array.isArray(filter.value)) {
      // Handle range filters
      const [min, max] = filter.value;
      if (min !== undefined && max !== undefined) {
        return `${columnDisplayName}: ${min} - ${max}`;
      } else if (min !== undefined) {
        return `${columnDisplayName}: ≥ ${min}`;
      } else if (max !== undefined) {
        return `${columnDisplayName}: ≤ ${max}`;
      }
    }

    // Handle special operator objects
    if (isOperatorObject(filter.value)) {
      const { operator, value } = filter.value;
      switch (operator) {
        case 'notEquals':
          return `${columnDisplayName}: not ${value}`;
        case 'before':
          return `${columnDisplayName}: before ${value}`;
        case 'after':
          return `${columnDisplayName}: after ${value}`;
        case 'withinLastDays':
          return `${columnDisplayName}: within last ${value} days`;
        default:
          return `${columnDisplayName}: ${value}`;
      }
    }

    return `${columnDisplayName}: ${filter.value}`;
  };

  const removeFilter = (filterId: string) => {
    const newFilters = columnFilters.filter(filter => filter.id !== filterId);
    onColumnFiltersChange(newFilters);

    // Also remove the corresponding filter rule
    setFilterRules(prev => prev.filter(rule => rule.columnId !== filterId));
  };

  return (
    <div className="space-y-2">
      {/* Active Filter Badges - Show when dropdown is closed and filters exist */}
      {!isOpen && columnFilters.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {columnFilters.map((filter) => (
            <Badge
              key={filter.id}
              variant="secondary"
              className="text-xs px-2 py-1 bg-blue-50 text-blue-700 hover:bg-blue-100"
            >
              {getFilterDescription(filter)}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  removeFilter(filter.id);
                }}
                className="ml-1 hover:text-blue-900"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}

      <Popover open={isOpen} onOpenChange={onOpenChange}>
        <PopoverTrigger asChild>
          {trigger}
        </PopoverTrigger>
        <PopoverContent className={'w-[90vw] max-w-[500px] p-4 overflow-auto max-h-[35vh]'} align="start">
          <div className="space-y-4">
            <div className="flex items-center justify-between overflow-scroll">
              <h4 className="font-medium text-sm">Filters</h4>
              {activeFiltersCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClearAllFilters}
                  className="h-6 px-2 text-xs"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  REMOVE ALL
                </Button>
              )}
            </div>

            {/* Smart Filter Toggle */}
            {smartFilterInfo && onToggleFilterMode && (
              <div className="flex items-center justify-between rounded-lg p-2 border" style={{ backgroundColor: '#f0f7f4', borderColor: '#406855' }}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="text-xs cursor-help" style={{ color: '#406855' }}>
                      {smartFilterInfo.enabled ? 'Smart filtering active' : 'Manual filtering mode'}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent className="w-[300px] max-h-[50vh] overflow-y-auto overflow-x-auto bg-white text-dark-gray rounded-md py-3 px-4 z-300 border border-medium-gray-20 text-xs">
                    Smart Filters automatically set the comps to display comps that are within 1 bedroom, 25% of the home square footage, built since 2010, have a reported closed rent within 6 months, and exclude Mobile Homes.
                  </TooltipContent>
                </Tooltip>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onToggleFilterMode}
                  className="gap-1 text-xs h-6"
                  style={{ borderColor: '#406855', color: '#406855' }}
                >
                  <RotateCcw className="h-3 w-3" />
                  {smartFilterInfo.enabled ? 'Manual' : 'Smart'}
                </Button>
              </div>
            )}

            {/* Filter Rules */}
            <div className="space-y-3">
              {filterRules.map((rule) => {
                const { filterType } = rule.columnId ? getColumnFilterInfo(rule.columnId) : { filterType: 'text' as const };
                const operators = getOperatorsByType(filterType);

                return (
                  <div key={rule.id} className="flex items-center gap-3 p-3 border rounded-md bg-gray-50 flex-wrap">
                    {/* Column Selection */}
                    <div className={`${isMobile ? 'w-full' : 'flex-1'}`}>
                      <label className="text-xs text-gray-600 block mb-1">Column</label>
                      <Select
                        value={rule.columnId}
                        onValueChange={(value) => {
                          // Update multiple fields at once to avoid race conditions
                          setFilterRules(prev => prev.map(r =>
                            r.id === rule.id
                              ? { ...r, columnId: value, operator: '', value: '' }
                              : r
                          ));
                        }}
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="Select column" />
                        </SelectTrigger>
                        <SelectContent>
                          {filterableColumns.map((column) => {
                            const displayName = getColumnDisplayName(column.id);
                            return (
                              <SelectItem key={column.id} value={column.id}>
                                {displayName}
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Operator Selection */}
                    {rule.columnId && (
                      <div className={`${isMobile ? 'w-full' : 'flex-1'}`}>
                        <label className="text-xs text-gray-600 block mb-1">Operator</label>
                        <Select
                          value={rule.operator}
                          onValueChange={(value) => {
                            // Update multiple fields at once to avoid race conditions
                            setFilterRules(prev => prev.map(r =>
                              r.id === rule.id
                                ? { ...r, operator: value, value: '' }
                                : r
                            ));
                          }}
                        >
                          <SelectTrigger className="h-8 w-full">
                            <SelectValue placeholder="Select operator" />
                          </SelectTrigger>
                          <SelectContent>
                            {operators.map((op) => (
                              <SelectItem key={op.value} value={op.value}>
                                {op.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    {/* Value Input */}
                    {rule.columnId && rule.operator && (
                      <div className={`${isMobile ? 'flex-grow' : 'flex-1'}`}>
                        <label className="text-xs text-gray-600 block mb-1">Value</label>
                        {renderValueInput(rule)}
                      </div>
                    )}

                    {/* Remove Button */}
                    <div className={`${isMobile ? 'self-end mt-auto' : 'flex items-end'} ml-auto`}>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFilterRule(rule.id)}
                        className="h-8 w-8 p-0 text-gray-500 hover:text-red-600"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Add Filter Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={addFilterRule}
              className="w-full h-8 gap-1"
            >
              <Plus className="h-3 w-3" />
              ADD FILTER
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
});

FilterDropdown.displayName = 'FilterDropdown';

export default FilterDropdown; 