export const calculateMedian = (values: number[]): number => {
  if (values.length === 0) {
    return 0;
  } else {
    values.sort((a, b) => a - b);

    const half = Math.floor(values.length / 2);

    if (values.length % 2) {
      return values[half];
    }

    return (values[half - 1] + values[half]) / 2.0;
  }
};

export const calculateAverage = (numbers: Array<number | null>): number => {
  // remove nulls and undefined values
  const filteredNumbers = numbers.filter(
    (n): n is number => n !== null && n !== undefined,
  );
  return filteredNumbers.reduce((a, b) => a + b, 0) / filteredNumbers.length;
};

/**
 * Calculates the average of two values. If one of the values is zero, returns the other value.
 * If neither value is zero, returns their average.
 *
 * @param value1 - The first number to average.
 * @param value2 - The second number to average.
 * @returns The average of value1 and value2 or the non-zero value if one of them is zero.
 */
export const getAverageWithZeroValueCheck = (
  value1: number,
  value2: number,
): number => {
  // If the first value is zero, return the second value.
  if (value1 === 0) {
    return value2;
  }
  // If the second value is zero, return the first value.
  else if (value2 === 0) {
    return value1;
  }
  // If neither value is zero, calculate and return the average.
  else {
    return (value1 + value2) / 2;
  }
};
