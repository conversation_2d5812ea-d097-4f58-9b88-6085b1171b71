import filtersDefault from './filters-default.json';
import filtersMatchModeRangesDefault from './filters-match-mode-ranges-default.json';
import {
  filterCompDataParams,
  filters,
  mlsCompData,
  mlsData,
  parcelData,
  portfolioPropertyData,
  realtorDotComCompData,
  sfrAndHotPadsCompData,
} from './types.d.ts';

// get fields from different subject property data for filters
const getFieldValuesFromSubjectPropertyData = ({
  subjectPropertyData,
  subjectPropertyType,
  subjectPropertyParcelData,
}: {
  subjectPropertyData: parcelData | mlsData | portfolioPropertyData;
  subjectPropertyType: 'parcel' | 'mls' | 'portfolio';
  subjectPropertyParcelData?: parcelData;
}) => {
  switch (subjectPropertyType) {
    case 'parcel':
      const parcelData = subjectPropertyData as parcelData;
      return {
        beds: parcelData.beds_count,
        baths: parcelData.baths,
        sqft: parcelData.total_area_sq_ft,
        yearBuilt: parcelData.year_built,
        lotSize: parcelData.area_acres,
      };
    case 'mls':
      const mlsData = subjectPropertyData as mlsData;
      return {
        beds: mlsData.beds_total || subjectPropertyParcelData?.beds_count || 0,
        baths: mlsData.baths_full || subjectPropertyParcelData?.baths || 0,
        sqft: mlsData.sqft || subjectPropertyParcelData?.total_area_sq_ft || 0,
        yearBuilt:
          mlsData.year_built || subjectPropertyParcelData?.year_built || 0,
        lotSize: subjectPropertyParcelData?.area_acres || 0,
      };
    case 'portfolio':
      const portfolioPropertyData =
        subjectPropertyData as portfolioPropertyData;
      return {
        beds:
          portfolioPropertyData.beds ||
          subjectPropertyParcelData?.beds_count ||
          0,
        baths:
          portfolioPropertyData.baths || subjectPropertyParcelData?.baths || 0,
        sqft:
          portfolioPropertyData.sqft ||
          subjectPropertyParcelData?.total_area_sq_ft ||
          0,
        yearBuilt:
          portfolioPropertyData.yearbuilt ||
          subjectPropertyParcelData?.year_built ||
          0,
        lotSize: subjectPropertyParcelData?.area_acres || 0,
      };
  }
};

// match mode
// 1st round filter
const generateFilterValuesUnderMatchMode = ({
  round,
  subjectPropertyData,
  subjectPropertyType,
  subjectPropertyParcelData,
}: {
  round: 'first' | 'second';
  subjectPropertyData: parcelData | mlsData | portfolioPropertyData;
  subjectPropertyType: 'parcel' | 'mls' | 'portfolio';
  subjectPropertyParcelData?: parcelData;
}) => {
  const subjectPropertyFieldValues = getFieldValuesFromSubjectPropertyData({
    subjectPropertyData,
    subjectPropertyType,
    ...(subjectPropertyType !== 'parcel' && { subjectPropertyParcelData }),
  });

  const filtersFirstRound = {
    ...filtersDefault,
    minBeds: Math.max(
      subjectPropertyFieldValues.beds - filtersMatchModeRangesDefault.rangeBeds,
      filtersDefault.minBeds,
    ),
    maxBeds: Math.min(
      subjectPropertyFieldValues.beds + filtersMatchModeRangesDefault.rangeBeds,
      filtersDefault.maxBeds,
    ),
    minBaths: Math.max(
      subjectPropertyFieldValues.baths -
        filtersMatchModeRangesDefault.rangeBaths,
      filtersDefault.minBaths,
    ),
    maxBaths: Math.min(
      subjectPropertyFieldValues.baths +
        filtersMatchModeRangesDefault.rangeBaths,
      filtersDefault.maxBaths,
    ),
    minSqft: Math.max(
      subjectPropertyFieldValues.sqft *
        (1 - filtersMatchModeRangesDefault.rangeSqft),
      filtersDefault.minSqft,
    ),
    maxSqft: Math.min(
      subjectPropertyFieldValues.sqft *
        (1 + filtersMatchModeRangesDefault.rangeSqft),
      filtersDefault.maxSqft,
    ),
    minLotSize: Math.max(
      subjectPropertyFieldValues.lotSize *
        (1 - filtersMatchModeRangesDefault.rangeLotSize),
      filtersDefault.minLotSize,
    ),
    maxLotSize: Math.min(
      subjectPropertyFieldValues.lotSize *
        (1 + filtersMatchModeRangesDefault.rangeLotSize),
      filtersDefault.maxLotSize,
    ),
    minYearBuilt: Math.max(
      subjectPropertyFieldValues.yearBuilt -
        filtersMatchModeRangesDefault.rangeYearBuilt,
      filtersDefault.minYearBuilt,
    ),
    maxYearBuilt: Math.min(
      subjectPropertyFieldValues.yearBuilt +
        filtersMatchModeRangesDefault.rangeYearBuilt,
      filtersDefault.maxYearBuilt,
    ),
  };

  if (round === 'first') {
    return filtersFirstRound;
  } else if (round === 'second') {
    return {
      ...filtersFirstRound,
      minBeds: subjectPropertyFieldValues.beds - 1,
      maxBeds: subjectPropertyFieldValues.beds + 1,
    };
  } else {
    return filtersDefault;
  }
};

// filter a single field
const filterSingleFieldMinMax = ({
  filterMin,
  filterMax,
  compDataFieldValue,
}: {
  filterMin: number;
  filterMax: number;
  compDataFieldValue: number | null | undefined;
}) => {
  // if compDataFieldValue is null or undefined, keep the comp and return true
  if (compDataFieldValue === null || compDataFieldValue === undefined) {
    return true;
  }
  // if compDataFieldValue is a number, check if it is within the range
  if (typeof compDataFieldValue === 'number') {
    // if (!(compDataFieldValue >= filterMin && compDataFieldValue <= filterMax)) {
    //   console.log(compDataFieldValue, filterMin, filterMax);
    // }
    return compDataFieldValue >= filterMin && compDataFieldValue <= filterMax;
  }
};

const filterIsPoolAllowed = ({
  filterIsPoolAllowed,
  compDataHasPool,
}: {
  filterIsPoolAllowed: boolean;
  compDataHasPool: boolean | string | null | undefined;
}) => {
  // keep the comp if hasPool in compData is null or undefined
  if (compDataHasPool === null || compDataHasPool === undefined) {
    return true;
  }
  // convert all values to boolean
  let compDataHasPoolBool: boolean;
  if (
    compDataHasPool === true ||
    (typeof compDataHasPool === 'string' &&
      compDataHasPool.toLowerCase() === 'true') ||
    compDataHasPool === '1'
  ) {
    compDataHasPoolBool = true;
  } else if (
    compDataHasPool === false ||
    (typeof compDataHasPool === 'string' &&
      compDataHasPool.toLowerCase() === 'false') ||
    compDataHasPool === '0'
  ) {
    compDataHasPoolBool = false;
  } else {
    return true;
  }
  // if isPoolAllowed is true, keep all comps
  if (filterIsPoolAllowed) {
    return true;
  } else {
    // if isPoolAllowed is false, keep comps that doesn't have pool
    return !compDataHasPoolBool;
  }
};

export const filterCompData = ({
  compData,
  compType,
  filters,
  leaseOrSale,
}: {
  compData: mlsCompData[] | sfrAndHotPadsCompData[] | realtorDotComCompData[];
  compType: 'mls' | 'sfrAndHotPads' | 'realtorDotCom';
  filters: filters;
  leaseOrSale: 'lease' | 'sale';
}) => {
  console.log('filterCompData compData', compData);
  console.log('filterCompData compType', compType);
  console.log('filterCompData filters', filters);
  console.log('filterCompData leaseOrSale', leaseOrSale);
  switch (compType) {
    case 'mls':
      const mlsCompData = compData as mlsCompData[];
      return mlsCompData.filter((mlsComp) => {
        return (
          filterSingleFieldMinMax({
            filterMin: filters.minBeds,
            filterMax: filters.maxBeds,
            compDataFieldValue: mlsComp.bed,
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minBaths,
            filterMax: filters.maxBaths,
            compDataFieldValue: mlsComp.bath,
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minSqft,
            filterMax: filters.maxSqft,
            compDataFieldValue: mlsComp.size,
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minLotSize,
            filterMax: filters.maxLotSize,
            compDataFieldValue: mlsComp.lot_size / 43560, // sqft convert to acres
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minYearBuilt,
            filterMax: filters.maxYearBuilt,
            compDataFieldValue: mlsComp.yearbuilt,
          }) &&
          filterSingleFieldMinMax({
            filterMin:
              leaseOrSale === 'lease' ? filters.minRent : filters.minSales,
            filterMax:
              leaseOrSale === 'lease' ? filters.maxRent : filters.maxSales,
            compDataFieldValue: mlsComp?.currentprice || mlsComp?.closeprice, // if the comp is active, currentprice will be available; if the comp is closed, closeprice will be available
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minCumulativeDaysOnMarket,
            filterMax: filters.maxCumulativeDaysOnMarket,
            compDataFieldValue: mlsComp.cdom,
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minCoveredParking,
            filterMax: filters.maxCoveredParking,
            compDataFieldValue: mlsComp.garage,
          }) &&
          filterIsPoolAllowed({
            filterIsPoolAllowed: filters.isPoolAllowed,
            compDataHasPool: mlsComp.pool,
          })
        );
      });
    case 'sfrAndHotPads':
      const sfrAndHotPadsCompData = compData as sfrAndHotPadsCompData[];
      return sfrAndHotPadsCompData.filter((sfrAndHotPadsComp) => {
        return (
          // SFR and HotPads comps have no lot size, pool, and garage
          // so we don't need to filter them
          filterSingleFieldMinMax({
            filterMin: filters.minBeds,
            filterMax: filters.maxBeds,
            compDataFieldValue: sfrAndHotPadsComp.bed_rooms,
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minBaths,
            filterMax: filters.maxBaths,
            compDataFieldValue: sfrAndHotPadsComp.bath_rooms,
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minSqft,
            filterMax: filters.maxSqft,
            compDataFieldValue: sfrAndHotPadsComp.square_feet,
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minYearBuilt,
            filterMax: filters.maxYearBuilt,
            compDataFieldValue: sfrAndHotPadsComp.yearbuilt,
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minCumulativeDaysOnMarket,
            filterMax: filters.maxCumulativeDaysOnMarket,
            compDataFieldValue: sfrAndHotPadsComp.cdom,
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minRent,
            filterMax: filters.maxRent,
            compDataFieldValue: sfrAndHotPadsComp.rent,
          })
        );
      });
    case 'realtorDotCom':
      const realtorDotComCompData = compData as realtorDotComCompData[];
      console.log('realtorDotComCompData', realtorDotComCompData);
      return realtorDotComCompData.filter((realtorDotComComp) => {
        return (
          // realtor.com comps have no lot size, pool, garage, and cumulative days on market
          // so we don't need to filter them
          filterSingleFieldMinMax({
            filterMin: filters.minBeds,
            filterMax: filters.maxBeds,
            compDataFieldValue: Number(realtorDotComComp.beds),
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minBaths,
            filterMax: filters.maxBaths,
            compDataFieldValue: Number(realtorDotComComp.baths),
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minSqft,
            filterMax: filters.maxSqft,
            compDataFieldValue: Number(realtorDotComComp.square_feet),
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minYearBuilt,
            filterMax: filters.maxYearBuilt,
            compDataFieldValue: Number(realtorDotComComp.year_built),
          }) &&
          filterSingleFieldMinMax({
            filterMin: filters.minRent,
            filterMax: filters.maxRent,
            compDataFieldValue: Number(realtorDotComComp.currentprice),
          })
        );
        // for debugging
        // see all filtered out comps
        // return (
        //   !filterSingleFieldMinMax({
        //     filterMin: filters.minBeds,
        //     filterMax: filters.maxBeds,
        //     compDataFieldValue: Number(realtorDotComComp.beds),
        //   }) ||
        //   !filterSingleFieldMinMax({
        //     filterMin: filters.minBaths,
        //     filterMax: filters.maxBaths,
        //     compDataFieldValue: Number(realtorDotComComp.baths),
        //   }) ||
        //   !filterSingleFieldMinMax({
        //     filterMin: filters.minSqft,
        //     filterMax: filters.maxSqft,
        //     compDataFieldValue: Number(realtorDotComComp.square_feet),
        //   }) ||
        //   !filterSingleFieldMinMax({
        //     filterMin: filters.minYearBuilt,
        //     filterMax: filters.maxYearBuilt,
        //     compDataFieldValue: Number(realtorDotComComp.year_built),
        //   }) ||
        //   !filterSingleFieldMinMax({
        //     filterMin: filters.minRent,
        //     filterMax: filters.maxRent,
        //     compDataFieldValue: Number(realtorDotComComp.currentprice),
        //   })
        // )
      });
    default:
      return [];
  }
};

// match mode
// filter
export const filterCompDataUnderMatchMode = (props: filterCompDataParams) => {
  const {
    subjectPropertyData,
    subjectPropertyType,
    subjectPropertyParcelData,
    leaseOrSale,
    mlsCompData,
  } = props;
  // generate match mode filters
  // first round
  const filtersFirstRound = generateFilterValuesUnderMatchMode({
    round: 'first',
    subjectPropertyData,
    subjectPropertyType,
    subjectPropertyParcelData,
  });
  const filterSecondRound = generateFilterValuesUnderMatchMode({
    round: 'second',
    subjectPropertyData,
    subjectPropertyType,
    subjectPropertyParcelData,
  });
  // if it's for lease, filter MLS, SFR, HotPads, and realtor.com comps
  if (leaseOrSale === 'lease') {
    const { sfrCompData, hotPadsCompData, realtorDotComCompData } = props;
    const filteredMLSCompData = filterCompData({
      compData: mlsCompData,
      compType: 'mls',
      filters: filtersFirstRound,
      leaseOrSale,
    });
    const filteredSFRCompData = filterCompData({
      compData: sfrCompData,
      compType: 'sfrAndHotPads',
      filters: filtersFirstRound,
      leaseOrSale,
    });
    const filteredHotPadsCompData = filterCompData({
      compData: hotPadsCompData,
      compType: 'sfrAndHotPads',
      filters: filtersFirstRound,
      leaseOrSale,
    });
    const filteredRealtorDotComCompData = filterCompData({
      compData: realtorDotComCompData,
      compType: 'realtorDotCom',
      filters: filtersFirstRound,
      leaseOrSale,
    });
    // if any filtered comps have 3 or more comps, filtering has succeeded and return all filtered comps
    if (
      filteredMLSCompData.length >= 3 ||
      filteredSFRCompData.length >= 3 ||
      filteredHotPadsCompData.length >= 3 ||
      filteredRealtorDotComCompData.length >= 3
    ) {
      return {
        filteredMLSCompData,
        filteredSFRCompData,
        filteredHotPadsCompData,
        filteredRealtorDotComCompData,
      };
    } else {
      // if all filtered comps have fewer than 3 comps, filter again with second round filters
      const filteredMLSCompDataSecondRound = filterCompData({
        compData: mlsCompData,
        compType: 'mls',
        filters: filterSecondRound,
        leaseOrSale,
      });
      const filteredSFRCompDataSecondRound = filterCompData({
        compData: sfrCompData,
        compType: 'sfrAndHotPads',
        filters: filterSecondRound,
        leaseOrSale,
      });
      const filteredHotPadsCompDataSecondRound = filterCompData({
        compData: hotPadsCompData,
        compType: 'sfrAndHotPads',
        filters: filterSecondRound,
        leaseOrSale,
      });
      const filteredRealtorDotComCompDataSecondRound = filterCompData({
        compData: realtorDotComCompData,
        compType: 'realtorDotCom',
        filters: filterSecondRound,
        leaseOrSale,
      });
      return {
        filteredMLSCompData: filteredMLSCompDataSecondRound,
        filteredSFRCompData: filteredSFRCompDataSecondRound,
        filteredHotPadsCompData: filteredHotPadsCompDataSecondRound,
        filteredRealtorDotComCompData: filteredRealtorDotComCompDataSecondRound,
      };
    }
  } else if (leaseOrSale === 'sale') {
    const filteredMLSCompData = filterCompData({
      compData: mlsCompData,
      compType: 'mls',
      filters: filtersFirstRound,
      leaseOrSale,
    });
    // if MLS filtered comps have 3 or more comps, filtering has succeeded and return all filtered comps
    if (filteredMLSCompData.length >= 3) {
      return {
        filteredMLSCompData,
      };
    } else {
      // if MLS filtered comps have fewer than 3 comps, filter again with second round filters
      const filteredMLSCompDataSecondRound = filterCompData({
        compData: mlsCompData,
        compType: 'mls',
        filters: filterSecondRound,
        leaseOrSale,
      });
      return {
        filteredMLSCompData: filteredMLSCompDataSecondRound,
      };
    }
  }
};

// calculate market rent / market value
