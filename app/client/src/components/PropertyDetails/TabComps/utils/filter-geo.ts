import booleanPointInPolygon from '@turf/boolean-point-in-polygon';
import { FeatureCollection, point } from '@turf/helpers';
import { LngLat } from 'mapbox-gl';
import {
  countyPolygon,
  mlsCompData,
  realtorDotComCompData,
  schoolDistrictPolygon,
  sfrAndHotPadsCompData,
  ZIPCodePolygon,
} from './types.d.ts';

// const serverType = process.env.UMI_APP_SERVER_TYPE;
const serverType = 'prod';

const getGeofenceData = async ({
  type,
  params,
}: {
  type: 'school district' | 'zip code' | 'county';
  params: {
    lat: number;
    lng: number;
  };
}) => {
  let urlSegment = '';
  switch (type) {
    case 'school district':
      urlSegment = 'district';
      break;
    case 'zip code':
      urlSegment = 'zipcode';
      break;
    case 'county':
      urlSegment = 'county';
      break;
    default:
      throw new Error('Invalid type');
  }
  return await fetch(
    `/api/cma/${serverType}/${urlSegment}/point?lat=${params.lat}&lng=${params.lng}`,
    {
      method: 'GET',
    },
  );
};

export const filterDataBySubjectPolygon = ({
  subjectCoordinates,
  data,
  polygons,
}: {
  subjectCoordinates: [number, number];
  data: mlsCompData[] | sfrAndHotPadsCompData[] | realtorDotComCompData[];
  polygons: schoolDistrictPolygon[] | countyPolygon[] | ZIPCodePolygon[];
}) => {
  if (
    Array.isArray(subjectCoordinates) &&
    subjectCoordinates.length === 2 &&
    subjectCoordinates[0] &&
    subjectCoordinates[1] &&
    typeof subjectCoordinates[0] === 'number' &&
    typeof subjectCoordinates[1] === 'number'
  ) {
    // 1. Find the subject property district
    const subjectPoint = point(subjectCoordinates);
    const subjectPolygon = polygons.find((polygon) =>
      booleanPointInPolygon(subjectPoint, polygon.geom),
    );

    // 2. Find which property data falls in subject property's district
    if (subjectPolygon) {
      const filteredData = data.filter((property) => {
        const getPropertyPoint = () => {
          if (
            'geography' in property &&
            property?.geography?.type === 'Point'
          ) {
            return property.geography;
          } else if ('geom' in property && property?.geom?.type === 'Point') {
            return property.geom;
          } else if (
            'latitude' in property &&
            'longitude' in property &&
            property.latitude &&
            property.longitude
          ) {
            return point([+property.longitude, +property.latitude]);
          } else {
            return null;
          }
        };
        const propertyPoint = getPropertyPoint();
        if (propertyPoint) {
          return booleanPointInPolygon(propertyPoint, subjectPolygon.geom);
        } else {
          return false;
        }
      });
      return filteredData;
    } else {
      return data;
    }
  } else {
    return data;
  }
};
