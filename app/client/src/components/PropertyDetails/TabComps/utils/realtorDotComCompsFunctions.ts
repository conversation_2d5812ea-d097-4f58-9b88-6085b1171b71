import { realtorDotComCompData } from './type';

export function testLog(stuff: any) {
  console.log('testSF', { stuff });
}

export function sqftToAcre(sqft: number) {
  const sqftPerAcre = 43560;
  return sqft / sqftPerAcre;
}

export function formatPricePerSqftArce(number: any) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(number);
}
export function calculateMedian(
  selectedRowKeys: import('react').Key[],
  landCompsDataForRender: realtorDotComCompData[],
): number {
  // Filter the data to only include selected rows
  const selectedData = landCompsDataForRender.filter((record) =>
    selectedRowKeys.includes(record.id),
  );

  // Extract the relevant prices based on the status of each record
  const prices = selectedData.map((record) => {
    return record.currentprice;
  });

  // Sort the prices to prepare for median calculation
  prices.sort((a, b) => Number(a) - Number(b));

  // Calculate the median price
  const mid = Math.floor(prices.length / 2);
  let median;
  if (prices.length % 2 === 0) {
    // If even, average the two middle values
    median = (Number(prices[mid - 1]) + Number(prices[mid])) / 2; // Ensure values are numbersy
  } else {
    // If odd, take the middle value
    median = prices[mid];
  }

  return median as number; // Ensure median is returned as a number
}

export function calculateMedianPricePerSqFt(
  selectedRowKeys: import('react').Key[],
  landCompsDataForRender: realtorDotComCompData[],
): number {
  // Filter the data to only include selected rows
  const selectedData = landCompsDataForRender.filter((record) =>
    selectedRowKeys.includes(record.id),
  );

  // Calculate the price per square foot for each selected entry
  const pricesPerSqFt = selectedData
    .map((record) => {
      const price = Number(record.currentprice);
      if (Number(record.square_feet) > 0) {
        // Ensure lot_size is positive to avoid division by zero
        return price / Number(record.square_feet);
      }
      return null; // or handle zero lot_size in a specific way if needed
    })
    .filter((price) => price !== null); // Filter out null values if lot_size was zero

  // Sort the prices per square foot to prepare for median calculation
  pricesPerSqFt.sort((a, b) => a - b);

  // Calculate the median price per square foot
  const mid = Math.floor(pricesPerSqFt.length / 2);
  let medianPricePerSqFt;
  if (pricesPerSqFt.length % 2 === 0) {
    // If even, average the two middle values
    medianPricePerSqFt = (pricesPerSqFt[mid - 1] + pricesPerSqFt[mid]) / 2;
  } else {
    // If odd, take the middle value
    medianPricePerSqFt = pricesPerSqFt[mid];
  }

  return medianPricePerSqFt;
}
