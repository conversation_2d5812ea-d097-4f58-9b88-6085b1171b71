export interface parcelData {
  area_acres: number;
  baths: number;
  beds_count: number;
  rent: number;
  sales: number;
  total_area_sq_ft: number;
  year_built: number;
}

export interface mlsData {
  baths_full: number;
  beds_total: number;
  // latitude: string;
  // list_price: number;
  // longitude: string;
  // market_rent: string;
  // market_sales: string;
  // property_sub_type: string;
  sqft: number;
  year_built: number;
}

export interface portfolioPropertyData {
  baths: number;
  beds: number;
  // lat: number;
  // lng: number;
  // meta: {
  //   rent: number;
  // }
  // rent: number;
  // sales: number;
  sqft: number;
  yearbuilt: number;
}

export interface mlsCompData {
  bath: number;
  bed: number;
  cdom: number;
  closeprice: number;
  currentprice: number;
  distance: number;
  dom: number;
  garage: number | null;
  latitude: number;
  longitude: number;
  lot_size: number;
  orginalprice: number;
  pool: string | null;
  propertysubtype: string | null;
  size: number;
  yearbuilt: number;
  mlsid: string;
  status: 'Closed' | 'Active' | 'Pending' | 'Hold';
  geography: {
    type: 'Point';
    coordinates: number[];
  };
  // Additional fields from sample data
  fulladdress: string;
  stateorprovince: string;
  zipcode: string;
  city: string;
  levels: string;
  subdivision: string;
  contractdate: string;
  closedate: string;
  offmarketdate: string;
  propertytype: string;
  metro: string;
  modificationtimestamp: string;
  placekey: string;
  listingkey: string | null;
  county: string;
  first_entry_timestamp: string;
  watersource: string | null;
  sfr_owner: string | null;
  deed_last_sale_date: string;
  deed_last_sale_price: string;
  building_permit_id: string | null;
  permit_number: string | null;
  description: string | null;
  pmt_type: string | null;
  new_construction: boolean;
}

export interface sfrAndHotPadsCompData {
  address: string;
  available_date: string | null;
  base_id: string;
  bath_rooms: number;
  bed_rooms: number;
  brand: string;
  cdom: number;
  close_date: string;
  distance: number;
  exists: boolean;
  status: string;
  geom: {
    type: 'Point';
    coordinates: number[];
  };
  placekey: string;
  postal_code: string | null;
  property_id: string;
  rent: number;
  square_feet: number;
  standard_city: string;
  standard_state: string;
  yearbuilt: number;
  propertysubtype: string | null;
  deed_last_sale_date: string;
  deed_last_sale_price: string;
  building_permit_id: string | null;
  permit_number: string | null;
  description: string | null;
  pmt_type: string | null;
  area_acres: number | null;
  last_rent_info: string;
}

export interface realtorDotComCompData {
  // essential fields
  baths: string;
  beds: string;
  currentprice: string;
  latitude: string;
  longitude: string;
  propertytype: string;
  square_feet: string;
  year_built: number | string;
  id: string;
  // extra
  status: string;
  address: string;
  city: string;
  state: string;
  postal_code: string;
  url: string;
  listeddate: any;
  listedPrice: any;
  removeddate: Date;
  reductions: string;
  avm1source: any;
  avm1estimate: any;
  avm1low: any;
  avm1high: any;
  avm1date: any;
  avm2source: any;
  avm2estimate: any;
  avm2low: any;
  avm2high: any;
  avm2date: any;
  avm3source: any;
  avm3estimate: any;
  avm3low: any;
  avm3high: any;
  avm3date: any;
  listed: any;
  list_price: any;
  placekey: any;
  distance: number;
}

export interface filters {
  // geo filters: need point-in-poly
  // default values are false under match mode
  isSameSchoolDistrict: boolean;
  isSameCounty: boolean;
  isSameZIPCode: boolean;
  // regular filters
  minRent: number; // for lease comps
  maxRent: number;
  minSales: number; // for sale comps
  maxSales: number;
  minBeds: number;
  maxBeds: number;
  minBaths: number;
  maxBaths: number;
  minSqft: number;
  maxSqft: number;
  minYearBuilt: number;
  maxYearBuilt: number;
  minLotSize: number;
  maxLotSize: number;
  minCumulativeDaysOnMarket: number; // cululative days on market
  maxCumulativeDaysOnMarket: number;
  minCoveredParking: number;
  maxCoveredParking: number;
  isPoolAllowed: boolean; // default value is true
}

// for lease, filter MLS, SFR, HotPads, and realtor.com comps
export interface filterCompDataParamsForLease {
  subjectPropertyData: parcelData | mlsData | portfolioPropertyData;
  subjectPropertyType: 'parcel' | 'mls' | 'portfolio';
  subjectPropertyParcelData?: parcelData;
  leaseOrSale: 'lease';
  mlsCompData: mlsCompData[];
  sfrCompData: sfrAndHotPadsCompData[];
  hotPadsCompData: sfrAndHotPadsCompData[];
  realtorDotComCompData: realtorDotComCompData[];
}

// for sale, filter MLS comps only
export interface filterCompDataParamsForSale {
  subjectPropertyData: parcelData | mlsData | portfolioPropertyData;
  subjectPropertyType: 'parcel' | 'mls' | 'portfolio';
  subjectPropertyParcelData?: parcelData;
  leaseOrSale: 'sale';
  mlsCompData: mlsCompData[];
}

export type filterCompDataParams =
  | filterCompDataParamsForLease
  | filterCompDataParamsForSale;

// school district polygon
export interface schoolDistrictPolygon {
  country: 'USA';
  geom: {
    type: 'MultiPolygon';
    coordinates: number[][][][];
  };
  lat: number;
  lng: number;
  metro: string;
  obj_area: string;
  obj_id: string;
  obj_name: string;
  obj_subtcd: string;
  obj_subtyp: string;
  obj_typ: string;
  reldate: string;
}

// county polygon
export interface countyPolygon {
  geom: {
    type: 'MultiPolygon';
    coordinates: number[][][][];
  };
  id: number;
  key: string;
  name: string;
}

// ZIP Code polygon
export interface ZIPCodePolygon {
  geom: {
    type: 'MultiPolygon';
    coordinates: number[][][][];
  };
  id: number;
  key: string;
}

export type MLSListingStatusType = 'Closed' | 'Active' | 'Pending' | 'Withdrawn' | 'status';
export type SFRListingStatusType = 'Closed' | 'Active' | 'Pending' | 'exists';
export type HotPadsListingStatusType = false | true | 'exists' | null;

export type constructionType = 'ALL' | 'NEW' | 'EXISTING';