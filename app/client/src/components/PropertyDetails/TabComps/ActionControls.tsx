import React from 'react';
import { SlidersHorizontal } from 'lucide-react';
import SliderToggle from './SliderToggle';
import ActionButton from '../../Common/ActionButton';

interface ActionControlsProps {
  onEditCriteriaClick: () => void;
  newConstructionOnly: boolean;
  onNewConstructionChange: (checked: boolean) => void;
  className?: string;
}

const ActionControls: React.FC<ActionControlsProps> = ({
  onEditCriteriaClick,
  newConstructionOnly,
  onNewConstructionChange,
  className = '',
}) => {
  return (
    <div className={`flex flex-wrap justify-center items-center gap-6 ${className}`}>
      <ActionButton
        text="Edit Criteria"
        onClick={onEditCriteriaClick}
        icon={<SlidersHorizontal size={12} />}
        className="py-1 px-2"
      />
      {/* <SliderToggle
        label="New Construction Only"
        checked={newConstructionOnly}
        onChange={onNewConstructionChange}
      /> */}
    </div>
  );
};

export default ActionControls;