import React, { ChangeEvent } from 'react';
import CustomCheckbox from '../../Common/CustomCheckbox';

interface TableTitleProps {
  title: string;
  isAllSelected: boolean;
  isIndeterminate: boolean;
  onSelectionChange: (event: ChangeEvent<HTMLInputElement>) => void;
  className?: string;
}

const TableTitle: React.FC<TableTitleProps> = ({
  title,
  isAllSelected,
  isIndeterminate,
  onSelectionChange,
  className = ''
}) => {
  return (
    <div className={`flex items-center ${className} `}>
      <div className="flex items-center justify-center w-[25px] h-8">
        <CustomCheckbox
          checked={isAllSelected}
          indeterminate={isIndeterminate}
          onChange={onSelectionChange}
        />
      </div>
      <h3 className="text-[13px] font-bold pl-2">{title}</h3>
    </div>
  );
};

export default TableTitle;