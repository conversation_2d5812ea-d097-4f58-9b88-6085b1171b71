import React, { ChangeEvent } from 'react';
import { calculateMedianRent } from '../../../lib/utils/formatUtils';
import { CompSectionProps, CompListingProps } from '../../../types/PropertyDetailPage.types';
import CompTable from './CompsTable';
import TableHeader from './TableHeader';

interface CompTableSectionProps {
  title: string;
  data: CompSectionProps | CompListingProps[] | undefined;
  selectedListings: string[];
  onListingSelect: (address: string, isSelected: boolean) => void;
  formatPrice: (price: number | string | null | undefined) => string;
}

const CompTableSection: React.FC<CompTableSectionProps> = ({
  title,
  data,
  selectedListings,
  onListingSelect,
  formatPrice
}) => {
  const listings = Array.isArray(data) ? data : data?.listings || [];
  const medianRentOfSelected = calculateMedianRent(listings, selectedListings);
  const selectedListingsCount = listings.filter(listing => selectedListings.includes(listing.address)).length;


  const handleSelectAllChange = (event: ChangeEvent<HTMLInputElement>) => {
    const isSelected = event.target.checked;
    listings.forEach(listing => onListingSelect(listing.address, isSelected));
  };

  const allSelected = listings.length > 0 && listings.every(listing => selectedListings.includes(listing.address));
  const someSelected = listings.length > 0 && listings.some(listing => selectedListings.includes(listing.address)) && !allSelected;

  if (!listings || listings.length === 0) {
    return (
      <div className="mb-4 bg-white">
        <h3>{title}</h3>
        <p>No comparable listings available.</p>
      </div>
    );
  }

  return (
    <div>
      <TableHeader
        title={title}
        isAllSelected={allSelected}
        isIndeterminate={someSelected}
        onSelectionChange={handleSelectAllChange}
        medianRent={medianRentOfSelected}
        totalSelected={selectedListingsCount}
        showSummary={selectedListings.length > 0}
        formatter={formatPrice}
      />

      <CompTable
        listings={listings}
        selectedListings={selectedListings}
        onListingSelect={onListingSelect}
        formatPrice={formatPrice}
      />
    </div>
  );
};

export default CompTableSection;