import { useMLSComps } from '../hooks/useMLSComps';
import { useEffect, useRef, useState } from 'react';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { useCompsContext } from '@/contexts/CompsContext';
import { mlsCompData } from '../utils/types';
import { columns } from './columns';
import { DataTable } from '../components/data-table';
import { isEqual } from 'lodash';

const MLSTable = () => {
  const {
    map,
    setCurrentMLSGeoJSON,
    searchingMode,
    setSelectedRowKeysMLSLease,
    setSelectedRowKeysMLSSale,
  } = useMarketplaceMapContext();

  const MLSContainer = useRef<HTMLDivElement>(null);

  const [MLSDataForRender, setMLSDataForRender] = useState<mlsCompData[]>([]);
  const [prevFilteredComps, setPrevFilteredComps] = useState<mlsCompData[]>([]);
  const [prevGeoJSON, setPrevGeoJSON] = useState<Record<string, unknown> | null>(null);

  const { loading, filteredComps, geoJSON } = useMLSComps();

  console.log('MLSTable - geoJSON', geoJSON);

  useEffect(() => {
    if (!isEqual(prevGeoJSON, geoJSON)) {
      if (geoJSON) {
        setCurrentMLSGeoJSON(geoJSON);
      }
      setPrevGeoJSON(geoJSON);
    }
  }, [geoJSON]);

  useEffect(() => {
    if (!isEqual(prevFilteredComps, filteredComps)) {
      setMLSDataForRender(filteredComps);
      // select all rows
      if (searchingMode === 'Lease') {
        console.log('useEffect - setSelectedRowKeysMLSLease', filteredComps.map(row => row.mlsid));
        setSelectedRowKeysMLSLease(filteredComps.map(row => row.mlsid));
      } else if (searchingMode === 'Sale') {
        setSelectedRowKeysMLSSale(filteredComps.map(row => row.mlsid));
      } else {
        setSelectedRowKeysMLSLease([]);
        setSelectedRowKeysMLSSale([]);
      }
      setPrevFilteredComps(filteredComps);
    }
  }, [filteredComps]);

  const handleRowClick = (record: mlsCompData) => {
    if (map && record.longitude && record.latitude) {
      map.flyTo({
        center: [record.longitude, record.latitude],
        zoom: 16,
        speed: 2,
        curve: 1,
        easing: (t: number) => t,
      });
    }
  };

  const getSetSelectedRowKeysFunction = () => {
    if (searchingMode === 'Lease') {
      return setSelectedRowKeysMLSLease;
    } else if (searchingMode === 'Sale') {
      return setSelectedRowKeysMLSSale;
    } else {
      return () => { };
    }
  };

  return (
    <div
      ref={MLSContainer}
      key="MLS Table"
      className="my-4 bg-white"
    >
      {/* add table title */}
      <div className="pl-1.5 pb-2 font-bold text-left">
        MLS
      </div>
      <DataTable
        rowKey="mlsid"
        columns={columns}
        data={MLSDataForRender as mlsCompData[]}
        loading={loading}
        setSelectedRowKeys={getSetSelectedRowKeysFunction()}
        onRowClick={handleRowClick}
      />

      {/* Summary section */}
      {/* <div className="px-4 py-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">
            Median Rent of Selected
          </span>
        </div>
      </div> */}
    </div>
  );
};

export default MLSTable;
