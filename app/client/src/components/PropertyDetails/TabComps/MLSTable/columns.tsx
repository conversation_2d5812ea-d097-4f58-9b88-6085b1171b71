"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Checkbox } from "@/components/ui/checkbox"
import { formatCurrency } from '@/lib/utils/lastSalePublicRecord'
import { formatter } from '@/lib/utils/money'
import { formatPricePerSqftArce } from '../utils/realtorDotComCompsFunctions'
import { mlsCompData } from '../utils/types'

export const columns: ColumnDef<mlsCompData>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={table.getIsAllRowsSelected()}
          onCheckedChange={(value) => table.toggleAllRowsSelected(!!value)}
          aria-label="Select all"
          className="h-4 w-4"
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="h-4 w-4"
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "address",
    header: () => <div className="text-[12px] text-left pl-2">Address</div>,
    cell: ({ row }) => {
      const record = row.original
      return (
        <div className="text-[12px] text-left pl-2">
          {record.fulladdress}, {record.city}, {record.stateorprovince} {record.zipcode}
        </div>
      )
    },
  },
  {
    accessorKey: "distance",
    header: () => <div className="text-[12px] text-center">Distance</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{(record.distance / 1609.34).toFixed(2)} mi</div>
    },
  },
  {
    accessorKey: "status",
    header: () => <div className="text-[12px] text-center">MLS Status</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.status}</div>
    },
  },
  {
    accessorKey: "rent",
    header: () => <div className="text-[12px] text-center">Rent</div>,
    cell: ({ row }) => {
      const record = row.original
      const price = record.status === 'Closed' ? record.closeprice : record.currentprice
      return <div className="text-[12px] text-center">{formatCurrency(price)}</div>
    },
  },
  {
    accessorKey: "propertysubtype",
    header: () => <div className="text-[12px] text-center">Property Subtype</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.propertysubtype || record.propertytype}</div>
    },
  },
  {
    accessorKey: "yearbuilt",
    header: () => <div className="text-[12px] text-center">Year Built</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.yearbuilt}</div>
    },
  },
  {
    accessorKey: "bed",
    header: () => <div className="text-[12px] text-center">Beds</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.bed}</div>
    },
  },
  {
    accessorKey: "bath",
    header: () => <div className="text-[12px] text-center">Baths</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.bath}</div>
    },
  },
  {
    accessorKey: "size",
    header: () => <div className="text-[12px] text-center">Sqft</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{formatter(record.size)}</div>
    },
  },
  {
    accessorKey: "closedate",
    header: () => <div className="text-[12px] text-center">Closing Date</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.closedate || 'N/A'}</div>
    },
  },
  {
    accessorKey: "rentPricePerSqft",
    header: () => <div className="text-[12px] text-center">Rent Price/Sqft</div>,
    cell: ({ row }) => {
      const record = row.original
      const price = record.status === 'Closed' ? record.closeprice : record.currentprice
      const pps = record.size > 0 ? price / record.size : 0
      return <div className="text-[12px] text-center">{pps ? formatPricePerSqftArce(pps) : 'N/A'}</div>
    },
  },
]
