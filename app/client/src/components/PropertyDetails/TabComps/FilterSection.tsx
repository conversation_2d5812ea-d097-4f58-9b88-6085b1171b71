import React from 'react';
import Selection from '../../Common/Selection';

interface FilterOption {
  value: string;
  label: string;
}

interface FilterSectionProps {
  modeOptions: FilterOption[];
  compingOptions: FilterOption[];
  preferenceOptions: FilterOption[];
  mode: string;
  compingSelection: string;
  preference: string;
  onModeChange: (value: string) => void;
  onCompingSelectionChange: (value: string) => void;
  onPreferenceChange: (value: string) => void;
  className?: string;
}

const FilterSection: React.FC<FilterSectionProps> = ({
  modeOptions,
  compingOptions,
  preferenceOptions,
  mode,
  compingSelection,
  preference,
  onModeChange,
  onCompingSelectionChange,
  onPreferenceChange,
  className = '',
}) => {
  return (
    <div className={`flex flex-wrap justify-between gap-4 ${className}`}>
      <Selection
        label="Mode:"
        options={modeOptions}
        value={mode}
        onChange={onModeChange}
      />
      <Selection
        label="Comping Selection:"
        options={compingOptions}
        value={compingSelection}
        onChange={onCompingSelectionChange}
      />
      <Selection
        label="Preference:"
        options={preferenceOptions}
        value={preference}
        onChange={onPreferenceChange}
      />
    </div>
  );
};

export default FilterSection;