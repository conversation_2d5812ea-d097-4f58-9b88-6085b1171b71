import { useMLSComps } from '../hooks/useMLSComps';
import { useSFRComps } from '../hooks/useSFRComps';
import { useHotPadsComps } from '../hooks/useHotPadsComps';
import { useRealtorDotComComps } from '../hooks/useRealtorDotComComps';
import { useEffect, useRef, useState } from 'react';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { mlsCompData, sfrAndHotPadsCompData, realtorDotComCompData } from '../utils/types';
import { columns } from './columns';
import { DataTable } from '../components/data-table';
import { isEqual } from 'lodash';

// Union type for combined data
type CombinedCompData = mlsCompData | sfrAndHotPadsCompData | realtorDotComCompData;

const TableCombined = () => {
  const {
    map,
    setCurrentMLSGeoJSON,
    setCurrentNationalOperatorsGeoJSON,
    setCurrentHotPadsGeoJSON,
    setCurrentRealtorDotComGeoJSON,
    setSelectedRowKeysMLSLease,
    setSelectedRowKeysMLSSale,
    setSelectedRowKeysNationalOperators,
    setSelectedRowKeysHotPads,
    setSelectedRowKeysRealtorDotCom,
    searchingMode,
  } = useMarketplaceMapContext();

  const combinedContainer = useRef<HTMLDivElement>(null);

  const [combinedDataForRender, setCombinedDataForRender] = useState<CombinedCompData[]>([]);
  const [prevFilteredComps, setPrevFilteredComps] = useState<CombinedCompData[]>([]);
  const [prevGeoJSON, setPrevGeoJSON] = useState<Record<string, unknown> | null>(null);

  // Use all the hooks
  const mlsComps = useMLSComps();
  const sfrComps = useSFRComps();
  const hotPadsComps = useHotPadsComps();
  const realtorDotComComps = useRealtorDotComComps();

  // Combine all data with unified id property
  const allComps: (CombinedCompData & { unifiedId: string })[] = [
    ...mlsComps.filteredComps.map(comp => ({ ...comp, unifiedId: comp.mlsid })),
    ...sfrComps.filteredComps.map(comp => ({ ...comp, unifiedId: comp.base_id })),
    ...hotPadsComps.filteredComps.map(comp => ({ ...comp, unifiedId: comp.base_id })),
    ...realtorDotComComps.filteredComps.map(comp => ({ ...comp, unifiedId: comp.id })),
  ];

  // Combine all GeoJSON data
  const allGeoJSON = {
    mls: mlsComps.geoJSON,
    sfr: sfrComps.geoJSON,
    hotPads: hotPadsComps.geoJSON,
    realtorDotCom: realtorDotComComps.geoJSON,
  };

  // Check if any data is loading
  const isLoading = mlsComps.loading || sfrComps.loading || hotPadsComps.loading || realtorDotComComps.loading;

  useEffect(() => {
    if (!isEqual(prevGeoJSON, allGeoJSON)) {
      // Update all GeoJSON layers
      if (allGeoJSON.mls) {
        setCurrentMLSGeoJSON(allGeoJSON.mls);
      }
      if (allGeoJSON.sfr) {
        setCurrentNationalOperatorsGeoJSON(allGeoJSON.sfr);
      }
      if (allGeoJSON.hotPads) {
        setCurrentHotPadsGeoJSON(allGeoJSON.hotPads);
      }
      if (allGeoJSON.realtorDotCom) {
        setCurrentRealtorDotComGeoJSON(allGeoJSON.realtorDotCom);
      }
      setPrevGeoJSON(allGeoJSON);
    }
  }, [allGeoJSON]);

  useEffect(() => {
    if (!isEqual(prevFilteredComps, allComps)) {
      setCombinedDataForRender(allComps);

      // Select all rows for each data source
      if (searchingMode === 'Lease') {
        setSelectedRowKeysMLSLease(mlsComps.filteredComps.map(row => row.mlsid));
        setSelectedRowKeysNationalOperators(sfrComps.filteredComps.map(row => row.base_id));
        setSelectedRowKeysHotPads(hotPadsComps.filteredComps.map(row => row.base_id));
        setSelectedRowKeysRealtorDotCom(realtorDotComComps.filteredComps.map(row => row.id));
      } else if (searchingMode === 'Sale') {
        setSelectedRowKeysMLSSale(mlsComps.filteredComps.map(row => row.mlsid));
        setSelectedRowKeysNationalOperators([]);
        setSelectedRowKeysHotPads([]);
        setSelectedRowKeysRealtorDotCom([]);
      } else {
        setSelectedRowKeysMLSLease([]);
        setSelectedRowKeysMLSSale([]);
        setSelectedRowKeysNationalOperators([]);
        setSelectedRowKeysHotPads([]);
        setSelectedRowKeysRealtorDotCom([]);
      }

      setPrevFilteredComps(allComps);
    }
  }, [allComps, searchingMode]);

  const handleRowClick = (record: CombinedCompData) => {
    if (!map) return;

    let coordinates: [number, number] | null = null;

    if ('longitude' in record && 'latitude' in record) {
      // MLS or Realtor.com data
      if (typeof record.longitude === 'number' && typeof record.latitude === 'number') {
        coordinates = [record.longitude, record.latitude];
      } else if (typeof record.longitude === 'string' && typeof record.latitude === 'string') {
        coordinates = [parseFloat(record.longitude), parseFloat(record.latitude)];
      }
    } else if ('geom' in record && record.geom?.coordinates) {
      // SFR/HotPads data
      coordinates = record.geom.coordinates as [number, number];
    }

    if (coordinates) {
      map.flyTo({
        center: coordinates,
        zoom: 16,
        speed: 2,
        curve: 1,
        easing: (t: number) => t,
      });
    }
  };

  // Custom setSelectedRowKeys function that handles the combined data
  const setSelectedRowKeys = (selectedKeys: string[]) => {
    // Separate keys by data source and update appropriate context
    const mlsKeys: string[] = [];
    const sfrKeys: string[] = [];
    const hotPadsKeys: string[] = [];
    const realtorDotComKeys: string[] = [];

    selectedKeys.forEach(key => {
      const record = allComps.find(comp => {
        if ('mlsid' in comp) return comp.mlsid === key;
        if ('base_id' in comp) return comp.base_id === key;
        if ('id' in comp) return comp.id === key;
        return false;
      });

      if (record) {
        if ('mlsid' in record) {
          mlsKeys.push(key);
        } else if ('base_id' in record) {
          if ('brand' in record && record.brand !== 'Hotpads') {
            sfrKeys.push(key);
          } else {
            hotPadsKeys.push(key);
          }
        } else if ('id' in record) {
          realtorDotComKeys.push(key);
        }
      }
    });

    if (searchingMode === 'Lease') {
      setSelectedRowKeysMLSLease(mlsKeys);
      setSelectedRowKeysNationalOperators(sfrKeys);
      setSelectedRowKeysHotPads(hotPadsKeys);
      setSelectedRowKeysRealtorDotCom(realtorDotComKeys);
    } else if (searchingMode === 'Sale') {
      setSelectedRowKeysMLSSale(mlsKeys);
      setSelectedRowKeysNationalOperators([]);
      setSelectedRowKeysHotPads([]);
      setSelectedRowKeysRealtorDotCom([]);
    }
  };

  return (
    <div
      ref={combinedContainer}
      key="Combined Table"
      className="my-4 bg-white"
    >
      <DataTable
        rowKey="unifiedId"
        columns={columns}
        data={combinedDataForRender}
        loading={isLoading}
        setSelectedRowKeys={setSelectedRowKeys}
        onRowClick={handleRowClick}
      />
    </div>
  );
};

export default TableCombined;
