"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Checkbox } from "@/components/ui/checkbox"
import { formatCurrency } from '@/lib/utils/lastSalePublicRecord'
import { formatter } from '@/lib/utils/money'
import { formatPricePerSqftArce } from '../utils/realtorDotComCompsFunctions'
import { mlsCompData, sfrAndHotPadsCompData, realtorDotComCompData } from '../utils/types'

// Union type for combined data
type CombinedCompData = mlsCompData | sfrAndHotPadsCompData | realtorDotComCompData;

// Helper function to get unified address
const getUnifiedAddress = (record: CombinedCompData): string => {
  if ('fulladdress' in record) {
    // MLS data
    return `${record.fulladdress}, ${record.city}, ${record.stateorprovince} ${record.zipcode}`;
  } else if ('address' in record && 'city' in record && 'state' in record) {
    // Realtor.com data
    return `${record.address}, ${record.city}, ${record.state} ${record.postal_code}`;
  } else if ('address' in record && 'standard_city' in record && 'standard_state' in record && 'postal_code' in record) {
    // SFR/HotPads data
    return `${record.address}, ${record.standard_city}, ${record.standard_state} ${record.postal_code}`;
  }
  return 'Address not available';
};

// Helper function to get brand/source
const getBrandOrSource = (record: CombinedCompData): string => {
  if ('brand' in record && record.brand && record.brand !== 'Hotpads') {
    // Everything with a brand (except Hotpads) that's not MLS should be "Institutional Groups"
    return 'Institutional Groups';
  } else if ('mlsid' in record) {
    return 'MLS';
  }
  return '3rd Party';
};

// Helper function to get unified price/rent
const getUnifiedPrice = (record: CombinedCompData): number => {
  if ('currentprice' in record && 'status' in record && 'closeprice' in record) {
    // MLS data
    return record.status === 'Closed' ? record.closeprice : record.currentprice;
  } else if ('rent' in record) {
    // SFR/HotPads data
    return record.rent;
  } else if ('currentprice' in record) {
    // Realtor.com data
    return Number(record.currentprice);
  }
  return 0;
};

// Helper function to get unified square footage
const getUnifiedSqft = (record: CombinedCompData): number => {
  if ('size' in record) {
    return record.size;
  } else if ('square_feet' in record) {
    return Number(record.square_feet);
  }
  return 0;
};

// Helper function to get unified beds
const getUnifiedBeds = (record: CombinedCompData): number => {
  if ('bed' in record) {
    return record.bed;
  } else if ('bed_rooms' in record) {
    return record.bed_rooms;
  } else if ('beds' in record) {
    return Number(record.beds);
  }
  return 0;
};

// Helper function to get unified baths
const getUnifiedBaths = (record: CombinedCompData): number => {
  if ('bath' in record) {
    return record.bath;
  } else if ('bath_rooms' in record) {
    return record.bath_rooms;
  } else if ('baths' in record) {
    return Number(record.baths);
  }
  return 0;
};

// Helper function to get unified closing date
const getUnifiedClosingDate = (record: CombinedCompData): string => {
  if ('closedate' in record && record.closedate) {
    return record.closedate;
  } else if ('close_date' in record && record.close_date) {
    return record.close_date;
  }
  return '';
};

// Helper function to get unified price per sqft
const getUnifiedPricePerSqft = (record: CombinedCompData): number => {
  const price = getUnifiedPrice(record);
  const sqft = getUnifiedSqft(record);
  return sqft > 0 ? price / sqft : 0;
};



export const columns: ColumnDef<CombinedCompData>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={table.getIsAllRowsSelected()}
          onCheckedChange={(value) => table.toggleAllRowsSelected(!!value)}
          aria-label="Select all"
          className="h-4 w-4"
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="h-4 w-4"
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "address",
    header: ({ column }) => (
      <div
        className="text-[12px] text-left pl-2 cursor-pointer hover:bg-gray-100 flex items-center gap-1"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Address
        {column.getIsSorted() === "asc" && <span className="text-xs">↑</span>}
        {column.getIsSorted() === "desc" && <span className="text-xs">↓</span>}
      </div>
    ),
    cell: ({ row }) => {
      const record = row.original;
      return (
        <div className="text-[12px] text-left pl-2">
          {getUnifiedAddress(record)}
        </div>
      );
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      const address = getUnifiedAddress(record).toLowerCase();
      return address.includes(String(filterValue).toLowerCase());
    },
  },
  {
    accessorKey: "distance",
    header: ({ column }) => (
      <div
        className="text-[12px] text-center cursor-pointer hover:bg-gray-100 flex items-center justify-center gap-1"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Distance
        {column.getIsSorted() === "asc" && <span className="text-xs">↑</span>}
        {column.getIsSorted() === "desc" && <span className="text-xs">↓</span>}
      </div>
    ),
    cell: ({ row }) => {
      const record = row.original;
      if ('distance' in record) {
        return <div className="text-[12px] text-center">{(record.distance / 1609.34).toFixed(2)} mi</div>;
      }
      return <div className="text-[12px] text-center">-</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      if ('distance' in record) {
        const distanceInMiles = record.distance / 1609.34;
        if (typeof filterValue === 'number') {
          return Math.abs(distanceInMiles - filterValue) < 0.01;
        } else if (Array.isArray(filterValue)) {
          const [min, max] = filterValue;
          if (min !== undefined && max !== undefined) {
            return distanceInMiles >= min && distanceInMiles <= max;
          } else if (min !== undefined) {
            return distanceInMiles >= min;
          } else if (max !== undefined) {
            return distanceInMiles <= max;
          }
        }
        return distanceInMiles === Number(filterValue);
      }
      return false;
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <div
        className="text-[12px] text-center cursor-pointer hover:bg-gray-100 flex items-center justify-center gap-1"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Status
        {column.getIsSorted() === "asc" && <span className="text-xs">↑</span>}
        {column.getIsSorted() === "desc" && <span className="text-xs">↓</span>}
      </div>
    ),
    cell: ({ row }) => {
      const record = row.original;
      if ('status' in record) {
        return <div className="text-[12px] text-center">{record.status}</div>;
      }
      return <div className="text-[12px] text-center">Active</div>;
    },
  },
  {
    accessorKey: "brand",
    header: ({ column }) => (
      <div
        className="text-[12px] text-center cursor-pointer hover:bg-gray-100 flex items-center justify-center gap-1"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Source
        {column.getIsSorted() === "asc" && <span className="text-xs">↑</span>}
        {column.getIsSorted() === "desc" && <span className="text-xs">↓</span>}
      </div>
    ),
    cell: ({ row }) => {
      const record = row.original;
      return <div className="text-[12px] text-center">{getBrandOrSource(record)}</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      let source = getBrandOrSource(record);

      // Handle operator objects (like { operator: 'notEquals', value: 'Institutional Groups' })
      if (typeof filterValue === 'object' && filterValue !== null && 'operator' in filterValue) {
        if (filterValue.operator === 'notEquals') {
          return source !== filterValue.value;
        }
      }

      // Handle direct string filtering
      if (filterValue === 'Institutional Groups') {
        // If the source is "Institutional Groups", it means it's not MLS or 3rd Party
        return source === 'Institutional Groups';
      }

      // Fix spelling for comparison
      if (source === 'Amhrest') {
        source = 'Amherst';
      }

      return source === filterValue;
    },
  },
  {
    id: "currentprice",
    accessorFn: (row) => getUnifiedPrice(row),
    header: ({ column }) => (
      <div
        className="text-[12px] text-center cursor-pointer hover:bg-gray-100 flex items-center justify-center gap-1"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Price/Rent
        {column.getIsSorted() === "asc" && <span className="text-xs">↑</span>}
        {column.getIsSorted() === "desc" && <span className="text-xs">↓</span>}
      </div>
    ),
    cell: ({ row }) => {
      const record = row.original;
      const price = getUnifiedPrice(record);
      return <div className="text-[12px] text-center">{formatCurrency(price)}</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      const price = getUnifiedPrice(record);
      console.log(`Price filter called: price=${price}, filterValue=${filterValue}, type=${typeof filterValue}`);

      // Handle different filter value types
      if (typeof filterValue === 'number') {
        const result = price === filterValue;
        console.log(`Price number comparison: ${price} === ${filterValue} = ${result}`);
        return result;
      } else if (Array.isArray(filterValue)) {
        const [min, max] = filterValue;
        if (min !== undefined && max !== undefined) {
          return price >= min && price <= max;
        } else if (min !== undefined) {
          return price >= min;
        } else if (max !== undefined) {
          return price <= max;
        }
        return true;
      }
      const result = price === Number(filterValue);
      console.log(`Price string comparison: ${price} === ${Number(filterValue)} = ${result}`);
      return result;
    },
  },
  {
    accessorKey: "propertysubtype",
    header: ({ column }) => (
      <div
        className="text-[12px] text-center cursor-pointer hover:bg-gray-100 flex items-center justify-center gap-1"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Property Type
        {column.getIsSorted() === "asc" && <span className="text-xs">↑</span>}
        {column.getIsSorted() === "desc" && <span className="text-xs">↓</span>}
      </div>
    ),
    cell: ({ row }) => {
      const record = row.original;
      if ('propertysubtype' in record) {
        return <div className="text-[12px] text-center">{record.propertysubtype || '-'}</div>;
      } else if ('propertytype' in record) {
        return <div className="text-[12px] text-center">{record.propertytype}</div>;
      }
      return <div className="text-[12px] text-center">-</div>;
    },
    filterFn: (row, columnId, filterValue: any) => {
      const record = row.original;
      let propertyType = '';

      if ('propertysubtype' in record && record.propertysubtype) {
        propertyType = record.propertysubtype;
      } else if ('propertytype' in record && record.propertytype) {
        propertyType = record.propertytype;
      }

      // Handle notEquals operator - FIXED LOGIC
      if (typeof filterValue === 'object' && filterValue?.operator === 'notEquals') {
        const isEqual = propertyType.toLowerCase() === filterValue.value.toLowerCase();
        return !isEqual; // Return the opposite for "not equals"
      }

      // Handle equals operator (default)
      return propertyType.toLowerCase() === (filterValue as string).toLowerCase();
    },
  },
  {
    accessorKey: "yearbuilt",
    header: ({ column }) => (
      <div
        className="text-[12px] text-center cursor-pointer hover:bg-gray-100 flex items-center justify-center gap-1"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Year Built
        {column.getIsSorted() === "asc" && <span className="text-xs">↑</span>}
        {column.getIsSorted() === "desc" && <span className="text-xs">↓</span>}
      </div>
    ),
    cell: ({ row }) => {
      const record = row.original;
      if ('yearbuilt' in record) {
        return <div className="text-[12px] text-center">{record.yearbuilt || '-'}</div>;
      } else if ('year_built' in record) {
        return <div className="text-[12px] text-center">{String(record.year_built)}</div>;
      }
      return <div className="text-[12px] text-center">-</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      let year = 0;
      if ('yearbuilt' in record && record.yearbuilt) {
        year = Number(record.yearbuilt);
      } else if ('year_built' in record) {
        year = Number(record.year_built);
      }

      if (typeof filterValue === 'number') {
        return year === filterValue;
      } else if (Array.isArray(filterValue)) {
        const [min, max] = filterValue;
        if (min !== undefined && max !== undefined) {
          return year >= min && year <= max;
        } else if (min !== undefined) {
          return year >= min;
        } else if (max !== undefined) {
          return year <= max;
        }
      }
      return year === Number(filterValue);
    },
  },
  {
    id: "beds",
    accessorFn: (row) => getUnifiedBeds(row),
    header: ({ column }) => (
      <div
        className="text-[12px] text-center cursor-pointer hover:bg-gray-100 flex items-center justify-center gap-1"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Bed
        {column.getIsSorted() === "asc" && <span className="text-xs">↑</span>}
        {column.getIsSorted() === "desc" && <span className="text-xs">↓</span>}
      </div>
    ),
    cell: ({ row }) => {
      const record = row.original;
      return <div className="text-[12px] text-center">{getUnifiedBeds(record)}</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      const beds = getUnifiedBeds(record);

      // Skip filter if filterValue is invalid
      if (filterValue === undefined || filterValue === null || filterValue === '') {
        return true;
      }

      // Handle operator objects (for future extensibility)
      if (typeof filterValue === 'object' && filterValue?.operator) {
        // Currently no special operator objects for beds filter
        return true;
      }

      if (typeof filterValue === 'number') {
        // Skip if NaN
        if (isNaN(filterValue)) {
          console.warn(`Beds filter: Invalid number value ${filterValue}, skipping filter`);
          return true;
        }
        return beds === filterValue;
      } else if (Array.isArray(filterValue)) {
        const [min, max] = filterValue;
        if (min !== undefined && max !== undefined) {
          // Skip if either value is NaN
          if (isNaN(min) || isNaN(max)) {
            console.warn(`Beds filter: Invalid range values [${min}, ${max}], skipping filter`);
            return true;
          }
          return beds >= min && beds <= max;
        } else if (min !== undefined) {
          if (isNaN(min)) {
            console.warn(`Beds filter: Invalid min value ${min}, skipping filter`);
            return true;
          }
          return beds >= min;
        } else if (max !== undefined) {
          if (isNaN(max)) {
            console.warn(`Beds filter: Invalid max value ${max}, skipping filter`);
            return true;
          }
          return beds <= max;
        }
      }

      // Handle string values
      const numValue = Number(filterValue);
      if (isNaN(numValue)) {
        console.warn(`Beds filter: Cannot convert "${filterValue}" to number, skipping filter`);
        return true;
      }
      return beds === numValue;
    },
  },
  {
    id: "baths",
    accessorFn: (row) => getUnifiedBaths(row),
    header: ({ column }) => (
      <div
        className="text-[12px] text-center cursor-pointer hover:bg-gray-100 flex items-center justify-center gap-1"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Bath
        {column.getIsSorted() === "asc" && <span className="text-xs">↑</span>}
        {column.getIsSorted() === "desc" && <span className="text-xs">↓</span>}
      </div>
    ),
    cell: ({ row }) => {
      const record = row.original;
      return <div className="text-[12px] text-center">{getUnifiedBaths(record)}</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      const baths = getUnifiedBaths(record);
      if (typeof filterValue === 'number') {
        return baths === filterValue;
      } else if (Array.isArray(filterValue)) {
        const [min, max] = filterValue;
        if (min !== undefined && max !== undefined) {
          return baths >= min && baths <= max;
        } else if (min !== undefined) {
          return baths >= min;
        } else if (max !== undefined) {
          return baths <= max;
        }
      }
      return baths === Number(filterValue);
    },
  },
  {
    id: "sqft",
    accessorFn: (row) => getUnifiedSqft(row),
    header: ({ column }) => (
      <div
        className="text-[12px] text-center cursor-pointer hover:bg-gray-100 flex items-center justify-center gap-1"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Sq Ft
        {column.getIsSorted() === "asc" && <span className="text-xs">↑</span>}
        {column.getIsSorted() === "desc" && <span className="text-xs">↓</span>}
      </div>
    ),
    cell: ({ row }) => {
      const record = row.original;
      const sqft = getUnifiedSqft(record);
      return <div className="text-[12px] text-center">{formatter(sqft)}</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      const sqft = getUnifiedSqft(record);
      if (typeof filterValue === 'number') {
        return sqft === filterValue;
      } else if (Array.isArray(filterValue)) {
        const [min, max] = filterValue;
        if (min !== undefined && max !== undefined) {
          return sqft >= min && sqft <= max;
        } else if (min !== undefined) {
          return sqft >= min;
        } else if (max !== undefined) {
          return sqft <= max;
        }
      }
      return sqft === Number(filterValue);
    },
  },
  {
    accessorKey: "cdom",
    header: ({ column }) => (
      <div
        className="text-[12px] text-center cursor-pointer hover:bg-gray-100 flex items-center justify-center gap-1"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        CDOM
        {column.getIsSorted() === "asc" && <span className="text-xs">↑</span>}
        {column.getIsSorted() === "desc" && <span className="text-xs">↓</span>}
      </div>
    ),
    cell: ({ row }) => {
      const record = row.original;
      if ('cdom' in record) {
        return <div className="text-[12px] text-center">{String(record.cdom)}</div>;
      } else if ('dom' in record) {
        return <div className="text-[12px] text-center">{String(record.dom)}</div>;
      }
      return <div className="text-[12px] text-center">-</div>;
    },
  },
  {
    id: "closing_date",
    accessorFn: (row) => getUnifiedClosingDate(row),
    header: ({ column }) => (
      <div
        className="text-[12px] text-center cursor-pointer hover:bg-gray-100 flex items-center justify-center gap-1"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Closing Date
        {column.getIsSorted() === "asc" && <span className="text-xs">↑</span>}
        {column.getIsSorted() === "desc" && <span className="text-xs">↓</span>}
      </div>
    ),
    cell: ({ row }) => {
      const record = row.original;
      if ('closedate' in record) {
        return <div className="text-[12px] text-center">{record.closedate || 'N/A'}</div>;
      } else if ('close_date' in record) {
        return <div className="text-[12px] text-center">{record.close_date}</div>;
      }
      return <div className="text-[12px] text-center">-</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      let dateStr = '';

      if ('closedate' in record && record.closedate) {
        dateStr = record.closedate;
      } else if ('close_date' in record && record.close_date) {
        dateStr = record.close_date;
      }

      if (!dateStr || dateStr === 'N/A') return false;

      // Parse the record date
      const recordDate = new Date(dateStr);
      if (isNaN(recordDate.getTime())) return false;

      // Handle different filter types
      if (typeof filterValue === 'object' && filterValue?.operator) {
        const { operator, value } = filterValue;

        switch (operator) {
          case 'before':
            const beforeDate = new Date(value);
            return recordDate < beforeDate;
          case 'after':
            const afterDate = new Date(value);
            return recordDate > afterDate;
          case 'withinLastDays':
            const daysAgo = parseInt(value);
            if (isNaN(daysAgo)) return false;
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysAgo);
            return recordDate >= cutoffDate;
          default:
            return true;
        }
      } else if (Array.isArray(filterValue)) {
        // Handle between dates
        const [startDate, endDate] = filterValue;
        if (startDate && endDate) {
          const start = new Date(startDate);
          const end = new Date(endDate);
          return recordDate >= start && recordDate <= end;
        } else if (startDate) {
          const start = new Date(startDate);
          return recordDate >= start;
        } else if (endDate) {
          const end = new Date(endDate);
          return recordDate <= end;
        }
      } else if (typeof filterValue === 'string') {
        // Handle direct date comparison
        const filterDate = new Date(filterValue);
        if (!isNaN(filterDate.getTime())) {
          return recordDate.toDateString() === filterDate.toDateString();
        }
      }

      return true;
    },
  },
  {
    id: "price_per_sqft",
    accessorFn: (row) => getUnifiedPricePerSqft(row),
    header: ({ column }) => (
      <div
        className="text-[12px] text-center cursor-pointer hover:bg-gray-100 flex items-center justify-center gap-1"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        $/Sqft
        {column.getIsSorted() === "asc" && <span className="text-xs">↑</span>}
        {column.getIsSorted() === "desc" && <span className="text-xs">↓</span>}
      </div>
    ),
    cell: ({ row }) => {
      const record = row.original;
      const price = getUnifiedPrice(record);
      const sqft = getUnifiedSqft(record);
      const pps = sqft > 0 ? price / sqft : 0;
      return <div className="text-[12px] text-center">{pps ? formatPricePerSqftArce(pps) : 'N/A'}</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      const price = getUnifiedPrice(record);
      const sqft = getUnifiedSqft(record);
      const pps = sqft > 0 ? price / sqft : 0;

      if (typeof filterValue === 'number') {
        return Math.abs(pps - filterValue) < 0.01;
      } else if (Array.isArray(filterValue)) {
        const [min, max] = filterValue;
        if (min !== undefined && max !== undefined) {
          return pps >= min && pps <= max;
        } else if (min !== undefined) {
          return pps >= min;
        } else if (max !== undefined) {
          return pps <= max;
        }
      }
      return Math.abs(pps - Number(filterValue)) < 0.01;
    },
  },
];