"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Checkbox } from "@/components/ui/checkbox"
import { formatCurrency } from '@/lib/utils/lastSalePublicRecord'
import { formatter } from '@/lib/utils/money'
import { formatPricePerSqftArce } from '../utils/realtorDotComCompsFunctions'
import { mlsCompData, sfrAndHotPadsCompData, realtorDotComCompData } from '../utils/types'

// Union type for combined data
type CombinedCompData = mlsCompData | sfrAndHotPadsCompData | realtorDotComCompData;

// Helper function to get unified address
const getUnifiedAddress = (record: CombinedCompData): string => {
  if ('fulladdress' in record) {
    // MLS data
    return `${record.fulladdress}, ${record.city}, ${record.stateorprovince} ${record.zipcode}`;
  } else if ('address' in record && 'city' in record && 'state' in record) {
    // Realtor.com data
    return `${record.address}, ${record.city}, ${record.state} ${record.postal_code}`;
  } else if ('address' in record && 'standard_city' in record && 'standard_state' in record && 'postal_code' in record) {
    // SFR/HotPads data
    return `${record.address}, ${record.standard_city}, ${record.standard_state} ${record.postal_code}`;
  }
  return 'Address not available';
};

// Helper function to get brand/source
const getBrandOrSource = (record: CombinedCompData): string => {
  if ('brand' in record && record.brand && record.brand !== 'Hotpads') {
    return record.brand;
  } else if ('mlsid' in record) {
    return 'MLS'; 
  }
  return '3rd Party';
};

// Helper function to get unified price/rent
const getUnifiedPrice = (record: CombinedCompData): number => {
  if ('currentprice' in record && 'status' in record && 'closeprice' in record) {
    // MLS data
    return record.status === 'Closed' ? record.closeprice : record.currentprice;
  } else if ('rent' in record) {
    // SFR/HotPads data
    return record.rent;
  } else if ('currentprice' in record) {
    // Realtor.com data
    return Number(record.currentprice);
  }
  return 0;
};

// Helper function to get unified square footage
const getUnifiedSqft = (record: CombinedCompData): number => {
  if ('size' in record) {
    return record.size;
  } else if ('square_feet' in record) {
    return Number(record.square_feet);
  }
  return 0;
};

// Helper function to get unified beds
const getUnifiedBeds = (record: CombinedCompData): number => {
  if ('bed' in record) {
    return record.bed;
  } else if ('bed_rooms' in record) {
    return record.bed_rooms;
  } else if ('beds' in record) {
    return Number(record.beds);
  }
  return 0;
};

// Helper function to get unified baths
const getUnifiedBaths = (record: CombinedCompData): number => {
  if ('bath' in record) {
    return record.bath;
  } else if ('bath_rooms' in record) {
    return record.bath_rooms;
  } else if ('baths' in record) {
    return Number(record.baths);
  }
  return 0;
};



export const columns: ColumnDef<CombinedCompData>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={table.getIsAllRowsSelected()}
          onCheckedChange={(value) => table.toggleAllRowsSelected(!!value)}
          aria-label="Select all"
          className="h-4 w-4"
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="h-4 w-4"
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "address",
    header: () => <div className="text-[12px] text-left pl-2">Address</div>,
    cell: ({ row }) => {
      const record = row.original;
      return (
        <div className="text-[12px] text-left pl-2">
          {getUnifiedAddress(record)}
        </div>
      );
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      const address = getUnifiedAddress(record).toLowerCase();
      return address.includes(String(filterValue).toLowerCase());
    },
  },
  {
    accessorKey: "distance",
    header: () => <div className="text-[12px] text-center">Distance</div>,
    cell: ({ row }) => {
      const record = row.original;
      if ('distance' in record) {
        return <div className="text-[12px] text-center">{(record.distance / 1609.34).toFixed(2)} mi</div>;
      }
      return <div className="text-[12px] text-center">-</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      if ('distance' in record) {
        const distanceInMiles = record.distance / 1609.34;
        if (typeof filterValue === 'number') {
          return Math.abs(distanceInMiles - filterValue) < 0.01;
        } else if (Array.isArray(filterValue)) {
          const [min, max] = filterValue;
          if (min !== undefined && max !== undefined) {
            return distanceInMiles >= min && distanceInMiles <= max;
          } else if (min !== undefined) {
            return distanceInMiles >= min;
          } else if (max !== undefined) {
            return distanceInMiles <= max;
          }
        }
        return distanceInMiles === Number(filterValue);
      }
      return false;
    },
  },
  {
    accessorKey: "status",
    header: () => <div className="text-[12px] text-center">Status</div>,
    cell: ({ row }) => {
      const record = row.original;
      if ('status' in record) {
        return <div className="text-[12px] text-center">{record.status}</div>;
      }
      return <div className="text-[12px] text-center">Active</div>;
    },
  },
  {
    accessorKey: "brand",
    header: () => <div className="text-[12px] text-center">Source</div>,
    cell: ({ row }) => {
      const record = row.original;
      return <div className="text-[12px] text-center">{getBrandOrSource(record)}</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      const source = getBrandOrSource(record);
      return source === filterValue;
    },
  },
  {
    accessorKey: "currentprice",
    header: () => <div className="text-[12px] text-center">Price/Rent</div>,
    cell: ({ row }) => {
      const record = row.original;
      const price = getUnifiedPrice(record);
      return <div className="text-[12px] text-center">{formatCurrency(price)}</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      const price = getUnifiedPrice(record);
      console.log(`Price filter called: price=${price}, filterValue=${filterValue}, type=${typeof filterValue}`);
      
      // Handle different filter value types
      if (typeof filterValue === 'number') {
        const result = price === filterValue;
        console.log(`Price number comparison: ${price} === ${filterValue} = ${result}`);
        return result;
      } else if (Array.isArray(filterValue)) {
        const [min, max] = filterValue;
        if (min !== undefined && max !== undefined) {
          return price >= min && price <= max;
        } else if (min !== undefined) {
          return price >= min;
        } else if (max !== undefined) {
          return price <= max;
        }
        return true;
      }
      const result = price === Number(filterValue);
      console.log(`Price string comparison: ${price} === ${Number(filterValue)} = ${result}`);
      return result;
    },
  },
  {
    accessorKey: "propertysubtype",
    header: () => <div className="text-[12px] text-center">Property Type</div>,
    cell: ({ row }) => {
      const record = row.original;
      if ('propertysubtype' in record) {
        return <div className="text-[12px] text-center">{record.propertysubtype || '-'}</div>;
      } else if ('propertytype' in record) {
        return <div className="text-[12px] text-center">{record.propertytype}</div>;
      }
      return <div className="text-[12px] text-center">-</div>;
    },
    filterFn: (row, columnId, filterValue: any) => {
      const record = row.original;
      let propertyType = '';
      
      if ('propertysubtype' in record && record.propertysubtype) {
        propertyType = record.propertysubtype;
      } else if ('propertytype' in record && record.propertytype) {
        propertyType = record.propertytype;
      }
      
      // Handle notEquals operator
      if (typeof filterValue === 'object' && filterValue?.operator === 'notEquals') {
        return propertyType.toLowerCase() !== filterValue.value.toLowerCase();
      }
      
      // Handle equals operator (default)
      return propertyType.toLowerCase() === (filterValue as string).toLowerCase();
    },
  },
  {
    accessorKey: "yearbuilt",
    header: () => <div className="text-[12px] text-center">Year Built</div>,
    cell: ({ row }) => {
      const record = row.original;
      if ('yearbuilt' in record) {
        return <div className="text-[12px] text-center">{record.yearbuilt || '-'}</div>;
      } else if ('year_built' in record) {
        return <div className="text-[12px] text-center">{String(record.year_built)}</div>;
      }
      return <div className="text-[12px] text-center">-</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      let year = 0;
      if ('yearbuilt' in record && record.yearbuilt) {
        year = Number(record.yearbuilt);
      } else if ('year_built' in record) {
        year = Number(record.year_built);
      }
      
      if (typeof filterValue === 'number') {
        return year === filterValue;
      } else if (Array.isArray(filterValue)) {
        const [min, max] = filterValue;
        if (min !== undefined && max !== undefined) {
          return year >= min && year <= max;
        } else if (min !== undefined) {
          return year >= min;
        } else if (max !== undefined) {
          return year <= max;
        }
      }
      return year === Number(filterValue);
    },
  },
  {
    accessorKey: "beds",
    header: () => <div className="text-[12px] text-center">Beds</div>,
    cell: ({ row }) => {
      const record = row.original;
      return <div className="text-[12px] text-center">{getUnifiedBeds(record)}</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      const beds = getUnifiedBeds(record);
      
      // Skip filter if filterValue is invalid
      if (filterValue === undefined || filterValue === null || filterValue === '') {
        return true;
      }
      
      if (typeof filterValue === 'number') {
        // Skip if NaN
        if (isNaN(filterValue)) {
          console.warn(`Beds filter: Invalid number value ${filterValue}, skipping filter`);
          return true;
        }
        return beds === filterValue;
      } else if (Array.isArray(filterValue)) {
        const [min, max] = filterValue;
        if (min !== undefined && max !== undefined) {
          // Skip if either value is NaN
          if (isNaN(min) || isNaN(max)) {
            console.warn(`Beds filter: Invalid range values [${min}, ${max}], skipping filter`);
            return true;
          }
          return beds >= min && beds <= max;
        } else if (min !== undefined) {
          if (isNaN(min)) {
            console.warn(`Beds filter: Invalid min value ${min}, skipping filter`);
            return true;
          }
          return beds >= min;
        } else if (max !== undefined) {
          if (isNaN(max)) {
            console.warn(`Beds filter: Invalid max value ${max}, skipping filter`);
            return true;
          }
          return beds <= max;
        }
      }
      
      // Handle string values
      const numValue = Number(filterValue);
      if (isNaN(numValue)) {
        console.warn(`Beds filter: Cannot convert "${filterValue}" to number, skipping filter`);
        return true;
      }
      return beds === numValue;
    },
  },
  {
    accessorKey: "baths",
    header: () => <div className="text-[12px] text-center">Baths</div>,
    cell: ({ row }) => {
      const record = row.original;
      return <div className="text-[12px] text-center">{getUnifiedBaths(record)}</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      const baths = getUnifiedBaths(record);
      if (typeof filterValue === 'number') {
        return baths === filterValue;
      } else if (Array.isArray(filterValue)) {
        const [min, max] = filterValue;
        if (min !== undefined && max !== undefined) {
          return baths >= min && baths <= max;
        } else if (min !== undefined) {
          return baths >= min;
        } else if (max !== undefined) {
          return baths <= max;
        }
      }
      return baths === Number(filterValue);
    },
  },
  {
    accessorKey: "sqft",
    header: () => <div className="text-[12px] text-center">Sq Ft</div>,
    cell: ({ row }) => {
      const record = row.original;
      const sqft = getUnifiedSqft(record);
      return <div className="text-[12px] text-center">{formatter(sqft)}</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      const sqft = getUnifiedSqft(record);
      if (typeof filterValue === 'number') {
        return sqft === filterValue;
      } else if (Array.isArray(filterValue)) {
        const [min, max] = filterValue;
        if (min !== undefined && max !== undefined) {
          return sqft >= min && sqft <= max;
        } else if (min !== undefined) {
          return sqft >= min;
        } else if (max !== undefined) {
          return sqft <= max;
        }
      }
      return sqft === Number(filterValue);
    },
  },
  {
    accessorKey: "cdom",
    header: () => <div className="text-[12px] text-center">CDOM</div>,
    cell: ({ row }) => {
      const record = row.original;
      if ('cdom' in record) {
        return <div className="text-[12px] text-center">{record.cdom}</div>;
      } else if ('dom' in record) {
        return <div className="text-[12px] text-center">{record.dom}</div>;
      }
      return <div className="text-[12px] text-center">-</div>;
    },
  },
  {
    accessorKey: "closing_date",
    header: () => <div className="text-[12px] text-center">Closing Date</div>,
    cell: ({ row }) => {
      const record = row.original;
      if ('closedate' in record) {
        return <div className="text-[12px] text-center">{record.closedate || 'N/A'}</div>;
      } else if ('close_date' in record) {
        return <div className="text-[12px] text-center">{record.close_date}</div>;
      }
      return <div className="text-[12px] text-center">-</div>;
    },
  },
  {
    accessorKey: "price_per_sqft",
    header: () => <div className="text-[12px] text-center">$/Sqft</div>,
    cell: ({ row }) => {
      const record = row.original;
      const price = getUnifiedPrice(record);
      const sqft = getUnifiedSqft(record);
      const pps = sqft > 0 ? price / sqft : 0;
      return <div className="text-[12px] text-center">{pps ? formatPricePerSqftArce(pps) : 'N/A'}</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      const record = row.original;
      const price = getUnifiedPrice(record);
      const sqft = getUnifiedSqft(record);
      const pps = sqft > 0 ? price / sqft : 0;
      
      if (typeof filterValue === 'number') {
        return Math.abs(pps - filterValue) < 0.01;
      } else if (Array.isArray(filterValue)) {
        const [min, max] = filterValue;
        if (min !== undefined && max !== undefined) {
          return pps >= min && pps <= max;
        } else if (min !== undefined) {
          return pps >= min;
        } else if (max !== undefined) {
          return pps <= max;
        }
      }
      return Math.abs(pps - Number(filterValue)) < 0.01;
    },
  },
];