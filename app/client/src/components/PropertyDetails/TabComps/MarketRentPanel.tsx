import React from 'react';
import SegmentedSwitch from '../../Common/SegmentedSwitch';
import ActionButton from '../../Common/ActionButton';

interface MarketRentPanelProps {
  estimatedMarketRent: string;
  viewOptions: string[];
  currentViewMode: string;
  onViewModeChange: (option: string) => void;
  onExportClick: () => void;
  className?: string;
}

const MarketRentPanel: React.FC<MarketRentPanelProps> = ({
  estimatedMarketRent,
  viewOptions,
  currentViewMode,
  onViewModeChange,
  onExportClick,
  className = '',
}) => {
  return (
    <div className={`flex justify-between items-center flex-wrap gap-4 ${className}`}>
      <div className="p-4 text-[12px] rounded border border-[var(--color-light-gray)] text-[var(--color-text-black)]">
        Estimated Market Rent: <strong className="text-[17px] mx-1">{estimatedMarketRent}</strong>
      </div>

      <div className="flex flex-col items-end gap-2.5 flex-wrap">
        <SegmentedSwitch
          options={viewOptions}
          selectedOption={currentViewMode.charAt(0).toUpperCase() + currentViewMode.slice(1)}
          onClick={onViewModeChange}
          className="h-[36px]"
        />
        <ActionButton text="Export to CSV" onClick={onExportClick} className="text-[var(--color-button-blue)]" />
      </div>
    </div>
  );
};

export default MarketRentPanel;