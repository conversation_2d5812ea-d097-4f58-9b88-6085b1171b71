import React from 'react';

interface TableSummaryProps {
  medianRent: number | string;
  totalSelected: number;
  showSummary: boolean;
  formatter: (value: number | string | null | undefined) => string;
  className?: string;
}

const TableSummary: React.FC<TableSummaryProps> = ({
  medianRent,
  totalSelected,
  showSummary,
  formatter,
  className = ''
}) => {
  if (!showSummary) return null;

  return (
    <div className={`flex items-center gap-4 text-[10px] text-[var(--color-text-black)] py-1.5 px-3 ${className}`}>
      <span>Median Rent of Selected: {formatter(medianRent)}</span>
      <div className="border-l border-[var(--color-light-gray)] h-3"></div>
      <span>Total: {totalSelected}</span>
    </div>
  );
};

export default TableSummary;