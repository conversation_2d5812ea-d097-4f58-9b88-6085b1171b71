import { useRealtorDotComComps } from '../hooks/useRealtorDotComComps';
import { formatter } from '@/lib/utils/money';
import { useEffect, useRef, useState } from 'react';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { useCompsContext } from '@/contexts/CompsContext';
import { realtorDotComCompData } from '../utils/types';
import { columns } from './columns';
import { DataTable } from '../components/data-table';
import { isEqual } from 'lodash';

const RealtorDotComTable = () => {
  const {
    map,
    setCurrentRealtorDotComGeoJSON,
    setSelectedRowKeysRealtorDotCom
  } = useMarketplaceMapContext();


  const RMFContainer = useRef<HTMLDivElement>(null);

  const [realtorDotComDataForRender, setRealtorDotComDataForRender] = useState<realtorDotComCompData[]>([]);
  const [prevFilteredComps, setPrevFilteredComps] = useState<realtorDotComCompData[]>([]);
  const [prevGeoJSON, setPrevGeoJSON] = useState<Record<string, unknown> | null>(null);

  const { loading, filteredComps, geoJSON } = useRealtorDotComComps();

  useEffect(() => {
    if (!isEqual(prevGeoJSON, geoJSON)) {
      if (geoJSON) {
        setCurrentRealtorDotComGeoJSON(geoJSON);
      }
      setPrevGeoJSON(geoJSON);
    }
  }, [geoJSON]);

  useEffect(() => {
    if (!isEqual(prevFilteredComps, filteredComps)) {
      setRealtorDotComDataForRender(filteredComps);
      // select all rows
      setSelectedRowKeysRealtorDotCom(filteredComps.map(row => row.id));
      setPrevFilteredComps(filteredComps);
    }
  }, [filteredComps]);

  const handleRowClick = (record: realtorDotComCompData) => {
    if (map) {
      map.flyTo({
        center: [parseFloat(record.longitude), parseFloat(record.latitude)],
        zoom: 16,
        speed: 2,
        curve: 1,
        easing: (t: number) => t,
      });
    }
  };

  return (
    <div
      ref={RMFContainer}
      key="RealtorDotCom Table"
      className="my-8 bg-white"
    >
      {/* add table title */}
      <div className="pl-1.5 pb-2 font-bold text-left">
        Secondary Portal Listings
      </div>
      <DataTable
        rowKey="id"
        columns={columns}
        data={realtorDotComDataForRender as realtorDotComCompData[]}
        loading={loading}
        setSelectedRowKeys={setSelectedRowKeysRealtorDotCom}
        onRowClick={handleRowClick}
      />
      
      {/* Summary section */}
      {/* <div className="px-4 py-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">
            Median Rent of Selected
          </span>
        </div>
      </div> */}
    </div>
  );
};

export default RealtorDotComTable;
