"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Checkbox } from "@/components/ui/checkbox"
import { formatCurrency } from '@/lib/utils/lastSalePublicRecord'
import { formatter } from '@/lib/utils/money'
import { formatPricePerSqftArce } from '../utils/realtorDotComCompsFunctions'
import { realtorDotComCompData } from '../utils/types'

export const columns: ColumnDef<realtorDotComCompData>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={table.getIsAllRowsSelected()}
          onCheckedChange={(value) => table.toggleAllRowsSelected(!!value)}
          aria-label="Select all"
          className="h-4 w-4"
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="h-4 w-4"
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: () => <div className="text-[12px] text-left pl-2">Address</div>,
    cell: ({ row }) => {
      const record = row.original
      return (
        <div className="text-[12px] text-left pl-2">
          {record.address}, {record.city}, {record.state} {record.postal_code}
        </div>
      )
    },
  },
  {
    accessorKey: "distance",
    header: () => <div className="text-[12px] text-center">Dist.</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{(record.distance / 1609.34).toFixed(2)} mi</div>
    },
  },
  {
    accessorKey: "status",
    header: () => <div className="text-[12px] text-center">Status</div>,
    cell: () => <div className="text-[12px] text-center">Closed</div>,
  },
  {
    accessorKey: "currentprice",
    header: () => <div className="text-[12px] text-center">Price</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{formatCurrency(record.currentprice)}</div>
    },
  },
  {
    accessorKey: "propertytype",
    header: () => <div className="text-[12px] text-center">Type</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.propertytype}</div>
    },
  },
  {
    accessorKey: "beds",
    header: () => <div className="text-[12px] text-center">Beds</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.beds}</div>
    },
  },
  {
    accessorKey: "baths",
    header: () => <div className="text-[12px] text-center">Baths</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.baths}</div>
    },
  },
  {
    accessorKey: "listDate",
    header: () => <div className="text-[12px] text-center">Listed</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.listeddate}</div>
    },
  },
  {
    accessorKey: "pricePerSqft",
    header: () => <div className="text-[12px] text-center">$/Sqft</div>,
    cell: ({ row }) => {
      const record = row.original
      const price = Number(record.currentprice)
      const pps = Number(record.square_feet) > 0 ? price / Number(record.square_feet) : 0
      return <div className="text-[12px] text-center">{pps ? formatPricePerSqftArce(pps) : 'N/A'}</div>
    },
  },
  {
    accessorKey: "square_feet",
    header: () => <div className="text-[12px] text-center">Sq Ft</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{formatter(record.square_feet)}</div>
    },
  },
  {
    accessorKey: "year_built",
    header: () => <div className="text-[12px] text-center">Built</div>,
    cell: ({ row }) => {
      const record = row.original
      return <div className="text-[12px] text-center">{record.year_built}</div>
    },
  },
] 