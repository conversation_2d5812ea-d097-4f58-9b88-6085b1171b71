import React from 'react';
import { PropertyDetails } from '@/lib/utils/types';
import TableCombined from './CombinedTable/TableCombined';
import { useSmartDefaultFilters } from './hooks/useSmartDefaultFilters';


interface TabCompsProps {
  subjectProperty: PropertyDetails;
}

const TabComps: React.FC<TabCompsProps> = ({ subjectProperty }) => {
  // Initialize smart default filters based on subject property
  const { smartFilterInfo, enableSmartFilters, disableSmartFilters } = useSmartDefaultFilters();

  const toggleFilterMode = () => {
    if (smartFilterInfo.enabled) {
      // Switch to manual mode
      disableSmartFilters();
    } else {
      // Switch to smart mode
      enableSmartFilters();
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <TableCombined
        smartFilterInfo={smartFilterInfo}
        onToggleFilterMode={toggleFilterMode}
      />
    </div>
  );
};

export default TabComps;