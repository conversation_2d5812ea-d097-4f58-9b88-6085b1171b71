import React from 'react';
import { PropertyDetails } from '@/lib/utils/types';
import TableCombined from './CombinedTable/TableCombined';
import EstMonthlyRentInput from './EstMonthlyRentInput';

interface TabCompsProps {
  subjectProperty: PropertyDetails;
}

const TabComps: React.FC<TabCompsProps> = ({ subjectProperty }) => {


  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <EstMonthlyRentInput />
      </div>
      <TableCombined />
    </div>
  );
};

export default TabComps;