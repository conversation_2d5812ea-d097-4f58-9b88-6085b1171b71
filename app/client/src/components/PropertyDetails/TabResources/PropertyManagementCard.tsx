import React from 'react';
import { FormattedPropertyManagement } from '../../../lib/utils/formatUtils';
import { WebsiteLink } from '@/components/Common/WebsiteLink';

interface PropertyManagementCardProps {
  company: FormattedPropertyManagement;
  onSeeMore: (company: FormattedPropertyManagement) => void;
}

const PropertyManagementCard: React.FC<PropertyManagementCardProps> = ({
  company,
  onSeeMore,
}) => {
  if (!company || !company.name) {
    return null;
  }

  return (
    <div className={`flex flex-col justify-between items-center border border-medium-gray-20 rounded-md px-2 py-3 h-full`}>
      <h3 className="text-xl mb-2 font-heading font-thin">{company.name}</h3>
      <div className="flex flex-col space-y-2 mb-2">
        <div className='flex justify-center'>

        <WebsiteLink url={company.website} />
        </div>
        <span className="text-sm">Property Management Fee: {company.pmFee}</span>
      </div>

      <button
        className="text-xs text-dark-gray hover:text-dark-gray/50  underline decoration-dark-gray hover:decoration-dark-gray/30  underline-offset-4 transition-colors  duration-200"
        onClick={() => onSeeMore(company)}
      >
        See More
      </button>
    </div>
  );
};

export default PropertyManagementCard;