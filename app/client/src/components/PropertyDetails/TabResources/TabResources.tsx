import React, { useState } from 'react'
import { FormattedPropertyManagement } from '../../../lib/utils/formatUtils';
import ModalWrapper from '../../Modal/ModalWrapper';
import PropertyManagementCard from './PropertyManagementCard';
import PropertyManagementInfo from './PropertyManagementInfo';

interface TabResourcesProps {
  data: FormattedPropertyManagement[];
}

const TabResources: React.FC<TabResourcesProps> = ({ data }) => {
  const [selectedManager, setSelectedManager] = useState<FormattedPropertyManagement | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = (company: FormattedPropertyManagement) => {
    setSelectedManager(company);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <div className="@container">
      <h3 className="text-base font-medium mb-3">Property Managers for this Market</h3>

      {data && data.length > 0 && <p className="text-sm text-dark-gray mb-6">Lennar has negotiated a set of preferred terms with leading property management firms, giving our investor buyers access to high-quality services at institutional pricing. Details on these managers and their Lennar-preferred rates are outlined here.</p>}

      {data && data.length > 0 ? (
        <div className="grid grid-cols-2 pb-100 gap-1">
          {data.map((company) => (
            <PropertyManagementCard
              key={company.name}
              company={company}
              onSeeMore={handleOpenModal}
            />
          ))}
        </div>
      ) : (
        <div className="col-span-full">
          <p className="text-left px-3 py-4 text-dark-gray">
            Preferred property management partners in this market are coming soon. We’re actively working to expand our network so investor buyers can access high-quality services and preferred pricing.
          </p>
        </div>
      )}

      {isModalOpen && selectedManager && (
        <div className="fixed inset-0 z-300 flex items-center justify-center bg-black/30">
          <div className="bg-white rounded-xl w-[80vw] max-w-[700px] max-h-[90dvh] overflow-auto">
            <ModalWrapper
              onClose={handleCloseModal}
              contentClassName=""
            >
              <PropertyManagementInfo data={[selectedManager]} />
            </ModalWrapper>
          </div>
        </div>
      )}

    </div>
  )
}

export default TabResources