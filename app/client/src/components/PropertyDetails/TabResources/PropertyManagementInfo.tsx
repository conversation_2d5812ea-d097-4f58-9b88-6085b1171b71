import React from 'react'
import { FormattedPropertyManagement } from '../../../lib/utils/formatUtils';
import { RESOURCES_CONFIG, RESOURCES_ORDER } from '../../../constants/tabResourcesConstants';
import { WebsiteLink } from '../../Common/WebsiteLink';
import { PhoneLink } from '../../Common/PhoneLink';
import { EmailLink } from '../../Common/EmailLink';

interface PropertyManagementInfoProps {
  data: FormattedPropertyManagement[];
}

const PropertyManagementInfo: React.FC<PropertyManagementInfoProps> = ({ data }) => {
  return (
    <>
      {data && data.length > 0 && (
        data.map((company) => (
          <section key={company.name} className="w-full px-3">
            <div className="grid grid-cols-1 gap-4">

              {/* Property Management Company info */}
              <div className="flex flex-row gap-4 col-span-1">
                {company.logo && (
                  <div className="flex justify-center h-14">
                    <img
                      src={company.logo}
                      alt={`${company.name} logo`}
                      className="h-full object-contain"
                    />
                  </div>
                )}
                <div>
                  <h3 className="text-2xl font-thin font-heading">{company.name}</h3>
                  <WebsiteLink url={company.website} />
                </div>
              </div>

              {/* Contact info */}
              <div className="flex flex-col gap-2 ml-2">
                {company.contact && <span className="inline-block text-xs">{company.contact}</span>}
                <div className="inline-block">
                  <PhoneLink phone={company.phone} />
                </div>
                <div className="inline-block">
                  <EmailLink email={company.email} />
                </div>
              </div>

              {/* Fees table */}
              <div className="col-span-1">
                <table className="w-full border-collapse">
                  <thead className="border-b border-medium-gray-20">
                    <tr>
                      <th className="text-left font-medium text-sm pb-2 w-1/2">Fees</th>
                      <th className="text-left font-medium text-sm pb-2 pl-2 w-1/2">Cost</th>
                    </tr>
                  </thead>
                  <tbody>
                    {RESOURCES_ORDER.map((feeKey) => {
                      const config = RESOURCES_CONFIG[feeKey];
                      const value = company[feeKey];

                      if (!value) {
                        return null;
                      }

                      return (
                        <tr key={feeKey}>
                          <td className="py-2 flex justify-start items-center gap-1 text-xs @md:whitespace-nowrap">
                            {config.label}
                          </td>
                          <td className="pl-2 text-xs">{value}</td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
            {/* Description */}
            <div className="mt-2">
              <p className="text-xs text-dark-gray/70">{company.description}</p>
            </div>
          </section>
        ))
      )}
    </>
  )
}

export default PropertyManagementInfo