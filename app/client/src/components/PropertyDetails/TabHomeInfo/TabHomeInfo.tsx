import React from "react";
import {
  HOME_INFO_ORDER,
  HOME_INFO_CONFIG,
  COMMUNITY_AMENITY_ORDER,
  COMMUNITY_AMENITY_CONFIG,
} from "../../../constants/tabHomeInfoConstants";
import DetailCategory from "./DetailCategory";
import { createDisplayItems } from "@/lib/utils/homeInfoUtils";

interface PropertyDetails {
  [key: string]: string | number | null;
}

interface TabHomeInfoProps {
  details: PropertyDetails;
}

const TabHomeInfo: React.FC<TabHomeInfoProps> = ({ details }) => {

  if (!details) {
    return (
      <div className="py-6 text-center italic">
        No details available for this property.
      </div>
    );
  }

  const homeInfoItems = createDisplayItems(details, HOME_INFO_ORDER, HOME_INFO_CONFIG);
  const communityAmenityItems = createDisplayItems(details, COMMUNITY_AMENITY_ORDER, COMMUNITY_AMENITY_CONFIG);
  const description = details.spec_description
    ? details.spec_description
    : details.sub_description
      ? details.sub_description
      : details.plan_description || null;

  return (
    <div className="overflow-y-auto">
      {/* Side-by-side layout using grid */}
      <div className="grid grid-cols-2 gap-6">
        {/* Home Section */}
        <section className="">
          <h2 className="text-base font-semibold">Home</h2>
          <div className="pt-2">
            {homeInfoItems.length > 0 ? (
              <>
                {homeInfoItems.map(item => (
                  <DetailCategory
                    key={item.field}
                    title={item.config.label}
                    data={item.value}
                    iconName={item.config.icon}
                  />
                ))}
              </>
            ) : (
              <div className="text-center italic py-4">No home information available</div>
            )}
          </div>
        </section>

        <div>
          {/* Community & Amenities Section */}
          <section>
            <h2 className="text-base font-semibold">Community & Amenity</h2>
            <div className="pt-2">
              {communityAmenityItems.length > 0 ? (
                <>
                  {communityAmenityItems.map(item => (
                    <DetailCategory
                      key={item.field}
                      title={item.config.label}
                      data={item.value}
                      iconName={item.config.icon}
                    />
                  ))}
                </>
              ) : (
                <div className="text-center italic py-4">No community or amenity information available</div>
              )}
            </div>

            {/* Description Section */}
            {description && (
              <section className="mt-3">
                <h2 className="text-base font-semibold">Description</h2>
                <div className="pt-2">{description}</div>
              </section>
            )}
          </section>
        </div>
      </div>
    </div>
  );
};

export default TabHomeInfo;