import React from "react";
import { getIconComponent } from "@/lib/utils/homeInfoUtils";

interface DetailCategoryProps {
  title: string;
  data?: string | number;
  iconName?: string;
}

const DetailCategory: React.FC<DetailCategoryProps> = ({ title, data, iconName }) => {
  if (!data) {
    return null;
  }

  return (
    <div className="flex justify-between items-center min-h-[36px] border-b border-medium-gray-20">
      <div className="flex items-center">
        <div className="flex-shrink-0 w-5 h-5 flex items-center justify-center">
          {getIconComponent(iconName)}
        </div>
        <span className="text-sm text-dark-gray">{title}:</span>
      </div>
      <span className="flex items-center justify-end text-right text-sm text-dark-gray">
        {data}
      </span>
    </div>
  );
};

export default DetailCategory;