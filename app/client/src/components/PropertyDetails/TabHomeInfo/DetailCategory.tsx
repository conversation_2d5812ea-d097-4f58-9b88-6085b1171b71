import React from "react";
interface DetailCategoryProps {
  title: string;
  data?: string | number;
}

const DetailCategory: React.FC<DetailCategoryProps> = ({ title, data }) => {
  if (!data) {
    return null;
  }

  return (
    <div className="flex justify-between items-center min-h-[36px] border-b border-medium-gray-20">
      <span className="text-sm text-dark-gray">{title}:</span>
      <span className="flex items-center justify-end text-right text-sm text-dark-gray">
        {data}
      </span>
    </div>
  );
};

export default DetailCategory;