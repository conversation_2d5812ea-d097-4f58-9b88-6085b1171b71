import React from 'react';
import PropertyDetailHeader from './PropertyDetailHeader.tsx';
import PropertyHighlightCard from './PropertyHighlightCard.tsx';
import TabNavigation from './TabNavigation.tsx';

interface PropertyOverviewProps {
  thumbnail: string;
  formattedPrice: string;
  address?: string;
  beds: number;
  baths: number;
  sqft: number;
  stories?: string;
  garage?: string;
  onStartOfferClick: () => void;
  thumbnailLoading?: boolean;
  thumbnailError?: boolean;
}

const PropertyOverview: React.FC<PropertyOverviewProps> = ({
  thumbnail,
  formattedPrice,
  address,
  beds,
  baths,
  sqft,
  stories,
  garage,
  onStartOfferClick,
  thumbnailLoading = false,
  thumbnailError = false,
}) => {

  return (
    <div className="flex flex-col gap-4">
      <PropertyDetailHeader onStartOfferClick={onStartOfferClick} />
      <PropertyHighlightCard
        thumbnail={thumbnail}
        formattedPrice={formattedPrice}
        thumbnailLoading={thumbnailLoading}
        thumbnailError={thumbnailError}
        address={address}
        beds={beds}
        baths={baths}
        sqft={sqft}
        stories={stories}
        garage={garage}
      />
      <TabNavigation />
    </div>
  );
};

export default PropertyOverview;