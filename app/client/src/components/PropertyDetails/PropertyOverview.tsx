import React, { useRef, useEffect } from 'react';
import { PhoneCall } from 'lucide-react';
import { useRouter } from '@tanstack/react-router';
import StarOutlineIcon from '@mui/icons-material/StarOutline';
import StarIcon from '@mui/icons-material/Star';
import { formatPhoneNumber } from "@/lib/utils/formatUtils";
import { useCart } from '@/contexts/CartContext';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import PropertyDetailHeader from './PropertyDetailHeader';
import PropertyHighlightCard from './PropertyHighlightCard';
import TabNavigation from './TabNavigation';
import MobilePropertyOverview from '../MobilePropertyOverview';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import ReturnToList from './ReturnToList';
import { useFormSubmission } from '@/contexts/FormSubmissionContext';
import { bookmarkManager } from '@/lib/utils/bookmarkManager';

interface PropertyOverviewProps {
  thumbnail: string;
  formattedPrice: string;
  address?: string;
  beds?: number;
  baths?: number;
  sqft?: number;
  stories?: string;
  garage?: string;
  thumbnailLoading?: boolean;
  thumbnailError?: boolean;
  phone?: string | null;
  decision?: string;
}

const PropertyOverview: React.FC<PropertyOverviewProps> = ({
  thumbnail,
  formattedPrice,
  address,
  beds,
  baths,
  sqft,
  stories,
  garage,
  thumbnailLoading = false,
  thumbnailError = false,
  phone = "",
  decision,
}) => {
  const { isMobile } = useBreakpoint();
  const pendingModalOpen = useRef<boolean>(false);
  const { addToCart, setIsCartModalOpen, isLoading, cart } = useCart();
  const { selectedBuyersViewRecord, proFormaAllValues, setSelectedBuyersViewRecord, setPropertyModalTabKey, setEventCoordinates, isBookmarked, setIsBookmarked } = useMarketplaceMapContext();
  const { isSubmitting, triggerFormSubmission } = useFormSubmission();
  const router = useRouter();

  const formattedPhone = formatPhoneNumber(phone);

  // Watch for cart changes and open modal when item is successfully added
  useEffect(() => {
    if (pendingModalOpen.current && selectedBuyersViewRecord?.property_id) {
      const propertyId = selectedBuyersViewRecord.property_id;
      const isNowInCart = cart?.items.some(item => item.propertyId === propertyId);

      if (isNowInCart) {
        pendingModalOpen.current = false;
        setIsCartModalOpen(true);
      }
    }
  }, [cart, selectedBuyersViewRecord, setIsCartModalOpen]);

  const handleAddToCartAndShowModal = async () => {
    if (!selectedBuyersViewRecord?.property_id) {
      return;
    }

    const propertyId = selectedBuyersViewRecord.property_id;
    const isInCart = cart?.items.some(item => item.propertyId === propertyId) || false;

    if (!isInCart) {
      try {
        // Set flag to open modal after cart state updates
        pendingModalOpen.current = true;

        // Get purchase price from Pro Forma if available
        const purchasePrice = proFormaAllValues?.["Bid Price"];
        const originalRent = proFormaAllValues?.adjustedRent;
        const buyerFinalRent = proFormaAllValues?.['Projected Monthly Rent']
        const payload = {
          subjectProperty: selectedBuyersViewRecord?.payload?.subjectProperty,
          proforma: proFormaAllValues
        }
        await addToCart(selectedBuyersViewRecord, purchasePrice, originalRent, buyerFinalRent, payload);

        // If addToCart didn't update the local state immediately, wait a bit
        setTimeout(() => {
          if (pendingModalOpen.current) {
            pendingModalOpen.current = false;
            setIsCartModalOpen(true);
          }
        }, 150);
      } catch (error) {
        console.error('Failed to add to cart:', error);
        pendingModalOpen.current = false;
        // Still show modal even if add fails so user can see the error state
        setIsCartModalOpen(true);
      }
    } else {
      // Property is already in cart, show modal immediately
      setIsCartModalOpen(true);
    }
  };

  const handleReturnToList = () => {
    setSelectedBuyersViewRecord(null);
    setPropertyModalTabKey(null);
    setEventCoordinates([NaN, NaN]);
  };

  const onClickBookmark = () => {
    // Optimistically update bookmark state immediately
    const propertyId = selectedBuyersViewRecord?.property_id;
    if (propertyId) {
      const newBookmarkState = bookmarkManager.toggleBookmark(propertyId, proFormaAllValues);
      setIsBookmarked(newBookmarkState);
      
      // Invalidate the properties route so the bookmark count updates immediately
      router.invalidate();
    }
    
    // Save to backend in background
    triggerFormSubmission();
  };

  if (isMobile) {
    return (
      <div className="flex flex-col gap-1">
        <TabNavigation />
        <div className="flex flex-row justify-between items-center mt-1">
          <ReturnToList handleReturnToList={handleReturnToList} />
          <div className="flex justify-center items-center gap-2">
            <button
              type="button"
              onClick={onClickBookmark}
              disabled={isSubmitting}
              className="px-2 flex items-center justify-center touch-manipulation cursor-pointer z-10 hover:opacity-70 transition-opacity"
              style={{ pointerEvents: 'auto' }}
            >
              {isBookmarked ? <StarIcon htmlColor='var(--color-green-primary)' style={{ fontSize: '38px' }} /> : <StarOutlineIcon htmlColor='var(--color-green-primary)' style={{ fontSize: '38px' }} />}
            </button>
            <a
              href={`tel:${formattedPhone.rawPhone}`}
              className="flex items-center justify-center rounded-full w-10 h-10 px-2 text-sm font-semibold bg-green-primary text-white hover:bg-green-primary/80 transition-colors"
              aria-label="Call agent"
            >
              <PhoneCall className="w-5 h-5" />
            </a>
          </div>
        </div>
        <MobilePropertyOverview
          formattedPrice={formattedPrice}
          address={address}
          onStartOfferClick={handleAddToCartAndShowModal}
          isLoading={isLoading}
          beds={beds}
          baths={baths}
          sqft={sqft}
          stories={stories}
          garage={garage}
        />
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <PropertyDetailHeader
        onStartOfferClick={handleAddToCartAndShowModal}
        phone={phone}
        isLoading={isLoading}
        handleReturnToList={handleReturnToList}
      />
      <PropertyHighlightCard
        thumbnail={thumbnail}
        formattedPrice={formattedPrice}
        thumbnailLoading={thumbnailLoading}
        thumbnailError={thumbnailError}
        address={address}
        beds={beds}
        baths={baths}
        sqft={sqft}
        stories={stories}
        garage={garage}
        decision={decision}
      />
      <TabNavigation />
    </div>
  );
};

export default PropertyOverview;