import React, { ReactNode } from 'react';

interface TabInsightsSectionHeaderProps {
  title: string;
  subtitle?: ReactNode;
  distance: number;
  rightContent?: ReactNode;
  className?: string;
}

const TabInsightsSectionHeader: React.FC<TabInsightsSectionHeaderProps> = ({
  title,
  subtitle,
  distance,
  rightContent,
  className = ""
}) => {
  return (
    <div className={`flex justify-between items-center w-full ${className}`}>
      <h2 className="text-left font-bold text-[17px]">
        {title}{" "}
        {subtitle || (
          <span className="font-normal">
            (within <span className="text-[var(--color-button-blue)]">{distance}</span>{" "}
            Miles)
          </span>
        )}
      </h2>
      {rightContent && <div>{rightContent}</div>}
    </div>
  );
};

export default TabInsightsSectionHeader;