import React from 'react';
import StackBar<PERSON>hart, { DataItem } from './StackBarChart';
import { calculatePercentage } from '@/lib/utils/formatUtils';
import ErrorMessage from '@/components/Common/ErrorMessage';

interface OwnershipChartProps {
  data: DataItem[] | null;
  total: number;
}

const OwnershipChart: React.FC<OwnershipChartProps> = ({
  data,
  total,
}) => {
  if (!data || !Array.isArray(data) || data.length === 0 ||
    (data[0]?.owner === 0 && data[0]?.renter === 0)) {
    return (
      <div className="flex w-full min-h-[94px] justify-center items-center">
        <ErrorMessage message="Owner vs. renter information isn't available for this location" />
      </div>
    );
  }

  const owner = data[0].owner ?? 0;
  const renter = data[0].renter ?? 0;
  const calculatedTotal = owner + renter;
  const displayTotal = calculatedTotal === total ? total : calculatedTotal;


  return (
    <>
      <div className="flex items-center w-full">
        <StackBarChart data={data} />
        <p className="w-20 text-right text-[13px] mt-1 text-[var(--color-dark-gray)] min-w-[65px]">
          Total: {total}
        </p>
      </div>
      <div className="w-full px-4">
        <div className="mt-2 text-[13px] text-gray-700">
          <div className="flex items-center mb-2">
            <div className="w-3 h-3 bg-[var(--color-button-blue)] mr-2" />
            Owner Occupied: {owner} (
            {calculatePercentage(owner, displayTotal)}
            )
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-[var(--color-light-gray)] mr-2" />
            Renter Occupied: {renter} (
            {calculatePercentage(renter, displayTotal)}
            )
          </div>
        </div>
      </div>
    </>
  );
};

export default OwnershipChart;