import React from "react";
interface RentRangeProps {
  min?: number;
  max?: number;
  onClear: () => void;
}
const RentRange: React.FC<RentRangeProps> = ({ min, max, onClear }) => {
  return (
    <div className="flex justify-between items-center rounded-full border border-[var(--color-light-gray)] px-3 py-1 bg-white h-[26px] min-w-[135px] cursor-pointer">
      <span className="text-[var(--color-text-black)] text-sm">{`$ ${min} - $ ${
        max ? max : "No Max"
      }`}</span>
      <div
        className="ml-2 text-[#FF4D4F] text-sm font-bold cursor-pointer transition-colors duration-200 hover:text-red-700"
        onClick={onClear}
        aria-label="Clear rent filter"
      >
        ×
      </div>
    </div>
  );
};

export default RentRange;
