import { useState } from "react";
import { createChartData } from "../../../lib/utils/formatUtils";
import { MarketConditionData } from "../../../hooks/useMarketConditionData";
import { calculateFinalConfig, generateTickValues, metricConfig, MetricType } from "../../../constants/marketConditions";
import SectionWrapper from "./SectionWrapper";
import Selection from "../../Common/Selection";
import MarketConditionChartPair from "./MarketConditionsChartPair";

interface MarketConditionsSectionProps {
  distance: number;
  months: number;
  currentMarketConditionData?: MarketConditionData[];
  priorMarketConditionData?: MarketConditionData[];
  isLoadingMarketConditions: boolean;
  marketConditionError: Error | null;
}

const MarketConditionsSection: React.FC<MarketConditionsSectionProps> = ({ distance, months, currentMarketConditionData, priorMarketConditionData, isLoadingMarketConditions, marketConditionError }) => {
  const [selected, setSelected] = useState<MetricType>("active");

  const handleMetricChange = (value: string | number) => {
    const stringValue = String(value);
    setSelected(stringValue as MetricType);
  };

  const currentChartData = createChartData(currentMarketConditionData, selected);
  const priorChartData = createChartData(priorMarketConditionData, selected);
  const combinedData = [...currentChartData, ...priorChartData];
  const dynamicTickValues = generateTickValues(combinedData);
  const config = calculateFinalConfig(selected, dynamicTickValues);
  const options = Object.entries(metricConfig).map(([value, config]) => ({
    value,
    label: config.label
  }));
  return (
    <SectionWrapper
      title="Market Conditions"
      distance={distance}
    >
      <div className="flex items-center p-4 w-full">
        <Selection
          label=""
          options={options}
          value={selected}
          onChange={handleMetricChange}
        />
      </div>
      <MarketConditionChartPair
        currentData={currentChartData}
        priorData={priorChartData}
        config={config}
        months={months}
        metricType={selected}
        error={marketConditionError}
        isLoading={isLoadingMarketConditions}
      />
    </SectionWrapper>
  );
};

export default MarketConditionsSection;