import React from "react";
import { PropertyDataPointWithFlag } from "@/lib/query/get-parcel-unit-mix-data";
import HorizontalBarChart from "./HorizontalBarChart";
import LoadingSpinner from "../../Common/LoadingSpinner";
import ErrorMessage from "../../Common/ErrorMessage";

interface CompositionChartsProps {
  bedroomData: PropertyDataPointWithFlag[] | undefined;
  bathroomData: PropertyDataPointWithFlag[] | undefined;
  squareFeetData: PropertyDataPointWithFlag[] | undefined;
  yearBuiltData: PropertyDataPointWithFlag[] | undefined;
  isLoading?: boolean;
}

const CompositionCharts: React.FC<CompositionChartsProps> = ({
  bedroomData,
  bathroomData,
  squareFeetData,
  yearBuiltData,
  isLoading = false,
}) => {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full min-h-[506px]">
        <LoadingSpinner />
      </div>
    );
  }

  const allEmpty =
    (!bedroomData || bedroomData.length === 0) &&
    (!bathroomData || bathroomData.length === 0) &&
    (!squareFeetData || squareFeetData.length === 0) &&
    (!yearBuiltData || yearBuiltData.length === 0);

  if (allEmpty) {
    return (
      <div className="flex justify-center items-center h-full min-h-[120px]">
        <ErrorMessage message="Details about nearby homes aren't available for this location" />
      </div>
    );
  }

  return (
    <div className="@container w-full p-4">
      <div className="grid grid-cols-1 @lg:grid-cols-2 gap-4 w-full p-4">
        <HorizontalBarChart data={bedroomData} title="Bedroom" />
        <HorizontalBarChart data={bathroomData} title="Bathroom" />
        <HorizontalBarChart data={squareFeetData} title="Square Feet" barSize={14} />
        <HorizontalBarChart data={yearBuiltData} title="Year Built" barSize={14} />
      </div>
    </div>
  );
};

export default CompositionCharts;