import React from "react";
import AddButton from "./AddButton";
import RentRange from "./RentRange";

interface RentFilterProps {
  min?: number;
  max?: number;
  onClear: () => void;
  onAdd: () => void;
}

const RentFilter: React.FC<RentFilterProps> = ({
  min = 0,
  max,
  onClear,
  onAdd,
}) => {
  return (
    <div className="flex items-center">
      <span className="text-[var(--color-text-black) text-[13px] mr-2">Rent:</span>
      <RentRange min={min} max={max} onClear={onClear} />
      <AddButton onAdd={onAdd} />
    </div>
  );
};

export default RentFilter;
