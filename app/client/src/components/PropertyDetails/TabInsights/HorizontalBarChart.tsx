import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  LabelList,
  ResponsiveContainer,
  Cell,
  CartesianGrid,
  Tooltip,
  TooltipProps,
} from "recharts";
import { formatPercentageRoundUp } from "../../../lib/utils/formatUtils";
import { PropertyDataPointWithFlag } from "../../../lib/query/get-parcel-unit-mix-data";

interface HorizontalBarChartProps {
  data?: PropertyDataPointWithFlag[];
  title?: string;
  barSize?: number;
}

const HorizontalBarChart: React.FC<HorizontalBarChartProps> = ({
  data = [],
  title,
  barSize = 20,
}) => {
  const maxValue = Math.max(...data.map(item => item.y), 1);
  let roundedMax: number;

  if (maxValue <= 5) {
    // For small values, use increments of 1
    roundedMax = Math.ceil(maxValue);
    if (roundedMax < 5) roundedMax = 5; // Minimum scale of 5
  } else if (maxValue <= 20) {
    // For medium values, round to nearest 5
    roundedMax = Math.ceil(maxValue / 5) * 5;
  } else if (maxValue <= 100) {
    // For larger values, round to nearest 20
    roundedMax = Math.ceil(maxValue / 20) * 20;
  } else {
    // For very large values, round to nearest 50
    roundedMax = Math.ceil(maxValue / 50) * 50;
  }

  roundedMax = Math.max(20, roundedMax);

  // Use provided tickValues or generate dynamic ticks based on roundedMax
  const calculatedTickValues = [
    0,
    roundedMax / 4,
    roundedMax / 2,
    (roundedMax * 3) / 4,
    roundedMax
  ];

  // Custom tooltip to show percentage and match status
  const CustomTooltip = ({ active, payload }: TooltipProps<number, string>) => {
    if (active && payload && payload.length) {
      const item = payload[0].payload;
      return (
        <div className="bg-[var(--color-white)] p-2 border border-[var(--color-light-gray)] shadow-sm rounded">
          <p className="text-small">{`${item.x}: ${item.y} homes`}</p>
          <p className="text-small text-[var(--color-dark-gray)]">{`${formatPercentageRoundUp(item.percent)} of area`}</p>
          {item.isSubjectProperty === true && (
            <p className="text-small font-medium text-[var(--color-button-blue)]">Subject property</p>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <div>
      <h2 className="text-left font-normal text-[14px] mb-2">{title}</h2>
      <ResponsiveContainer width="100%" height={200}>
        <BarChart
          data={data}
          layout="horizontal"
          barSize={barSize}
          margin={{ top: 5, right: 0, left: 0, bottom: 0 }}
        >
          <CartesianGrid />
          <XAxis dataKey="x" ticks={[]} tick={{ fontSize: 12 }} />
          <YAxis
            domain={[0, roundedMax]}
            orientation="right"
            ticks={calculatedTickValues}
            tick={{ fontSize: 12 }}
            label={{
              value: "# of Homes",
              position: "right",
              offset: -24,
              angle: 270,
              dy: 30,
              style: { fontSize: 12 },
            }}
          />
          <Tooltip content={<CustomTooltip />} cursor={{ fill: "rgba(0, 0, 0, 0.04)" }} />
          <Bar dataKey="y" radius={[1, 1, 0, 0]} barSize={barSize} >
            {data.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={entry.isSubjectProperty === true ? "#1890FF" : "#D9D9D9"}
              />
            ))}
            <LabelList
              dataKey="percent"
              position="top"
              formatter={(v: number) => `${formatPercentageRoundUp(v)}`}
              style={{ fontSize: 10 }}
              fill="#6a6a6a"
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default HorizontalBarChart;