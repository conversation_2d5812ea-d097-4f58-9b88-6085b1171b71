interface AddButtonProps {
  onAdd: () => void;
}

const AddButton: React.FC<AddButtonProps> = ({ onAdd }) => {
  return (
    <div
      className="ml-1 text-[var(--color-text-black)] hover:bg-gray-100 rounded-full w-6 h-6 flex items-center justify-center border border-[var(--color-light-gray)]"
      onClick={onAdd}
      aria-label="Add rent filter"
    >
      <span className="flex items-center justify-center w-full h-full text-sm leading-none cursor-pointer">
        +
      </span>
    </div>
  );
};
export default AddButton;
