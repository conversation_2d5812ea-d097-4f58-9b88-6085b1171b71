import React from "react";
import VerticalBar<PERSON>hart from "./VerticalBarChart";
import { MetricConfiguration, MetricType } from "@/constants/marketConditions";
import ErrorMessage from "@/components/Common/ErrorMessage";
import LoadingSpinner from "@/components/Common/LoadingSpinner";

interface ChartData {
  name: string;
  value: number;
}

interface MarketConditionChartPairProps {
  currentData: ChartData[];
  priorData: ChartData[];
  config: MetricConfiguration;
  months: number;
  metricType: MetricType;
  error: Error | null;
  isLoading: boolean;
}

const MarketConditionChartPair: React.FC<MarketConditionChartPairProps> = ({
  currentData,
  priorData,
  config,
  months,
  metricType,
  error,
  isLoading,
}) => {

  if (error) {
    return (
      <div className="min-h-[360px] flex items-center justify-center">
        <ErrorMessage message="We couldn't retrieve the Market conditions information at this time. Please try again later." />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-[360px] flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }
  return (
    <div className="grid grid-cols-2 gap-4 w-full p-4">
      <div>
        <VerticalBarChart
          data={currentData}
          title="Now"
          xAxisLabel={config.xAxisLabel}
          maxDomain={config.maxDomain}
          tickValues={config.tickValues}
          fillColor="#1890ff"
          dataKey="value"
          valuePrefix={config.valuePrefix}
          metricType={metricType}
        />
      </div>
      <div>
        <VerticalBarChart
          data={priorData}
          title={`${months} months ago`}
          xAxisLabel={config.xAxisLabel}
          maxDomain={config.maxDomain}
          tickValues={config.tickValues}
          fillColor="#e6f4ff"
          dataKey="value"
          valuePrefix={config.valuePrefix}
          metricType={metricType}
        />
      </div>
    </div>
  );
};

export default MarketConditionChartPair;