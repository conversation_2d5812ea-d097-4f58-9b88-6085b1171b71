import React, { useMemo } from "react";
import { useSearch } from "@tanstack/react-router";
import { PropertyDataPointWithFlag } from "@/lib/query/get-parcel-unit-mix-data";
import { Bedrooms, defaultMonths, FormattedCurrentDate, MAX_PRICE, MIN_PRICE, serverType, useMarketConditionData } from "../../../hooks/useMarketConditionData";
import OwnershipSection from "./OwnershipSection";
import CompositionSection from "./CompositionSection";
import MarketConditionsSection from "./MarketConditionsSection";
// import MarketConditionHistorySection from "./MarketConditionHistorySection";

interface TabInsightsProps {
  ownershipData: Array<{ name: string; owner: number; renter: number }>;
  totalParcels: number;
  distance: number;
  bedroomData: PropertyDataPointWithFlag[];
  bathroomData: PropertyDataPointWithFlag[];
  squareFeetData: PropertyDataPointWithFlag[];
  yearBuiltData: PropertyDataPointWithFlag[];
  isLoading: boolean;
}

export const TabInsights: React.FC<TabInsightsProps> = ({
  ownershipData,
  totalParcels,
  distance,
  bedroomData,
  bathroomData,
  squareFeetData,
  yearBuiltData,
  isLoading = false,
}) => {
  /// NOTE: These filter state variables are prepared for future implementation when it's necesarry.
  // Currently using default values only. When requirements are finalized, we can add a settings panel component to allow users.
  // to customize these market condition parameters (lease mode, time period, bedrooms, etc.).
  // If we keep as defaults, we can remove the state variables and consider to fecth data on parent.
  const [isLeaseMode, setIsLeaseMode] = React.useState(true);
  const [months, setMonths] = React.useState(defaultMonths);
  const [bedrooms, setBedrooms] = React.useState<Bedrooms>('');

  // (All: '' | Resale: 'false' | New Construction: 'true') from CMA
  const [newConstruction, setNewConstruction] = React.useState<
    'true' | 'false' | ''
  >(''); // Default is empty string meaning All
  const [prices, setPrices] = React.useState<[number | null, number | null]>([
    MIN_PRICE,
    MAX_PRICE,
  ]);

  const search = useSearch({ from: '/_authenticated/properties/$id' });
  const { lat, lng } = search;

  const areaData = useMemo(() => ({
    latitude: lat,
    longitude: lng,
    radius: distance * 1609.34,
  }), [lat, lng]);

  const {
    data: currentMarketConditionData,
    error: currentMarketError,
    isLoading: isLoadingCurrentMarket
  } = useMarketConditionData({
    serverType,
    isLeaseMode,
    areaData,
    numberOfMonths: months,
    timePeriod: 'current',
    bedrooms,
    enabled: true,
    newConstruction,
    prices,
    date: FormattedCurrentDate,
  });

  const {
    data: priorMarketConditionData,
    error: priorMarketError,
    isLoading: isLoadingPriorMarket
  } = useMarketConditionData({
    serverType,
    isLeaseMode,
    areaData,
    numberOfMonths: months,
    timePeriod: 'prior',
    bedrooms,
    enabled: true,
    newConstruction,
    prices,
    date: FormattedCurrentDate,
  });

  const marketConditionError = currentMarketError || priorMarketError;
  const isLoadingMarketConditions = isLoadingCurrentMarket || isLoadingPriorMarket;

  return (
    <div className="flex flex-col justify-center items-center gap-4 ">
      <OwnershipSection distance={distance} data={ownershipData} total={totalParcels} />
      <CompositionSection distance={distance} bedroomData={bedroomData} bathroomData={bathroomData} squareFeetData={squareFeetData} yearBuiltData={yearBuiltData} isLoading={isLoading} />
      <MarketConditionsSection distance={distance} currentMarketConditionData={currentMarketConditionData} priorMarketConditionData={priorMarketConditionData}
        months={months} marketConditionError={marketConditionError} isLoadingMarketConditions={isLoadingMarketConditions} />

      {/* <MarketConditionHistorySection /> */}
    </div>
  );
};
