import React from 'react';

export interface LegendItem {
  color: string;
  label: string;
}

interface ChartLegendProps {
  items: LegendItem[];
  className?: string;
}

const ChartLegend: React.FC<ChartLegendProps> = ({ items, className = "" }) => {
  return (
    <div className={`w-full px-4 mt-2 ${className}`}>
      <div className="flex space-x-4 text-xs">
        {items.map((item, index) => (
          <div key={index} className="flex items-center mr-[40px] last:mr-0">
            <div
              className="w-3 h-3 mr-2"
              style={{ backgroundColor: item.color }}
            />
            <p className="text-[13px]">{item.label}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ChartLegend;