import React, { ReactNode } from 'react';
import TabInsightsSectionHeader from './TabInsightsSectionHeader';

interface SectionWrapperProps {
  title: string;
  subtitle?: ReactNode;
  distance: number;
  rightContent?: ReactNode;
  className?: string;
  children: ReactNode;
}

const SectionWrapper: React.FC<SectionWrapperProps> = ({
  title,
  subtitle,
  distance,
  rightContent,
  className = "",
  children
}) => {
  return (
    <section className={`flex flex-col w-full justify-center items-center py-[23px] px-[16px] border border-medium-gray-20 rounded-md ${className}`}>
      <TabInsightsSectionHeader
        title={title}
        subtitle={subtitle}
        distance={distance}
        rightContent={rightContent}
      />

      {children}
    </section>
  );
};

export default SectionWrapper;