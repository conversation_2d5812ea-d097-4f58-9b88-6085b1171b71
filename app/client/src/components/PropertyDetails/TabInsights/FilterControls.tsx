import React from 'react';
import Selection from '../../Common/Selection';
import RentFilter from './RentFilter';
import AreaFilter from './AreaFilter';

interface FilterOption {
  value: string;
  label: string;
}

interface FilterControlsProps {
  // Change the type to accept readonly arrays
  activityOptions: FilterOption[];
  bedroomOptions: FilterOption[];
  selected: string;
  selectedBedroom: string;
  onSelectionChange: (value: string) => void;
  onBedroomChange: (value: string) => void;
  onClearRentFilter: () => void;
  onAddRentFilter: () => void;
  className?: string;
}

const FilterControls: React.FC<FilterControlsProps> = ({
  activityOptions,
  bedroomOptions,
  selected,
  selectedBedroom,
  onSelectionChange,
  onBedroomChange,
  onClearRentFilter,
  onAddRentFilter,
  className = ""
}) => {
  return (
    <div className={`flex flex-wrap gap-4 mt-6 mb-4 w-full ${className}`}>
      <div className="flex items-center space-x-4">
        <Selection
          label="Activity:"
          options={activityOptions}
          value={selected}
          onChange={onSelectionChange}
        />
        <Selection
          label="Bedroom:"
          options={bedroomOptions}
          value={selectedBedroom}
          onChange={onBedroomChange}
        />
      </div>
      <div className="flex items-center space-x-2">
        <RentFilter
          min={0}
          max={undefined}
          onClear={onClearRentFilter}
          onAdd={onAddRentFilter}
        />
        <AreaFilter />
      </div>
    </div>
  );
};

export default FilterControls;