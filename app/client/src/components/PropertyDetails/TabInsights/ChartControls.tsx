import React from 'react';
import FilterTabs from './FilterTabs';
import SegmentedSwitch from '../../Common/SegmentedSwitch';
import ToggleSwitch from '../../Common/ToggleSwitch';

interface ChartControlsProps {
  options: string[];
  toggleLabel: string;
  className?: string;
  onSeggmentedSwitchChange?: (value: string) => void;
  selectedOption?: string;
}

const ChartControls: React.FC<ChartControlsProps> = ({
  options,
  toggleLabel,
  className = "",
  onSeggmentedSwitchChange,
  selectedOption
}) => {

  return (
    <div className={`flex w-full justify-between lg:pl-14 lg:pr-8 ${className}`}>
      <div className="flex">
        <FilterTabs />
      </div>
      <div className="flex gap-2 lg:gap-4">
        <SegmentedSwitch options={options} onClick={onSeggmentedSwitchChange} selectedOption={selectedOption} />
        <ToggleSwitch label={toggleLabel} />
      </div>
    </div>
  );
};

export default ChartControls;