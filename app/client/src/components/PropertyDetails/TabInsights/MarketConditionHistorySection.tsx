import { useState } from "react";
import SectionWrapper from "./SectionWrapper";
import FilterControls from "./FilterControls";
import ChartControls from "./ChartControls";
import MedianRentChart from "./MedianRentChart";
import ActionButton from "@/components/Common/ActionButton";

// Sample data (replace with your real data)
const data = [
  { date: "Apr 2020", value: 1800, closed: 1750 },
  // ...existing data remains the same
  { date: "Mar 2025", value: 2530, closed: 2600 },
];

const options = ["Rent", "RSF"];
const label = "YOY";
const activityOptions = [
  { value: "rent", label: "Rent" },
  { value: "inventory", label: "Inventory" },
  { value: "dom", label: "DOM" },
];

const bedroomOptions = [
  { value: "any", label: "Any" },
  { value: "1", label: "1" },
  { value: "2", label: "2" },
  { value: "3", label: "3" },
];

type ActivityType = "rent" | "inventory" | "dom";
type BedroomType = "any" | "1" | "2" | "3";
type ChartDisplayOption = "Rent" | "RSF";

const MarketConditionHistorySection = () => {
  const [selected, setSelected] = useState<ChartDisplayOption>("Rent");
  const [selectedActivity, setSelectedActivity] = useState<ActivityType>("rent");
  const [selectedBedroom, setSelectedBedroom] = useState<BedroomType>("any");

  const handleClearRentFilter = () => {
    console.log("Clear rent filter");
  };

  const handleAddRentFilter = () => {
    console.log("Add rent filter");
  };

  const handleActivityChange = (value: string) => {
    setSelectedActivity(value as ActivityType);
  };

  const handleBedroomChange = (value: string) => {
    setSelectedBedroom(value as BedroomType);
  };

  return (
    <SectionWrapper
      title="Market Condition History"
      distance={5}
      rightContent={<ActionButton
        text="Export CSV"
        onClick={() => { }}
      />}
    >
      <FilterControls
        activityOptions={activityOptions}
        bedroomOptions={bedroomOptions}
        selected={selectedActivity}
        selectedBedroom={selectedBedroom}
        onSelectionChange={handleActivityChange}
        onBedroomChange={handleBedroomChange}
        onClearRentFilter={handleClearRentFilter}
        onAddRentFilter={handleAddRentFilter}
      />
      <ChartControls
        options={options}
        toggleLabel={label}
        onSeggmentedSwitchChange={(value) => setSelected(value as ChartDisplayOption)}
        selectedOption={selected}
      />
      <MedianRentChart data={data} />
    </SectionWrapper>
  );
};

export default MarketConditionHistorySection;