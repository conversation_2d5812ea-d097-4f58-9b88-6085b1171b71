import { formatDisplayValue } from "@/lib/utils/formatUtils";
import {
  BarC<PERSON>,
  Bar,
  <PERSON>A<PERSON>s,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Re<PERSON>onsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ist,
  CartesianGrid,
} from "recharts";

interface DataItem {
  name: string;
  value: number;
}

interface VerticalBarChartProps {
  data: DataItem[];
  title?: string;
  tickValues?: number[];
  fillColor?: string;
  xAxisLabel?: string;
  maxDomain?: number;
  dataKey?: string;
  valuePrefix?: string;
  metricType: string;
}

const VerticalBarChart: React.FC<VerticalBarChartProps> = ({
  data,
  title,
  tickValues,
  fillColor = "#1890ff",
  xAxisLabel = "Value",
  maxDomain = 50,
  dataKey = "value",
  valuePrefix = "",
  metricType = "",
}) => {

  return (
    <>
      <h2 className="text-center font-bold text-sm mb-2">{title}</h2>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={data}
          layout="vertical"
          margin={{
            left: -40,
            right: 40,
            top: 0,
            bottom: 10,
          }}
          width={500}
        >
          <CartesianGrid />
          <XAxis
            type="number"
            domain={[0, maxDomain]}
            tick={{ fontSize: 10 }}
            ticks={tickValues}
            label={{ value: xAxisLabel, position: "bottom", offset: -5, style: { fontSize: 12 } }}
            width={500}
          />
          <YAxis
            type="category"
            dataKey="name"
            width={120}
            ticks={[]}
            tick={{ fontSize: 10 }}
          />
          <Tooltip
            cursor={{ fill: "rgba(0, 0, 0, 0.04)" }}
            contentStyle={{
              backgroundColor: "#fff",
              border: "1px solid #ccc",
              borderRadius: "4px",
              padding: "8px",
            }}
            itemStyle={{
              color: "#6a6a6a",
            }}
            formatter={(value: number) => [formatDisplayValue(value, metricType, valuePrefix), xAxisLabel]}
          />
          <Bar dataKey={dataKey} fill={fillColor} barSize={20}>
            <LabelList
              dataKey={dataKey}
              position="right"
              offset={10}
              formatter={(v: number) => (v ? formatDisplayValue(v, metricType, valuePrefix) : "")}
              style={{ fontSize: 10 }}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </>
  );
};

export default VerticalBarChart;