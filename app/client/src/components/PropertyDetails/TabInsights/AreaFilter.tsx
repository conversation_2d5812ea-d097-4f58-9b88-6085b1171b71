import { useState } from "react";
import { Check } from "lucide-react";

const OPTIONS = ["AOI", "ZIP Code", "School District", "County", "Metro"];

const AreaFilter = () => {
  const [query, setQuery] = useState("");
  const [selected, setSelected] = useState<string[]>(["School District"]);
  const [open, setOpen] = useState(false);

  const filtered = OPTIONS.filter((opt) =>
    opt.toLowerCase().includes(query.toLowerCase())
  );

  const removeSelected = (opt: string) => {
    setSelected(selected.filter((s) => s !== opt));
  };

  const handleSelect = (opt: string) => {
    if (selected.includes(opt)) {
      removeSelected(opt);
    } else {
      setSelected([...selected, opt]);
    }
    setQuery("");
    setOpen(true);
  };

  return (
    <div className="flex items-center space-x-1">
      <label className="text-[var(--color-text-black) text-[13px] ">Area:</label>
      <div className="relative max-w-[140px]">
        {/* Input pill */}
        <div
          className={`flex items-center border border-[var(--color-light-gray)] rounded bg-white px-2 py-1 flex-wrap gap-1 cursor-pointer`}
        >
          {selected.map((opt) => (
            <span
              key={opt}
              className="flex items-center bg-[var(--color-super-light-gray)] text-sm rounded cursor-pointer transition-colors duration-200 hover:bg-[var(--color-light-gray)]"
            >
              {opt}
              <div
                className="ml-1 text-[var(--color-dark-gray)] hover:text-[var(--color-text-black)] cursor-pointer transition-colors duration-200"
                onClick={(e) => {
                  e.stopPropagation();
                  removeSelected(opt);
                }}
              >
                ×
              </div>
            </span>
          ))}
          <input
            type="text"
            className="flex-1 outline-none bg-transparent text-sm text-[var(--color-text-black)] item-center"
            value={query}
            onChange={(e) => {
              setQuery(e.target.value);
              setOpen(true);
            }}
            onClick={() => setOpen((prev) => !prev)}
            placeholder={
              selected.length === 0 ? "Select area(s)..." : undefined
            }
          />
        </div>
        {/* Dropdown */}
        {open && (
          <div className="absolute left-0 w-full bg-white shadow-lg rounded mt-1 z-10 overflow-auto">
            {filtered.length === 0 ? (
              <div className="px-2 py-2 text-[var(--color-text-black)]">
                No results
              </div>
            ) : (
              filtered.map((opt) => (
                <div
                  key={opt}
                  onClick={() => handleSelect(opt)}
                  className={`flex items-center text-[var(--color-text-black)] text-sm cursor-pointer p-1 hover:bg-gray-100
                  `}
                >
                  <span className="text-sm">{opt}</span>
                  {selected.includes(opt) && (
                    <span className="text-[var(--color-text-black)] ml-auto">
                      <Check className="w-4 h-4" />
                    </span>
                  )}
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AreaFilter;