import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianG<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
  B<PERSON>,
} from "recharts";

interface MedianRentChartProps {
  data: { date: string; value: number; closed: number }[];
}

const MedianRentChart: React.FC<MedianRentChartProps> = ({ data }) => {
  return (
    <>
      <ResponsiveContainer width="100%" height={360}>
        <LineChart
          data={data}
          margin={{ top: 20, right: 30, left: 0, bottom: 0 }}
        >
          <CartesianGrid />
          <XAxis
            dataKey="date"
            minTickGap={20}
            padding={{ left: 10, right: 10 }}
            ticks={[]}
            tick={{ fontSize: 10 }}
          />
          <YAxis
            domain={["dataMin-100", "dataMax+100"]}
            tickFormatter={(v) => `$${v.toLocaleString()}`}
            tick={{ fontSize: 13 }}
            width={70}
          />
          <Tooltip formatter={(v: number) => `$${v.toL<PERSON>aleString()}`} />
          <Legend
            verticalAlign="bottom"
            height={36}
            iconType="plainline"
            wrapperStyle={{
              paddingTop: -100,
              paddingBottom: 10,
              fontSize: '12px'
            }}
          />
          <Line
            type="monotone"
            dataKey="value"
            stroke="#1890FF"
            strokeWidth={3}
            dot={{ r: 4, stroke: "#1890FF", fill: "#fff", strokeWidth: 2 }}
            activeDot={{ r: 6 }}
            name="Active Median Rent"
          />
          <Brush
            dataKey="date"
            height={24}
            stroke="#D9D9D9"
            travellerWidth={8}
            startIndex={data.length - 12}
            y={320}
          />
        </LineChart>
      </ResponsiveContainer>
    </>
  );
};

export default MedianRentChart;