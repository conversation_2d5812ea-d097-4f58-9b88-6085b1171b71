
import TabInsightsSectionWrapper from "./SectionWrapper";

import OwnershipChart from "./OwnershipChart";
import type { DataItem } from "./StackBarChart";

interface OwnershipSectionProps {
  distance: number;
  data: DataItem[];
  total: number;
}

const OwnershipSection: React.FC<OwnershipSectionProps> = ({ distance, data, total }) => {

  return (
    <TabInsightsSectionWrapper title="Owner vs. Renter" distance={distance}>
      <OwnershipChart
        data={data}
        total={total}
      />
    </TabInsightsSectionWrapper>
  );
};

export default OwnershipSection;
