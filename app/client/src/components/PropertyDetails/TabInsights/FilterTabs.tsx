import React, { useState } from "react";

const options = ["All", "Resale", "New Construction"];

const FilterTabs = () => {
  const [selected, setSelected] = useState("All");

  return (
    <div className="inline-flex rounded bg-[var(--color-super-light-gray)] shadow-sm p-[2px]">
      {options.map((option) => (
        <div
          key={option}
          className={`px-2 py-[2px] rounded text-sm cursor-pointer transition-colors duration-200 
            ${
              selected === option
                ? "bg-white text-[var(--color-text-black)] hover:text-[var(--color-dark-gray)]"
                : "bg-transparent text-[rgba(0,0,0,0.65)] hover:text-[var(--color-text-black)]"
            }
          `}
          onClick={() => setSelected(option)}
        >
          {option}
        </div>
      ))}
    </div>
  );
};

export default FilterTabs;
