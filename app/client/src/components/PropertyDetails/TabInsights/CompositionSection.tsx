
import { PropertyDataPointWithFlag } from "@/lib/query/get-parcel-unit-mix-data";
import SectionWrapper from "./SectionWrapper";
import CompositionCharts from "./CompositionCharts";
import ChartLegend from "./ChartLegend";

interface CompositionSectionProps {
  distance: number;
  bedroomData: PropertyDataPointWithFlag[] | undefined
  bathroomData: PropertyDataPointWithFlag[] | undefined;
  squareFeetData: PropertyDataPointWithFlag[] | undefined;
  yearBuiltData: PropertyDataPointWithFlag[] | undefined;
  isLoading?: boolean;
}

const CompositionSection: React.FC<CompositionSectionProps> = ({
  distance,
  bedroomData,
  bathroomData,
  squareFeetData,
  yearBuiltData,
  isLoading = false,
}) => {
  const legendItems = [
    { color: 'var(--color-button-blue)', label: 'Subject Property' },
    { color: 'var(--color-light-gray)', label: 'Nearby Properties' }
  ];

  return (
    <SectionWrapper title="Composition of Nearby Homes" distance={distance}>
      <CompositionCharts
        bedroomData={bedroomData}
        bathroomData={bathroomData}
        squareFeetData={squareFeetData}
        yearBuiltData={yearBuiltData}
        isLoading={isLoading}
      />
      <ChartLegend items={legendItems} />
    </SectionWrapper>
  );
};

export default CompositionSection;
