import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>onsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

export interface DataItem {
  name: string;
  owner: number;
  renter: number;
}

interface StackBarChartProps {
  data: DataItem[];
}

const StackBarChart: React.FC<StackBarChartProps> = ({ data }) => {
  return (
    <ResponsiveContainer width="100%" height={40}>
      <BarChart layout="vertical" data={data} barSize={14}>
        <XAxis type="number" hide />
        <YAxis type="category" dataKey="name" hide />

        <Bar
          dataKey="owner"
          stackId="a"
          fill="#1890FF"
          radius={[25, 0, 0, 25]}
        ></Bar>
        <Bar
          dataKey="renter"
          stackId="a"
          fill="#D9D9D9"
          radius={[0, 25, 25, 0]}
        ></Bar>
        <Tooltip
          cursor={{ fill: "rgba(0, 0, 0, 0.04)" }}
          itemStyle={{
            color: "#6a6a6a",
          }}

        />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default StackBarChart;
