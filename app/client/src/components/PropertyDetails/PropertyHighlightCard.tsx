import React, { useState } from "react";
import PropertySpecifications from "./PropertySpecifications";
import fallbackImage from '@/assets/images/TX Core_Watermill_Ramsey_3330_Living_4.jpg';

// TODO: Review all types and ensure they are correct because api data and code keeps changing
interface PropertyHighlightCardProps {
  thumbnail: string;
  formattedPrice: string;
  address?: string;
  beds: number;
  baths: number;
  sqft: number;
  stories?: string;
  garage?: string;
  thumbnailLoading?: boolean;
  thumbnailError?: boolean;
}

const PropertyHighlightCard: React.FC<PropertyHighlightCardProps> = ({
  thumbnail,
  formattedPrice,
  address,
  beds,
  baths,
  sqft,
  stories,
  garage,
  thumbnailLoading = false,
  thumbnailError = false,
}) => {
  const [imgLoading, setImgLoading] = useState(true);
  const isLoading = thumbnailLoading || imgLoading;

  return (
    <div className="flex flex-row flex-1 items-center gap-4">
      <div className={`flex w-1/3 max-w-[210px] relative`}>
        {isLoading && (
          <div className="absolute inset-0 bg-[var(--color-light-gray)] animate-pulse rounded-xl" />
        )}
        <img
          className={`aspect-[174/143] object-cover rounded-xl w-full transition-opacity duration-500 ease-in-out ${isLoading ? "opacity-0" : "opacity-100"
            }`}
          src={thumbnailError || !thumbnail || thumbnail === '' ? fallbackImage : thumbnail}
          alt={`Property at ${address}`}
          loading="lazy"
          onLoad={() => setImgLoading(false)}
          onError={() => setImgLoading(false)}
        />
      </div>

      <div className="flex flex-col justify-start text-[var(--color-text-black)]">
        <div className="space-y-2">
          <div className="flex items-baseline gap-4">
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-heading font-light">
              {formattedPrice}
            </h1>
            <p className="text-dark-gray text-sm lg:text-base">
              List Price
            </p>
          </div>

          <p className="text-dark-gray text-sm lg:text-base">
            {address}
          </p>
        </div>

        <PropertySpecifications
          beds={beds}
          baths={baths}
          sqft={sqft}
          stories={stories}
          garage={garage}
        />
      </div>
    </div>
  );
};

export default PropertyHighlightCard;
