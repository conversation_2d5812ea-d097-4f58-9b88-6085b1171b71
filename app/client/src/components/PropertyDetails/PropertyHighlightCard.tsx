import React, { useState } from "react";
import { useRouter } from '@tanstack/react-router';
import { useFormSubmission } from '@/contexts/FormSubmissionContext';
import PropertySpecifications from "./PropertySpecifications";
import fallbackImage from '@/assets/images/TX Core_Watermill_Ramsey_3330_Living_4.jpg';
import StarOutlineIcon from '@mui/icons-material/StarOutline';
import StarIcon from '@mui/icons-material/Star';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { bookmarkManager } from '@/lib/utils/bookmarkManager';
import PromotionalBanner from "../PromotionalBanner";

// TODO: Review all types and ensure they are correct because api data and code keeps changing
interface PropertyHighlightCardProps {
  thumbnail: string;
  formattedPrice: string;
  address?: string;
  beds?: number;
  baths?: number;
  sqft?: number;
  stories?: string;
  garage?: string;
  thumbnailLoading?: boolean;
  thumbnailError?: boolean;
  decision?: string;
}

const PropertyHighlightCard: React.FC<PropertyHighlightCardProps> = ({
  thumbnail,
  formattedPrice,
  address,
  beds,
  baths,
  sqft,
  stories,
  garage,
  thumbnailLoading = false,
  thumbnailError = false,
}) => {
  const [imgLoading, setImgLoading] = useState(true);
  const isLoading = thumbnailLoading || imgLoading;

  // Get form submission context
  const { triggerFormSubmission, isSubmitting } = useFormSubmission();
  const { isBookmarked, setIsBookmarked, selectedBuyersViewRecord, proFormaAllValues } = useMarketplaceMapContext();
  const router = useRouter();

  const onClickBookmark = () => {
    // Optimistically update bookmark state immediately
    const propertyId = selectedBuyersViewRecord?.property_id;
    if (propertyId) {
      const newBookmarkState = bookmarkManager.toggleBookmark(propertyId, proFormaAllValues);
      setIsBookmarked(newBookmarkState);

      // Invalidate the properties route so the bookmark count updates immediately
      router.invalidate();
    }

    // Save to backend in background
    triggerFormSubmission();
    setIsBookmarked(!isBookmarked);
  };

  return (
    <div className="flex flex-row flex-1 items-center gap-4">
      <div className={`flex w-1/3 max-w-[210px] relative`}>
        {isLoading && (
          <div className="absolute inset-0 bg-[var(--color-light-gray)] animate-pulse rounded-xl" />
        )}
        <img
          className={`aspect-[174/143] object-cover rounded-xl w-full transition-opacity duration-500 ease-in-out ${isLoading ? "opacity-0" : "opacity-100"
            }`}
          src={thumbnailError || !thumbnail || thumbnail === '' ? fallbackImage : thumbnail}
          alt={`Property at ${address}`}
          loading="lazy"
          onLoad={() => setImgLoading(false)}
          onError={() => setImgLoading(false)}
        />
      </div>

      <div className="flex flex-col justify-start text-[var(--color-text-black)]">
        <div className="flex flex-col space-y-2 ml-2.5 mb-3">
          <PromotionalBanner isMobile={false} />
          <div className="flex items-baseline-last gap-3">
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-heading font-light">
              {formattedPrice}
            </h1>
            <p className="text-dark-gray text-sm lg:text-base !leading-3.5 relative top-[1px]">
              List<br />Price
            </p>
            {/* Submit button for pro forma form */}
            <div className="relative top-[-8px]">
              <button
                type="button"
                onClick={onClickBookmark}
                disabled={isSubmitting}
                className="cursor-pointer z-10 p-1 hover:opacity-70 transition-opacity"
                style={{ pointerEvents: 'auto' }}
              >
                {isBookmarked ? <StarIcon htmlColor='var(--color-dark-green)' /> : <StarOutlineIcon htmlColor='var(--color-dark-green)' />}
              </button>
            </div>
          </div>

          <p className="text-dark-gray text-sm lg:text-base">
            {address}
          </p>
        </div>
        <PropertySpecifications
          beds={beds}
          baths={baths}
          sqft={sqft}
          stories={stories}
          garage={garage}
        />
      </div>
    </div>
  );
};

export default PropertyHighlightCard;
