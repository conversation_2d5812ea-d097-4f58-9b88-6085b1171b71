import React, { ReactNode } from 'react';

interface ListTypeToggleButtonProps {
  isActive: boolean;
  onClick: () => void;
  disabled?: boolean;
  children: ReactNode;
}

const ListTypeToggleButton: React.FC<ListTypeToggleButtonProps> = ({
  isActive,
  onClick,
  disabled = false,
  children
}) => {
  return (
    <button
      className={`rounded-lg px-2 py-1.5 border-none cursor-pointer transition-all duration-200 ease-in-out 
        ${isActive
          ? "bg-blue-20 font-bold text-dark-gray"
          : "bg-light-gray hover:bg-light-gray/80 text-dark-gray hover:text-black"
        }`}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

export default ListTypeToggleButton;