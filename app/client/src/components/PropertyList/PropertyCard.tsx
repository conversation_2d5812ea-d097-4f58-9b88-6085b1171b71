import React from "react";
import { Link, useSearch } from "@tanstack/react-router";
import { Phone } from 'lucide-react';
import { formatPrice, formatPercentage, formatNumber, formatYearMonth, formatPhoneNumber } from "../../lib/utils/formatUtils.tsx";
import { convertPriceToNumber } from "../../lib/utils/stringMethods.ts";
import { selectLennarHeroImage } from "../../lib/utils/selectLennarHeroImage.ts";
import { LennarSinglePropertyDataType } from "../../lib/utils/types";
import { useIntersectionObserver } from "../../hooks/useIntersectionObserver";
import { usePropertyImages } from "../../hooks/usePropertyImages";
import PropertyImage from "./PropertyImage.tsx";
import fallbackImage from '../../assets/images/TX Core_Watermill_Ramsey_3330_Living_4.jpg';


const PropertyCard: React.FC<{
  property: LennarSinglePropertyDataType;
  index?: number;
  totalCount?: number;
}> = ({
  property,
  index = 0,
  totalCount = 0
}: {
  property: LennarSinglePropertyDataType;
  index?: number;
  totalCount?: number;
}) => {
    const { property_id, full_address, city, state, postal_code } = property;
    const { beds, baths, sqft } = property.payload.subjectProperty;
    const base_price = convertPriceToNumber(property?.payload?.subjectProperty?.meta?.
      base_price
    );

    const move_in_month = property?.payload?.subjectProperty?.meta?.move_in_month;
    const capRate = property?.payload?.proforma?.buyAndHold?.["Projected Yield on Bid Price"];
    const community = property.payload.subjectProperty.meta.community;
    const phone = property.payload.subjectProperty.meta.agent_phone;

    const formattedPrice = formatPrice(base_price);
    const formattedCapRate = formatPercentage(capRate);
    const formattedSqft = formatNumber(sqft);
    const address = `${city}, ${state} ${postal_code}`;
    const formattedEstimatedCompletion = move_in_month ? formatYearMonth(move_in_month) : "";
    const formattedPhone = phone ? formatPhoneNumber(phone) : "";

    const search = useSearch({ from: '/_authenticated/properties' });

    // Intersection observer for lazy loading
    const { elementRef, isVisible } = useIntersectionObserver({
      threshold: 0.1,
      rootMargin: '100px', // Start loading 100px before the element is visible
    });

    // Extract images directly from property meta data (only when visible for performance)
    const { images: orderedImages, loading: imagesLoading, error: imagesError } = usePropertyImages(
      isVisible ? property : undefined
    );

    // Get hero image (first image will be elevation if available, due to our ordering)
    const heroImage = orderedImages.length > 0 ? orderedImages[0].url : fallbackImage;

    return (
      <div ref={elementRef} className="flex flex-col w-full hover:shadow-md hover:transform hover:scale-[1.01] cursor-pointer transition-all duration-200 ease-in-out">
        <Link
          to="/properties/$id/proForma"
          params={{ id: String(property_id) }}
          search={{
            ...search,
            placekey: property.placekey,
            lat: property?.payload?.subjectProperty?.lat,
            lng: property?.payload?.subjectProperty?.lng,
            streetnum: property?.full_address?.split(' ')[0],
            address: property?.full_address,
            city: property?.city,
            state: property?.state,
            zip_code: property?.postal_code,
            latitude: property?.payload?.subjectProperty?.lat.toString(),
            longitude: property?.payload?.subjectProperty?.lng.toString(),
            beds: property?.payload?.subjectProperty?.beds.toString(),
            baths: property?.payload?.subjectProperty?.baths.toString(),
            sqft: property?.payload?.subjectProperty?.sqft.toString(),
            yearbuilt: property?.payload?.subjectProperty?.yearbuilt.toString(),
          }}
          preload={false}
          className=""
        >

          {/* Image Container */}
          <div className="w-full overflow-hidden rounded-t-xl border-t border-r border-l border-medium-gray-20">
            <PropertyImage
              imagesLoading={imagesLoading}
              imagesError={imagesError}
              heroImage={heroImage}
              address={address}
            />
          </div>

          {/* Details Container */}
          <div className="flex flex-col gap-3 p-2 rounded-b-xl border-b border-r border-l border-medium-gray-20">
            {/* Header with Price and Address */}
            <div className="flex flex-col w-full gap-3">
              <p className="flex text-dark-gray font-heading font-light text-3xl my-auto text-left">
                {formattedPrice}
              </p>
              <p className="text-dark-gray text-sm h-[100px]">
                <span className="block">{full_address}</span>
                <span className="block">{address}</span>
                <span className="block mt-1">Community: {community}</span>
                <div className="flex items-center justify-start w-full">{formattedPhone &&
                  (<><Phone size={12} className="mr-1" />{formattedPhone}</>)}
                </div>
              </p>
            </div>

            {/* Property Specifications Grid */}
            <div className="self-center w-full">
              <div className="grid grid-cols-9 w-full">
                {/* Values row */}
                <div className="text-center font-bold text-sm text-dark-gray col-span-1 overflow-hidden">{beds}</div>
                <div className="text-center font-bold text-sm text-dark-gray col-span-1 overflow-hidden">{baths}</div>
                <div className="text-center font-bold text-sm text-dark-gray col-span-2 overflow-hidden whitespace-nowrap">{formattedSqft}</div>
                <div className="text-center font-bold text-sm text-dark-gray col-span-2 overflow-hidden whitespace-nowrap">{formattedCapRate}</div>
                <div className="text-center font-bold text-sm text-dark-gray col-span-3 overflow-hidden whitespace-nowrap">{formattedEstimatedCompletion}</div>

                {/* Labels row */}
                <div className="text-center text-xs col-span-1 text-dark-gray">Bd</div>
                <div className="text-center text-xs col-span-1 text-dark-gray">Ba</div>
                <div className="text-center text-xs col-span-2 text-dark-gray">Sqft</div>
                <div className="text-center text-xs col-span-2 text-dark-gray whitespace-nowrap">Cap Rate</div>
                <div className="text-center text-xs col-span-3 text-dark-gray whitespace-nowrap">Est Move in Month</div>
              </div>
            </div>
          </div>
        </Link >
      </div >
    );
  };

export default PropertyCard;