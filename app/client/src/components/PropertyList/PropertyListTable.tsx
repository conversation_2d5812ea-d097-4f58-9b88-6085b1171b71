import React from 'react';
import { Link, useSearch } from "@tanstack/react-router";
import { LennarSinglePropertyDataType } from "@/lib/utils/types";
import { formatPrice, formatPercentage, formatNumber, removeAllDecimal, getFormattedAddress, formatMetroName, formatYearMonth } from "../../lib/utils/formatUtils";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface PropertyListTableProps {
  properties: LennarSinglePropertyDataType[];
}

// Define the data type for the table
interface PropertyTableData {
  id: string;
  property: LennarSinglePropertyDataType;
  community: string;
  address: string;
  price: string;
  bedsBaths: string;
  sqft: string;
  capRate: string;
  estCompletion: string;
  metro: string;
}

const PropertyListTable: React.FC<PropertyListTableProps> = ({ properties }) => {
  const search = useSearch({ from: '/_authenticated/properties' });

  if (!properties) return null;

  // Transform properties data for the table
  const tableData: PropertyTableData[] = properties.map((property) => {
    const { property_id, full_address, city, state, postal_code } = property;
    const community = property.payload.subjectProperty.meta.community;
    const { beds, baths, sqft } = property.payload.subjectProperty;
    const base_price = property?.payload?.subjectProperty?.meta?.base_price;
    const move_in_month = property?.payload?.subjectProperty?.meta?.move_in_month;
    const capRate = property?.payload?.proforma?.buyAndHold?.["Projected Yield on Bid Price"];
    const metro = property?.payload?.subjectProperty?.cbsaName;

    const formattedPostalCode = removeAllDecimal(postal_code);
    const formattedPrice = formatPrice(base_price);
    const formatSqft = formatNumber(sqft);
    const formattedCapRate = formatPercentage(capRate);
    const formattedMetoroName = formatMetroName(metro);
    const address = getFormattedAddress({
      streetAddress: full_address,
      city: city,
      state: state,
      postalCode: formattedPostalCode
    });
    const formattedEstimatedCompletion = move_in_month ? formatYearMonth(move_in_month) : '';

    return {
      id: String(property_id),
      property,
      address,
      community,
      price: formattedPrice,
      bedsBaths: `${beds}/${baths}`,
      sqft: formatSqft,
      capRate: formattedCapRate,
      estCompletion: formattedEstimatedCompletion,
      metro: formattedMetoroName
    };
  });

  return (
    <div className="flex flex-col w-full border border-[var(--color-light-gray)] rounded-lg h-[calc(100vh-205px)]">
      <Table>
        <TableHeader>
          <TableRow className="bg-light-gray border-b border-medium-gray-20">
            <TableHead className="p-3 font-semibold text-dark-gray text-xs text-left">Community</TableHead>
            <TableHead className="p-3 font-semibold text-dark-gray text-xs text-left">Metro</TableHead>
            <TableHead className="p-3 font-semibold text-dark-gray text-xs">Address</TableHead>
            <TableHead className="p-3 font-semibold text-dark-gray text-xs text-center">Price</TableHead>
            <TableHead className="p-3 font-semibold text-dark-gray text-xs text-center">Bd/Ba</TableHead>
            <TableHead className="p-3 font-semibold text-dark-gray text-xs text-center">Sqft</TableHead>
            <TableHead className="p-3 font-semibold text-dark-gray text-xs text-center">Cap Rate</TableHead>
            <TableHead className="p-3 font-semibold text-dark-gray text-xs text-right">Est Move In Month</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tableData.map((row) => (
            <TableRow key={row.id} className="border-b border-medium-gray-20 last:border-b-0">
              <TableCell className="p-0 text-left text-dark-gray text-xs">
                <Link
                  to="/properties/$id"
                  params={{ id: row.id }}
                  search={{
                    source: search.source,
                    listType: search.listType,
                    viewStyle: search.viewStyle,
                    placekey: row.property.placekey,
                    lat: row.property?.payload?.subjectProperty?.lat,
                    lng: row.property?.payload?.subjectProperty?.lng,
                    streetnum: row.property?.full_address?.split(' ')[0],
                    address: row.property?.full_address,
                    city: row.property?.city,
                    state: row.property?.state,
                    zip_code: row.property?.postal_code,
                    latitude: row.property?.payload?.subjectProperty?.lat?.toString(),
                    longitude: row.property?.payload?.subjectProperty?.lng?.toString(),
                    beds: row.property?.payload?.subjectProperty?.beds?.toString(),
                    baths: row.property?.payload?.subjectProperty?.baths?.toString(),
                    sqft: row.property?.payload?.subjectProperty?.sqft?.toString(),
                    yearbuilt: row.property?.payload?.subjectProperty?.yearbuilt?.toString(),
                  }}
                  preload={false}
                  className="block p-3 text-xs no-underline transition-colors duration-200 ease-in-out hover:bg-blue-20"
                >
                  {row.community}
                </Link>
              </TableCell>
              <TableCell className="p-3 text-left text-dark-gray text-xs">
                {row.metro}
              </TableCell>
              <TableCell className="p-3 text-left text-dark-gray text-xs">
                {row.address}
              </TableCell>
              <TableCell className="p-3 text-center text-dark-gray text-xs">
                {row.price}
              </TableCell>
              <TableCell className="p-3 text-center text-dark-gray text-xs">
                {row.bedsBaths}
              </TableCell>
              <TableCell className="p-3 text-center text-dark-gray text-xs">
                {row.sqft}
              </TableCell>
              <TableCell className="p-3 text-center text-dark-gray text-xs">
                {row.capRate}
              </TableCell>
              <TableCell className="p-3 text-right text-dark-gray text-xs">
                {row.estCompletion}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default PropertyListTable;