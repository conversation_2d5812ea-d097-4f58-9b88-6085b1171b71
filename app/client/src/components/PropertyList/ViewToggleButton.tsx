import React, { ReactNode } from 'react';

interface ViewToggleButtonProps {
  isActive: boolean;
  onClick: () => void;
  children: ReactNode;
  ariaLabel: string;
  disabled?: boolean;
}

const ViewToggleButton: React.FC<ViewToggleButtonProps> = ({
  isActive,
  onClick,
  children,
  ariaLabel,
  disabled = false,
}) => {
  const isDisabled = disabled || isActive;
  return (
    <button
      className={`rounded p-1 flex items-center justify-center  transition-colors duration-200 
        ${isActive
          ? "text-white bg-green-primary"
          : "text-dark-gray hover:text-black hover:bg-light-gray cursor-pointer"
        }`}
      onClick={onClick}
      aria-label={ariaLabel}
      disabled={isDisabled}
    >
      {children}
    </button>
  );
};

export default ViewToggleButton;