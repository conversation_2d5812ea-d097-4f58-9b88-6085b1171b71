import React from 'react';
import { MapPin, Globe } from 'lucide-react';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';

interface ViewportFilterToggleProps {
  className?: string;
}

const ViewportFilterToggle: React.FC<ViewportFilterToggleProps> = ({ className = '' }) => {
  const { isViewportFilteringEnabled, setIsViewportFilteringEnabled } = useMarketplaceMapContext();

  const handleToggle = () => {
    setIsViewportFilteringEnabled(!isViewportFilteringEnabled);
  };

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <button
        onClick={handleToggle}
        className={`
          flex items-center gap-1 px-2 py-1 rounded text-xs font-medium transition-colors
          ${isViewportFilteringEnabled 
            ? 'bg-blue-100 text-blue-700 hover:bg-blue-200' 
            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }
        `}
        title={isViewportFilteringEnabled 
          ? 'Showing properties visible on map' 
          : 'Showing all filtered properties'
        }
      >
        {isViewportFilteringEnabled ? (
          <MapPin size={12} />
        ) : (
          <Globe size={12} />
        )}
        <span>
          {isViewportFilteringEnabled ? 'Map View' : 'All'}
        </span>
      </button>
    </div>
  );
};

export default ViewportFilterToggle; 