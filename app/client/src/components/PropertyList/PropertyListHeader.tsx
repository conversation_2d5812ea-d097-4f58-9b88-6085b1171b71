import React from 'react';
import ListTypeToggle from './ListTypeToggle';
import { ViewToggle } from './ViewToggle';
import { SortBy } from '../../routes/_authenticated/properties';
import PropertySorter from './PropertySorter';
import ViewportFilterToggle from './ViewportFilterToggle';

interface PropertyListHeaderProps {
  activeButton: 'listings' | 'submitted';
  onListTypeChange: (listType: 'listings' | 'submitted') => void;
  viewStyle: "grid" | "list";
  onViewStyleChange: (style: "grid" | "list") => void;
  isLoading: boolean;
  sortBy: SortBy;
  setSortBy: (sortBy: SortBy) => void;
  sortDirection: "asc" | "desc";
  setSortDirection: (sortDirection: "asc" | "desc") => void;
  numberOfSubmittedProperties: number;
  numberOfListingsProperties: number;
  numberOfFilteredProperties: number;
}

const PropertyListHeader: React.FC<PropertyListHeaderProps> = ({
  activeButton,
  onListTypeChange,
  viewStyle,
  onViewStyleChange,
  isLoading,
  sortBy,
  setSortBy,
  sortDirection,
  setSortDirection,
  numberOfSubmittedProperties,
  numberOfListingsProperties,
  numberOfFilteredProperties,
}) => {
  const getListingMatchText = () => {
    const totalNumber = activeButton === 'listings' ? numberOfListingsProperties : numberOfSubmittedProperties;
    if (totalNumber > 1) {
      // return `${numberOfFilteredProperties}/${totalNumber} listings match`;
      return (
        <>
          <span className='font-bold'>{numberOfFilteredProperties}</span>
          {' '}/{' '}
          <span className='font-regular'>{totalNumber}</span>
          {' '}
          <span className='text-xs'>listings match</span>
        </>
      )
      
    } else if (totalNumber === 1) {
      return (
        <>
          <span className='font-bold'>1</span>
          {' '}/{' '}
          <span className='font-regular'>{totalNumber}</span>
          {' '}
          <span className='text-xs'>listing matches</span>
        </>
      )
    } else {
      return `No listings match`;
    }
  };

  return (
    <div className="flex justify-between items-center px-4 py-0 md:px-5">
      <ListTypeToggle
        activeButton={activeButton}
        onListTypeChange={onListTypeChange}
        isLoading={isLoading}
        numberOfSubmittedProperties={numberOfSubmittedProperties}
        numberOfListingsProperties={numberOfListingsProperties}
      />

      <div className='text-xs'>
        {getListingMatchText()}
      </div>

      <div className="flex items-center gap-2">
        <ViewportFilterToggle />
        <PropertySorter
          sortBy={sortBy}
          setSortBy={setSortBy}
          sortDirection={sortDirection}
          setSortDirection={setSortDirection}
        />
        <ViewToggle
          viewStyle={viewStyle}
          onViewStyleChange={onViewStyleChange}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
};

export default PropertyListHeader