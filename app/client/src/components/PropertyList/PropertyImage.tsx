import { useState } from 'react';
import fallbackImage from '../../assets/images/TX Core_Watermill_Ramsey_3330_Living_4.jpg';

interface PropertyImageProps {
  imagesLoading: boolean;
  imagesError: boolean;
  heroImage: string;
  address: string;
}

const PropertyImage: React.FC<PropertyImageProps> = ({
  imagesLoading,
  imagesError,
  heroImage,
  address
}) => {
  // Track browser-level image loading state (different from API loading state)
  // - imagesLoading: Are we fetching the URLs from the API
  // - imgLoading: Has the browser finished downloading the image
  const [imgLoading, setImgLoading] = useState(true);


  // Show loading state while waiting for image URLs from the API
  if (imagesLoading) {
    return (
      <div className="relative w-full h-[180px] aspect-[174/143]">
        <div className="absolute inset-0 bg-[var(--color-light-gray)] animate-pulse" />
      </div>
    );
  }

  if (imagesError) {
    return (
      <div className="w-full h-[180px] aspect-[174/143] bg-[var(--color-light-gray)] flex items-center justify-center">
        <img
          src={fallbackImage}
          alt="Property"
          className="w-full h-full object-cover"
          style={{ display: 'block', margin: 0 }}
        />
      </div>
    );
  }

  return (
    <div className="relative w-full h-[180px]  aspect-[174/143]">
      {/* We have the image URL, but browser might still be downloading the actual image */}
      {imgLoading && (
        <div className="absolute inset-0 bg-[var(--color-light-gray)] animate-pulse" />
      )}
      <img
        src={heroImage}
        alt={`Property at ${address}`}
        loading="lazy"
        className={`w-full h-full object-cover transition-opacity duration-500 ease-in-out ${imgLoading ? "opacity-0" : "opacity-100"}`}
        style={{ display: 'block', margin: 0 }}
        onLoad={() => setImgLoading(false)}
        onError={() => setImgLoading(false)}
      />
    </div>
  );
};

export default PropertyImage;