import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import type { SortBy } from "@/routes/_authenticated/properties";
import { ChevronsUpDown } from "lucide-react";
import { useState } from "react";

const PropertySorter = ({
  sortBy,
  setSortBy,
  sortDirection,
  setSortDirection,
}: {
  sortBy: SortBy;
  setSortBy: (sortBy: SortBy) => void;
  sortDirection: "asc" | "desc";
  setSortDirection: (sortDirection: "asc" | "desc") => void;
}) => {
  const [sortCombo, setSortCombo] = useState('');
  const onChangeSortCombo = (value: string) => {
    setSortCombo(value);
    // separate a string by '-'
    const [sortBy, sortDirection] = value.split('-');
    setSortBy(sortBy as SortBy);
    setSortDirection(sortDirection as "asc" | "desc");
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="link" className="flex items-center justify-center w-[28px] h-[28px] p-0 bg-light-gray rounded-lg">
          <ChevronsUpDown className="w-[16px] h-[16px] text-dark-gray" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-auto pr-4">
        <DropdownMenuLabel className="text-xs font-semibold text-[var(--color-dark-gray)]">Sort by</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuRadioGroup value={sortCombo} onValueChange={onChangeSortCombo}>
          <DropdownMenuRadioItem value="price-asc" className="text-xs text-dark-gray">Price: Low to High</DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="price-desc" className="text-xs text-dark-gray">Price: High to Low</DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="capRate-asc" className="text-xs text-dark-gray">Cap Rate: Low to High</DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="capRate-desc" className="text-xs text-dark-gray">Cap Rate: High to Low</DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="estCompletion-asc" className="text-xs text-dark-gray">Est. Completion: Old to New</DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="estCompletion-desc" className="text-xs text-dark-gray">Est. Completion: New to Old</DropdownMenuRadioItem>
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default PropertySorter;