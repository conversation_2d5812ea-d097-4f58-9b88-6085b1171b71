import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import type { SortBy } from "@/routes/_authenticated/properties";
import { ChevronsUpDown } from "lucide-react";
import { useState, useMemo, useEffect } from "react";
import { parseFilterInUrlSearchParams } from "@/lib/utils/parseFilterInUrlSearchParams";
import { useRouter, useSearch } from "@tanstack/react-router";



const PropertySorter = ({
  // sortBy,
  // setSortBy,
  // sortDirection,
  // setSortDirection,
}: {
  // sortBy: SortBy;
  // setSortBy: (sortBy: SortBy) => void;
  // sortDirection: "asc" | "desc";
  // setSortDirection: (sortDirection: "asc" | "desc") => void;
}) => {
  const router = useRouter();
  const search = useSearch({ from: '/_authenticated/properties' });

  // Determine current sort combo based on URL parameters
  const currentSortCombo = useMemo(() => {
    if (search.sort === 'basePrice' && search.direction === 'asc') {
      return 'price-asc';
    } else if (search.sort === 'basePrice' && search.direction === 'desc') {
      return 'price-desc';
    } else if (search.sort === 'yieldOnBidPrice' && search.direction === 'asc') {
      return 'capRate-asc';
    } else if (search.sort === 'yieldOnBidPrice' && search.direction === 'desc') {
      return 'capRate-desc';
    }
    return '';
  }, [search.sort, search.direction]);

  const [sortCombo, setSortCombo] = useState(currentSortCombo);

  // Update local state when URL changes
  useEffect(() => {
    setSortCombo(currentSortCombo);
  }, [currentSortCombo]);

  // const onChangeSortCombo = (value: string) => {
  //   setSortCombo(value);
  //   // separate a string by '-'
  //   const [sortBy, sortDirection] = value.split('-');
  //   setSortBy(sortBy as SortBy);
  //   setSortDirection(sortDirection as "asc" | "desc");
  // };

  const onChangeSortCombo = (value: string) => {
    setSortCombo(value);
    
    switch (value) {
      case 'price-asc':
        router.navigate({
          to: '/properties',
          search: {
            ...search,
            sort: 'basePrice',
            direction: 'asc',
          },
        });
        break;
      case 'price-desc':
        router.navigate({
          to: '/properties',
          search: {
            ...search,
            sort: 'basePrice',
            direction: 'desc',
          },
        });
        break;
      case 'capRate-asc':
        router.navigate({
          to: '/properties',
          search: {
            ...search,
            sort: 'yieldOnBidPrice',
            direction: 'asc',
          },
        });
        break;
      case 'capRate-desc':
        router.navigate({
          to: '/properties',
          search: {
            ...search,
            sort: 'yieldOnBidPrice',
            direction: 'desc',
          },
        });
        break;
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="link" className="flex items-center justify-center w-[28px] h-[28px] p-0 bg-light-gray rounded-lg">
          <ChevronsUpDown className="w-[16px] h-[16px] text-dark-gray" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-auto pr-4">
        <DropdownMenuLabel className="text-xs font-semibold text-[var(--color-dark-gray)]">Sort by</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuRadioGroup value={sortCombo} onValueChange={onChangeSortCombo}>
          <DropdownMenuRadioItem value="price-asc" className="text-xs text-dark-gray">Price: Low to High</DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="price-desc" className="text-xs text-dark-gray">Price: High to Low</DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="capRate-asc" className="text-xs text-dark-gray">Cap Rate: Low to High</DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="capRate-desc" className="text-xs text-dark-gray">Cap Rate: High to Low</DropdownMenuRadioItem>
          {/* <DropdownMenuRadioItem value="estCompletion-asc" className="text-xs text-dark-gray">Est. Completion: Old to New</DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="estCompletion-desc" className="text-xs text-dark-gray">Est. Completion: New to Old</DropdownMenuRadioItem> */}
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default PropertySorter;