import React from 'react';
import ListTypeToggleButton from './ListTypeToggleButton';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';

interface ListTypeToggleProps {
  activeButton: 'listings' | 'submitted' | 'bookmarked';
  onListTypeChange: (listType: 'listings' | 'submitted' | 'bookmarked') => void;
  isLoading: boolean;
  numberOfListingsProperties: number;
  numberOfSubmittedProperties: number;
  numberOfBookmarkedProperties: number;
}

const ListTypeToggle: React.FC<ListTypeToggleProps> = ({
  activeButton,
  onListTypeChange,
  isLoading,
  numberOfSubmittedProperties,
  numberOfBookmarkedProperties,
}) => {
  const { totalNumberOfProperties } = useMarketplaceMapContext();

  // numberOfListingsProperties = 3559 //temporary hardcoded value

  return (
    <div className="flex text-xs rounded-lg bg-blue-20">
      <ListTypeToggleButton
        isActive={activeButton === "listings"}
        onClick={() => onListTypeChange("listings")}
        disabled={isLoading}
      >
        <div>
          All ({totalNumberOfProperties})
        </div>
      </ListTypeToggleButton>

      <ListTypeToggleButton
        isActive={activeButton === "bookmarked"}
        onClick={() => onListTypeChange("bookmarked")}
        disabled={isLoading}
      >
        <div>Bookmarked ({numberOfBookmarkedProperties})</div>
      </ListTypeToggleButton>

      <ListTypeToggleButton
        isActive={activeButton === "submitted"}
        onClick={() => onListTypeChange("submitted")}
        disabled={isLoading}
      >
        <div>
          Submitted ({numberOfSubmittedProperties})
        </div>
      </ListTypeToggleButton>
    </div>
  );
};

export default ListTypeToggle;