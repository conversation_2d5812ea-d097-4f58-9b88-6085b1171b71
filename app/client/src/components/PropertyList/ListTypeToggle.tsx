import React from 'react';
import ListTypeToggleButton from './ListTypeToggleButton';

interface ListTypeToggleProps {
  activeButton: 'listings' | 'submitted';
  onListTypeChange: (listType: 'listings' | 'submitted') => void;
  isLoading: boolean;
  numberOfListingsProperties: number;
  numberOfSubmittedProperties: number;
}

const ListTypeToggle: React.FC<ListTypeToggleProps> = ({
  activeButton,
  onListTypeChange,
  isLoading,
  numberOfListingsProperties,
  numberOfSubmittedProperties,
}) => {
  return (
    <div className="flex text-xs rounded-lg bg-blue-20">
      <ListTypeToggleButton
        isActive={activeButton === "listings"}
        onClick={() => onListTypeChange("listings")}
        disabled={isLoading}
      >
        <div>
          All ({numberOfListingsProperties})
        </div>
      </ListTypeToggleButton>

      <ListTypeToggleButton
        isActive={activeButton === "submitted"}
        onClick={() => onListTypeChange("submitted")}
        disabled={isLoading}
      >
        <div>
          Submitted ({numberOfSubmittedProperties})
        </div>
      </ListTypeToggleButton>
    </div>
  );
};

export default ListTypeToggle;