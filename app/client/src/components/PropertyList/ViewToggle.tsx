import React from 'react';
import { LayoutGrid, Menu } from "lucide-react";
import ViewToggleButton from './ViewToggleButton';
import { Separator } from '@radix-ui/react-separator';

interface ViewToggleProps {
  viewStyle: "grid" | "list";
  onViewStyleChange: (style: "grid" | "list") => void;
  isLoading: boolean;
}

export const ViewToggle: React.FC<ViewToggleProps> = ({
  viewStyle,
  onViewStyleChange,
  isLoading
}) => {
  return (
    <div className="flex items-center gap-1">
      <ViewToggleButton
        isActive={viewStyle === "grid"}
        onClick={() => onViewStyleChange("grid")}
        ariaLabel="Grid view"
        disabled={isLoading}
      >
        <LayoutGrid size={16} strokeWidth={1} absoluteStrokeWidth={true} />
      </ViewToggleButton>

      <Separator orientation="vertical" className="w-[1px] h-[20px] bg-[var(--color-light-gray)]" />

      <ViewToggleButton
        isActive={viewStyle === "list"}
        onClick={() => onViewStyleChange("list")}
        ariaLabel="List view"
        disabled={isLoading}
      >
        <Menu size={16} strokeWidth={1} absoluteStrokeWidth={true} />
      </ViewToggleButton>
    </div>
  );
};