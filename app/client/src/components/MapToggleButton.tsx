import React from "react";
import { Shrink, Expand, MapPinned, LayoutList } from 'lucide-react';
import { useBreakpoint } from "@/hooks/useBreakpoint";
import { useLocation } from "@tanstack/react-router";
import { isPropertyDetailPage } from "../lib/utils/formatUtils";

interface MapToggleButtonProps {
  isOpen: boolean;
  onToggle: () => void;
}

const MapToggleButton: React.FC<MapToggleButtonProps> = ({ isOpen, onToggle }) => {
  const { isMobile } = useBreakpoint();
  const location = useLocation();

  const getButtonText = () => {
    try {
      const pathname = location.pathname;
      if (isPropertyDetailPage(pathname)) {
        return 'Home Details';
      }
      return 'List';
    } catch (error) {
      console.error('Error getting button text:', error);
      return 'List';
    }
  };

  if (isMobile) {
    return (
      <div className="fixed bottom-11 left-0 right-0 flex justify-center z-50">
        <button
          className="bg-green-primary shadow-lg rounded-full p-3 flex justify-center items-center"
          onClick={onToggle}
          aria-expanded={isOpen}
          aria-label={isOpen ? `View ${getButtonText()}` : 'View Map'}
        >
          {isOpen ? (
            <div className="flex items-center gap-1.5 animate-fade-in text-white">
              <LayoutList size={18} strokeWidth={1.5} />
              <span className="text-sm font-medium">{getButtonText()}</span>
            </div>
          ) : (
            <div className="flex items-center gap-1.5 animate-fade-in text-white">
              <MapPinned size={18} strokeWidth={1.5} />
              <span className="text-sm font-medium">Map</span>
            </div>
          )}
        </button>
      </div>
    );
  }

  return (
    <button
      className="flex justify-center items-center rounded-sm border border-medium-gray-20 cursor-pointer hover:bg-light-gray h-[32px] px-2 "
      onClick={onToggle}
    >
      <div
        className={`flex items-center gap-1`}
      >
        {isOpen ? (
          <div key="close-map-container" className="flex items-center gap-1 animate-fade-in">
            <Shrink key="close-icon" size={15} strokeWidth={1} />
            <span key="close-text" className="text-sm whitespace-nowrap text-dark-gray">
              Close Map
            </span>
          </div>
        ) : (
          <div key="open-map-container" className="flex items-center gap-1 animate-fade-in">
            <Expand key="open-icon" size={15} strokeWidth={1} />
            <span key="open-text" className=" text-sm whitespace-nowrap">
              Open Map
            </span>
          </div>
        )}
      </div>
    </button>
  );
};

export default MapToggleButton;