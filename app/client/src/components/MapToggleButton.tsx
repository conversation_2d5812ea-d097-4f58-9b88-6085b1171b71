import React from "react";
import { Shrink, Expand } from 'lucide-react';

interface MapToggleButtonProps {
  isOpen: boolean;
  onToggle: () => void;
}

const MapToggleButton: React.FC<MapToggleButtonProps> = ({ isOpen, onToggle }) => {
  return (
    <button
      className="flex justify-center items-center rounded-sm border border-medium-gray-20 cursor-pointer hover:bg-light-gray h-[32px] px-2 "
      onClick={onToggle}
    >
      <div
        className={`flex items-center gap-1`}
      >
        {isOpen ? (
          <div key="close-map-container" className="flex items-center gap-1 animate-fade-in">
            <Shrink key="close-icon" size={15} strokeWidth={1} />
            <span key="close-text" className="text-sm whitespace-nowrap text-dark-gray">
              Close Map
            </span>
          </div>
        ) : (
          <div key="open-map-container" className="flex items-center gap-1 animate-fade-in">
            <Expand key="open-icon" size={15} strokeWidth={1} />
            <span key="open-text" className=" text-sm whitespace-nowrap">
              Open Map
            </span>
          </div>
        )}
      </div>
    </button>
  );
};

export default MapToggleButton;