import { SquareArrowOutUpRight } from "lucide-react";
import { FOOTER_LINKS } from "../constants/footerLinks";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";


const Footer = () => {

  const currentYear = new Date().getFullYear();

  return (
    <footer className="flex flex-row justify-between h-[68px] px-4 border border-t-medium-gray-20 bg-white text-black items-center gap-4 lg:px-8 lg:py-4">
      <div className="flex flex-col max-w-[640px] lg:w-3/5 gap-2 justify-center">
        <Tooltip>
          <TooltipTrigger>
            <div className="flex gap-2 ">
              <p
                className="text-tiny text-left tracking-tight leading-none text-button-blue hover:text-button-blue/80 transition-all duration-300 ease-in-out hover:translate-x-0.5"
              >
                Disclaimer: The information provided in this document is for general
                informational purposes only and does not constitute investment
                advice...
              </p>
              <SquareArrowOutUpRight size={10} />
            </div>
          </TooltipTrigger>
          <TooltipContent className="w-[860px] max-h-[84vh] overflow-y-auto overflow-x-auto bg-white text-dark-gray rounded-md py-5 px-4 z-300 border border-medium-gray-20 text-xs">

            <p >
              The information provided in this document is for general informational purposes only and does not constitute investment advice. Lennar Corporation does not guarantee the accuracy, completeness, or reliability of any information provided regarding rental investments. Any reliance you place on such information is strictly at your own risk. Before making any investment decisions, you should consult with a qualified financial advisor who understands your individual circumstances and objectives. Lennar Corporation is not responsible for any losses or damages that may arise from your reliance on the information provided. Investing in rental properties involves risks, including the potential loss of principal. Past performance is not indicative of future results. Market conditions, property values, and rental income can fluctuate, and there is no assurance that any investment strategy will be successful.
              Items shown are based on currently available information and are subject to change without notice. The information presented is of no guarantee of the present or future market conditions and market values. Before making a decision to purchase based in whole or in part on tax treatment or tax benefits, customer should consult a qualified tax advisor. Plans to build out communities as proposed are subject to change without notice. Please contact the school district for the most current information about specific schools. Seller does not represent and cannot guarantee that the community will be serviced by any particular public school/school district or, once serviced by a particular school/school district, that the same school/school district will service the project for any particular period of time.  Schools that your children are eligible to attend may change over time. Maps are not to scale and are for relative location purposes only. Copyright © {currentYear} Lennar Corporation. Lennar and the Lennar logo are U.S. registered service marks or service marks of Lennar Corporation and/or its subsidiaries.
            </p>
            <p className="mt-4">
              Alabama – Lennar Homes Coastal Realty, LLC.<br />
              Arizona – Lennar Sales Corp.; HSP Arizona, Inc. ROC 242267B-2; ROC 138431B; ROC 144869A; Lennar Arizona Construction, Inc. ROC 228129B; Lennar Arizona, Inc. d/b/a Lennar Homes ROC 232731B; Lennar Communities Development, Inc. ROC 137295KA<br />
              California – CalAtlantic Group, Inc. (Responsible Broker: Joanna Duke) #02058246; BMR Construction, Inc. 830955; Lennar Sales Corp. (Responsible Broker: Joanna Duke) #01252753; CalAtlantic Group, Inc. 1037780; Lennar Communities, Inc. 66241; Lennar Homes of California, Inc. 728102<br />
              Florida – Lennar Realty, Inc.; Lennar Homes, LLC CBC038894; CGC062343, CGC1518166, CBC1257529, CGC1523282, CBC1260831, CGC1526578, CBC051237; Standard Pacific of Florida GP, Inc. CGC1506052, CGC1517342; U.S. Home Corporation CGC1518911; WCI Communities, LLC CGC031523<br />
              Idaho – RCE - 57241 / Maryland – CalAtlantic Group, Inc. MHBR No. 128; U.S. Home Corporation MHBR No. 316 / Minnesota – Lennar Sales Corp; CalAtlantic Group, LLC, BC736565; U.S. Home, LLC, BC001413 U.S. Home, LLC<br />
              Nevada – Lennar Sales Corp.; Greystone Nevada, LLC 48844; Ryland Homes Nevada, LLC; Lennar Reno, LLC 64226; Ryland Homes Nevada, LLC 56652<br />
              New Jersey – Lennar Sales Corp.<br />
              North Carolina – Lennar Sales Corp.<br />
              Oregon – Lennar Sales Corp. #201206464; Lennar Northwest, Inc. CCB #195307<br />
              Pennsylvania – Lennar Sales Corp.<br />
              South Carolina – Lennar Carolinas, LLC<br />
              Tennessee – Lennar Sales Corp. ph. 615-465-4328<br />
              Utah – Lennar Homes of Utah, Inc.<br />
            </p>
          </TooltipContent>
        </Tooltip>
        <div>
          <p className="text-tiny tracking-tight leading-none text-dark-gray">
            © {currentYear} Lennar Corporation. Lennar and the Lennar logo are
            U.S. registered service marks or service marks of Lennar Corporation
            and/or its subsidiaries. Seller's Broker: in expanded disclaimer. Construction
            License(s): in expanded disclaimer.
          </p>
        </div>
      </div>

      <div className="flex flex-col items-start justify-center min-w-[180px] gap-1">
        {FOOTER_LINKS.map((link) => (
          <div
            key={link.id}
            className="flex items-center gap-2"
          >
            {link.onClick ? (
              <button
                onClick={link.onClick}
                className="text-tiny text-left tracking-tight leading-none hover:text-button-blue/80 transition-all duration-300 ease-in-out hover:translate-x-0.5 text-button-blue"
              >
                {link.title}
              </button>
            ) : (
              <a
                href={link.href}
                className="text-tiny text-left tracking-tight leading-none hover:text-button-blue/80 transition-all duration-300 ease-in-out hover:translate-x-0.5 text-button-blue"
                target="_blank"
              >
                {link.title}
              </a>
            )}
            {link.icon && link.isExternal && <link.icon size={link.iconSize} />}
          </div>
        ))}

        <Tooltip>
          <TooltipTrigger>
            <div className="flex mt-1 items-center gap-2">
              <p
                className="text-tiny text-left tracking-tight leading-none  hover:text-button-blue/80 transition-all duration-300 ease-in-out hover:translate-x-0.5 text-button-blue "
              >
                User Data Control
              </p>
              <SquareArrowOutUpRight size={10} />
            </div>
          </TooltipTrigger>
          <TooltipContent className="w-[1/3] max-w-[600px] overflow-y-auto bg-white text-black rounded-md py-5 px-4 z-300 border border-medium-gray-20 text-xs">

            <p >
              You can request a file with a copy of your account data, including activity and settings.
            </p>
            <div className="flex items-start justify-center mt-3 space-x-8">
              <button className="flex items-center justify-center px-3 py-2 bg-button-blue font-semibold text-white text-xs rounded-md hover:bg-button-blue/80 hover:translate-x-0.5 transition-colors duration-200 ">
                Request
              </button>
              <div className="flex items-center gap-2 my-auto">
                <a href="https://lennar-privacy.my.onetrust.com/webform/********-ebef-41ce-ad79-79c8275b52ce/4a145a0f-1efc-4ad5-8878-7c3609052d3d" target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-button-blue hover:bg-button-blue/80 hover:translate-x-0.5 transition-all duration-200 ease-in-out ">
                  Lennar Privacy Request
                </a>
                <SquareArrowOutUpRight size={10} />
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </div>
    </footer>
  );
};

export default Footer;
