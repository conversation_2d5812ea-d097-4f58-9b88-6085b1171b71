import { SquareArrowOutUpRight } from "lucide-react";
import lennarLogo from '@/assets/Lennar_logos.jpg';
import FinancingDisclosure from "./Common/FinancingDisclosure";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { LEGAL_DISCLAIMERS } from "@/constants/legalDisclaimer";
import FooterLinks from "./FooterLinks";
import CopyrightText from "./CopyrightText";


const Footer = () => {
  return (
    <footer className="flex flex-row justify-between h-[68px] px-4 border border-t-medium-gray-20 bg-white text-black items-center gap-4 lg:px-8 lg:py-4">
      <div className="flex flex-col max-w-[640px] lg:w-3/5 gap-2 justify-center">
        <Tooltip>
          <TooltipTrigger>
            <div className="flex gap-2 ">
              <p
                className="text-tiny text-left tracking-tight leading-none text-button-blue hover:text-button-blue/80 transition-all duration-300 ease-in-out hover:translate-x-0.5"
              >
                Disclaimer: Financing is available through seller's affiliate Lennar Mortgage, LLC, but use of Lennar Mortgage, LLC is not required to...
              </p>
              <SquareArrowOutUpRight size={10} />
            </div>
          </TooltipTrigger>
          <TooltipContent className="w-[860px] max-h-[84vh] overflow-y-auto overflow-x-auto bg-white text-dark-gray rounded-md py-5 px-4 z-300 border border-medium-gray-20 text-xs">
            <FinancingDisclosure />
            <p className="text-xs text-dark-gray mt-3">
              <sup>*</sup> {LEGAL_DISCLAIMERS.MORTGAGE_DISCLAIMER}
            </p>
            <p className="text-xs text-dark-gray mt-3">
              {LEGAL_DISCLAIMERS.SERVICES_DISCLAIMER}
            </p>
          </TooltipContent>
        </Tooltip>
        <CopyrightText />
      </div>

      <div className="flex items-center justify-items-end">
        <img
          src={lennarLogo}
          alt="Lennar Logo"
          className="h-10 w-auto object-contain pr-8"
        />
        <FooterLinks />
      </div>
    </footer>
  );
};

export default Footer;
