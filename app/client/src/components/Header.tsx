import React from 'react';
import { FilterConfig } from '../types/PropertiesFilterTypes';

import { UserBadge } from './UserBadge';
import LennarLogo from './Icons/LennarLogo';
import PropertyFilters from './PropertyFilters';
import MapToggleButton from './MapToggleButton';


type HeaderProps = {
  isOpen: boolean;
  onToggle: () => void;
  filters: FilterConfig[]
  minCapRate: string;
  handleMinCapRateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;

};

const Header: React.FC<HeaderProps> = ({ isOpen, onToggle, filters, minCapRate, handleMinCapRateChange }) => {
  return (
    <header className="flex items-center justify-between border-b border-medimu-gray-20 px-4 py-2">
      <div className="flex items-center gap-4">
        <LennarLogo className="h-6" color="var(--color-green-primary)" />
        <p className="font-heading font-semibold text-base text-green-primary">Investor Marketplace</p>
      </div>
      <div className="flex gap-5">
        <div className="flex gap-4 items-center justify-center">
          <PropertyFilters
            filters={filters}
            minCapRate={minCapRate}
            handleMinCapRateChange={handleMinCapRateChange}
          />
          <MapToggleButton
            isOpen={isOpen}
            onToggle={onToggle}
          />
        </div>
        <UserBadge />
      </div>
    </header>
  );
};

export default Header;