import React from 'react';
import { FilterConfig } from '../types/PropertiesFilterTypes';

import { UserBadge } from './UserBadge';
import LennarLogo from './Icons/LennarLogo';
import PropertyFilters from './PropertyFilters';
import MapToggleButton from './MapToggleButton';
import CartIcon from './Cart/CartIcon';
import { Link } from '@tanstack/react-router';

import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';


type HeaderProps = {
  isOpen: boolean;
  onToggle: () => void;
  filters: FilterConfig[]
  minCapRate: string;
  handleMinCapRateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

const Header: React.FC<HeaderProps> = ({ isOpen, onToggle, filters, minCapRate, handleMinCapRateChange }) => {

  const { setSelectedBuyersViewRecord, setPropertyModalTabKey, setEventCoordinates, setHasInitialized } = useMarketplaceMapContext();
  
  return (
    <header className="flex items-center justify-between border-b border-medium-gray-20 px-4 py-2">
      <div className="flex items-center gap-4">
        <Link 
          to="/" 
          onClick={() => {
            setSelectedBuyersViewRecord(null);
            setPropertyModalTabKey(null);
            setEventCoordinates([NaN, NaN]);
            setHasInitialized(false);
          }}
          className="flex items-center gap-4"
        >
          <LennarLogo className="h-4" color="var(--color-green-primary)" />
          <span className="font-heading font-semibold text-base text-green-primary">Investor Marketplace</span>
        </Link>
      </div>
      <div className="flex gap-5">
        <div className="flex gap-4 items-center justify-center">
          <PropertyFilters
            filters={filters}
            minCapRate={minCapRate}
            handleMinCapRateChange={handleMinCapRateChange}
          />
          <CartIcon />
          <MapToggleButton
            isOpen={isOpen}
            onToggle={onToggle}
          />
        </div>
        <UserBadge />
      </div>
    </header>
  );
};

export default Header;