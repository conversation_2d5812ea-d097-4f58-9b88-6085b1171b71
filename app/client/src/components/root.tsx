import { Outlet } from '@tanstack/react-router'
import { UserBadge } from './UserBadge'
import { useLocation } from '@tanstack/react-router'

export function Root() {
  const location = useLocation();
  const isLoginPage = location.pathname === '/login';

  return (
    <div className="min-h-screen bg-background">
      {!isLoginPage && (
        <header className="p-4 border-b">
          <div className="container mx-auto flex justify-between items-center">
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/93f425a6840c3275a19732db837dcaf770c31647"
              className="h-8"
              alt="Logo"
            />
            <UserBadge />
          </div>
        </header>
      )}
      <main className={isLoginPage ? 'h-screen' : 'container mx-auto py-4'}>
        <Outlet />
      </main>
    </div>
  )
}