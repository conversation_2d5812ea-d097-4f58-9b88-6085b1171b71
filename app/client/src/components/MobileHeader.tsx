import React, { useState } from 'react';
import { ShoppingCart } from 'lucide-react';
import { Settings2 } from 'lucide-react';
import { Link } from '@tanstack/react-router';
import LennarLogo from './Icons/LennarLogo';
import { UserBadge } from './UserBadge';
import { useCart } from '@/contexts/CartContext';
import MobilePropertyFilters from './MobilePropertyFilters';

interface MobileHeaderProps {
  // Add any props you might need
  className?: string;
}

const MobileHeader: React.FC<MobileHeaderProps> = ({ className = '' }) => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const { getCartItemCount, setIsCartModalOpen, isLoading } = useCart();
  const itemCount = getCartItemCount();

  const toggleFilter = () => {
    setIsFilterOpen(prev => !prev);
  };
  return (
    <>
      <div className={`w-full ${className}`}>
        <header className="flex items-center justify-between border-b border-medium-gray-20 px-4 py-2">
          <div className="flex justify-start items-center mt-2">
            <Link to="/" className="flex-col my-auto">
              <LennarLogo className="h-3" color="var(--color-green-primary)" />
              <span className="font-heading font-semibold text-sm text-green-primary">Investor Marketplace</span>
            </Link>
          </div>
          <div className="flex justify-center items-center gap-3">
            <button className="p-1.5">
              <Settings2 size={26} className="cursor-pointer" onClick={toggleFilter} />
            </button>
            <div className="flex relative">
              <button className="bg-dark-gray rounded-full flex items-center justify-center w-10 h-10" onClick={() => setIsCartModalOpen(true)} disabled={isLoading} aria-label="Open Cart">
                <ShoppingCart stroke="white" size={20} />
              </button>
              {itemCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red text-white text-xs font-semibold rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1">
                  {itemCount}
                </span>
              )}
            </div>
            <UserBadge />
          </div>
        </header>
      </div>
      {isFilterOpen && (
        <MobilePropertyFilters
          isOpen={isFilterOpen}
          onClose={() => setIsFilterOpen(false)}
        />
      )}
    </>

  );
};

export default MobileHeader;