import React from 'react';
import { PopoverTriggerProps } from '@radix-ui/react-popover';
import { validateHTMLColorHex } from 'validate-color';

import { useThemes } from '@/lib/hooks/useThemes';

import { cn } from '@/lib/utils/styles';

import { Button } from './button';
import { Input } from './input';
import { Popover, PopoverContent, PopoverTrigger } from './popover';

const DEFAULT_COLORS = [
  '#31a354',
  '#de2d26',
  '#FF6900',
  '#FCB900',
  '#7BDCB5',
  '#8ED1FC',
  '#0693E3',
  '#ABB8C3',
  '#EB144C',
  '#9900EF',
];

interface ContextProps {
  inputValue: string;
  setInputValue: (value: string) => void;
  onValueChange: (value: string) => void;
}
const Context = React.createContext<ContextProps | undefined>(undefined);

interface ColorPickerProps extends PopoverTriggerProps {
  value: string;
  onValueChange: (value: string) => void;
  children: React.ReactNode;
}
const ColorPicker = React.forwardRef<HTMLDivElement, ColorPickerProps>(
  ({ className, ...props }, ref) => {
    const { theme } = useThemes();
    const [inputValue, setInputValue] = React.useState(
      validateHTMLColorHex(props.value) ? props.value : ''
    );

    const ctx = React.useMemo(
      () => ({ inputValue, setInputValue, onValueChange: props.onValueChange }),
      [inputValue]
    );

    return (
      <Context.Provider value={ctx}>
        <Popover>
          <PopoverTrigger asChild {...props}>
            <Button
              className={cn(
                'h-9 w-9 border-2 p-0',
                theme === 'dark' ? 'border-secondary-foreground' : 'border-muted',
                className
              )}
              style={{ backgroundColor: inputValue }}
              variant="ghost"
              size="icon"
            ></Button>
          </PopoverTrigger>
          <PopoverContent ref={ref} className="bg-transparent p-0">
            {props.children}
          </PopoverContent>
        </Popover>
      </Context.Provider>
    );
  }
);

ColorPicker.displayName = 'ColorPicker';

const useColorPicker = () => {
  const ctx = React.useContext(Context);
  if (!ctx) {
    throw new Error('useColorPicker must be used within a ColorPicker');
  }
  return ctx;
};

interface HexColorPickerProps extends React.HTMLAttributes<HTMLDivElement> {
  colors?: string[];
}
const HexColorPicker = React.forwardRef<HTMLDivElement, HexColorPickerProps>(
  ({ className, colors = DEFAULT_COLORS }, ref) => {
    const { theme } = useThemes();
    const { inputValue, setInputValue, onValueChange } = useColorPicker();

    const colorOptions = colors.filter((c) => validateHTMLColorHex(c));

    const [wrongColor, setWrongColor] = React.useState(false);

    const handleColorChange = (value: string) => {
      setInputValue(value);
      if (validateHTMLColorHex(value)) {
        onValueChange(value);
        if (wrongColor) setWrongColor(false);
      } else {
        setWrongColor(true);
      }
    };

    return (
      <div
        ref={ref}
        className={cn(
          'flex flex-row flex-wrap gap-1 rounded-md border bg-card p-3 shadow-lg',
          theme === 'dark' ? 'border-secondary-foreground' : 'border-muted',
          className
        )}
      >
        {colorOptions.map((color) => (
          <div
            key={color}
            className={cn(
              'h-8 w-8 cursor-pointer rounded-md border-2',
              inputValue === color
                ? theme !== 'dark'
                  ? 'border-primary'
                  : 'border-primary'
                : theme === 'dark'
                  ? 'border-secondary-foreground'
                  : 'border-muted'
            )}
            style={{ backgroundColor: color }}
            onClick={() => handleColorChange(color)}
          />
        ))}
        <Input
          className={cn(
            'w-24',
            wrongColor ? 'border-red-500 ring-red-500 focus-visible:ring-red-600' : ''
          )}
          value={inputValue.toUpperCase()}
          onChange={(e) => handleColorChange(e.target.value)}
        />
      </div>
    );
  }
);
HexColorPicker.displayName = 'HexColorPicker';

export { ColorPicker, HexColorPicker };
