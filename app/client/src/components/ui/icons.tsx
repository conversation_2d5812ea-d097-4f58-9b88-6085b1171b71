import {
  BlendIcon,
  CalendarIcon,
  Check,
  ChevronLeftIcon,
  ChevronRightIcon,
  EllipsisVerticalIcon,
  EyeIcon,
  EyeOffIcon,
  FolderPlusIcon,
  Grid3x3,
  GripVerticalIcon,
  InfoIcon,
  LandPlotIcon,
  LaptopIcon,
  Layers3,
  ListPlusIcon,
  Loader2Icon,
  LoaderIcon,
  LocateIcon,
  LogOutIcon,
  LucideMapPinned,
  MinusIcon,
  MoonIcon,
  PanelsTopLeftIcon,
  Rows2Icon,
  Rows3Icon,
  Rows4Icon,
  Settings2Icon,
  SettingsIcon,
  StarIcon,
  SunIcon,
  TelescopeIcon,
  TrashIcon,
  User2Icon,
  X,
} from 'lucide-react';
import type { LucideIcon as LucideIconType } from 'lucide-react';

export type LucideIcon = LucideIconType;

export const icons = {
  Blend: BlendIcon,
  Calendar: CalendarIcon,
  ChevronLeft: ChevronLeftIcon,
  ChevronRight: ChevronRightIcon,
  EllipsisVertical: EllipsisVerticalIcon,
  Eye: EyeIcon,
  EyeOff: EyeOffIcon,
  FolderPlus: FolderPlusIcon,
  GripVertical: GripVerticalIcon,
  Info: InfoIcon,
  LandPlot: LandPlotIcon,
  System: LaptopIcon,
  Layers: Layers3,
  ListPlus: ListPlusIcon,
  Loading: Loader2Icon,
  LoadingSpinner: LoaderIcon,
  Locate: LocateIcon,
  Logout: LogOutIcon,
  MapPinned: LucideMapPinned,
  Minus: MinusIcon,
  Moon: MoonIcon,
  PanelsTopLeft: PanelsTopLeftIcon,
  Rows2: Rows2Icon,
  Rows3: Rows3Icon,
  Rows4: Rows4Icon,
  Settings2: Settings2Icon,
  Settings: SettingsIcon,
  Star: StarIcon,
  Sun: SunIcon,
  Telescope: TelescopeIcon,
  Trash: TrashIcon,
  User: User2Icon,
  Grid3x3: Grid3x3,
  X: X,
  Check: Check,
} as const;
