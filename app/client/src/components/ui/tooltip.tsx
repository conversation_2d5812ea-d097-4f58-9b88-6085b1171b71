import * as React from 'react';
import * as TooltipPrimitive from '@radix-ui/react-tooltip';

import { cn } from '@/lib/utils/styles';

const TooltipProvider = TooltipPrimitive.Provider;

const Tooltip = TooltipPrimitive.Root;

const TooltipTrigger = TooltipPrimitive.Trigger;

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Portal>
    <TooltipPrimitive.Content
      ref={ref}
      sideOffset={sideOffset}
      className={cn(
        'z-50 overflow-hidden rounded-md bg-white text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
        className
      )}
      {...props}
    />
  </TooltipPrimitive.Portal>
));
TooltipContent.displayName = TooltipPrimitive.Content.displayName;

type MobileTooltipProps = {
  content: React.ReactNode;
  children: React.ReactNode;
  className?: string;
};
const MobileTooltip = ({ content, children, className }: MobileTooltipProps) => {
  const [open, setOpen] = React.useState(false);
  const isTouch = typeof window !== 'undefined' && ('ontouchstart' in window || navigator.maxTouchPoints > 0);

  // Close tooltip on outside tap (mobile)
  React.useEffect(() => {
    if (!isTouch || !open) return;
    const close = (e: TouchEvent) => setOpen(false);
    window.addEventListener('touchstart', close);
    return () => window.removeEventListener('touchstart', close);
  }, [isTouch, open]);

  const handleClick = (e: React.MouseEvent) => {
    if (isTouch) {
      e.preventDefault();
      e.stopPropagation();
      setOpen((v) => !v);
    }
  };

  return (
    <TooltipProvider>
      <Tooltip open={isTouch ? open : undefined} onOpenChange={isTouch ? setOpen : undefined}>
        <TooltipTrigger asChild onClick={handleClick}>
          {children}
        </TooltipTrigger>
        <TooltipContent className={className}>
          {content}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider, MobileTooltip };
