import * as React from 'react';
import { ChevronDownIcon, ChevronUpIcon } from '@radix-ui/react-icons';
import {
  NumberFormatBase,
  useNumericFormat,
  type NumberFormatBaseProps,
  type NumberFormatValues,
  type NumericFormatProps,
} from 'react-number-format';

import { cn } from '@/lib/utils/styles';

import { Button, ButtonProps } from './button';

interface NumberFieldContextProps {
  delta: number;
  setDelta: React.Dispatch<React.SetStateAction<number>>;
  disableNumberField: boolean;
}
const NumberFieldContext = React.createContext<NumberFieldContextProps | undefined>(undefined);

interface NumberFieldProps extends React.HTMLAttributes<HTMLDivElement> {
  disabled?: boolean;
}
const NumberField = React.forwardRef<HTMLDivElement, NumberFieldProps>(
  ({ className, children, disabled = false }, ref) => {
    const [delta, setDelta] = React.useState<number>(0);

    return (
      <NumberFieldContext.Provider value={{ delta, setDelta, disableNumberField: disabled }}>
        <div
          ref={ref}
          className={cn(
            'relative w-fit overflow-hidden rounded-md border',
            !disabled ? 'focus-within:ring-1 focus-within:ring-ring' : '',
            className
          )}
        >
          {children}
        </div>
      </NumberFieldContext.Provider>
    );
  }
);

const convertToNumber = (value: string | number | null | undefined) => {
  if (typeof value === 'number') return value;
  const parsed = parseFloat(`${value}`);
  return isNaN(parsed) ? undefined : parsed;
};

interface NumberFieldInputProps
  extends Omit<NumericFormatProps & NumberFormatBaseProps, 'onValueChange'> {
  onValueChange?: (value: number) => void;
}
const NumberFieldInput = React.forwardRef<HTMLInputElement, NumberFieldInputProps>(
  ({ className, onValueChange, ...props }, ref) => {
    const isControlled = props.value !== undefined;
    const context = React.useContext(NumberFieldContext);
    const [value, setValue] = React.useState(convertToNumber(props.value || props.defaultValue));
    const numericFormat = useNumericFormat({
      ...props,
      value: isControlled ? props.value : value,
    });

    React.useEffect(() => {
      if (!context || context.delta === 0) return;
      const newValue = (value || 0) + context.delta;

      if (
        numericFormat &&
        numericFormat.isAllowed &&
        !numericFormat.isAllowed({ floatValue: newValue } as NumberFormatValues)
      )
        return;
      setValue(newValue);
      onValueChange && onValueChange(newValue);
      context.setDelta(0);
    }, [context?.delta]);

    return (
      <NumberFormatBase
        getInputRef={ref}
        className={cn(
          'h-9 w-full rounded-md bg-transparent px-3 py-1 text-sm outline-none transition-colors placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50',
          !context ? 'border focus:ring-1 focus:ring-ring' : '',
          className
        )}
        disabled={context ? context.disableNumberField || props.disabled : false}
        {...numericFormat}
        {...(props.format && { format: props.format })}
        onValueChange={(inputValue) => {
          if (!inputValue.floatValue) return;
          setValue(inputValue.floatValue);
          onValueChange && onValueChange(inputValue.floatValue);
        }}
      />
    );
  }
);

interface NumberFieldStepperProps extends ButtonProps {
  step?: number;
}
const NumberFieldIncrement = React.forwardRef<HTMLButtonElement, NumberFieldStepperProps>(
  ({ className, variant, size, asChild = false, step = 1, ...props }, ref) => {
    const context = React.useContext(NumberFieldContext);

    if (!context) {
      throw new Error('NumberFieldIncrement must be used within a NumberField component');
    }

    return (
      <Button
        ref={ref}
        className={cn(
          'align-center flex h-full w-full flex-col justify-center rounded-none border-r-0 border-t-0 p-0 text-muted-foreground hover:text-foreground',
          'absolute right-0 top-0 h-1/2 w-6',
          className
        )}
        variant="outline"
        onClick={() => context.setDelta(step)}
        disabled={context ? context.disableNumberField || props.disabled : false}
        {...props}
      >
        <ChevronUpIcon />
      </Button>
    );
  }
);
NumberFieldIncrement.displayName = 'NumberFieldIncrement';

const NumberFieldDecrement = React.forwardRef<HTMLButtonElement, NumberFieldStepperProps>(
  ({ className, variant, size, asChild = false, step = 1, ...props }, ref) => {
    const context = React.useContext(NumberFieldContext);

    if (!context) {
      throw new Error('NumberFieldDecrement must be used within a NumberField component');
    }

    return (
      <Button
        ref={ref}
        className={cn(
          'align-center flex h-full w-full flex-col justify-center rounded-none border-b-0 border-r-0 p-0 text-muted-foreground hover:text-foreground',
          'absolute bottom-0 right-0 h-1/2 w-6',
          className
        )}
        variant="outline"
        onClick={() => context.setDelta(-step)}
        disabled={context ? context.disableNumberField || props.disabled : false}
        {...props}
      >
        <ChevronDownIcon />
      </Button>
    );
  }
);
NumberFieldDecrement.displayName = 'NumberFieldDecrement';

export { NumberField, NumberFieldInput, NumberFieldIncrement, NumberFieldDecrement };
