import { use<PERSON><PERSON>back, useState } from "react";
import {
  GraduationCap,
  Waves,
  Car,
  MapPin,
  Apple,
  School,
  BookOpen,
  FileChartLine,
  Building2,
  Fence,
  DollarSign,
  Blocks,
  BrickWall,
  CloudRainWind,
  Landmark,
  BriefcaseBusiness,
  HousePlus,
  Zap,
  TrainTrack,
  Check,
} from "lucide-react";
import BoundaryIcon from "./Icons/BoundaryIcon";
import TrainIcon from "./Icons/TrainIcon";
import BusStopIcon from "./Icons/BusStopIcon";

interface MenuItem {
  icon: React.ReactNode;
  label: string;
  submenu: SubMenuItem[];
}

interface SubMenuItem {
  label: string;
  icon: React.ReactNode;
}

const submenuData: MenuItem[] = [
  {
    icon: <GraduationCap className="w-6 h-6" />,
    label: "School",
    submenu: [
      { label: "Districts", icon: <Apple className="w-4 h-4" /> },
      { label: "Zones", icon: <School className="w-4 h-4" /> },
      { label: "Charter", icon: <BookOpen className="w-4 h-4" /> },
    ],
  },
  {
    icon: (
      <BoundaryIcon className="w-6 h-6 ml-1" color="var(--color-text-black)" />
    ),
    label: "Boundary",
    submenu: [
      { label: "Metro", icon: <FileChartLine className="w-4 h-4" /> },
      { label: "City", icon: <Building2 className="w-4 h-4" /> },
      { label: "Zip Code", icon: <Fence className="w-4 h-4" /> },
      { label: "Tax", icon: <DollarSign className="w-4 h-4" /> },
      { label: "Subdivision", icon: <Blocks className="w-4 h-4" /> },
      { label: "Neighborhood", icon: <BrickWall className="w-4 h-4" /> },
    ],
  },
  {
    icon: <Waves className="w-6 h-6" color="black" />,
    label: "Flood",
    submenu: [
      { label: "Flood Zone", icon: <CloudRainWind className="w-4 h-4" /> },
    ],
  },
  {
    icon: <MapPin className="w-6 h-6" />,
    label: "POI",
    submenu: [
      { label: "Institutional Owners", icon: <Landmark className="w-4 h-4" /> },
      {
        label: "Major Employers",
        icon: <BriefcaseBusiness className="w-4 h-4" />,
      },
      { label: "Mobile Home Park", icon: <HousePlus className="w-4 h-4" /> },
    ],
  },
  {
    icon: <Car className="w-7 h-7" />,
    label: "Infra",
    submenu: [
      { label: "Power Lines", icon: <Zap className="w-4 h-4" /> },
      { label: "Rail Network", icon: <TrainTrack className="w-4 h-4" /> },
      { label: "Transit Line", icon: <TrainIcon className="w-4 h-4" /> },
      { label: "Bus Stops", icon: <BusStopIcon className="w-4 h-4" /> },
    ],
  },
];

const MapSideBar = () => {
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [selectedItems, setSelectedItems] = useState<{
    [key: string]: string[];
  }>({});

  const handleClick = useCallback((label: string) => {
    setActiveSubmenu((prevState) => (prevState === label ? null : label));
  }, []);

  const handleSubItemClick = (category: string, item: string) => {
    setSelectedItems((prevSelected) => {
      const categoryItems = prevSelected[category] || [];

      // If item already selected, remove it; otherwise add it
      if (categoryItems.includes(item)) {
        return {
          ...prevSelected,
          [category]: categoryItems.filter((i) => i !== item),
        };
      } else {
        return {
          ...prevSelected,
          [category]: [...categoryItems, item],
        };
      }
    });
  };

  // Helper to check if an item is selected
  const isItemSelected = (category: string, item: string): boolean => {
    return selectedItems[category]?.includes(item) || false;
  };

  return (
    <div className="flex absolute top-0 z-10">
      {/* Main sidebar container with fixed width */}
      <div className="flex flex-col justify-center items-center rounded-br-[12px] border-r border-b border-medium-gray-20 py-4 px-3 bg-white space-y-4 w-16">
        {submenuData.map((item) => (
          <div
            key={item.label}
            className="relative w-full flex flex-col items-center"
            onClick={() => handleClick(item.label)}
          >
            <div className="flex flex-col items-center space-y-1 cursor-pointer h-[52px]">
              <div className="flex justify-center items-center h-7 w-7">
                {item.icon}
              </div>
              <p className="text-center text-xs">{item.label}</p>

              {/* Badge positioned absolutely to avoid affecting layout */}
              <div className="absolute top-0 right-1">
                {selectedItems[item.label]?.length > 0 && (
                  <div className="bg-[var(--color-button-blue)] rounded-full w-3 h-3"></div>
                )}
              </div>
            </div>

            {/* Submenu with fixed position from sidebar right border */}
            <div
              className={`absolute left-full -top-4 ml-2 bg-white border border-medium-gray-20 rounded shadow-lg transition-opacity duration-200 z-50 ${activeSubmenu === item.label
                ? "opacity-100 pointer-events-auto"
                : "opacity-0 pointer-events-none"
                }`}
              onClick={(e) => e.stopPropagation()} // Prevent submenu clicks from closing the menu
            >
              <ul className="py-2 min-w-[210px]">
                {item.submenu.map((sub) => (
                  <li
                    key={sub.label}
                    className={`px-4 py-1 text-sm hover:bg-gray-100 cursor-pointer whitespace-nowrap flex items-center justify-between `}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSubItemClick(item.label, sub.label);
                    }}
                  >
                    <div className="flex items-center">
                      <span className="mr-2">{sub.icon}</span>
                      <span>{sub.label}</span>
                    </div>
                    {isItemSelected(item.label, sub.label) && (
                      <span className="ml-2">
                        <Check className="w-4 h-4" />
                      </span>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MapSideBar;
