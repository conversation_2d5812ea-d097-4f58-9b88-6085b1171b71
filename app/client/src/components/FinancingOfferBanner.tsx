import React from 'react';
import ForSaleIcon from '@/components/Icons/ForSaleIcon';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface FinancingOfferBannerProps {
  onSeeDetailsClick: () => void;
  className?: string;
}

const FinancingOfferBanner: React.FC<FinancingOfferBannerProps> = ({
  onSeeDetailsClick,
  className = ''
}) => {
  const { isMobile } = useBreakpoint();
  return (
    <div className={`col-span-full bg-dark-blue p-4 rounded-xl shadow-sm border border-dark-blue ${className}`}>
      <div className="flex items-center justify-start gap-5">
        <ForSaleIcon className="h-8 w-8 text-white" />
        <div className="flex flex-col space-y-1">
          <p className={`${isMobile ? 'text-lg' : 'text-xl'} font-thin text-white font-heading`}>
            Promotional 7/6 ARM Rate of 4.990% (6.249% APR)<sup>*</sup>
          </p>
          <span
            className={`inline-flex ${isMobile ? 'text-xs' : 'text-sm'} text-white hover:text-white/50 transition-colors duration-200 underline decoration-[0.5px] underline-offset-4 cursor-pointer`}
            onClick={onSeeDetailsClick}
          >
            See details
          </span>
        </div>
      </div>
    </div>
  );
};

export default FinancingOfferBanner;