import { useEffect, useState } from 'react';
import { useAuthenticator } from '@aws-amplify/ui-react';
import { getUserEmail, getUserRole } from '@/lib/utils/auth';

import styles from './UserBadge.module.css';

export function UserBadge() {
  const { user, signOut } = useAuthenticator();
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const fetchUserData = async () => {
      const [email, role] = await Promise.all([
        getUserEmail(),
        getUserRole()
      ]);
      setUserEmail(email);
      setUserRole(role);
    };
    fetchUserData();
  }, []);

  if (!user || !userEmail) return null;

  const getFirstLetter = (email: string) => {
    if (!email) return 'A';
    return email.charAt(0).toUpperCase();
  }

  return (
    <div className="relative">
      <button
        className="w-10 h-10 rounded-full bg-green-primary border-none text-white text-lg font-medium flex items-center justify-center cursor-pointer transition-colors duration-200"
        onClick={() => setIsOpen(!isOpen)}
      >
        {getFirstLetter(userEmail)}
      </button>

      {isOpen && (
        <div className="absolute top-[calc(100%+0.5rem)] right-0 bg-white rounded-lg shadow-md p-3 min-w-[200px] flex flex-col gap-3 z-50">
          <div className="flex flex-col gap-1">
            <span className="text-sm text-dark-gray break-all">
              {userEmail}
            </span>
            {userRole && (
              <span className="text-xs text-dark-gray font-medium">
                {userRole}
              </span>
            )}
          </div>
          <button
            onClick={signOut}
            className="py-2 px-4 bg-light-gray border border-medium-gray-20 rounded-md text-dark-gray text-sm cursor-pointer transition-all duration-200 w-full text-align-center hover:bg-medium-gray-20"
          >
            Sign Out
          </button>
        </div>
      )}
    </div>
  );
}