import React from 'react'
import FinancingDisclosure from './Common/FinancingDisclosure';
import { LEGAL_DISCLAIMERS } from '@/constants/legalDisclaimer';
import lennarLogo from '../assets/Lennar_logos.jpg';
import FooterLinks from './FooterLinks';
import CopyrightText from './CopyrightText';

const MobileFooter = () => {
  return (
    <footer className="flex flex-col bg-white pb-20 mt-20 pt-2 px-1 border-t border-medium-gray-20 text-xs text-dark-gray h-full">
      <FinancingDisclosure />
      <p className="text-xs text-dark-gray mt-3">
        <sup>*</sup> {LEGAL_DISCLAIMERS.MORTGAGE_DISCLAIMER}
      </p>
      <p className="text-xs text-dark-gray mt-3">
        {LEGAL_DISCLAIMERS.SERVICES_DISCLAIMER}
      </p>
      <div className="flex justify-between items-center mt-3">
        <FooterLinks />
        <img
          src={lennarLogo}
          alt="Lennar Logo"
          className="h-6 w-auto object-contain"
        />
      </div>
      <div className="flex flex-col items-center justify-between pb-25 mt-3">
        <CopyrightText />
      </div>
    </footer >
  )

}

export default MobileFooter