import React, { useState } from 'react';
import LegalDisclaimerModal from "./Modal/LegalDisclaimerModal";

interface PromotionalBannerProps {
  className?: string;
  isMobile?: boolean;
}

const PromotionalBanner: React.FC<PromotionalBannerProps> = ({
  className = '',
  isMobile = false,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <div className={`bg-dark-blue text-white rounded-lg ${isMobile ? 'text-xs' : 'text-sm'} font-medium w-full p-1.5 ${className}`}>
        <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-left font-heading`}>Promotional 7/6 ARM Rate of 4.990% (6.249% APR)<sup>*</sup></p>
        <span
          className="text-xs underline decoration-[0.5px] underline-offset-4 hover:text-white/50 transition-colors duration-200 cursor-pointer"
          onClick={() => setIsModalOpen(true)}
        >
          See details
        </span>
        {/* </p> */}
      </div>

      <LegalDisclaimerModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Legal Disclaimer"
      />
    </>
  );
};

export default PromotionalBanner;