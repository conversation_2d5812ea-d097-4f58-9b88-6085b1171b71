import React from 'react';
import { LayoutGrid, Menu } from "lucide-react";

interface ViewToggleProps {
  viewStyle: "grid" | "list";
  setViewStyle: (style: "grid" | "list") => void;
  isLoading: boolean;
}

export const ViewToggle: React.FC<ViewToggleProps> = ({
  viewStyle,
  setViewStyle,
  isLoading
}) => {
  return (
    <div className="flex gap-1">
      <button
        className={`bg-transparent rounded p-1 flex items-center justify-center ${viewStyle === "grid"
          ? "text-dark-gray"
          : "text-light-gray"
          }`}
        onClick={() => setViewStyle("grid")}
        aria-label="Grid view"
        disabled={isLoading}
      >
        <LayoutGrid />
      </button>
      <button
        className={`bg-transparent rounded p-1 flex items-center justify-center ${viewStyle === "list"
          ? "text-dark-gray"
          : "text-light-gray"
          }`}
        onClick={() => setViewStyle("list")}
        aria-label="List view"
        disabled={isLoading}
      >
        <Menu />
      </button>
    </div>
  );
};