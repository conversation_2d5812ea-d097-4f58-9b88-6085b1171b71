import React, { ReactNode } from 'react';
import { X } from 'lucide-react';

interface ModalWrapperProps {
  onClose: () => void;
  children: ReactNode;
  className?: string;
  contentClassName?: string;
}

const ModalWrapper: React.FC<ModalWrapperProps> = ({
  onClose,
  children,
  className = '',
  contentClassName = 'px-7 space-y-3'
}) => {
  return (
    <div className={`flex flex-col pb-8 ${className}`}>
      <div className="flex justify-end">
        <button
          className="cursor-pointer text-[var(--color-dark-gray)] hover:text-[var(--color-text-black)] py-2 px-1 transition-colors"
          onClick={onClose}
          aria-label="Close modal"
        >
          <X className="w-6 h-6" />
        </button>
      </div>

      <div className={`flex flex-col  ${contentClassName}`}>
        {children}
      </div>
    </div>
  );
};

export default ModalWrapper;