import React from 'react';
import { OfferSubmissionStep } from '../../types/offerTypes';
import ModalWrapper from './ModalWrapper';
import OfferSubmissionHeader from './OfferSubmissionHeader';
import ProgressIndicator from './ProgressIndicator';
import ModalButton from './ModalButton';
import ContactInfo from '../ContactInfo';
import { useBreakpoint } from '@/hooks/useBreakpoint'
import { formatTitleWithStepCount } from '@/lib/utils/formatUtils';

interface StartOfferModalProps {
  address?: string;
  lotNumber?: string | number;
  currentModalStep: OfferSubmissionStep;
  handleStartOffer: () => void;
  handleMainClose: () => void;
  isPropertyCount?: boolean;
  agentPhone?: string;
}

const StartOfferModal: React.FC<StartOfferModalProps> = ({
  address,
  lotNumber,
  currentModalStep,
  handleStartOffer,
  handleMainClose,
  isPropertyCount = false,
  agentPhone
}) => {
  const { isMobile } = useBreakpoint();
  const formattedTitle = formatTitleWithStepCount(currentModalStep, "Start Purchase", isMobile);

  return (
    <ModalWrapper onClose={handleMainClose}>
      <OfferSubmissionHeader
        title={formattedTitle}
        onClose={handleMainClose}
        address={address}
        lotNumber={lotNumber}
        isPropertyCount={isPropertyCount}
      />
      {!isMobile && <ProgressIndicator currentStep={currentModalStep} />}
      <p className="text-left text-dark-gray text-sm">
        Congratulations on starting the offer process. Offers are non-binding. The following screens will guide you through the buyer process. Once submitted, someone from Lennar will respond to your offer.
      </p>

      {agentPhone && (
        <div className="flex items-center justify-center gap-2 my-4">
          <span className="text-sm text-gray-600">Contact:</span>
          <ContactInfo phone={agentPhone} />
        </div>
      )}

      <ModalButton onClick={handleStartOffer} title="Start Purchase" />
    </ModalWrapper>
  );
};

export default StartOfferModal;