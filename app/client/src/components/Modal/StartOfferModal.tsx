import React from 'react';
import { OfferSubmissionStep } from '../../types/offerTypes';

import ModalWrapper from './ModalWrapper';
import OfferSubmissionHeader from './OfferSubmissionHeader';
import ProgressIndicator from './ProgressIndicator';
import ModalButton from './ModalButton';

interface StartOfferModalProps {
  address?: string;
  lotNumber?: string | number;
  currentModalStep: OfferSubmissionStep;
  handleStartOffer: () => void;
  handleMainClose: () => void;
}

const StartOfferModal: React.FC<StartOfferModalProps> = ({
  address,
  lotNumber,
  currentModalStep,
  handleStartOffer,
  handleMainClose
}) => {
  return (
    <ModalWrapper onClose={handleMainClose}>
      <OfferSubmissionHeader
        title="Start Purchase"
        onClose={handleMainClose}
        address={address}
        lotNumber={lotNumber}
      />
      <ProgressIndicator currentStep={currentModalStep} />
      <p className="text-left text-dark-gray text-sm">
        Congratulations on starting the offer process. Offers are non-binding. The following screens will guide you through the buyer process. Once submitted, someone from Lennar will respond to your offer.
      </p>
      <ModalButton onClick={handleStartOffer} title="Start Purchase" />
    </ModalWrapper>
  );
};

export default StartOfferModal;