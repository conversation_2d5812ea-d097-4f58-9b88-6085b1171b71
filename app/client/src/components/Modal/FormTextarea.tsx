import React from 'react';

interface FormTextareaProps {
  id: string;
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  placeholder?: string;
  required?: boolean;
  minHeight?: string;
}

const FormTextarea: React.FC<FormTextareaProps> = ({
  id,
  label,
  value,
  onChange,
  placeholder,
  required = false,
  minHeight = 'min-h-[70px]'
}) => {
  return (
    <div className="flex flex-col text-[13px] ">
      <label htmlFor={id} className="text-dark-gray">{label}</label>
      <textarea
        id={id}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className={`py-2 px-1 border border-[var(--color-dark-gray)] rounded w-full ${minHeight} resize-vertical`}
        required={required}
      />
    </div>
  );
};

export default FormTextarea;