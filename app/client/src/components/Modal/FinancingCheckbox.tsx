import React from 'react';
import { Check } from 'lucide-react';

interface FinancingCheckboxProps {
  value: "yes" | "no" | "";
  onChange: (value: "yes" | "no" | "") => void;
  isRequired?: boolean;
  error?: string;
}

const FinancingCheckbox: React.FC<FinancingCheckboxProps> = ({
  value,
  onChange,
  isRequired = true,
  error
}) => {
  return (
    <div className="flex flex-col">
      <div className="flex items-center gap-3">
        <p className="text-dark-gray text-[13px]">
          Do you require financing?
          {isRequired && <span className="ml-1">*</span>}
        </p>
        <div className="flex gap-3">
          <label htmlFor="financingYes" className="flex items-center gap-2 text-[13px] cursor-pointer">
            <div className="relative inline-flex items-center justify-center">
              <input
                type="checkbox"
                id="financingYes"
                name="financing"
                checked={value === "yes"}
                onChange={() => onChange("yes")}
                className="appearance-none w-5 h-5 border border-dark-gray rounded bg-white cursor-pointer"
              />
              {value === "yes" && (
                <Check
                  size={12}
                  className="absolute pointer-events-none text-black"
                />
              )}
            </div>
            Yes
          </label>
          <label htmlFor="financingNo" className="flex items-center gap-2 text-[13px] cursor-pointer">
            <div className="relative inline-flex items-center justify-center">
              <input
                type="checkbox"
                id="financingNo"
                name="financing"
                checked={value === "no"}
                onChange={() => onChange("no")}
                className="appearance-none w-5 h-5 border border-dark-gray rounded bg-white cursor-pointer"
              />
              {value === "no" && (
                <Check
                  size={12}
                  className="absolute pointer-events-none text-black"
                />
              )}
            </div>
            No
          </label>
        </div>
      </div>
      {error && (
        <p className="text-red text-xs">{error}</p>
      )}
    </div>
  );
};

export default FinancingCheckbox;