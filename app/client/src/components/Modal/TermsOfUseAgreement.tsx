import React from 'react';

interface TermsOfUseAgreementProps {
  openTermsModal: (e: React.MouseEvent) => void;
  className?: string;
}

const TermsOfUseAgreement: React.FC<TermsOfUseAgreementProps> = ({
  openTermsModal,
  className = "text-dark-gray text-center text-[13px]"
}) => {
  return (
    <p className={className}>
      <button
        onClick={openTermsModal}
        className="bg-transparent border-0 text-button-blue hover:underline cursor-pointer p-0 ml-1"
      >
        Disclaimer
      </button>
    </p>
  );
};

export default TermsOfUseAgreement;