import { Label, Input, NumberField as NumberFieldReactAria } from "react-aria-components";

interface FormInputProps {
  id: string;
  label: string;
  type?: 'text' | 'email' | 'tel' | 'number';
  value: number;
  onChange: (value: number) => void;
  placeholder?: string;
  required?: boolean;
  colorClass?: string;
  width?: string;
}

const getFormatOptions = (inputValueType: 'currency' | 'percent' | 'number' | undefined) => {
  if (!inputValueType || inputValueType === 'currency') {
    return {
      style: 'currency' as const,
      currency: 'USD' as const,
      currencyDisplay: 'symbol' as const,
      currencySign: 'accounting' as const,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    };
  } else if (inputValueType === 'percent') {
    return {
      style: 'percent' as const,
      minimumFractionDigits: 0,
      maximumFractionDigits: 5,
    };
  } else if (inputValueType === 'number') {
    return {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    };
  }
};

const FormInputNumber = ({
  id,
  label,
  value,
  onChange,
  colorClass = '',
  width = 'w-full'
}: FormInputProps) => {
  return (
    <NumberFieldReactAria
      name={id}
      value={value}
      onChange={onChange}
      // minValue={minValue || 0}
      // maxValue={maxValue || 0}
      formatOptions={getFormatOptions('currency')}
      className={`flex flex-col text-[13px] ${width || ""} `}
    >
      <Label className="text-dark-gray">{label}</Label>
      <Input className={`px-2 py-1 border border-[var(--color-dark-gray)] rounded w-full ${colorClass}`} />
    </NumberFieldReactAria>
  )
};

export default FormInputNumber;