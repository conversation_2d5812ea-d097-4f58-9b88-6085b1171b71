import React, { ReactNode } from 'react';

interface CompsFilterSectionProps {
  title: string;
  children: ReactNode;
  className?: string;
}

const CompsFilterSection: React.FC<CompsFilterSectionProps> = ({
  title,
  children,
  className = ""
}) => {
  return (
    <div className={`mb-4 ${className}`}>
      <h3 className="text-sm font-medium m-0 mb-3 text-[var(--color-text-black) border-b border-[var(--color-super-light-gray)]">{title}</h3>
      {children}
    </div>
  );
};

export default CompsFilterSection;