import React, { useState } from 'react';
import CustomCheckbox from '../Common/CustomCheckbox';

interface TermsWithCheckboxProps {
  termsText: string | React.ReactNode;
  link?: string;
  onCheckChange: (isChecked: boolean) => void;
  checkboxLabel?: string;
  initialChecked?: boolean;
  className?: string;
}

const TermsWithCheckbox: React.FC<TermsWithCheckboxProps> = ({
  termsText,
  link,
  onCheckChange,
  checkboxLabel = "I agree",
  initialChecked = false,
  className = ""
}) => {
  const [isChecked, setIsChecked] = useState(initialChecked);

  const handleCheckboxChange = () => {
    const newValue = !isChecked;
    setIsChecked(newValue);
    onCheckChange(newValue);
  };

  return (
    <div className={`flex flex-col gap-1 text-[12px] text-dark-gray max-h-[400px] overflow-y-auto ${className}`}>
      <p>{termsText}{" "}{link && <a href={link} target="_blank" rel="noopener noreferrer" className="text-[var(--color-primary)] transition-all duration-300 hover:underline hover:text-dark-gray">
        {link}
      </a>}</p>


      <div className="flex items-center gap-1">
        <CustomCheckbox
          checked={isChecked}
          onChange={() => handleCheckboxChange()}
        />
        <label className="cursor-pointer" onClick={() => handleCheckboxChange()}>
          {checkboxLabel}
        </label>
      </div>
    </div>
  );
};

export default TermsWithCheckbox;