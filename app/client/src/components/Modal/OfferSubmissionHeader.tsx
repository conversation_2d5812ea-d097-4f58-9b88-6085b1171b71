import React from 'react';

interface OfferSubmissionHeaderProps {
  title: string;
  onClose: () => void;
  address?: string;
  lotNumber?: string | number;
}

const OfferSubmissionHeader: React.FC<OfferSubmissionHeaderProps> = ({
  title,
  address,
  lotNumber
}) => {
  return (
    <div className="flex flex-col ">
      <div className="flex flex-col items-center gap-1 ">
        <h2 className="font-heading font-light text-center text-3xl text-dark-gray">
          {title}
        </h2>
        {(address || lotNumber) && (
          <div className="flex gap-2 justify-center text-base text-dark-gray">
            {address && <p className="inline-block">{address}</p>}
            {lotNumber && <p>Lot {lotNumber}</p>}
          </div>
        )}
      </div>
    </div>
  );
};

export default OfferSubmissionHeader;