import React from 'react';
import StartOfferModal from './StartOfferModal';
import SubmitOfferModal from './SubmitOfferModal';
import SubmitSuccessModal from './SubmitSuccessModal';
import OfferTermsOfUseModal from './OfferTermsOfUseModal';
import { OfferSubmissionStep } from '../../types/offerTypes';
import { useOfferForm } from '../../hooks/useOfferForm';
import { useMarketplaceMapContext } from '@/contexts/MarketplaceMapContext';
import { convertPriceToNumber } from '@/lib/utils/stringMethods';

interface OfferSubmissionFlowProps {
    isOpen: boolean;
    onClose: () => void;
    address?: string;
    lotNumber?: string | number;
    loginEmail: string;
    agentPhone?: string;
}

const OfferSubmissionFlow: React.FC<OfferSubmissionFlowProps> = ({ isOpen, onClose, address, lotNumber, loginEmail, agentPhone }) => {
    const {
        currentModalStep,
        formState,
        isTermsOfUseOpen,
        userRole,
        updateFormField,
        handleStartOffer,
        handleSubmitOffer,
        openTermsModal: openActualTermsModal,
        closeTermsModal: closeActualTermsModal
    } = useOfferForm({ initialEmail: loginEmail });

    const { selectedBuyersViewRecord } = useMarketplaceMapContext();

    if (!isOpen) {
        return null;
    }

    const handleMainClose = () => {
        if (isTermsOfUseOpen) {
            closeActualTermsModal();
        } else {
            onClose();
        }
    };

    return (
        <div className="fixed inset-0 bg-[rgba(0,0,0,0.6)] flex justify-center items-center z-200">
            <div className="bg-white rounded-lg shadow-lg max-w-[600px] w-full max-h-[80vh] overflow-y-auto">
                {isTermsOfUseOpen ? (
                    <OfferTermsOfUseModal onClose={closeActualTermsModal} />
                ) : (
                    <>
                        {currentModalStep === OfferSubmissionStep.START && (
                            <StartOfferModal
                                address={address}
                                lotNumber={lotNumber}
                                currentModalStep={currentModalStep}
                                handleStartOffer={handleStartOffer}
                                handleMainClose={handleMainClose}
                                agentPhone={agentPhone}
                            />
                        )}

                        {currentModalStep === OfferSubmissionStep.SUBMIT && (
                            <SubmitOfferModal
                                email={formState.email}
                                setEmail={(value) => updateFormField('email', value)}
                                phone={formState.phone}
                                setPhone={(value) => updateFormField('phone', value)}
                                firstName={formState.firstName}
                                setFirstName={(value) => updateFormField('firstName', value)}
                                lastName={formState.lastName}
                                setLastName={(value) => updateFormField('lastName', value)}
                                purchasePrice={formState.purchasePrice}
                                setPurchasePrice={(value) => updateFormField('purchasePrice', value)}
                                comments={formState.comments}
                                setComments={(value) => updateFormField('comments', value)}
                                financing={formState.financing}
                                setFinancing={(value) => updateFormField('financing', value)}
                                PM={formState.PM}
                                setPM={(value) => updateFormField('PM', value)}
                                compensation={formState.compensation}
                                setCompensation={(value) => updateFormField('compensation', value)}
                                userRole={userRole}
                                address={address}
                                lotNumber={lotNumber}
                                currentModalStep={currentModalStep}
                                handleSubmitOffer={handleSubmitOffer}
                                handleMainClose={handleMainClose}
                                openTermsModal={openActualTermsModal}
                                listPrice={convertPriceToNumber(selectedBuyersViewRecord?.payload?.subjectProperty?.meta?.base_price || '0')}
                                agentPhone={agentPhone}
                            />
                        )}

                        {currentModalStep === OfferSubmissionStep.SUCCESS && (
                            <SubmitSuccessModal
                                handleMainClose={handleMainClose}
                                currentModalStep={currentModalStep} />
                        )}
                    </>
                )}
            </div>
        </div>
    );
};

export default OfferSubmissionFlow;