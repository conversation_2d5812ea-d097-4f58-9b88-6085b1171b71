import React from 'react';
import { OfferSubmissionStep } from "../../types/offerTypes"
import CheckmarkIcon from '../Icons/CheckmarkIcon';
import { OFFER_STEP_NUMBERS } from '../../constants/offerConstants';

interface StepIndicatorProps {
  stepId: OfferSubmissionStep;
  isCompletedOrActive: boolean;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({ stepId, isCompletedOrActive }) => (
  <div className={`w-8 h-8 rounded-full flex items-center justify-center relative ${isCompletedOrActive
    ? 'bg-green-primary border-green-primary text-white'
    : 'bg-white border border-[var(--color-text-black)] text-[var(--color-text-black)]'
    }`}>
    {isCompletedOrActive
      ? <CheckmarkIcon width={20} height={20} />
      : OFFER_STEP_NUMBERS[stepId]
    }
  </div>
);

export default StepIndicator;