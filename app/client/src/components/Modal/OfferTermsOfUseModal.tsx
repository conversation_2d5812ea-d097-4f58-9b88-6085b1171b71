import React from 'react';
import OfferSubmissionHeader from './OfferSubmissionHeader';
import ModalButton from './ModalButton';
import ModalWrapper from './ModalWrapper';

interface OfferTermsOfUseModalProps {
    onClose: () => void;
}

const OfferTermsOfUseModal: React.FC<OfferTermsOfUseModalProps> = ({ onClose }) => {
    const currentYear = new Date().getFullYear();
    return (
        <ModalWrapper onClose={onClose} contentClassName="px-7 space-y-4">
            <OfferSubmissionHeader
                title="Disclaimer"
                onClose={onClose}
            />
            <div className="flex flex-col gap-3 text-sm text-dark-gray">
                <p >
                    The information provided in this document is for general informational purposes only and does not constitute investment advice. Lennar Corporation does not guarantee the accuracy, completeness, or reliability of any information provided regarding rental investments. Any reliance you place on such information is strictly at your own risk. Before making any investment decisions, you should consult with a qualified financial advisor who understands your individual circumstances and objectives. Lennar Corporation is not responsible for any losses or damages that may arise from your reliance on the information provided. Investing in rental properties involves risks, including the potential loss of principal. Past performance is not indicative of future results. Market conditions, property values, and rental income can fluctuate, and there is no assurance that any investment strategy will be successful.
                    Items shown are based on currently available information are subject to change without notice. The information presented is of no guarantee of the present or future market conditions and market values. Before making a decision to purchase based in whole or in part on tax treatment or tax benefits, customer should consult a qualified tax advisor. Plans to build out communities as proposed are subject to change without notice. Please contact the school district for the most current information about specific schools. Seller does not represent and cannot guarantee that the community will be serviced by any particular public school/school district or, once serviced by a particular school/school district, that the same school/school district will service the project for any particular period of time.  Schools that your children are eligible to attend may change over time. Maps are not to scale and are for relative location purposes only. Copyright © {currentYear} Lennar Corporation. Lennar and the Lennar logo are U.S. registered service marks or service marks of Lennar Corporation and/or its subsidiaries.
                </p>

                <div className="pt-2">
                    <ModalButton title="Back" onClick={onClose} />
                </div>
            </div>
        </ModalWrapper>
    );
};

export default OfferTermsOfUseModal;