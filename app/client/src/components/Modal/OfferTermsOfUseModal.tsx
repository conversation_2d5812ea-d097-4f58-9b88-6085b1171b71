import React from 'react';
import OfferSubmissionHeader from './OfferSubmissionHeader';
import ModalButton from './ModalButton';
import ModalWrapper from './ModalWrapper';
import { disclaimer } from '@/constants/disclaimer';
import FinancingDisclosure from '../Common/FinancingDisclosure';

interface OfferTermsOfUseModalProps {
    onClose: () => void;
}

const OfferTermsOfUseModal: React.FC<OfferTermsOfUseModalProps> = ({ onClose }) => {
    return (
        <ModalWrapper onClose={onClose} contentClassName="px-7 space-y-4">
            <OfferSubmissionHeader
                title="Disclaimer"
                onClose={onClose}
            />
            <div className="flex flex-col gap-3 text-sm text-dark-gray">
                <FinancingDisclosure />
                <p className="mt-1">
                    {disclaimer}
                </p>

                <div className="pt-2">
                    <ModalButton title="Back" onClick={onClose} />
                </div>
            </div>
        </ModalWrapper>
    );
};

export default OfferTermsOfUseModal;