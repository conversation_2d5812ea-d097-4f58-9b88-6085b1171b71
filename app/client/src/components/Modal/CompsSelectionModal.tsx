import React, { useState } from 'react';
import { X } from 'lucide-react';
import { SubjectPropertyCharacteristics } from '../../types/PropertyDetailPage.types';
import SegmentedSwitch from '../Common/SegmentedSwitch';
import FilterSection from './CompsFilterSection';
import SliderToggle from '../PropertyDetails/TabComps/SliderToggle';

interface CompsSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  propertyDetails: SubjectPropertyCharacteristics;
}

const statusOptions = ['All', 'Closed', 'Pending', 'Active'];

const CompsSelectionModal: React.FC<CompsSelectionModalProps> = ({
  isOpen,
  onClose,
  propertyDetails,
}) => {
  const [status, setStatus] = useState('All');
  const [includeSchoolDistrict, setIncludeSchoolDistrict] = useState(false);
  const [includeCounty, setIncludeCounty] = useState(false);
  const [includeZipCode, setIncludeZipCode] = useState(false);
  const [poolAllowed, setPoolAllowed] = useState('yes');

  // You can adjust this based on actual business requirements
  const dateRangeDays = 365;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-lg w-[700px] max-w-[95%] max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-4">
          <h2 className="text-lg font-medium m-0">Filters</h2>
          <button
            onClick={onClose}
            className="bg-transparent border-0 p-1 cursor-pointer rounded-full hover:bg-gray-100"
          >
            <X size={20} strokeWidth={1.75} className="text-[var(--color-text-black)]" />
          </button>
        </div>
        <div className="overflow-y-auto p-5 flex-1">
          <div className="flex gap-6">
            {/* Left Column */}
            <div className="w-1/2">
              {/* Status */}
              <FilterSection title="Status">
                <SegmentedSwitch
                  options={statusOptions}
                  selectedOption={status}
                  onClick={setStatus}
                  className="justify-start"
                />
              </FilterSection>

              {/* Date Range */}
              <FilterSection title="Date Range">
                <div className="flex items-center gap-2">
                  <span className="text-[15px] text-gray-800 font-medium">Within</span>
                  <span className="text-[var(--color-button-blue)] text-[14px] font-semibold">{dateRangeDays}</span>
                  <span className="text-gray-800 text-[15px] font-normal">Days</span>
                </div>
              </FilterSection>

              {/* Within Same */}

              <FilterSection title="Within Same">
                <SliderToggle
                  label="School District"
                  checked={includeSchoolDistrict}
                  onChange={setIncludeSchoolDistrict}
                  className="justify-end"
                />
                <div className="flex flex-col gap-2">

                  <label className="flex justify-between items-center w-full cursor-pointer">
                    <span className="text-xs text-gray-600">School District</span>
                    <div className="relative w-10 h-5">
                      <input
                        type="checkbox"
                        checked={includeSchoolDistrict}
                        onChange={() => setIncludeSchoolDistrict(!includeSchoolDistrict)}
                        className="sr-only"
                      />
                      <div className={`absolute inset-0 rounded-full transition-colors ${includeSchoolDistrict ? 'bg-[var(--color-button-blue)]' : 'bg-gray-300'}`}></div>
                      <div className={`absolute w-4 h-4 bg-white rounded-full top-0.5 left-0.5 transition-transform ${includeSchoolDistrict ? 'translate-x-5' : 'translate-x-0'}`}></div>
                    </div>
                  </label>

                  <label className="flex justify-between items-center w-full cursor-pointer">
                    <span className="text-xs text-gray-600">County</span>
                    <div className="relative w-10 h-5">
                      <input
                        type="checkbox"
                        checked={includeCounty}
                        onChange={() => setIncludeCounty(!includeCounty)}
                        className="sr-only"
                      />
                      <div className={`absolute inset-0 rounded-full transition-colors ${includeCounty ? 'bg-[var(--color-button-blue)]' : 'bg-gray-300'}`}></div>
                      <div className={`absolute w-4 h-4 bg-white rounded-full top-0.5 left-0.5 transition-transform ${includeCounty ? 'translate-x-5' : 'translate-x-0'}`}></div>
                    </div>
                  </label>

                  <label className="flex justify-between items-center w-full cursor-pointer">
                    <span className="text-xs text-gray-600">ZIP code</span>
                    <div className="relative w-10 h-5">
                      <input
                        type="checkbox"
                        checked={includeZipCode}
                        onChange={() => setIncludeZipCode(!includeZipCode)}
                        className="sr-only"
                      />
                      <div className={`absolute inset-0 rounded-full transition-colors ${includeZipCode ? 'bg-[var(--color-button-blue)]' : 'bg-gray-300'}`}></div>
                      <div className={`absolute w-4 h-4 bg-white rounded-full top-0.5 left-0.5 transition-transform ${includeZipCode ? 'translate-x-5' : 'translate-x-0'}`}></div>
                    </div>
                  </label>
                </div>
              </FilterSection>

              {/* Rent Price */}
              <FilterSection title="Rent Price">
                <div className="flex items-center gap-1.5">
                  <input
                    className="w-[90px] py-0.5 px-2 text-xs border border-gray-300 rounded text-[var(--color-button-blue)] bg-white"
                    placeholder="$ 0"
                  />
                  <span className="text-gray-400 text-base">-</span>
                  <input
                    className="w-[90px] py-0.5 px-2 text-xs border border-gray-300 rounded text-[var(--color-button-blue)] bg-white"
                    placeholder="No Max"
                  />
                </div>
              </FilterSection>

              {/* Beds */}
              <FilterSection title="Beds">
                <div className="flex items-center gap-1.5">
                  <span className="text-xs text-[var(--color-button-blue)]">No Min</span>
                  <span className="text-gray-400 text-base">-</span>
                  <span className="text-xs text-[var(--color-button-blue)]">No Max</span>
                </div>
              </FilterSection>


              {/* Baths */}
              <FilterSection title="Baths">
                <div className="flex items-center gap-1.5">
                  <span className="text-xs text-[var(--color-button-blue)]">No Min</span>
                  <span className="text-gray-400 text-base">-</span>
                  <span className="text-xs text-[var(--color-button-blue)]">No Max</span>
                </div>
              </FilterSection>
            </div>

            {/* Right Column */}
            <div className="w-1/2">
              {/* Sqft */}
              <FilterSection title="Sqft">
                <div className="flex items-center gap-1.5">
                  <span className="text-xs text-[var(--color-button-blue)]">No Min</span>
                  <span className="text-gray-400 text-base">-</span>
                  <span className="text-xs text-[var(--color-button-blue)]">No Max</span>
                </div>
              </FilterSection>

              {/* Lot Size */}
              <FilterSection title="Lot Size">
                <div className="flex items-center gap-1.5">
                  <span className="text-xs text-[var(--color-button-blue)]">No Min</span>
                  <span className="text-gray-400 text-base">-</span>
                  <span className="text-xs text-[var(--color-button-blue)]">No Max</span>
                </div>
              </FilterSection>


              {/* Covered Parking */}
              <FilterSection title="Covered Parking">
                <div className="flex items-center gap-1.5">
                  <span className="text-xs text-[var(--color-button-blue)]">No Min</span>
                  <span className="text-gray-400 text-base">-</span>
                  <span className="text-xs text-[var(--color-button-blue)]">No Max</span>
                </div>
              </FilterSection>

              {/* Pool Allowed */}
              <FilterSection title="Pool Allowed">
                <select
                  className="w-[110px] py-0.5 px-2 text-xs border border-gray-300 rounded text-[var(--color-button-blue)] bg-white"
                  value={poolAllowed}
                  onChange={e => setPoolAllowed(e.target.value)}
                >
                  <option value="yes">Yes</option>
                  <option value="no">No</option>
                  <option value="unknown">Unknown</option>
                </select>

              </FilterSection>

              {/* Year Built */}
              <FilterSection title="Year Built">
                <div className="flex items-center gap-1.5">
                  <span className="text-xs text-[var(--color-button-blue)]">1900</span>
                  <span className="text-gray-600 text-lg mx-2">&rarr;</span>
                  <span className="text-xs text-[var(--color-button-blue)]">2025</span>
                </div>
              </FilterSection>

              {/* Cumulative DOM */}
              <FilterSection title="Cumulative DOM">
                <div className="flex items-center gap-1.5">
                  <span className="text-xs text-[var(--color-button-blue)]">No Min</span>
                  <span className="text-gray-400 text-base">-</span>
                  <span className="text-xs text-[var(--color-button-blue)]">No Max</span>
                </div>
              </FilterSection>

            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 flex justify-end space-x-2">
          <button className="px-4 py-1.5 text-sm rounded border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 transition-colors">
            Reset
          </button>
          <button
            className="px-4 py-1.5 text-sm rounded border-none bg-[var(--color-button-blue)] text-white cursor-pointer hover:bg-[var(--color-button-blue)]/90 transition-colors"
            onClick={onClose}
          >
            Apply
          </button>
        </div>
      </div >
    </div >
  );
};

export default CompsSelectionModal;