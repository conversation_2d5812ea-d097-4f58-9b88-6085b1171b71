import React, { useState } from 'react';
import { X } from 'lucide-react';

interface FilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  propertyDetails?: {
    sqFt?: number;
    lotSize?: number;
    beds?: number;
    baths?: number;
    yearBuilt?: number;
  };
}

const CompsFilterModal: React.FC<FilterModalProps> = ({ isOpen, onClose, propertyDetails = {} }) => {
  const [dateRange, setDateRange] = useState<string>('3 Mo.');
  const [sqftRange, setSqftRange] = useState<string>('25%');
  const [lotSize, setLotSize] = useState<string>('25%');
  const [beds, setBeds] = useState<string>('1');
  const [baths, setBaths] = useState<string>('1');
  const [yearBuilt, setYearBuilt] = useState<string>('30');
  const [distanceRange, setDistanceRange] = useState<string>('5');
  const [includeActive, setIncludeActive] = useState<boolean>(false);
  const [includePending, setIncludePending] = useState<boolean>(false);
  const [includeSold, setIncludeSold] = useState<boolean>(true);
  const [includeExpired, setIncludeExpired] = useState<boolean>(false);

  // "Within Same" toggles
  const [includeSchoolDistrict, setIncludeSchoolDistrict] = useState<boolean>(false);
  const [includeCounty, setIncludeCounty] = useState<boolean>(false);
  const [includeZipCode, setIncludeZipCode] = useState<boolean>(false);

  const [poolAllowed, setPoolAllowed] = useState<string>('unknown');

  const mockedStatusIsClosed = true;

  if (!isOpen) return null;

  const calculateSqftRange = () => {
    const baseSqft = propertyDetails.sqFt || 600;
    const percentage = parseInt(sqftRange === '∞' ? '100' : sqftRange);
    const range = Math.floor(baseSqft * (percentage / 100));
    return `${baseSqft - range} - ${baseSqft + range}`;
  };

  const calculateLotSizeRange = () => {
    const baseLotSize = propertyDetails.lotSize || 18.577;
    const percentage = parseInt(lotSize === '∞' ? '100' : lotSize);
    const range = baseLotSize * (percentage / 100);
    return `${(baseLotSize - range).toFixed(3)} - ${(baseLotSize + range).toFixed(3)}`;
  };

  const calculateBedsRange = () => {
    const baseBeds = propertyDetails.beds || 1;
    const diff = parseInt(beds === '∞' ? '3' : beds);
    return `No Min - ${baseBeds + diff}`;
  };

  const calculateBathsRange = () => {
    const baseBaths = propertyDetails.baths || 1;
    const diff = parseInt(baths === '∞' ? '3' : baths);
    return `No Min - ${baseBaths + diff}`;
  };

  const calculateYearBuiltRange = () => {
    const baseYear = propertyDetails.yearBuilt || 2000;
    const diff = parseInt(yearBuilt === '∞' ? '100' : yearBuilt);
    return `${baseYear - diff} - ${baseYear + diff}`;
  };

  const handleDateRangeClick = (value: string) => {
    setDateRange(value);
  };

  const handleSqftRangeClick = (value: string) => {
    setSqftRange(value);
  };

  const handleLotSizeClick = (value: string) => {
    setLotSize(value);
  };

  const handleBedsClick = (value: string) => {
    setBeds(value);
  };

  const handleBathsClick = (value: string) => {
    setBaths(value);
  };

  const handleYearBuiltClick = (value: string) => {
    setYearBuilt(value);
  };

  const getClosedWithinNumber = (range: string) => {
    switch (range) {
      case '3 Mo.': return 90;
      case '6 Mo.': return 180;
      case '9 Mo.': return 270;
      case '12 Mo.': return 360;
      case '24 Mo.': return 720;
      default: return '∞';
    }
  };

  const handleDistanceRangeClick = (value: string) => {
    setDistanceRange(value);
  };

  const renderButtonGroup = (
    options: string[],
    activeValue: string,
    handleClick: (value: string) => void
  ) => {
    return (
      <div className="flex flex-wrap gap-1.5">
        {options.map((item) => (
          <button
            key={item}
            className={`min-h-[28px] px-2.5 py-0.5 text-[13px] border rounded-md cursor-pointer transition-all ${activeValue === item
              ? "bg-[var(--color-button-blue)] border-[var(--color-button-blue)] text-white"
              : "bg-white border-gray-300 text-gray-600 hover:bg-gray-50"
              }`}
            onClick={() => handleClick(item)}
          >
            {item}
          </button>
        ))}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-200">
      <div className="bg-white rounded-xl shadow-lg w-[700px] max-w-[95%] max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h2 className="text-lg font-medium m-0">Filters</h2>
          <button
            onClick={onClose}
            className="bg-transparent border-0 p-1 cursor-pointer rounded-full hover:bg-gray-100"
          >
            <X size={20} strokeWidth={1.75} className="text-gray-500" />
          </button>
        </div>

        <div className="overflow-y-auto p-5 flex-1">
          <div className="flex gap-6">
            {/* Left Column */}
            <div className="w-1/2">
              <div className="w-[90px] py-0.5 px-2 text-xs border rounded text-[var(--color-button-blue)] bg-white mb-3">
                {mockedStatusIsClosed ? 'Active' : 'Closed'}
              </div>

              {/* Date Range Section */}
              <div className="mb-4">
                <h3 className="text-sm font-medium m-0 mb-1 text-gray-700">Date Range</h3>
                <p className="text-xs text-gray-500 m-0 mb-2">Relative to subject property</p>

                <span className="text-[15px] text-gray-800 font-medium ml-0.5">Within</span>
                <div className="ml-10 mb-1.5 mt-1">
                  <div className="grid grid-cols-3 gap-1.5 w-max">
                    {['3 Mo.', '6 Mo.', '9 Mo.', '12 Mo.', '24 Mo.', '∞'].map((item) => (
                      <button
                        key={item}
                        className={`min-h-[28px] px-2.5 py-0.5 text-[13px] border rounded-md cursor-pointer transition-all ${dateRange === item
                          ? "bg-[var(--color-button-blue)] border-[var(--color-button-blue)] text-white"
                          : "bg-white border-gray-300 text-gray-600 hover:bg-gray-50"
                          }`}
                        onClick={() => handleDateRangeClick(item)}
                      >
                        {item}
                      </button>
                    ))}
                  </div>
                </div>
                <div className="mt-2.5 leading-tight">
                  <span className="text-[var(--color-button-blue)] text-[15px] font-medium">Closed within </span><br />
                  <span className="text-[var(--color-button-blue)] text-[14px] font-semibold mr-6">{getClosedWithinNumber(dateRange)}</span>
                  <span className="text-gray-800 text-[15px] font-normal">Days</span>
                </div>
              </div>

              {/* Within Same Section */}
              <div className="mb-4">
                <h3 className="text-sm font-medium m-0 mb-1 text-gray-700">Within Same</h3>
                <div className="flex flex-col gap-2">
                  <label className="flex justify-between items-center w-full cursor-pointer">
                    <span className="text-xs text-gray-600">School District</span>
                    <div className="relative w-10 h-5">
                      <input
                        type="checkbox"
                        checked={includeSchoolDistrict}
                        onChange={() => setIncludeSchoolDistrict(!includeSchoolDistrict)}
                        className="sr-only"
                      />
                      <div className={`absolute inset-0 rounded-full transition-colors ${includeSchoolDistrict ? 'bg-[var(--color-button-blue)]' : 'bg-gray-300'}`}></div>
                      <div className={`absolute w-4 h-4 bg-white rounded-full top-0.5 left-0.5 transition-transform ${includeSchoolDistrict ? 'translate-x-5' : 'translate-x-0'}`}></div>
                    </div>
                  </label>

                  <label className="flex justify-between items-center w-full cursor-pointer">
                    <span className="text-xs text-gray-600">County</span>
                    <div className="relative w-10 h-5">
                      <input
                        type="checkbox"
                        checked={includeCounty}
                        onChange={() => setIncludeCounty(!includeCounty)}
                        className="sr-only"
                      />
                      <div className={`absolute inset-0 rounded-full transition-colors ${includeCounty ? 'bg-[var(--color-button-blue)]' : 'bg-gray-300'}`}></div>
                      <div className={`absolute w-4 h-4 bg-white rounded-full top-0.5 left-0.5 transition-transform ${includeCounty ? 'translate-x-5' : 'translate-x-0'}`}></div>
                    </div>
                  </label>

                  <label className="flex justify-between items-center w-full cursor-pointer">
                    <span className="text-xs text-gray-600">ZIP code</span>
                    <div className="relative w-10 h-5">
                      <input
                        type="checkbox"
                        checked={includeZipCode}
                        onChange={() => setIncludeZipCode(!includeZipCode)}
                        className="sr-only"
                      />
                      <div className={`absolute inset-0 rounded-full transition-colors ${includeZipCode ? 'bg-[var(--color-button-blue)]' : 'bg-gray-300'}`}></div>
                      <div className={`absolute w-4 h-4 bg-white rounded-full top-0.5 left-0.5 transition-transform ${includeZipCode ? 'translate-x-5' : 'translate-x-0'}`}></div>
                    </div>
                  </label>
                </div>
              </div>

              <div className="mb-4">
                <h3 className="text-sm font-medium m-0 mb-1 text-gray-700">Rent Price</h3>
                <div className="flex items-center gap-1.5">
                  <input
                    type="text"
                    className="w-[90px] py-0.5 px-2 text-xs border border-gray-300 rounded text-[var(--color-button-blue)] bg-white"
                    placeholder="No Min"
                  />
                  <span className="text-gray-400 text-base">-</span>
                  <input
                    type="text"
                    className="w-[90px] py-0.5 px-2 text-xs border border-gray-300 rounded text-[var(--color-button-blue)] bg-white"
                    placeholder="No Max"
                  />
                </div>
              </div>

              {/* Beds Section */}
              <div className="mb-4">
                <h3 className="text-sm font-medium m-0 mb-1 text-gray-700">Beds</h3>
                <p className="text-xs text-gray-500 m-0 mb-2">Relative to subject property</p>
                <div className="flex items-center gap-1.5">
                  <span className="text-[13px] text-gray-500 mr-0.5">+/-</span>
                  {renderButtonGroup(
                    ['0', '1', '2', '3', '∞'],
                    beds,
                    handleBedsClick
                  )}
                </div>
                <div className="mt-0.5">
                  <p className="text-xs text-[var(--color-button-blue)] m-0">{calculateBedsRange()}</p>
                </div>
              </div>

              {/* Baths Section */}
              <div className="mb-4">
                <h3 className="text-sm font-medium m-0 mb-1 text-gray-700">Baths</h3>
                <p className="text-xs text-gray-500 m-0 mb-2">Relative to subject property</p>
                <div className="flex items-center gap-1.5">
                  <span className="text-[13px] text-gray-500 mr-0.5">+/-</span>
                  {renderButtonGroup(
                    ['0', '1', '2', '3', '∞'],
                    baths,
                    handleBathsClick
                  )}
                </div>
                <div className="mt-0.5">
                  <p className="text-xs text-[var(--color-button-blue)] m-0">{calculateBathsRange()}</p>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="w-1/2">
              {/* Sqft Section */}
              <div className="mb-4">
                <h3 className="text-sm font-medium m-0 mb-1 text-gray-700">Sqft</h3>
                <p className="text-xs text-gray-500 m-0 mb-2">Relative to subject property</p>
                <div className="flex items-center gap-1.5">
                  <span className="text-[13px] text-gray-500 mr-0.5">+/-</span>
                  {renderButtonGroup(
                    ['5%', '10%', '15%', '20%', '25%', '∞'],
                    sqftRange,
                    handleSqftRangeClick
                  )}
                </div>
                <div className="mt-0.5">
                  <p className="text-xs text-[var(--color-button-blue)] m-0">{calculateSqftRange()}</p>
                </div>
              </div>

              {/* Lot Size */}
              <div className="mb-4">
                <h3 className="text-sm font-medium m-0 mb-1 text-gray-700">Lot Size</h3>
                <p className="text-xs text-gray-500 m-0 mb-2">Relative to subject property</p>
                <div className="flex items-center gap-1.5">
                  <span className="text-[13px] text-gray-500 mr-0.5">+/-</span>
                  {renderButtonGroup(
                    ['5%', '10%', '15%', '20%', '25%', '∞'],
                    lotSize,
                    handleLotSizeClick
                  )}
                </div>
                <div className="mt-0.5">
                  <p className="text-xs text-[var(--color-button-blue)] m-0">{calculateLotSizeRange()}</p>
                </div>
              </div>

              {/* Year Built */}
              <div className="mb-4">
                <h3 className="text-sm font-medium m-0 mb-1 text-gray-700">Year Built</h3>
                <p className="text-xs text-gray-500 m-0 mb-2">Relative to subject property</p>
                <div className="flex items-center gap-1.5">
                  <span className="text-[13px] text-gray-500 mr-0.5">+/-</span>
                  {renderButtonGroup(
                    ['5', '15', '25', '35', '∞'],
                    yearBuilt,
                    handleYearBuiltClick
                  )}
                </div>
                <div className="mt-0.5">
                  <p className="text-xs text-[var(--color-button-blue)] m-0">{calculateYearBuiltRange()}</p>
                </div>
              </div>

              {/* Cumulative DOM */}
              <div className="mb-4">
                <h3 className="text-sm font-medium m-0 mb-1 text-gray-700">Cumulative DOM</h3>
                <div className="flex items-center gap-1.5">
                  <input
                    type="number"
                    className="w-[90px] py-0.5 px-2 text-xs border border-gray-300 rounded text-[var(--color-button-blue)] bg-white"
                    placeholder="No Min"
                  />
                  <span className="text-gray-400 text-base">-</span>
                  <input
                    type="number"
                    className="w-[90px] py-0.5 px-2 text-xs border border-gray-300 rounded text-[var(--color-button-blue)] bg-white"
                    placeholder="No Max"
                  />
                </div>
              </div>

              {/* Covered Parking */}
              <div className="mb-4">
                <h3 className="text-sm font-medium m-0 mb-1 text-gray-700">Covered Parking</h3>
                <div className="flex items-center gap-1.5">
                  <input
                    type="number"
                    className="w-[90px] py-0.5 px-2 text-xs border border-gray-300 rounded text-[var(--color-button-blue)] bg-white"
                    placeholder="No Min"
                  />
                  <span className="text-gray-400 text-base">-</span>
                  <input
                    type="number"
                    className="w-[90px] py-0.5 px-2 text-xs border border-gray-300 rounded text-[var(--color-button-blue)] bg-white"
                    placeholder="No Max"
                  />
                </div>
              </div>

              {/* Pool Allowed */}
              <div className="mb-4">
                <h3 className="text-sm font-medium m-0 mb-1 text-gray-700">Pool Allowed</h3>
                <select
                  className="w-[110px] py-0.5 px-2 text-xs border border-gray-300 rounded text-[var(--color-button-blue)] bg-white"
                  value={poolAllowed}
                  onChange={e => setPoolAllowed(e.target.value)}
                >
                  <option value="unknown">Unknown</option>
                  <option value="yes">Yes</option>
                  <option value="no">No</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 flex justify-end space-x-2">
          <button className="px-4 py-1.5 text-sm rounded border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 transition-colors">
            Reset
          </button>
          <button
            className="px-4 py-1.5 text-sm rounded border-none bg-[var(--color-button-blue)] text-white cursor-pointer hover:bg-[var(--color-button-blue)]/90 transition-colors"
            onClick={onClose}
          >
            Apply
          </button>
        </div>
      </div>
    </div>
  );
};

export default CompsFilterModal;