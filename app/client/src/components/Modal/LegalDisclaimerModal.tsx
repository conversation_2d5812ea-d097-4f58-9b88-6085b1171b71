import React from 'react';
import ModalWrapper from './ModalWrapper';
import { LEGAL_DISCLAIMERS } from '@/constants/legalDisclaimer';

interface LegalDisclaimerModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
}
// For Banner
const LegalDisclaimerModal: React.FC<LegalDisclaimerModalProps> = ({
  isOpen,
  onClose,
  title = ""
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-300 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-xl w-[80vw] max-w-[1000px] max-h-[90dvh] overflow-auto">
        <ModalWrapper
          onClose={onClose}
          contentClassName="px-7 space-y-4 mb-4"
        >
          <h3 className="text-3xl font-thin font-heading">{title}</h3>
          <p className="text-sm text-dark-gray font-thin">
            <sup>*</sup> {LEGAL_DISCLAIMERS.MORTGAGE_DISCLAIMER}
          </p>
          <p className="text-xs text-dark-gray font-thin">
            {LEGAL_DISCLAIMERS.SERVICES_DISCLAIMER}
          </p>
          <button
            className="w-30 mt-8 px-8 py-4 bg-dark-gray text-white text-base font-medium hover:bg-dark-gray/80 transition-colors duration-200 rounded-4xl"
            onClick={onClose}
          >
            Close
          </button>

        </ModalWrapper>
      </div>
    </div>
  );
};

export default LegalDisclaimerModal;