import React, { useState } from 'react';
import { Printer } from 'lucide-react';

import ModalWrapper from './ModalWrapper';
import ModalButton from './ModalButton';
import TermsWithCheckbox from './TermsWithCheckbox';
import ActionButton from '../Common/ActionButton';

interface SignupTermsOfUseModalProps {
  onClose: () => void;
  onAccept: () => void;
}

{/* <SignupTermsOfUseModal onClose={closeActualTermsModal} onAccept={handleAceptTerms} /> */ }

const termsContents = [
  {
    content: "The information provided in this document is for general informational purposes only and does not constitute investment advice. Lennar Corporation does not guarantee the accuracy, completeness, or reliability of any information provided regarding rental investments. Any reliance you place on such information is strictly at your own risk. Before making any investment decisions, you should consult with a qualified financial advisor who understands your individual circumstances and objectives. Lennar Corporation is not responsible for any losses or damages that may arise from your reliance on the information provided. Investing in rental properties involves risks, including the potential loss of principal. Past performance is not indicative of future results. Market conditions, property values, and rental income can fluctuate, and there is no assurance that any investment strategy will be successful.",
    checkboxLabel: "I agree"
  },
  {
    content: "By selecting “I consent”, you consent to receive marketing SMS communications from Lennar Homes and other communications (via mail, email, or telephone) from Lennar Homes and its affiliates, including Lennar Mortgage, LLC, Lennar Title, Inc., and Lennar Insurance Agency, LLC. Calls and text messages from Lennar Homes may be sent via autodialer and use an artificial or prerecorded voice. Consent not required for purchase of Lennar Home. Message and data rates may apply. Message frequency may vary. Text HELP for help. Text STOP to unsubscribe. You may unsubscribe at any time and no further messages will be sent. See our privacy policy",
    checkboxLabel: "I consent"
  },
  {
    content: "Terms & Conditions:",
    link: "https://www.lennar.com/termsandconditions",
    checkboxLabel: "I agree"
  },
  {
    content: "Privacy Policy:",
    link: "/privacy-policy/",
    checkboxLabel: "I agree"
  },
  {
    content: "Do Not Sell or Share:",
    link: "https://www.lennar.com/contact/communicationpreferences",
    checkboxLabel: "I agree"
  },
]

const SignupTermsOfUseModal: React.FC<SignupTermsOfUseModalProps> = ({ onClose, onAccept }) => {
  const [checkboxStates, setCheckboxStates] = useState<boolean[]>(
    new Array(termsContents.length).fill(false)
  );

  // Function to handle individual checkbox changes
  const handleCheckboxChange = (index: number) => {
    const newCheckboxStates = [...checkboxStates];
    newCheckboxStates[index] = !newCheckboxStates[index];
    setCheckboxStates(newCheckboxStates);
  };

  // Check if all checkboxes are selected
  const allChecked = checkboxStates.every(state => state === true);

  return (
    <ModalWrapper onClose={onClose} contentClassName="px-7 space-y-4">
      <h4 className="text-xl font-semibold text-[var(--color-text-black)]">
        Terms of Use
      </h4>
      {termsContents.map((terms, index) => (
        <TermsWithCheckbox
          key={index}
          termsText={terms.content}
          link={terms.link}
          onCheckChange={() => handleCheckboxChange(index)}
          checkboxLabel={terms.checkboxLabel}

        />
      ))}

      <div className="flex justify-between">
        <ActionButton
          text="Print"
          onClick={() => window.print()}
          icon={<Printer size={14} />}
          className="py-1 px-4"
        />
        <ModalButton title="I agree with the Terms of Use" onClick={onAccept} className="w-1/2 text-[14px]" disabled={!allChecked} />
      </div>
    </ModalWrapper>
  );
};

export default SignupTermsOfUseModal;