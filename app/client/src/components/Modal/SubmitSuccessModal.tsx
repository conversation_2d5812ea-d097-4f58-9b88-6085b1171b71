import React from 'react';
import { PartyPopper } from 'lucide-react';
import { OfferSubmissionStep } from '../../types/offerTypes';

import ProgressIndicator from './ProgressIndicator';
import ModalWrapper from './ModalWrapper';
import OfferSubmissionHeader from './OfferSubmissionHeader';

interface SubmitSuccessModalProps {
  handleMainClose: () => void;
  currentModalStep: OfferSubmissionStep;
}

const SubmitSuccessModal: React.FC<SubmitSuccessModalProps> = ({
  handleMainClose,
  currentModalStep
}) => {
  return (
    <ModalWrapper onClose={handleMainClose}>
      <OfferSubmissionHeader
        title="Success!"
        onClose={handleMainClose}
      />
      <ProgressIndicator currentStep={currentModalStep} />
      <div className="px-12 text-center">
        <p className="mb-6 text-left text-sm text-dark-gray mx-auto">
          Thank you for submitting this offer. Please check your inbox for an
          email confirmation. A Lennar associate will be reaching out to you
          soon to go through the next steps with you directly.
          If you have any questions or comments in the meantime,
          please email <a href="mailto:<EMAIL>" className="font-medium text-button-blue"><EMAIL></a>.
        </p>
        <PartyPopper size={56} className="text-dark-gray mx-auto" strokeWidth={1} />
      </div>
    </ModalWrapper>
  );
};

export default SubmitSuccessModal;