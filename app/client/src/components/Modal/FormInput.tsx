import React from 'react';

interface FormInputProps {
  id: string;
  label: string;
  type?: 'text' | 'email' | 'tel' | 'number';
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  required?: boolean;
  colorClass?: string;
  width?: string;
  disabled?: boolean;
}

const FormInput: React.FC<FormInputProps> = ({
  id,
  label,
  type = 'text',
  value,
  onChange,
  placeholder,
  required = false,
  colorClass = '',
  width = 'w-full',
  disabled = false
}) => {
  return (
    <div className={`flex flex-col text-[13px]  ${width}`}>
      <label htmlFor={id} className="text-dark-gray">{label}</label>
      <input
        id={id}
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className={`px-2 py-1 border border-dark-gray rounded w-full ${colorClass} ${disabled ? 'text-gray-500 bg-gray-100 border-gray-300 cursor-not-allowed ' : ''}`}
        required={required}
        disabled={disabled}
      />
    </div>
  );
};

export default FormInput;