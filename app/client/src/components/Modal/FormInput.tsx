import React from 'react';

interface FormInputProps {
  id: string;
  label: string;
  type?: 'text' | 'email' | 'tel' | 'number';
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  required?: boolean;
  colorClass?: string;
  width?: string;
}

const FormInput: React.FC<FormInputProps> = ({
  id,
  label,
  type = 'text',
  value,
  onChange,
  placeholder,
  required = false,
  colorClass = '',
  width = 'w-full'
}) => {
  return (
    <div className={`flex flex-col text-[13px]  ${width}`}>
      <label htmlFor={id} className="text-dark-gray">{label}</label>
      <input
        id={id}
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className={`px-2 py-1 border border-dark-gray rounded w-full ${colorClass}`}
        required={required}
      />
    </div>
  );
};

export default FormInput;