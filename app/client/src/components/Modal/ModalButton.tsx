import React from 'react';

interface ModalButtonProps {
  title: string;
  onClick: () => void;
  disabled?: boolean;
  className?: string;
}

const ModalButton: React.FC<ModalButtonProps> = ({ title, onClick, disabled = false, className = "w-full font-bold text-[18px]" }) => {
  return (
    <button
      className={`h-10 rounded transition-colors duration-200 ${className}
        ${disabled
          ? 'bg-medium-gray-20 text-[var(--color-dark-gray)] cursor-not-allowed'
          : 'bg-green-primary text-white hover:bg-green-primary/80 cursor-pointer'
        }`}
      onClick={onClick}
      disabled={disabled}
    >
      {title}
    </button>
  );
};

export default ModalButton;