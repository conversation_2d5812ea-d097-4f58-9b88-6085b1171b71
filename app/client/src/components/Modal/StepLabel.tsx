import React from 'react';
import { OfferSubmissionStep } from '../../types/offerTypes';
import { OFFER_STEP_LABELS } from '../../constants/offerConstants';

interface StepLabelProps {
  stepId: OfferSubmissionStep;
  isCompletedOrActive: boolean;
}

const StepLabel: React.FC<StepLabelProps> = ({ stepId, isCompletedOrActive }) => (
  <p className={`mt-2 text-sm text-center whitespace-nowrap ${isCompletedOrActive
      ? 'text-[var(--color-button-blue)]'
      : 'text-[var(--color-dark-gray)]'
    }`}>
    {OFFER_STEP_LABELS[stepId]}
  </p>
);

export default StepLabel;