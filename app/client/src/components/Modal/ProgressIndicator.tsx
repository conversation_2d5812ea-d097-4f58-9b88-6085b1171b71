import React, { Fragment } from 'react';
import { OfferSubmissionStep } from '../../types/offerTypes';
import { OFFER_STEP_NUMBERS } from '../../constants/offerConstants';

import StepIndicator from './StepIndicator';
import Step<PERSON>abel from './StepLabel';
import ConnectingLine from './ConnectingLine';

interface ProgressIndicatorProps {
    currentStep: OfferSubmissionStep;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({ currentStep }) => {

    // Extract steps from constants, sort them by their numeric order,
    // and map to an array of objects with step IDs for rendering
    const steps = Object.entries(OFFER_STEP_NUMBERS)
        .sort(([, a], [, b]) => a - b) // Sort by step number to ensure correct order
        .map(([step]) => ({ id: step as OfferSubmissionStep }));

    // Helper function to determine if a step should be marked as completed or active
    const isStepActiveOrCompleted = (stepId: OfferSubmissionStep): boolean => {
        if (currentStep === OfferSubmissionStep.SUCCESS) return true;
        if (currentStep === OfferSubmissionStep.SUBMIT) {
            return stepId === OfferSubmissionStep.START || stepId === OfferSubmissionStep.SUBMIT;
        }
        return stepId === currentStep;
    };

    const isLineActive = (stepIndex: number): boolean => {
        const stepId = steps[stepIndex].id;
        return OFFER_STEP_NUMBERS[stepId] < OFFER_STEP_NUMBERS[currentStep] ||
            currentStep === OfferSubmissionStep.SUCCESS;
    };

    return (
        <div className="flex items-start justify-between px-20 text-center">
            {steps.map((step, index) => {
                const isCompletedOrActive = isStepActiveOrCompleted(step.id);

                return (
                    <Fragment key={step.id}>
                        <div className="flex flex-col items-center text-center basis-0 grow relative w-20">
                            <StepIndicator
                                stepId={step.id}
                                isCompletedOrActive={isCompletedOrActive}
                            />
                            <StepLabel
                                stepId={step.id}
                                isCompletedOrActive={isCompletedOrActive}
                            />
                        </div>
                        {index < steps.length - 1 && (
                            <ConnectingLine isActive={isLineActive(index)} />
                        )}
                    </Fragment>
                );
            })}
        </div>
    );
};

export default ProgressIndicator;