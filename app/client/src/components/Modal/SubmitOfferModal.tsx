import React from 'react';
import { OfferSubmissionStep, UserRole } from '../../types/offerTypes';

import ModalWrapper from './ModalWrapper';
import OfferSubmissionHeader from './OfferSubmissionHeader';
import ProgressIndicator from './ProgressIndicator';
import FormInput from './FormInput';
import FormTextarea from './FormTextarea';
import FinancingCheckbox from './FinancingCheckbox';
import TermsOfUseAgreement from './TermsOfUseAgreement';
import ModalButton from './ModalButton';
import FormInputNumber from './FormInpuNumber';

interface SubmitOfferModalProps {
  email: string;
  setEmail: (value: string) => void;
  phone: string;
  setPhone: (value: string) => void;
  firstName: string;
  setFirstName: (value: string) => void;
  lastName: string;
  setLastName: (value: string) => void;
  purchasePrice: number;
  setPurchasePrice: (value: number) => void;
  comments: string;
  setComments: (value: string) => void;
  financing: "yes" | "no" | "";
  setFinancing: (value: "yes" | "no" | "") => void;
  compensation: string;
  setCompensation: (value: string) => void;
  userRole: UserRole;
  address?: string;
  lotNumber?: string | number;
  currentModalStep: OfferSubmissionStep;
  handleSubmitOffer: () => void;
  handleMainClose: () => void;
  openTermsModal: (e: React.MouseEvent) => void;
  listPrice: number;
}

const SubmitOfferModal: React.FC<SubmitOfferModalProps> = ({
  email,
  setEmail,
  phone,
  setPhone,
  firstName,
  setFirstName,
  lastName,
  setLastName,
  purchasePrice,
  setPurchasePrice,
  comments,
  setComments,
  financing,
  setFinancing,
  compensation,
  setCompensation,
  userRole,
  address,
  lotNumber,
  currentModalStep,
  handleSubmitOffer,
  handleMainClose,
  openTermsModal,
  listPrice,
}) => {
  // generate a random number between 0.795 to 0.805 of the list price
  const randomNumber = Math.random() * (0.805 - 0.795) + 0.795;
  const lowestPurchasePrice = listPrice * randomNumber;

  return (
    <ModalWrapper onClose={handleMainClose}>
      <OfferSubmissionHeader
        title="Submit Offer"
        onClose={handleMainClose}
        address={address}
        lotNumber={lotNumber}
      />
      <ProgressIndicator currentStep={currentModalStep} />

      <form className="flex flex-col gap-1 text-base">
        {/* First Name and Last Name */}
        <div className="flex gap-2">
          <FormInput
            id="firstName"
            label="First Name *"
            type="text"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            width="w-1/2"
            required
          />
          <FormInput
            id="lastName"
            label="Last Name *"
            type="text"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            width="w-1/2"
            required
          />
        </div>
        {/* email and phone */}
        <div className="flex gap-2">
          <FormInput
            id="email"
            label="Email *"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            width="w-1/2"
            required
          />
          <FormInput
            id="phone"
            label="Phone Number *"
            type="tel"
            value={phone}
            placeholder="Enter your Phone Number"
            onChange={(e) => setPhone(e.target.value)}
            width="w-1/2"
            required
          />
        </div>
        {/* Offer price */}
        <FormInputNumber
          id="purchasePrice"
          label="Purchase Price *"
          type="number"
          value={purchasePrice}
          onChange={setPurchasePrice}
          required
        />
        {+purchasePrice < lowestPurchasePrice && (
          <p className="text-red text-sm">
            Please increase your offer price
          </p>
        )}
        {/* Comment */}
        <FormTextarea
          id="comments"
          label="Comments"
          value={comments}
          onChange={(e) => setComments(e.target.value)}
          placeholder="Enter your Comments"
          minHeight="min-h-[70px]"
        />

        {/* Commented out compensation field - all roles now show financing checkbox
        {userRole === 'agent' ? (
          <FormInput
            id="compensation"
            label="What is your compensation? *"
            type="text"
            value={compensation}
            onChange={(e) => setCompensation(e.target.value)}
            placeholder="Enter your compensation"
            required
          />
        ) : (
          <FinancingCheckbox
            value={financing}
            onChange={setFinancing}
          />
        )}
        */}
        <FinancingCheckbox
          value={financing}
          onChange={setFinancing}
          error={financing === "" ? "Please select an option" : ""}
          isRequired
        />
        {/* Term of use */}
        <TermsOfUseAgreement openTermsModal={openTermsModal} />
      </form>
      <ModalButton
        onClick={handleSubmitOffer}
        title="Submit Offer"
        disabled={+purchasePrice < lowestPurchasePrice ||
          financing === "" ||
          !firstName ||
          !lastName ||
          !email ||
          !phone}
      />
    </ModalWrapper>
  );
};

export default SubmitOfferModal;