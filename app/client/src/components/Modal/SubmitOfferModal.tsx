import React from 'react';
import { OfferSubmissionStep, UserRole } from '../../types/offerTypes';

import ModalWrapper from './ModalWrapper';
import OfferSubmissionHeader from './OfferSubmissionHeader';
import ProgressIndicator from './ProgressIndicator';
import FormInput from './FormInput';
import FormTextarea from './FormTextarea';
import FinancingCheckbox from './FinancingCheckbox';
import TermsOfUseAgreement from './TermsOfUseAgreement';
import ModalButton from './ModalButton';
import FormInputNumber from './FormInpuNumber';
import ContactInfo from '../ContactInfo';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import { formatTitleWithStepCount } from '@/lib/utils/formatUtils';
import PMCheckbox from './PMCheckbox';

interface SubmitOfferModalProps {
  email: string;
  setEmail: (value: string) => void;
  phone: string;
  setPhone: (value: string) => void;
  firstName: string;
  setFirstName: (value: string) => void;
  lastName: string;
  setLastName: (value: string) => void;
  purchasePrice: number;
  setPurchasePrice: (value: number) => void;
  comments?: string;
  setComments: (value: string) => void;
  financing: "yes" | "no" | "";
  setFinancing: (value: "yes" | "no" | "") => void;
  PM: "yes" | "no" | "";
  setPM: (value: "yes" | "no" | "") => void;
  compensation: string;
  setCompensation: (value: string) => void;
  userRole: UserRole;
  address?: string;
  lotNumber?: string | number;
  currentModalStep: OfferSubmissionStep;
  handleSubmitOffer: () => void;
  handleMainClose: () => void;
  openTermsModal: (e: React.MouseEvent) => void;
  listPrice: number;
  agentPhone?: string;
}

const SubmitOfferModal: React.FC<SubmitOfferModalProps> = ({
  email,
  setEmail,
  phone,
  setPhone,
  firstName,
  setFirstName,
  lastName,
  setLastName,
  purchasePrice,
  setPurchasePrice,
  comments,
  setComments,
  financing,
  setFinancing,
  PM,
  setPM,
  address,
  lotNumber,
  currentModalStep,
  handleSubmitOffer,
  handleMainClose,
  openTermsModal,
  listPrice,
  agentPhone,
}) => {
  const { isMobile } = useBreakpoint();
  const formattedTitle = formatTitleWithStepCount(currentModalStep, "Submit Offer", isMobile);

  return (
    <ModalWrapper onClose={handleMainClose}>
      <OfferSubmissionHeader
        title={formattedTitle}
        onClose={handleMainClose}
        address={address}
        lotNumber={lotNumber}
      />
      {!isMobile && <ProgressIndicator currentStep={currentModalStep} />}

      {agentPhone && (
        <div className="flex items-center justify-center gap-2 mb-4">
          <span className="text-sm text-gray-600">Contact:</span>
          <ContactInfo phone={agentPhone} />
        </div>
      )}

      <form className="flex flex-col gap-1 text-base">
        {/* First Name and Last Name */}
        <div className="flex gap-2">
          <FormInput
            id="firstName"
            label="First Name *"
            type="text"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            width="w-1/2"
            required
          />
          <FormInput
            id="lastName"
            label="Last Name *"
            type="text"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            width="w-1/2"
            required
          />
        </div>
        {/* email and phone */}
        <div className="flex gap-2">
          <FormInput
            id="email"
            label="Email *"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            width="w-1/2"
            required
            disabled={true}
          />
          <FormInput
            id="phone"
            label="Phone Number *"
            type="tel"
            value={phone}
            placeholder="Enter your Phone Number"
            onChange={(e) => setPhone(e.target.value)}
            width="w-1/2"
            required
          />
        </div>
        {/* Offer price */}
        <FormInputNumber
          id="purchasePrice"
          label="Purchase Price *"
          type="number"
          value={purchasePrice}
          onChange={setPurchasePrice}
          required
        />
        {/* {+purchasePrice < lowestPurchasePrice && (
          <p className="text-red text-sm">
            Please increase your offer price
          </p>
        )} */}
        {/* Comment */}
        <FormTextarea
          id="comments"
          label="Comments"
          value={comments || ''}
          onChange={(e) => setComments(e.target.value)}
          placeholder="Enter your Comments"
          minHeight="min-h-[70px]"
        />
        <FinancingCheckbox
          value={financing}
          onChange={setFinancing}
          error={financing === "" ? "Please select an option" : ""}
          isRequired
        />
        {/* PM Checkbox */}
        <PMCheckbox
          value={PM}
          onChange={setPM}
          error={PM === "" ? "Please select an option" : ""}
          isRequired
        />
        {/* Term of use */}
        <TermsOfUseAgreement openTermsModal={openTermsModal} />
      </form>
      <ModalButton
        onClick={handleSubmitOffer}
        title="Submit Offer"
        disabled={
          // +purchasePrice < lowestPurchasePrice ||
          financing === "" ||
          PM === "" ||
          !firstName ||
          !lastName ||
          !email ||
          !phone}
      />
    </ModalWrapper>
  );
};

export default SubmitOfferModal;