import React from 'react';
import { LennarSinglePropertyDataType } from '@/lib/utils/types';
import PropertyCard from '../components/PropertyList/PropertyCard';
import FinancingOfferBanner from '../components/FinancingOfferBanner';
import { shouldShowBanner } from '../lib/utils/layoutUtils';

interface PropertyCardWithBannerProps {
  property: LennarSinglePropertyDataType;
  index: number;
  totalCount?: number;
  containerWidth: number;
  onSeeDetailsClick: () => void;
  containerPadding?: number;
}

const PropertyCardWithBanner: React.FC<PropertyCardWithBannerProps> = ({
  property,
  index,
  totalCount,
  containerWidth,
  onSeeDetailsClick,
  containerPadding = 32
}) => {
  const showBanner = shouldShowBanner({
    containerWidth,
    index,
    containerPadding
  });

  return (
    <>
      <PropertyCard
        property={property}
        index={index}
        totalCount={totalCount}
      />
      {
        showBanner && (
          <FinancingOfferBanner onSeeDetailsClick={onSeeDetailsClick} />
        )
      }
    </>
  );
};

export default PropertyCardWithBanner;