import React from 'react'
import PromotionalBanner from './PromotionalBanner';
import PropertySpecifications from './PropertyDetails/PropertySpecifications';
import AddToCartButton from './Cart/AddToCartButton';


interface MobilePropertyOverviewProps {
  formattedPrice: string;
  address?: string;
  onStartOfferClick: () => void;
  beds?: number;
  baths?: number;
  sqft?: number;
  stories?: string;
  garage?: string;
  isLoading?: boolean;
}

const MobilePropertyOverview: React.FC<MobilePropertyOverviewProps> = ({
  formattedPrice,
  address,
  onStartOfferClick,
  beds,
  baths,
  sqft,
  stories,
  garage,
  isLoading = false
}) => {

  return (
    <div className="flex flex-col gap-1.5 text-base text-dark-gray mt-2 font-medium">
      <PromotionalBanner isMobile={true} />
      <p>{address}</p>
      <PropertySpecifications
        beds={beds}
        baths={baths}
        sqft={sqft}
        stories={stories}
        garage={garage}
      />
      <div className="flex items-center justify-between gap-4">
        <div className="flex flex-col items-center">
          <span className="inline-block font-thin text-2xl font-heading">{formattedPrice}</span>
          <span className="inline-block font-thin text-xs -mt-1">List Price</span>
        </div>
        <AddToCartButton />
        <button onClick={onStartOfferClick} disabled={isLoading} className="flex-1 min-h-8 px-3 bg-green-primary border border-green-primary text-white rounded-lg text-base">
          {isLoading ? "Loading..." : (
            <>
              Start Purchase
            </>
          )}
        </button>

      </div>

    </div>
  )
}

export default MobilePropertyOverview