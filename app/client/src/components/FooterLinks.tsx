import React from 'react';
import { SquareArrowOutUpRight } from "lucide-react";
import { FOOTER_LINKS } from "../constants/footerLinks";
import { MobileTooltip } from "@/components/ui/tooltip";
import { useBreakpoint } from '@/hooks/useBreakpoint';

const FooterLinks: React.FC = () => {
  const { isMobile } = useBreakpoint();
  return (
    <div className="flex flex-col items-start justify-center min-w-[180px] gap-1 text-button-blue my-1">
      {FOOTER_LINKS.map((link) => (
        <div
          key={link.id}
          className={`flex items-center  ${isMobile ? 'gap-5' : 'gap-2'}`}
        >
          {link.onClick ? (
            // Tailwind classes don't work here because the global CSS in index.css has !important rules
            // that override any Tailwind classes. The footer button styling in index.css uses !important
            // for all properties, forcing those styles regardless of inline classes. Use data-mobile attribute
            // instead, which is already configured to work with the CSS rules in index.css.
            <button
              onClick={link.onClick}
              data-mobile={isMobile ? "true" : "false"}
            >
              {link.title}
            </button>
          ) : (
            <a
              href={link.href}
              className={` ${isMobile ? 'py-1 text-xs' : 'text-tiny'} text-left tracking-tight leading-none hover:text-button-blue/80 transition-all duration-300 ease-in-out  text-button-blue`}
              target="_blank"
            >
              {link.title}
            </a>
          )}
          {link.icon && <link.icon size={link.iconSize} />}
        </div>
      ))}

      <MobileTooltip
        className="mx-2"
        content={
          <div className={`${isMobile ? 'w-[90vw]' : 'w-[1/3] max-w-[600px]'} overflow-y-auto overflow-x-auto bg-white text-dark-gray rounded-md py-5 px-4 mx-auto z-300 border border-medium-gray-20 text-xs`}>
            <p>
              You can request a file with a copy of your account data, including activity and settings.
            </p>
            <div className="flex items-start justify-center mt-3 space-x-8">
              <button className="flex items-center justify-center px-3 py-2 bg-button-blue font-semibold text-white text-xs rounded-md hover:bg-button-blue/80 transition-colors duration-200 ">
                Request
              </button>
              <div className="flex items-center gap-2 my-auto">
                <a href="https://lennar-privacy.my.onetrust.com/webform/********-ebef-41ce-ad79-79c8275b52ce/4a145a0f-1efc-4ad5-8878-7c3609052d3d" target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-button-blue hover:bg-button-blue/80 transition-all duration-200 ease-in-out ">
                  Lennar Privacy Request
                </a>
                <SquareArrowOutUpRight size={10} />
              </div>
            </div>
          </div>
        }
      >
        <div className="flex items-center gap-2">
          <p
            className={`${isMobile ? 'py-1 text-xs' : 'text-tiny '} text-left tracking-tight leading-none  hover:text-button-blue/80 transition-all duration-300 ease-in-out  text-button-blue cursor-pointer`}
          >
            User Data Control
          </p>
          <SquareArrowOutUpRight size={10} />
        </div>
      </MobileTooltip>
    </div>
  );
};

export default FooterLinks;