import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LENNAR_MARKETS } from "@/constants/markets";

import { Command, CommandInput, CommandList } from "@/components/ui/command";

import {
  CheckIcon,
  ChevronDownIcon,
  RefreshCcwIcon,
  RotateCcwIcon,
  SearchIcon,
} from "lucide-react";
import React from "react";

import { ScrollArea } from "@/components/ui/scroll-area";
import { isEqual } from "lodash";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

import { Link, useNavigate, useSearch } from "@tanstack/react-router";

export const MarketFilter = (props: { useMobile?: boolean }) => {
  const { useMobile = false } = props;

  const navigate = useNavigate();
  const search = useSearch({ strict: false });
  const [searchMarketText, setSearchMarketText] = React.useState("");

  // mobile
  const [isOpen, setIsOpen] = React.useState(false);
  const [expandedStates, setExpandedStates] = React.useState<Set<string>>(
    new Set()
  );

  const urlMarkets = React.useMemo(() => {
    return (
      decodeURIComponent(search.filter || "")
        .split(";")
        .find((f) => f.includes("market"))
        ?.split("=in=")[1]
        ?.replace(/^\(|\)$/g, "")
        ?.split(",")
        ?.map((m) => m.trim()?.replace(/^'|'$/g, "")) || []
    );
  }, [search.filter]);

  const [selectedMarkets, setSelectedMarkets] =
    React.useState<string[]>(urlMarkets);

  const filteredMarkets = React.useMemo(() => {
    return LENNAR_MARKETS.filter((m) => {
      const matchesStateLevel = m.state
        .toLowerCase()
        .includes(searchMarketText.toLowerCase());
      const matchesMarket = m.markets.some((stateMarket) =>
        stateMarket.toLowerCase().includes(searchMarketText.toLowerCase())
      );
      return matchesStateLevel || matchesMarket;
    }).map((m) => ({
      ...m,
      markets: m.state.toLowerCase().includes(searchMarketText.toLowerCase())
        ? m.markets
        : m.markets.filter((stateMarket) =>
            stateMarket.toLowerCase().includes(searchMarketText.toLowerCase())
          ),
    }));
  }, [searchMarketText]);

  React.useEffect(() => {
    if (isEqual(urlMarkets, selectedMarkets)) return;

    setSelectedMarkets(urlMarkets);
  }, [urlMarkets]);

  const currentSelectedStates = React.useMemo(() => {
    const states = LENNAR_MARKETS.filter((m) =>
      m.markets.some((stateMarket) => selectedMarkets.includes(stateMarket))
    ).map((m) => m.state);

    return states;
  }, [selectedMarkets]);

  const marketsFilterChanged = React.useMemo(() => {
    return !isEqual(
      selectedMarkets.sort((a, b) => a.localeCompare(b)),
      urlMarkets.sort((a, b) => a.localeCompare(b))
    );
  }, [selectedMarkets, urlMarkets]);

  // mobile
  const toggleState = (state: string) => {
    const newExpanded = new Set(expandedStates);
    if (newExpanded.has(state)) {
      newExpanded.delete(state);
    } else {
      newExpanded.add(state);
    }
    setExpandedStates(newExpanded);
  };

  const toggleMarket = (market: string) => {
    const newSelected = selectedMarkets.includes(market)
      ? selectedMarkets.filter((m) => m !== market)
      : [...selectedMarkets, market];

    setSelectedMarkets?.(newSelected);
    
    // Immediately update URL when market is toggled
    navigate({
      search: (prev) => ({
        ...prev,
        filter: encodeURIComponent(
          [
            ...(newSelected.length > 0
              ? [
                  `market=in=(${newSelected
                    .map((m) => `'${m}'`)
                    .sort((a, b) => a.localeCompare(b))
                    .join(",")})`,
                ]
              : []),
            ...(decodeURIComponent(prev.filter || "")
              ?.split(";")
              .filter((f) => !f.includes("market") && f.length > 0) ||
              []),
          ].join(";")
        ),
      }),
    });
  };

  const displayText =
    selectedMarkets.length > 0
      ? `${selectedMarkets.length} market${selectedMarkets.length === 1 ? "" : "s"} selected`
      : "Select Market";

  if (useMobile) {
    return (
      <div className="w-full">
        <DropdownMenu
          open={isOpen}
          onOpenChange={(open) => {
            setIsOpen(open);
            // No navigation here - we handle it immediately in toggleMarket()
          }}
        >
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={isOpen}
              className="w-full justify-between font-normal bg-transparent"
            >
              <span className="truncate">{displayText}</span>
              <div className="flex items-center gap-1">
                <ChevronDownIcon className="h-4 w-4 shrink-0 opacity-50" />
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[var(--radix-dropdown-menu-trigger-width)] p-0 z-1000"
            align="start"
          >
            <div className="p-3 border-b">
              <div className="relative">
                <SearchIcon className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search..."
                  value={searchMarketText}
                  onChange={(e) => setSearchMarketText(e.target.value)}
                  className="!pl-10"
                />
              </div>
            </div>

            <div className="max-h-80 overflow-y-auto">
              {filteredMarkets.map((stateData) => (
                <div key={stateData.state}>
                  <DropdownMenuCheckboxItem
                    className="flex w-full items-center justify-between px-3 py-2 text-left hover:bg-accent"
                    onSelect={(e) => {
                      e.preventDefault();
                    }}
                    onClick={() => toggleState(stateData.state)}
                  >
                    <div className="flex flex-row items-center">
                      {currentSelectedStates.includes(stateData.state) && (
                        <CheckIcon size={14} className="mr-2" />
                      )}
                      <span className="font-medium">{stateData.state}</span>
                    </div>
                    <ChevronDownIcon
                      className={cn(
                        "h-4 w-4 transition-transform",
                        expandedStates.has(stateData.state) && "rotate-180"
                      )}
                    />
                  </DropdownMenuCheckboxItem>
                  {expandedStates.has(stateData.state) && (
                    <div className="bg-muted/30">
                      {stateData.markets.map((market) => (
                        <DropdownMenuCheckboxItem
                          key={market}
                          className="flex w-full items-center gap-2 px-6 py-2 text-left hover:bg-accent"
                          onSelect={(e) => {
                            e.preventDefault();
                          }}
                          onClick={() => toggleMarket(market)}
                        >
                          <div className="flex h-4 w-4 items-center justify-center">
                            {selectedMarkets.includes(market) && (
                              <CheckIcon className="h-3 w-3 text-primary" />
                            )}
                          </div>
                          <span className="text-sm">{market}</span>
                        </DropdownMenuCheckboxItem>
                      ))}
                    </div>
                  )}
                </div>
              ))}
              {filteredMarkets.length === 0 && (
                <div className="px-3 py-6 text-center text-sm text-muted-foreground">
                  No markets found
                </div>
              )}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
        {selectedMarkets.length > 0 && (
          <Button
            variant={"ghost"}
            className="text-muted-foreground cursor-pointer"
            asChild
          >
            <Link
              to="."
              params={(prev) => prev}
              // prettier-ignore
              search={(prev) => ({ ...prev, filter: encodeURIComponent(((decodeURIComponent(prev.filter || "")?.split(';').filter((f) => !f.includes('market')) || []).join(';'))) })}
            >
              <RotateCcwIcon className="h-4 w-4" />
              <span>Reset Markets</span>
            </Link>
          </Button>
        )}
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="relative justify-between h-auto py-1 bg-blue-20 text-dark-gray font-bold border border-transparent hover:bg-blue-20/80 cursor-pointer"
        >
          {selectedMarkets.length > 0 && (
            <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-red" />
          )}
          Market <ChevronDownIcon size={18} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="p-0 z-300" align="start">
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="Search..."
            value={searchMarketText}
            onValueChange={setSearchMarketText}
          />
          <CommandList className="max-h-auto overflow-y-auto">
            <div>
              <Button
                variant={"ghost"}
                className="cursor-pointer text-muted-foreground w-full"
                asChild
              >
                <Link
                  to="/properties"
                  params={(prev) => prev}
                  // prettier-ignore
                  search={(prev) => ({ ...prev, filter: encodeURIComponent(((decodeURIComponent(prev.filter || "")?.split(';').filter((f) => !f.includes('market')) || []).join(';'))) })}
                >
                  <RefreshCcwIcon />
                  <span className="text-xs font-medium">Reset Markets</span>
                </Link>
              </Button>
            </div>
            <ScrollArea className="h-[300px]" type="always">
              {filteredMarkets.length === 0 ? (
                <div className="py-4 px-2 text-sm text-muted-foreground">
                  No results found.
                </div>
              ) : (
                <React.Fragment>
                  {searchMarketText.length > 0 && filteredMarkets.length > 0
                    ? filteredMarkets.map((market) => (
                        <div>
                          <DropdownMenuLabel>{market.state}</DropdownMenuLabel>
                          <div className="px-2">
                            {market.markets.map((m) => (
                              <DropdownMenuCheckboxItem
                                key={m}
                                checked={selectedMarkets.includes(m)}
                                onSelect={(e) => {
                                  e.preventDefault();
                                }}
                                onCheckedChange={(checked) => {
                                  setSelectedMarkets((prev) => {
                                    if (checked) {
                                      return [...prev, m];
                                    }
                                    return prev.filter((item) => item !== m);
                                  });
                                }}
                              >
                                {m}
                              </DropdownMenuCheckboxItem>
                            ))}
                          </div>
                        </div>
                      ))
                    : LENNAR_MARKETS.map((market) => (
                        <DropdownMenuSub key={market.state}>
                          <DropdownMenuSubTrigger>
                            {currentSelectedStates.includes(market.state) && (
                              <CheckIcon size={14} className="mr-2" />
                            )}
                            {market.state}
                          </DropdownMenuSubTrigger>
                          <DropdownMenuPortal>
                            <DropdownMenuSubContent className="z-400">
                              {market.markets.map((m) => (
                                <DropdownMenuCheckboxItem
                                  key={m}
                                  checked={selectedMarkets.includes(m)}
                                  onSelect={(e) => {
                                    e.preventDefault();
                                  }}
                                  onCheckedChange={(checked) => {
                                    setSelectedMarkets((prev) => {
                                      if (checked) {
                                        return [...prev, m];
                                      }
                                      return prev.filter((item) => item !== m);
                                    });
                                  }}
                                >
                                  {m}
                                </DropdownMenuCheckboxItem>
                              ))}
                            </DropdownMenuSubContent>
                          </DropdownMenuPortal>
                        </DropdownMenuSub>
                      ))}
                </React.Fragment>
              )}
            </ScrollArea>
            {marketsFilterChanged && (
              <div className="p-2 border-t">
                <Button className="w-full bg-green-primary cursor-pointer">
                  <Link
                    to="/properties"
                    params={(prev) => prev}
                    search={(prev) => ({
                      ...prev,
                      filter: encodeURIComponent(
                        [
                          ...(selectedMarkets.length > 0
                            ? [
                                `market=in=(${selectedMarkets
                                  .map((m) => `'${m}'`)
                                  .sort((a, b) => a.localeCompare(b))
                                  .join(",")})`,
                              ]
                            : []),
                          ...(decodeURIComponent(prev.filter || "")
                            ?.split(";")
                            .filter(
                              (f) => !f.includes("market") && f.length > 0
                            ) || []),
                        ].join(";")
                      ),
                    })}
                  >
                    Apply Filters
                  </Link>
                </Button>
              </div>
            )}
          </CommandList>
        </Command>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
