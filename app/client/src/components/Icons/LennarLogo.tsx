import * as React from "react";
import { SVGProps } from "react";

interface LennarLogoProps extends SVGProps<SVGSVGElement> {
  color?: string;
  className?: string;
}

const LennarLogo: React.FC<LennarLogoProps> = ({ color, className }) => (
  <svg
    viewBox="0 0 102 10"
    fill={color}
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M4.127 0H0v9.367h11.508V7.561H4.127V0zM14.152 9.367H25.66V7.561h-7.381V5.574h7.381V3.768h-7.381V1.806h7.381V0H14.152v9.367zM41.091 6.22L33.623 0h-4.679v9.367h3.778V3.122l7.294 6.245h4.853V0h-3.778v6.22zM98.455 4.258c.552-.439.814-1.007.814-1.703 0-.49-.175-.93-.523-1.265-.35-.335-.843-.593-1.424-.826-.582-.206-1.22-.335-1.918-.412A61.39 61.39 0 0092.992 0h-7.963v9.367h4.127V1.73h2.528c.494 0 .9 0 1.279.026.377.026.697.051.959.129.406.103.668.232.842.387.175.155.233.361.233.593 0 .258-.058.465-.204.62-.116.155-.348.31-.61.438-.29.13-.668.233-1.104.258-.436.026-.988.052-1.627.052h-1.54l5.433 5.135h5.057l-4.272-4c1.017-.31 1.802-.67 2.325-1.11zM72.33 0L66.52 9.367h4.213l3.836-6.554 1.657 2.838h-2.5L72.65 7.458h4.65l1.104 1.91h4.214L76.806 0H72.33zM60.328 6.22L52.859 0h-4.678v9.367h3.777V3.122l7.294 6.245h4.853V0h-3.777v6.22zM100.372.438h.61c.204 0 .407.078.407.31 0 .103-.058.232-.174.258.116.026.174.155.174.258 0 .052 0 .207.058.232h-.319c-.029-.025-.029-.129-.029-.154 0-.104-.029-.207-.175-.207h-.232v.387h-.32V.438zm.32.465h.261c.087 0 .146-.052.146-.13 0-.076-.059-.102-.146-.102h-.261v.232z"></path>
  </svg>
);
export default LennarLogo;
