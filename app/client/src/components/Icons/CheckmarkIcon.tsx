import * as React from "react";
import { SVGProps } from "react";

interface CheckmarkIconProps extends SVGProps<SVGSVGElement> {
  color?: string;
  className?: string;
}

const CheckmarkIcon: React.FC<CheckmarkIconProps> = ({ color, className, ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke={color || "currentColor"}
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    <polyline points="20 6 9 17 4 12"></polyline>
  </svg>
);

export default CheckmarkIcon;