import * as React from "react";
import { SVGProps } from "react";

interface ForSaleIconProps extends SVGProps<SVGSVGElement> {
  color?: string;
  className?: string;
}

const ForSaleIcon: React.FC<ForSaleIconProps> = () => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5.73971 4.6L0.799705 4.6L0.799705 8.4L5.73971 8.4M10.2997 4.60001L31.1997 4.60001L31.1997 8.40001L10.2997 8.4" stroke="white" strokeLinejoin="round" />
    <rect x="31.1997" y="11.44" width="15.2" height="17.48" transform="rotate(90 31.1997 11.44)" stroke="white" strokeLinejoin="round" />
    <path d="M16.7598 8.39999V11.44" stroke="white" />
    <path d="M27.3999 8.39999V11.44" stroke="white" />
    <path d="M6.11992 31.2L9.91992 31.2L9.91992 5.74001L9.91992 0.800011L6.11992 0.80001L6.11992 5.74001L6.11992 31.2Z" stroke="white" strokeLinejoin="round" />
    <path d="M19.04 17.5967V22.08H21.5425V19.6659H23.3776V22.08H25.88V17.5967L22.46 15.24L19.04 17.5967Z" stroke="white" strokeLinejoin="round" />
  </svg>
);
export default ForSaleIcon;




