import * as React from "react";
import { SVGProps } from "react";

interface BoundaryIconProps extends SVGProps<SVGSVGElement> {
  color?: string;
  className?: string;
}

const BoundaryIcon: React.FC<BoundaryIconProps> = ({ color, className }) => (
  <svg
    viewBox="0 0 24 24"
    fill={color}
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M3 20C2.16667 20 1.45833 19.7083 0.875 19.125C0.291667 18.5417 0 17.8333 0 17C0 16.35 0.187667 15.771 0.563 15.263C0.938333 14.755 1.41733 14.392 2 14.174V5.824C1.41667 5.608 0.937667 5.24567 0.563 4.737C0.188333 4.22833 0.000666667 3.64933 0 3C0 2.16667 0.291667 1.45833 0.875 0.875C1.45833 0.291667 2.16667 0 3 0C3.65 0 4.22933 0.187667 4.738 0.563C5.24667 0.938333 5.609 1.41733 5.825 2H14.175C14.375 1.41667 14.7293 0.937667 15.238 0.563C15.7467 0.188333 16.334 0.000666667 17 0C17.8333 0 18.5417 0.291667 19.125 0.875C19.7083 1.45833 20 2.16667 20 3C20 3.66667 19.8127 4.25433 19.438 4.763C19.0633 5.27167 18.584 5.62567 18 5.825V14.175C18.5833 14.3917 19.0627 14.7543 19.438 15.263C19.8133 15.7717 20.0007 16.3507 20 17C20 17.8333 19.7083 18.5417 19.125 19.125C18.5417 19.7083 17.8333 20 17 20C16.35 20 15.771 19.8127 15.263 19.438C14.755 19.0633 14.392 18.584 14.174 18H5.824C5.60733 18.5833 5.24467 19.0627 4.736 19.438C4.22733 19.8133 3.64867 20.0007 3 20ZM3 4C3.28333 4 3.521 3.904 3.713 3.712C3.905 3.52 4.00067 3.28267 4 3C3.99933 2.71733 3.90333 2.48 3.712 2.288C3.52067 2.096 3.28333 2 3 2C2.71667 2 2.47933 2.096 2.288 2.288C2.09667 2.48 2.00067 2.71733 2 3C1.99933 3.28267 2.09533 3.52033 2.288 3.713C2.48067 3.90567 2.718 4.00133 3 4ZM17 4C17.2833 4 17.521 3.904 17.713 3.712C17.905 3.52 18.0007 3.28267 18 3C17.9993 2.71733 17.9033 2.48 17.712 2.288C17.5207 2.096 17.2833 2 17 2C16.7167 2 16.4793 2.096 16.288 2.288C16.0967 2.48 16.0007 2.71733 16 3C15.9993 3.28267 16.0953 3.52033 16.288 3.713C16.4807 3.90567 16.718 4.00133 17 4ZM5.825 16H14.175C14.325 15.5667 14.5583 15.1917 14.875 14.875C15.1917 14.5583 15.5667 14.325 16 14.175V5.825C15.5667 5.675 15.1917 5.44167 14.875 5.125C14.5583 4.80833 14.325 4.43333 14.175 4H5.825C5.675 4.43333 5.44167 4.80833 5.125 5.125C4.80833 5.44167 4.43333 5.675 4 5.825V14.175C4.43333 14.325 4.80833 14.5583 5.125 14.875C5.44167 15.1917 5.675 15.5667 5.825 16ZM17 18C17.2833 18 17.521 17.904 17.713 17.712C17.905 17.52 18.0007 17.2827 18 17C17.9993 16.7173 17.9033 16.48 17.712 16.288C17.5207 16.096 17.2833 16 17 16C16.7167 16 16.4793 16.096 16.288 16.288C16.0967 16.48 16.0007 16.7173 16 17C15.9993 17.2827 16.0953 17.5203 16.288 17.713C16.4807 17.9057 16.718 18.0013 17 18ZM3 18C3.28333 18 3.521 17.904 3.713 17.712C3.905 17.52 4.00067 17.2827 4 17C3.99933 16.7173 3.90333 16.48 3.712 16.288C3.52067 16.096 3.28333 16 3 16C2.71667 16 2.47933 16.096 2.288 16.288C2.09667 16.48 2.00067 16.7173 2 17C1.99933 17.2827 2.09533 17.5203 2.288 17.713C2.48067 17.9057 2.718 18.0013 3 18Z" />
  </svg>
);
export default BoundaryIcon;