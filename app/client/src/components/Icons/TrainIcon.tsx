import * as React from "react";
import { SVGProps } from "react";

interface TrainSvgProps extends SVGProps<SVGSVGElement> {
  color?: string;
  className?: string;
}

const TrainSvg: React.FC<TrainSvgProps> = ({ color = "none", className }) => (
  <svg
    viewBox="0 0 24 24"
    fill={color}
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18 3H6C4.89543 3 4 3.89543 4 5V17C4 18.1046 4.89543 19 6 19H18C19.1046 19 20 18.1046 20 17V5C20 3.89543 19.1046 3 18 3Z"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4 11H20M12 3V11M8 19L6 22M18 22L16 19M8 15H8.01M16 15H16.01"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default TrainSvg;