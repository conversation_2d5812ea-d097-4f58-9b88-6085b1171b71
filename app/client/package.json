{"name": "@marketplace/client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit || true && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@aws-amplify/auth": "^6.12.4", "@aws-amplify/ui-react": "^6.11.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.6", "@radix-ui/react-visually-hidden": "^1.1.0", "@spatiallaser/map": "^1.0.280", "@tailwindcss/vite": "^4.1.7", "@tanstack/query-sync-storage-persister": "^5.77.1", "@tanstack/react-query": "^5.77.1", "@tanstack/react-query-devtools": "^5.79.0", "@tanstack/react-query-persist-client": "^5.79.0", "@tanstack/react-router": "^1.120.10", "@tanstack/react-router-devtools": "^1.120.10", "@tanstack/react-table": "^8.21.3", "@tanstack/router-devtools": "^1.120.13", "@turf/turf": "^7.2.0", "aws-amplify": "^6.14.4", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dayjs": "^1.11.13", "lodash": "^4.17.21", "lucide-react": "^0.522.0", "mapbox-gl": "^3.12.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-aria-components": "^1.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.58.0", "react-hot-toast": "^2.5.2", "react-map-gl": "^8.0.4", "react-number-format": "^5.4.4", "recharts": "^2.15.3", "sonner": "^1.5.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.7"}, "devDependencies": {"@eslint/js": "^9.27.0", "@tanstack/router-vite-plugin": "^1.120.10", "@types/lodash": "^4.17.18", "@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "geojson": "^0.5.0", "globals": "^16.2.0", "postcss": "^8.5.3", "postcss-cli": "^11.0.1", "prettier": "^3.6.0", "prettier-plugin-tailwindcss": "^0.6.13", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5"}}