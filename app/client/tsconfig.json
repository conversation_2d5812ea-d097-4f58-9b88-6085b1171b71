{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["src/*"]}, "target": "ES2020", "module": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "skipLibCheck": true}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}