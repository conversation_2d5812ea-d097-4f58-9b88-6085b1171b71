# Shopping Cart Implementation Guide

## Overview

This document outlines the implementation of the shopping cart feature for property offers in the Lennar Marketplace application. The feature allows users to add multiple properties to a cart, review them together, and submit bulk offers.

## Features Implemented

### Frontend Components

1. **Cart Icon** (`CartIcon.tsx`)

   - Displays in header next to "Close Map" button
   - Shows badge with item count
   - Opens cart modal when clicked
2. **Add to <PERSON><PERSON>** (`AddToCartButton.tsx`)

   - Located next to "Start Purchase" button in PropertyDetailHeader
   - Shows different states: "Add to Cart", "In Cart", "Remove"
   - Hover interaction for removal when property is in cart
3. **Cart Modal** (`CartModal.tsx`)

   - Displays all cart items in table format
   - Allows editing offer prices
   - Checkbox selection for bulk submission
   - Progress to bulk offer form
4. **Cart Item Row** (`CartItemRow.tsx`)

   - Individual property display with offer price input
   - Confirmation checkbox
   - Remove button
5. **Bulk Offer Form** (`BulkOfferForm.tsx`)

   - User contact information
   - Role-specific fields (agent compensation, financing requirements)
   - Offer summary

### State Management

- **CartContext** (`CartContext.tsx`) - Global cart state management
- **CartTypes** (`cartTypes.ts`) - TypeScript interfaces
- **Cart API** (`cart-api.ts`) - API service layer
- **Bulk Submission** (`bulk-submit-offers.ts`) - Handles bulk offer processing

### Data Persistence

- **localStorage**: Cart data persists across browser sessions
- **Database**: Full cart synchronization with backend (schema provided)

## API Integration Requirements

### New API Endpoints Needed

```typescript
// Cart Management
GET    /api/sbs/prod/api/v1/lennar/cart
POST   /api/sbs/prod/api/v1/lennar/cart
DELETE /api/sbs/prod/api/v1/lennar/cart/clear

// Cart Items
POST   /api/sbs/prod/api/v1/lennar/cart/items
PATCH  /api/sbs/prod/api/v1/lennar/cart/items/:propertyId
DELETE /api/sbs/prod/api/v1/lennar/cart/items/:propertyId

// Bulk Submissions
POST   /api/sbs/prod/api/v1/lennar/cart/submit-bulk-offers
GET    /api/sbs/prod/api/v1/lennar/bulk-submissions/:id
```

### Database Schema

The complete database schema is provided in `database_schema.sql` with these key tables:

- `user_carts` - Main cart table
- `cart_items` - Individual properties in cart
- `bulk_offer_submissions` - Tracks bulk submissions
- `bulk_offer_results` - Individual offer results

### Existing API Integration

The bulk submission system integrates with existing APIs:

- `submitOffer()` - Used for individual property submissions
- `sendOfferData()` - Additional offer data submission
- Authentication system - Uses existing `getUserToken()`

## Implementation Steps

### 1. Backend API Development

Implement the cart management endpoints using the provided schema:

```sql
-- See database_schema.sql for complete implementation
CREATE TABLE lennar.user_carts (...);
CREATE TABLE lennar.cart_items (...);
-- etc.
```

### 2. Frontend Integration

The frontend is ready to use. Key integration points:

```typescript
// Add CartProvider to your app
<CartProvider>
  <App />
  <CartModal />
</CartProvider>

// Use cart functionality in components
const { addToCart, removeFromCart, isPropertyInCart } = useCart();
```

### 3. Property Data Conversion

The system automatically converts property data using `convertPropertyToCartItem()`:

```typescript
// Handles both LennarSinglePropertyDataType and other formats
const cartProperty = convertPropertyToCartItem(propertyData);
```

## Usage Examples

### Adding Property to Cart

```typescript
const { addToCart } = useCart();

// In PropertyDetailHeader
<AddToCartButton />

// Programmatically
await addToCart(selectedBuyersViewRecord);
```

### Bulk Offer Submission

```typescript
const { submitSelectedOffers } = useCart();

const userInfo = {
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  userRole: 'investor',
  financingRequired: true
};

const submission = await submitSelectedOffers(userInfo);
```

## Error Handling

The system includes comprehensive error handling:

- Network failures
- Validation errors
- Partial submission failures
- Authentication errors

## Performance Considerations

- **Sequential Submission**: Default behavior to respect API rate limits
- **Parallel Option**: Available with configurable concurrency
- **localStorage Caching**: Immediate UI updates
- **Lazy Loading**: Cart data loaded on demand

## Testing Considerations

### Unit Tests Needed

1. Cart state management
2. Property data conversion
3. API error handling
4. Form validation

### Integration Tests

1. End-to-end cart workflow
2. Bulk submission process
3. Data persistence
4. Error recovery

## Security Considerations

- User authentication required for all cart operations
- Input validation on offer prices and user data
- Rate limiting for bulk submissions
- CSRF protection on API endpoints

## Future Enhancements

1. **Saved Carts**: Multiple named carts per user
2. **Cart Sharing**: Share cart with team members
3. **Offer Templates**: Pre-filled offer templates
4. **Advanced Analytics**: Bulk submission tracking
5. **Mobile Optimization**: Touch-friendly cart interface

## Troubleshooting

### Common Issues

1. **Cart not persisting**: Check localStorage availability
2. **API errors**: Verify authentication tokens
3. **Bulk submission failures**: Check individual property data
4. **Performance issues**: Consider reducing batch sizes

### Debug Information

The system logs detailed information:

```typescript
// Enable debug logging
localStorage.setItem('cart_debug', 'true');

// Check console for:
// - Cart state changes
// - API requests/responses
// - Bulk submission progress
```

## Support

For implementation support:

1. Review provided code examples
2. Check browser console for error messages
3. Verify API endpoint implementations
4. Test with sample data from `cart-api.ts`

# Shopping Cart API Specification

## Overview

This document provides complete API specifications for implementing the shopping cart backend system for the Lennar Marketplace application. This spec is designed for AI implementation with all necessary types, schemas, and business logic included.

## Base Configuration

```typescript
// Base URL
const API_BASE_URL = '/api/sbs/prod/api/v1/lennar';

// Authentication: Bearer token required for all endpoints
// Extract user_id from JWT token for database operations
```

## TypeScript Types & Interfaces

```typescript
// Database Entity Types
interface UserCart {
  id: number;
  user_id: string;
  created_at: string;
  updated_at: string;
  status: 'active' | 'archived' | 'submitted';
  total_items: number;
}

interface CartItem {
  id: number;
  cart_id: number;
  property_id: number;
  property_address: string;
  property_city?: string;
  property_state?: string;
  property_postal_code?: string;
  listed_price?: number;
  beds?: number;
  baths?: number;
  sqft?: number;
  placekey?: string;
  offer_price?: number;
  is_confirmed: boolean;
  notes?: string;
  created_at: string;
  updated_at: string;
}

interface BulkOfferSubmission {
  id: number;
  cart_id: number;
  user_id: string;
  total_properties: number;
  successful_submissions: number;
  failed_submissions: number;
  submission_status: 'pending' | 'processing' | 'completed' | 'failed';
  user_email: string;
  user_phone?: string;
  user_first_name?: string;
  user_last_name?: string;
  user_role: 'agent' | 'investor' | 'agent_investor';
  created_at: string;
  completed_at?: string;
}

interface BulkOfferResult {
  id: number;
  bulk_submission_id: number;
  cart_item_id: number;
  property_id: number;
  offer_price: number;
  submission_status: 'pending' | 'success' | 'failed';
  api_response_id?: string;
  error_message?: string;
  submitted_at: string;
}

// API Request/Response Types
interface CartResponse {
  id: number;
  userId: string;
  status: 'active' | 'archived' | 'submitted';
  totalItems: number;
  items: CartItemResponse[];
  createdAt: string;
  updatedAt: string;
}

interface CartItemResponse {
  id: number;
  cartId: number;
  propertyId: number;
  propertyAddress: string;
  propertyCity?: string;
  propertyState?: string;
  propertyPostalCode?: string;
  listedPrice?: number;
  beds?: number;
  baths?: number;
  sqft?: number;
  placekey?: string;
  offerPrice?: number;
  isConfirmed: boolean;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface CreateCartItemRequest {
  propertyId: number;
  propertyAddress: string;
  propertyCity?: string;
  propertyState?: string;
  propertyPostalCode?: string;
  listedPrice?: number;
  beds?: number;
  baths?: number;
  sqft?: number;
  placekey?: string;
  isConfirmed: boolean;
}

interface UpdateCartItemRequest {
  offerPrice?: number;
  isConfirmed?: boolean;
  notes?: string;
}

interface BulkSubmissionRequest {
  email: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  userRole: 'agent' | 'investor' | 'agent_investor';
  compensation?: string;
  financingRequired?: boolean;
}

// Error Response Type
interface ApiError {
  error: string;
  message: string;
  code?: string;
  details?: any;
}
```

## API Endpoints Specification

### 1. Get User Cart

```typescript
GET /api/sbs/prod/api/v1/lennar/cart

// Headers
Authorization: Bearer <jwt_token>

// Response Success (200)
CartResponse

// Response Not Found (404)
{
  "error": "NOT_FOUND",
  "message": "No active cart found for user"
}

// Implementation Logic
async function getUserCart(userId: string): Promise<CartResponse | null> {
  // 1. Query database for active cart
  const cart = await db.query(`
    SELECT * FROM lennar.user_carts 
    WHERE user_id = $1 AND status = 'active'
  `, [userId]);
  
  if (!cart.rows[0]) {
    return null;
  }
  
  // 2. Get cart items
  const items = await db.query(`
    SELECT * FROM lennar.cart_items 
    WHERE cart_id = $1 
    ORDER BY created_at ASC
  `, [cart.rows[0].id]);
  
  // 3. Transform to API response format
  return {
    id: cart.rows[0].id,
    userId: cart.rows[0].user_id,
    status: cart.rows[0].status,
    totalItems: cart.rows[0].total_items,
    items: items.rows.map(transformCartItem),
    createdAt: cart.rows[0].created_at,
    updatedAt: cart.rows[0].updated_at
  };
}

function transformCartItem(dbItem: any): CartItemResponse {
  return {
    id: dbItem.id,
    cartId: dbItem.cart_id,
    propertyId: dbItem.property_id,
    propertyAddress: dbItem.property_address,
    propertyCity: dbItem.property_city,
    propertyState: dbItem.property_state,
    propertyPostalCode: dbItem.property_postal_code,
    listedPrice: dbItem.listed_price,
    beds: dbItem.beds,
    baths: dbItem.baths,
    sqft: dbItem.sqft,
    placekey: dbItem.placekey,
    offerPrice: dbItem.offer_price,
    isConfirmed: dbItem.is_confirmed,
    notes: dbItem.notes,
    createdAt: dbItem.created_at,
    updatedAt: dbItem.updated_at
  };
}
```

### 2. Create Cart

```typescript
POST /api/sbs/prod/api/v1/lennar/cart

// Headers
Authorization: Bearer <jwt_token>
Content-Type: application/json

// Request Body (empty object)
{}

// Response Success (201)
CartResponse

// Response Conflict (409)
{
  "error": "CART_EXISTS",
  "message": "User already has an active cart"
}

// Implementation Logic
async function createCart(userId: string): Promise<CartResponse> {
  // 1. Check if active cart exists
  const existing = await db.query(`
    SELECT id FROM lennar.user_carts 
    WHERE user_id = $1 AND status = 'active'
  `, [userId]);
  
  if (existing.rows[0]) {
    throw new ApiError('CART_EXISTS', 'User already has an active cart', 409);
  }
  
  // 2. Create new cart
  const result = await db.query(`
    INSERT INTO lennar.user_carts (user_id, status, total_items)
    VALUES ($1, 'active', 0)
    RETURNING *
  `, [userId]);
  
  const cart = result.rows[0];
  
  return {
    id: cart.id,
    userId: cart.user_id,
    status: cart.status,
    totalItems: cart.total_items,
    items: [],
    createdAt: cart.created_at,
    updatedAt: cart.updated_at
  };
}
```

### 3. Add Item to Cart

```typescript
POST /api/sbs/prod/api/v1/lennar/cart/items

// Headers
Authorization: Bearer <jwt_token>
Content-Type: application/json

// Request Body
CreateCartItemRequest

// Response Success (201)
CartItemResponse

// Response Conflict (409)
{
  "error": "ITEM_EXISTS",
  "message": "Property already in cart"
}

// Implementation Logic
async function addItemToCart(userId: string, itemData: CreateCartItemRequest): Promise<CartItemResponse> {
  // 1. Get or create active cart
  let cart = await getUserActiveCart(userId);
  if (!cart) {
    cart = await createCart(userId);
  }
  
  // 2. Check if property already in cart
  const existing = await db.query(`
    SELECT id FROM lennar.cart_items 
    WHERE cart_id = $1 AND property_id = $2
  `, [cart.id, itemData.propertyId]);
  
  if (existing.rows[0]) {
    throw new ApiError('ITEM_EXISTS', 'Property already in cart', 409);
  }
  
  // 3. Insert cart item (total_items updated by trigger)
  const result = await db.query(`
    INSERT INTO lennar.cart_items (
      cart_id, property_id, property_address, property_city, 
      property_state, property_postal_code, listed_price, 
      beds, baths, sqft, placekey, is_confirmed
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
    RETURNING *
  `, [
    cart.id, itemData.propertyId, itemData.propertyAddress,
    itemData.propertyCity, itemData.propertyState, itemData.propertyPostalCode,
    itemData.listedPrice, itemData.beds, itemData.baths, itemData.sqft,
    itemData.placekey, itemData.isConfirmed
  ]);
  
  return transformCartItem(result.rows[0]);
}

async function getUserActiveCart(userId: string): Promise<UserCart | null> {
  const result = await db.query(`
    SELECT * FROM lennar.user_carts 
    WHERE user_id = $1 AND status = 'active'
  `, [userId]);
  
  return result.rows[0] || null;
}
```

### 4. Update Cart Item

```typescript
PATCH /api/sbs/prod/api/v1/lennar/cart/items/:propertyId

// Headers
Authorization: Bearer <jwt_token>
Content-Type: application/json

// Path Parameters
propertyId: number

// Request Body
UpdateCartItemRequest

// Response Success (200)
CartItemResponse

// Response Not Found (404)
{
  "error": "ITEM_NOT_FOUND",
  "message": "Cart item not found"
}

// Implementation Logic
async function updateCartItem(
  userId: string, 
  propertyId: number, 
  updates: UpdateCartItemRequest
): Promise<CartItemResponse> {
  
  // 1. Verify item belongs to user's active cart
  const item = await db.query(`
    SELECT ci.* FROM lennar.cart_items ci
    JOIN lennar.user_carts uc ON ci.cart_id = uc.id
    WHERE uc.user_id = $1 AND uc.status = 'active' AND ci.property_id = $2
  `, [userId, propertyId]);
  
  if (!item.rows[0]) {
    throw new ApiError('ITEM_NOT_FOUND', 'Cart item not found', 404);
  }
  
  // 2. Build dynamic update query
  const updateFields = [];
  const values = [];
  let paramIndex = 1;
  
  if (updates.offerPrice !== undefined) {
    updateFields.push(`offer_price = $${paramIndex++}`);
    values.push(updates.offerPrice);
  }
  
  if (updates.isConfirmed !== undefined) {
    updateFields.push(`is_confirmed = $${paramIndex++}`);
    values.push(updates.isConfirmed);
  }
  
  if (updates.notes !== undefined) {
    updateFields.push(`notes = $${paramIndex++}`);
    values.push(updates.notes);
  }
  
  updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
  values.push(item.rows[0].id);
  
  // 3. Execute update
  const result = await db.query(`
    UPDATE lennar.cart_items 
    SET ${updateFields.join(', ')}
    WHERE id = $${paramIndex}
    RETURNING *
  `, values);
  
  return transformCartItem(result.rows[0]);
}
```

### 5. Remove Item from Cart

```typescript
DELETE /api/sbs/prod/api/v1/lennar/cart/items/:propertyId

// Headers
Authorization: Bearer <jwt_token>

// Path Parameters
propertyId: number

// Response Success (204)
// No content

// Response Not Found (404)
{
  "error": "ITEM_NOT_FOUND",
  "message": "Cart item not found"
}

// Implementation Logic
async function removeCartItem(userId: string, propertyId: number): Promise<void> {
  
  // 1. Delete item (total_items updated by trigger)
  const result = await db.query(`
    DELETE FROM lennar.cart_items 
    WHERE id IN (
      SELECT ci.id FROM lennar.cart_items ci
      JOIN lennar.user_carts uc ON ci.cart_id = uc.id
      WHERE uc.user_id = $1 AND uc.status = 'active' AND ci.property_id = $2
    )
  `, [userId, propertyId]);
  
  if (result.rowCount === 0) {
    throw new ApiError('ITEM_NOT_FOUND', 'Cart item not found', 404);
  }
}
```

### 6. Clear Cart

```typescript
DELETE /api/sbs/prod/api/v1/lennar/cart/clear

// Headers
Authorization: Bearer <jwt_token>

// Response Success (204)
// No content

// Response Not Found (404)
{
  "error": "CART_NOT_FOUND",
  "message": "No active cart found"
}

// Implementation Logic
async function clearCart(userId: string): Promise<void> {
  
  // 1. Delete all items from active cart (total_items updated by trigger)
  const result = await db.query(`
    DELETE FROM lennar.cart_items 
    WHERE cart_id IN (
      SELECT id FROM lennar.user_carts 
      WHERE user_id = $1 AND status = 'active'
    )
  `, [userId]);
  
  if (result.rowCount === 0) {
    throw new ApiError('CART_NOT_FOUND', 'No active cart found', 404);
  }
}
```

### 7. Submit Bulk Offers

```typescript
POST /api/sbs/prod/api/v1/lennar/cart/submit-bulk-offers

// Headers
Authorization: Bearer <jwt_token>
Content-Type: application/json

// Request Body
BulkSubmissionRequest

// Response Success (202)
{
  "submissionId": number,
  "message": "Bulk submission started",
  "totalProperties": number
}

// Response Bad Request (400)
{
  "error": "NO_CONFIRMED_ITEMS",
  "message": "No confirmed items found in cart"
}

// Implementation Logic
async function submitBulkOffers(
  userId: string, 
  submissionData: BulkSubmissionRequest
): Promise<{ submissionId: number; message: string; totalProperties: number }> {
  
  // 1. Get confirmed cart items
  const confirmedItems = await db.query(`
    SELECT ci.* FROM lennar.cart_items ci
    JOIN lennar.user_carts uc ON ci.cart_id = uc.id
    WHERE uc.user_id = $1 AND uc.status = 'active' 
    AND ci.is_confirmed = true AND ci.offer_price > 0
  `, [userId]);
  
  if (confirmedItems.rows.length === 0) {
    throw new ApiError('NO_CONFIRMED_ITEMS', 'No confirmed items found in cart', 400);
  }
  
  // 2. Create bulk submission record
  const submission = await db.query(`
    INSERT INTO lennar.bulk_offer_submissions (
      cart_id, user_id, total_properties, submission_status,
      user_email, user_phone, user_first_name, user_last_name, user_role
    ) 
    SELECT uc.id, $1, $2, 'pending', $3, $4, $5, $6, $7
    FROM lennar.user_carts uc 
    WHERE uc.user_id = $1 AND uc.status = 'active'
    RETURNING id
  `, [
    userId, confirmedItems.rows.length, submissionData.email,
    submissionData.phone, submissionData.firstName, submissionData.lastName,
    submissionData.userRole
  ]);
  
  const submissionId = submission.rows[0].id;
  
  // 3. Create bulk offer result records
  for (const item of confirmedItems.rows) {
    await db.query(`
      INSERT INTO lennar.bulk_offer_results (
        bulk_submission_id, cart_item_id, property_id, offer_price
      ) VALUES ($1, $2, $3, $4)
    `, [submissionId, item.id, item.property_id, item.offer_price]);
  }
  
  // 4. Start async processing (implement based on your queue system)
  await startBulkOfferProcessing(submissionId);
  
  return {
    submissionId,
    message: "Bulk submission started",
    totalProperties: confirmedItems.rows.length
  };
}

// Note: Implement this based on your background job system
async function startBulkOfferProcessing(submissionId: number): Promise<void> {
  // Queue background job to process offers
  // This should call your existing submitOffer API for each property
  // Update bulk_offer_results and bulk_offer_submissions tables with results
}
```

### 8. Get Bulk Submission Status

```typescript
GET /api/sbs/prod/api/v1/lennar/bulk-submissions/:submissionId

// Headers
Authorization: Bearer <jwt_token>

// Path Parameters
submissionId: number

// Response Success (200)
{
  "id": number,
  "cartId": number,
  "userId": string,
  "totalProperties": number,
  "successfulSubmissions": number,
  "failedSubmissions": number,
  "submissionStatus": "pending" | "processing" | "completed" | "failed",
  "userEmail": string,
  "userPhone": string,
  "userFirstName": string,
  "userLastName": string,
  "userRole": "agent" | "investor" | "agent_investor",
  "createdAt": string,
  "completedAt": string,
  "results": Array<{
    "propertyId": number,
    "offerPrice": number,
    "status": "pending" | "success" | "failed",
    "errorMessage": string
  }>
}

// Implementation Logic
async function getBulkSubmissionStatus(
  userId: string, 
  submissionId: number
): Promise<any> {
  
  // 1. Get submission (verify ownership)
  const submission = await db.query(`
    SELECT * FROM lennar.bulk_offer_submissions 
    WHERE id = $1 AND user_id = $2
  `, [submissionId, userId]);
  
  if (!submission.rows[0]) {
    throw new ApiError('SUBMISSION_NOT_FOUND', 'Bulk submission not found', 404);
  }
  
  // 2. Get results
  const results = await db.query(`
    SELECT property_id, offer_price, submission_status, error_message
    FROM lennar.bulk_offer_results 
    WHERE bulk_submission_id = $1
    ORDER BY submitted_at ASC
  `, [submissionId]);
  
  return {
    ...submission.rows[0],
    results: results.rows
  };
}
```

## Error Handling

```typescript
class ApiError extends Error {
  constructor(
    public code: string,
    public message: string,
    public statusCode: number = 500,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Global error handler
function handleApiError(error: any, req: Request, res: Response, next: NextFunction) {
  if (error instanceof ApiError) {
    return res.status(error.statusCode).json({
      error: error.code,
      message: error.message,
      details: error.details
    });
  }
  
  // Database errors
  if (error.code === '23505') { // Unique violation
    return res.status(409).json({
      error: 'CONFLICT',
      message: 'Resource already exists'
    });
  }
  
  // Default error
  return res.status(500).json({
    error: 'INTERNAL_ERROR',
    message: 'An unexpected error occurred'
  });
}
```

## Authentication Middleware

```typescript
async function authenticateUser(req: Request, res: Response, next: NextFunction) {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({
        error: 'UNAUTHORIZED',
        message: 'Authentication token required'
      });
    }
    
    // Verify JWT and extract user_id
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = { id: decoded.sub }; // Adjust based on your JWT structure
    
    next();
  } catch (error) {
    return res.status(401).json({
      error: 'INVALID_TOKEN',
      message: 'Invalid authentication token'
    });
  }
}
```

## Background Job Processing

```typescript
// Example implementation for bulk offer processing
async function processBulkOffers(submissionId: number): Promise<void> {
  // 1. Update status to processing
  await db.query(`
    UPDATE lennar.bulk_offer_submissions 
    SET submission_status = 'processing' 
    WHERE id = $1
  `, [submissionId]);
  
  // 2. Get all pending results
  const results = await db.query(`
    SELECT bor.*, ci.property_address 
    FROM lennar.bulk_offer_results bor
    JOIN lennar.cart_items ci ON bor.cart_item_id = ci.id
    WHERE bor.bulk_submission_id = $1 AND bor.submission_status = 'pending'
  `, [submissionId]);
  
  let successful = 0;
  let failed = 0;
  
  // 3. Process each offer
  for (const result of results.rows) {
    try {
      // Call your existing offer submission API
      const response = await submitSingleOffer({
        propertyId: result.property_id,
        offerPrice: result.offer_price,
        // ... other required fields
      });
      
      // Update success
      await db.query(`
        UPDATE lennar.bulk_offer_results 
        SET submission_status = 'success', api_response_id = $1
        WHERE id = $2
      `, [response.id, result.id]);
      
      successful++;
      
    } catch (error) {
      // Update failure
      await db.query(`
        UPDATE lennar.bulk_offer_results 
        SET submission_status = 'failed', error_message = $1
        WHERE id = $2
      `, [error.message, result.id]);
      
      failed++;
    }
  }
  
  // 4. Update final status
  await db.query(`
    UPDATE lennar.bulk_offer_submissions 
    SET submission_status = 'completed', 
        successful_submissions = $1, 
        failed_submissions = $2,
        completed_at = CURRENT_TIMESTAMP
    WHERE id = $3
  `, [successful, failed, submissionId]);
}
```

## Implementation Notes

1. **Database Connection**: Use connection pooling for PostgreSQL
2. **Transactions**: Wrap critical operations in database transactions
3. **Validation**: Validate all input data before database operations
4. **Logging**: Log all API operations for debugging
5. **Rate Limiting**: Implement rate limiting for bulk operations
6. **Caching**: Consider caching frequently accessed cart data

This specification provides everything needed to implement the complete shopping cart backend API system. The frontend is already implemented and will work seamlessly once these APIs are deployed.
