
/** @type {import('tailwindcss').Config} */
module.exports = {
  prefix: 'tw-',
  content: [
    "./app/client/index.html",
    "./app/client/src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    screens: {
      sm: '375px',
      md: '768px',
      lg: '1440px',
      xl: '1920px',
      '2xl': '2560px',
    },
    extend: {
      colors: {
        black: {
          DEFAULT: '#1a1a1a',
          text: '#222222', //'text-black'
        },
        white: '#FEFEFE',
        gray: {
          100: '#F5F5F5', //'super-light-gray'
          300: '#D9D9D9', //'light-gray'
          700: '#6A6A6A', //'dark-gray'
        },
        blue: {
          100: '#E6F4FF', //'lennar-blue-light'
          300: '#1890FF', //'button-blue'
          700: '#0#315D9E', //'lennar-blue-dark'
        },
        green: {
          100: '#E7FFE7', //'light-green'
          700: '#008000', //'dark-green'
        }
      },
    },
  },
  plugins: [],
}